# Spot-Server 现货交易服务器

## 项目概述

Spot-Server 是一个基于 Go 语言开发的现货交易服务器系统，采用微服务架构设计，支持多交易所接入、实时行情处理、订单撮合、风险控制等核心功能。

## 技术栈

- **语言**: Go 1.18+
- **数据库**: MySQL, Redis
- **消息队列**: RabbitMQ
- **RPC框架**: RPCX
- **Web框架**: Gin
- **WebSocket**: Gorilla WebSocket
- **配置管理**: YAML
- **日志**: Zap
- **监控**: pprof

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Client    │    │  Mobile Client  │    │  Third Party    │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      API Gateway          │
                    └─────────────┬─────────────┘
                                 │
        ┌────────────────────────┼────────────────────────┐
        │                       │                        │
┌───────▼───────┐    ┌──────────▼──────────┐    ┌────────▼────────┐
│   Core Service │    │   Order Service     │    │  Market Service │
└───────┬───────┘    └──────────┬──────────┘    └────────┬────────┘
        │                       │                        │
        └───────────────────────┼────────────────────────┘
                               │
    ┌──────────────────────────┼──────────────────────────┐
    │                         │                          │
┌───▼────┐  ┌────▼────┐  ┌────▼────┐  ┌────▼────┐  ┌───▼────┐
│ Limit  │  │ KLine   │  │ Closing │  │ Push    │  │ Task   │
│Service │  │ Service │  │ Service │  │ Service │  │Service │
└────────┘  └─────────┘  └─────────┘  └─────────┘  └────────┘
```

## 核心服务模块

### 1. Core Service (核心服务)
- **路径**: `pkg/core/`
- **功能**: 系统核心业务逻辑，用户管理，权限控制
- **端口**: 配置文件指定
- **依赖**: MySQL, Redis, User RPC, Order RPC, Wallet RPC

### 2. Order Service (订单服务)
- **路径**: `pkg/order/`
- **功能**: 订单处理，撮合引擎，交易执行
- **特性**: 
  - 订单生命周期管理
  - 实时撮合引擎
  - 风险控制
  - 成交通知

### 3. Market Service (行情服务)
- **路径**: `pkg/market/`
- **功能**: 多交易所行情接入，数据处理和分发
- **支持交易所**:
  - Binance (币安)
  - Huobi (火币)
  - OKEx
  - Coinbase
  - Bitfinex
  - 其他主流交易所

### 4. Limit Service (限价服务)
- **路径**: `pkg/limit/`
- **功能**: 限价单处理，条件单触发
- **特性**:
  - 限价单队列管理
  - 价格监控
  - 自动触发机制

### 5. KLine Service (K线服务)
- **路径**: `pkg/kline/`
- **功能**: K线数据生成，历史数据管理
- **特性**:
  - 多时间周期K线
  - 实时数据更新
  - 历史数据存储

### 6. Closing Service (清算服务)
- **路径**: `pkg/closing/`
- **功能**: 交易清算，资金结算
- **特性**:
  - 多线程处理
  - 资金安全保障
  - 清算记录

### 7. Plan Order Service (计划订单服务)
- **路径**: `pkg/planorder/`
- **功能**: 计划订单，策略订单
- **特性**:
  - 条件触发
  - 策略执行
  - 定时任务

### 8. Push Service (推送服务)
- **路径**: `pkg/push/`
- **功能**: 实时消息推送，WebSocket连接管理
- **特性**:
  - 实时行情推送
  - 订单状态通知
  - 用户消息推送

### 9. Task Service (任务服务)
- **路径**: `pkg/task/`
- **功能**: 定时任务，后台作业处理
- **特性**:
  - 定时任务调度
  - 数据统计
  - 系统维护

## 公共库 (libs/)

### 核心库
- **cache**: Redis缓存操作
- **database**: MySQL数据库操作
- **conf**: 配置文件管理
- **log**: 日志系统
- **messagequeue**: 消息队列
- **proto**: 协议定义

### 交易所接入库
- **binance**: 币安交易所API
- **huobi**: 火币交易所API
- **okex**: OKEx交易所API
- **coinbase**: Coinbase交易所API
- **bitfinex**: Bitfinex交易所API
- 其他交易所接入库...

### 工具库
- **utils**: 通用工具函数
- **crypto**: 加密解密
- **convert**: 数据转换
- **nums**: 数值处理
- **sign**: 签名验证

## 快速开始

### 环境要求
- Go 1.18+
- MySQL 5.7+
- Redis 6.0+
- RabbitMQ 3.8+

### 安装依赖
```bash
go mod download
```

### 配置文件
复制并修改配置文件：
```bash
cp config/config.example.yaml config/config.yaml
```

### 启动服务
```bash
# 启动核心服务
go run pkg/core/main.go

# 启动订单服务
go run pkg/order/main.go

# 启动行情服务
go run pkg/market/main.go

# 启动其他服务...
```

## 部署

### Docker 部署
```bash
# 构建镜像
./deploy/_build.sh

# 部署服务
./deploy/_deploy.sh
```

### 传统部署
```bash
# 编译
go build -o bin/core pkg/core/main.go
go build -o bin/order pkg/order/main.go
# ... 其他服务

# 启动
./bin/core
./bin/order
# ... 其他服务
```

## 文档目录

### API 文档
- [API 总览](api/README.md) - API接口文档和使用指南
- [Core Service API](api/core.md) - 核心服务API
- [Order Service API](api/order.md) - 订单服务API
- [Market Service API](api/market.md) - 行情服务API
- [WebSocket API](api/websocket.md) - WebSocket接口文档

### 开发指南
- [开发指南](development/README.md) - 完整的开发指南
- [环境搭建](development/setup.md) - 开发环境配置
- [代码规范](development/coding-standards.md) - 编码规范和最佳实践
- [测试指南](development/testing.md) - 单元测试和集成测试
- [性能优化](development/performance.md) - 性能优化技巧

### 部署指南
- [部署指南](deployment/README.md) - 完整的部署文档
- [Docker 部署](deployment/docker.md) - Docker容器化部署
- [Kubernetes 部署](deployment/kubernetes.md) - K8s集群部署
- [配置管理](deployment/configuration.md) - 配置文件管理

### 运维指南
- [运维指南](operations/README.md) - 完整的运维文档
- [监控告警](operations/monitoring.md) - 系统监控和告警配置
- [日志管理](operations/logging.md) - 日志收集和分析
- [性能调优](operations/performance.md) - 系统性能优化
- [故障排查](operations/troubleshooting.md) - 常见问题和解决方案
- [备份恢复](operations/backup.md) - 数据备份和灾难恢复

### 架构设计
- [系统架构](architecture/overview.md) - 整体架构设计
- [数据库设计](architecture/database.md) - 数据库表结构设计
- [缓存设计](architecture/cache.md) - 缓存架构和策略
- [消息队列](architecture/mq.md) - 消息队列设计
- [安全设计](architecture/security.md) - 安全架构和措施

## 贡献指南

请阅读 [CONTRIBUTING.md](CONTRIBUTING.md) 了解如何参与项目开发。

## 许可证

本项目采用 [MIT License](LICENSE)。
