# API 文档

## 概述

Spot-Server 提供 RESTful API 和 WebSocket API 两种接口方式，支持现货交易的各种操作。

## 认证方式

### API Key 认证
所有私有接口都需要进行API Key认证：

```http
POST /api/v1/order/place
Content-Type: application/json
X-API-KEY: your_api_key
X-SIGNATURE: signature
X-TIMESTAMP: timestamp

{
  "symbol": "BTCUSDT",
  "side": "buy",
  "type": "limit",
  "amount": "0.001",
  "price": "50000"
}
```

### 签名算法
```go
// 签名字符串格式: timestamp + method + requestPath + body
signString := timestamp + "POST" + "/api/v1/order/place" + requestBody
signature := HmacSha256(signString, secretKey)
```

## 接口列表

### 公共接口

#### 获取服务器时间
```http
GET /api/v1/time
```

#### 获取交易对信息
```http
GET /api/v1/symbols
```

#### 获取市场深度
```http
GET /api/v1/depth?symbol=BTCUSDT&limit=100
```

#### 获取最新价格
```http
GET /api/v1/ticker/price?symbol=BTCUSDT
```

#### 获取24小时统计
```http
GET /api/v1/ticker/24hr?symbol=BTCUSDT
```

#### 获取K线数据
```http
GET /api/v1/klines?symbol=BTCUSDT&interval=1m&limit=500
```

### 私有接口

#### 账户信息
```http
GET /api/v1/account
```

#### 下单
```http
POST /api/v1/order/place
Content-Type: application/json

{
  "symbol": "BTCUSDT",
  "side": "buy",
  "type": "limit",
  "amount": "0.001",
  "price": "50000",
  "timeInForce": "GTC"
}
```

#### 撤销订单
```http
DELETE /api/v1/order/cancel
Content-Type: application/json

{
  "symbol": "BTCUSDT",
  "orderId": "*********"
}
```

#### 查询订单
```http
GET /api/v1/order?symbol=BTCUSDT&orderId=*********
```

#### 查询历史订单
```http
GET /api/v1/orders?symbol=BTCUSDT&limit=100
```

#### 查询成交历史
```http
GET /api/v1/trades?symbol=BTCUSDT&limit=100
```

## WebSocket API

### 连接地址
```
wss://api.example.com/ws
```

### 订阅行情数据

#### 深度数据
```json
{
  "method": "SUBSCRIBE",
  "params": ["btcusdt@depth"],
  "id": 1
}
```

#### K线数据
```json
{
  "method": "SUBSCRIBE",
  "params": ["btcusdt@kline_1m"],
  "id": 2
}
```

#### 24小时统计
```json
{
  "method": "SUBSCRIBE",
  "params": ["btcusdt@ticker"],
  "id": 3
}
```

### 私有数据流

#### 账户更新
```json
{
  "method": "SUBSCRIBE",
  "params": ["account"],
  "id": 4
}
```

#### 订单更新
```json
{
  "method": "SUBSCRIBE",
  "params": ["orders"],
  "id": 5
}
```

## 错误码

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1001 | 参数错误 |
| 1002 | 签名错误 |
| 1003 | API Key 无效 |
| 1004 | 权限不足 |
| 2001 | 交易对不存在 |
| 2002 | 余额不足 |
| 2003 | 订单不存在 |
| 2004 | 价格超出限制 |
| 2005 | 数量超出限制 |
| 3001 | 系统维护中 |
| 3002 | 服务暂不可用 |

## 限流规则

### REST API 限流
- 公共接口：每秒 100 次请求
- 私有接口：每秒 50 次请求
- 下单接口：每秒 10 次请求

### WebSocket 限流
- 连接数限制：每个IP最多5个连接
- 订阅限制：每个连接最多订阅50个数据流
- 消息频率：每秒最多100条消息

## 数据格式

### 订单状态
- `NEW`: 新建订单
- `PARTIALLY_FILLED`: 部分成交
- `FILLED`: 完全成交
- `CANCELED`: 已撤销
- `REJECTED`: 已拒绝

### 订单类型
- `LIMIT`: 限价单
- `MARKET`: 市价单
- `STOP_LOSS`: 止损单
- `STOP_LOSS_LIMIT`: 止损限价单
- `TAKE_PROFIT`: 止盈单
- `TAKE_PROFIT_LIMIT`: 止盈限价单

### 时间有效性
- `GTC`: Good Till Cancel (一直有效直到撤销)
- `IOC`: Immediate Or Cancel (立即成交或撤销)
- `FOK`: Fill Or Kill (全部成交或撤销)

## 示例代码

### Go 示例
```go
package main

import (
    "bytes"
    "crypto/hmac"
    "crypto/sha256"
    "encoding/hex"
    "encoding/json"
    "fmt"
    "io/ioutil"
    "net/http"
    "strconv"
    "time"
)

type OrderRequest struct {
    Symbol      string `json:"symbol"`
    Side        string `json:"side"`
    Type        string `json:"type"`
    Amount      string `json:"amount"`
    Price       string `json:"price"`
    TimeInForce string `json:"timeInForce"`
}

func placeOrder(apiKey, secretKey string, order OrderRequest) error {
    url := "https://api.example.com/api/v1/order/place"
    
    body, _ := json.Marshal(order)
    timestamp := strconv.FormatInt(time.Now().UnixMilli(), 10)
    
    // 生成签名
    signString := timestamp + "POST" + "/api/v1/order/place" + string(body)
    signature := sign(signString, secretKey)
    
    req, _ := http.NewRequest("POST", url, bytes.NewBuffer(body))
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("X-API-KEY", apiKey)
    req.Header.Set("X-SIGNATURE", signature)
    req.Header.Set("X-TIMESTAMP", timestamp)
    
    client := &http.Client{}
    resp, err := client.Do(req)
    if err != nil {
        return err
    }
    defer resp.Body.Close()
    
    respBody, _ := ioutil.ReadAll(resp.Body)
    fmt.Println(string(respBody))
    
    return nil
}

func sign(message, secretKey string) string {
    h := hmac.New(sha256.New, []byte(secretKey))
    h.Write([]byte(message))
    return hex.EncodeToString(h.Sum(nil))
}
```

### JavaScript 示例
```javascript
const crypto = require('crypto');
const axios = require('axios');

class SpotAPI {
    constructor(apiKey, secretKey) {
        this.apiKey = apiKey;
        this.secretKey = secretKey;
        this.baseURL = 'https://api.example.com';
    }
    
    sign(message) {
        return crypto
            .createHmac('sha256', this.secretKey)
            .update(message)
            .digest('hex');
    }
    
    async placeOrder(order) {
        const timestamp = Date.now().toString();
        const path = '/api/v1/order/place';
        const body = JSON.stringify(order);
        
        const signString = timestamp + 'POST' + path + body;
        const signature = this.sign(signString);
        
        const config = {
            method: 'POST',
            url: this.baseURL + path,
            headers: {
                'Content-Type': 'application/json',
                'X-API-KEY': this.apiKey,
                'X-SIGNATURE': signature,
                'X-TIMESTAMP': timestamp
            },
            data: body
        };
        
        try {
            const response = await axios(config);
            return response.data;
        } catch (error) {
            throw error;
        }
    }
}

// 使用示例
const api = new SpotAPI('your_api_key', 'your_secret_key');

api.placeOrder({
    symbol: 'BTCUSDT',
    side: 'buy',
    type: 'limit',
    amount: '0.001',
    price: '50000',
    timeInForce: 'GTC'
}).then(result => {
    console.log(result);
}).catch(error => {
    console.error(error);
});
```

## 更多信息

- [Core Service API](core.md)
- [Order Service API](order.md)
- [Market Service API](market.md)
- [WebSocket API](websocket.md)
