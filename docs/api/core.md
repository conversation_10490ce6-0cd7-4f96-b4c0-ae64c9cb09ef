# Core Service API

Core Service 是 Spot-Server 的核心服务，负责用户管理、权限控制、系统配置等基础功能。

## 基础信息

- **服务名称**: Core Service
- **默认端口**: 8080
- **API 前缀**: `/api/v1/core`
- **认证方式**: JWT Token + API Key

## 认证接口

### 用户登录

**接口地址**: `POST /api/v1/core/auth/login`

**请求参数**:
```json
{
  "username": "<EMAIL>",
  "password": "password123",
  "captcha": "1234",
  "captcha_id": "captcha_uuid"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "refresh_token_string",
    "expires_in": 86400,
    "user_info": {
      "user_id": "123456",
      "username": "<EMAIL>",
      "nickname": "用户昵称",
      "avatar": "https://example.com/avatar.jpg",
      "role": "user",
      "status": "active"
    }
  }
}
```

### 用户注册

**接口地址**: `POST /api/v1/core/auth/register`

**请求参数**:
```json
{
  "username": "<EMAIL>",
  "password": "password123",
  "confirm_password": "password123",
  "email_code": "123456",
  "invite_code": "INVITE123",
  "agree_terms": true
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "注册成功",
  "data": {
    "user_id": "123457",
    "username": "<EMAIL>",
    "status": "pending_verification"
  }
}
```

### 刷新Token

**接口地址**: `POST /api/v1/core/auth/refresh`

**请求头**:
```
Authorization: Bearer refresh_token_string
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "token": "new_access_token",
    "expires_in": 86400
  }
}
```

### 用户登出

**接口地址**: `POST /api/v1/core/auth/logout`

**请求头**:
```
Authorization: Bearer access_token
```

**响应示例**:
```json
{
  "code": 0,
  "message": "登出成功"
}
```

## 用户管理

### 获取用户信息

**接口地址**: `GET /api/v1/core/user/profile`

**请求头**:
```
Authorization: Bearer access_token
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "user_id": "123456",
    "username": "<EMAIL>",
    "nickname": "用户昵称",
    "avatar": "https://example.com/avatar.jpg",
    "email": "<EMAIL>",
    "phone": "138****8888",
    "real_name": "张三",
    "id_card": "330***********1234",
    "kyc_level": 2,
    "vip_level": 1,
    "created_at": "2023-01-01T00:00:00Z",
    "last_login": "2023-12-01T10:00:00Z",
    "status": "active",
    "settings": {
      "language": "zh-CN",
      "timezone": "Asia/Shanghai",
      "notification": {
        "email": true,
        "sms": true,
        "push": true
      }
    }
  }
}
```

### 更新用户信息

**接口地址**: `PUT /api/v1/core/user/profile`

**请求头**:
```
Authorization: Bearer access_token
```

**请求参数**:
```json
{
  "nickname": "新昵称",
  "avatar": "https://example.com/new_avatar.jpg",
  "settings": {
    "language": "en-US",
    "timezone": "UTC",
    "notification": {
      "email": true,
      "sms": false,
      "push": true
    }
  }
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "更新成功",
  "data": {
    "user_id": "123456",
    "nickname": "新昵称",
    "avatar": "https://example.com/new_avatar.jpg",
    "updated_at": "2023-12-01T10:30:00Z"
  }
}
```

### 修改密码

**接口地址**: `PUT /api/v1/core/user/password`

**请求头**:
```
Authorization: Bearer access_token
```

**请求参数**:
```json
{
  "old_password": "old_password123",
  "new_password": "new_password123",
  "confirm_password": "new_password123"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "密码修改成功"
}
```

## 安全设置

### 获取API Key列表

**接口地址**: `GET /api/v1/core/security/api-keys`

**请求头**:
```
Authorization: Bearer access_token
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "api_keys": [
      {
        "id": "key_001",
        "name": "交易API",
        "api_key": "ak_1234567890abcdef",
        "permissions": ["trade", "read"],
        "ip_whitelist": ["*************", "********"],
        "status": "active",
        "created_at": "2023-01-01T00:00:00Z",
        "last_used": "2023-12-01T09:30:00Z"
      }
    ],
    "total": 1
  }
}
```

### 创建API Key

**接口地址**: `POST /api/v1/core/security/api-keys`

**请求头**:
```
Authorization: Bearer access_token
```

**请求参数**:
```json
{
  "name": "新的API Key",
  "permissions": ["trade", "read"],
  "ip_whitelist": ["*************"],
  "note": "用于自动交易"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "API Key创建成功",
  "data": {
    "id": "key_002",
    "name": "新的API Key",
    "api_key": "ak_abcdef1234567890",
    "secret_key": "sk_fedcba0987654321",
    "permissions": ["trade", "read"],
    "ip_whitelist": ["*************"],
    "status": "active",
    "created_at": "2023-12-01T10:45:00Z"
  }
}
```

### 删除API Key

**接口地址**: `DELETE /api/v1/core/security/api-keys/{key_id}`

**请求头**:
```
Authorization: Bearer access_token
```

**响应示例**:
```json
{
  "code": 0,
  "message": "API Key删除成功"
}
```

### 启用/禁用双因子认证

**接口地址**: `POST /api/v1/core/security/2fa/toggle`

**请求头**:
```
Authorization: Bearer access_token
```

**请求参数**:
```json
{
  "action": "enable",
  "secret": "JBSWY3DPEHPK3PXP",
  "code": "123456"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "双因子认证已启用",
  "data": {
    "backup_codes": [
      "12345678",
      "87654321",
      "11223344"
    ]
  }
}
```

## 系统配置

### 获取系统配置

**接口地址**: `GET /api/v1/core/config/system`

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "system_name": "Spot Trading Platform",
    "version": "v1.0.0",
    "maintenance": false,
    "trading_enabled": true,
    "deposit_enabled": true,
    "withdraw_enabled": true,
    "supported_languages": ["zh-CN", "en-US", "ja-JP"],
    "default_language": "zh-CN",
    "timezone": "Asia/Shanghai",
    "features": {
      "spot_trading": true,
      "margin_trading": false,
      "futures_trading": false,
      "options_trading": false
    }
  }
}
```

### 获取交易对配置

**接口地址**: `GET /api/v1/core/config/symbols`

**查询参数**:
- `status`: 状态筛选 (active/inactive)
- `base_currency`: 基础货币筛选
- `quote_currency`: 计价货币筛选

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "symbols": [
      {
        "symbol": "BTCUSDT",
        "base_currency": "BTC",
        "quote_currency": "USDT",
        "status": "active",
        "price_precision": 2,
        "amount_precision": 6,
        "min_amount": "0.000001",
        "max_amount": "1000",
        "min_notional": "10",
        "maker_fee": "0.001",
        "taker_fee": "0.001",
        "trading_enabled": true,
        "created_at": "2023-01-01T00:00:00Z"
      }
    ],
    "total": 1
  }
}
```

### 获取手续费配置

**接口地址**: `GET /api/v1/core/config/fees`

**请求头**:
```
Authorization: Bearer access_token
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "user_id": "123456",
    "vip_level": 1,
    "trading_fees": {
      "spot": {
        "maker": "0.001",
        "taker": "0.001"
      }
    },
    "withdraw_fees": {
      "BTC": "0.0005",
      "ETH": "0.005",
      "USDT": "1.0"
    },
    "deposit_fees": {
      "BTC": "0",
      "ETH": "0",
      "USDT": "0"
    }
  }
}
```

## 通知管理

### 获取通知列表

**接口地址**: `GET /api/v1/core/notifications`

**请求头**:
```
Authorization: Bearer access_token
```

**查询参数**:
- `type`: 通知类型 (system/trade/security)
- `status`: 状态 (read/unread)
- `page`: 页码 (默认1)
- `limit`: 每页数量 (默认20)

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "notifications": [
      {
        "id": "notif_001",
        "type": "trade",
        "title": "订单成交通知",
        "content": "您的订单 #12345 已完全成交",
        "status": "unread",
        "created_at": "2023-12-01T10:00:00Z",
        "data": {
          "order_id": "12345",
          "symbol": "BTCUSDT",
          "side": "buy",
          "amount": "0.001",
          "price": "50000"
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 1,
      "pages": 1
    },
    "unread_count": 1
  }
}
```

### 标记通知为已读

**接口地址**: `PUT /api/v1/core/notifications/{notification_id}/read`

**请求头**:
```
Authorization: Bearer access_token
```

**响应示例**:
```json
{
  "code": 0,
  "message": "通知已标记为已读"
}
```

### 批量标记通知为已读

**接口地址**: `PUT /api/v1/core/notifications/read-all`

**请求头**:
```
Authorization: Bearer access_token
```

**请求参数**:
```json
{
  "type": "trade"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "所有通知已标记为已读",
  "data": {
    "updated_count": 5
  }
}
```

## 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 10001 | 用户名或密码错误 | 检查登录凭据 |
| 10002 | 用户不存在 | 确认用户名是否正确 |
| 10003 | 用户已被禁用 | 联系客服 |
| 10004 | 验证码错误 | 重新获取验证码 |
| 10005 | 验证码已过期 | 重新获取验证码 |
| 10006 | 密码强度不够 | 使用更强的密码 |
| 10007 | 邮箱已被注册 | 使用其他邮箱或找回密码 |
| 10008 | 手机号已被注册 | 使用其他手机号或找回密码 |
| 10009 | 邀请码无效 | 检查邀请码是否正确 |
| 10010 | API Key不存在 | 检查API Key是否正确 |
| 10011 | API Key已被禁用 | 启用API Key或创建新的 |
| 10012 | IP地址不在白名单 | 添加IP到白名单 |
| 10013 | 权限不足 | 检查API Key权限设置 |
| 10014 | 双因子认证码错误 | 检查认证器时间同步 |
| 10015 | 操作过于频繁 | 稍后再试 |

## 示例代码

### Go 示例

```go
package main

import (
    "bytes"
    "encoding/json"
    "fmt"
    "io/ioutil"
    "net/http"
)

type LoginRequest struct {
    Username string `json:"username"`
    Password string `json:"password"`
    Captcha  string `json:"captcha"`
    CaptchaID string `json:"captcha_id"`
}

type LoginResponse struct {
    Code    int    `json:"code"`
    Message string `json:"message"`
    Data    struct {
        Token       string `json:"token"`
        RefreshToken string `json:"refresh_token"`
        ExpiresIn   int    `json:"expires_in"`
        UserInfo    struct {
            UserID   string `json:"user_id"`
            Username string `json:"username"`
            Nickname string `json:"nickname"`
            Role     string `json:"role"`
            Status   string `json:"status"`
        } `json:"user_info"`
    } `json:"data"`
}

func Login(username, password, captcha, captchaID string) (*LoginResponse, error) {
    url := "https://api.example.com/api/v1/core/auth/login"
    
    req := LoginRequest{
        Username:  username,
        Password:  password,
        Captcha:   captcha,
        CaptchaID: captchaID,
    }
    
    body, _ := json.Marshal(req)
    
    resp, err := http.Post(url, "application/json", bytes.NewBuffer(body))
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()
    
    respBody, _ := ioutil.ReadAll(resp.Body)
    
    var loginResp LoginResponse
    err = json.Unmarshal(respBody, &loginResp)
    if err != nil {
        return nil, err
    }
    
    return &loginResp, nil
}

func main() {
    resp, err := Login("<EMAIL>", "password123", "1234", "captcha_uuid")
    if err != nil {
        fmt.Printf("Login failed: %v\n", err)
        return
    }
    
    if resp.Code == 0 {
        fmt.Printf("Login successful, token: %s\n", resp.Data.Token)
    } else {
        fmt.Printf("Login failed: %s\n", resp.Message)
    }
}
```

### JavaScript 示例

```javascript
class CoreAPI {
    constructor(baseURL) {
        this.baseURL = baseURL;
        this.token = null;
    }
    
    async login(username, password, captcha, captchaId) {
        const response = await fetch(`${this.baseURL}/api/v1/core/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username,
                password,
                captcha,
                captcha_id: captchaId
            })
        });
        
        const data = await response.json();
        
        if (data.code === 0) {
            this.token = data.data.token;
            localStorage.setItem('access_token', this.token);
        }
        
        return data;
    }
    
    async getUserProfile() {
        const response = await fetch(`${this.baseURL}/api/v1/core/user/profile`, {
            headers: {
                'Authorization': `Bearer ${this.token}`
            }
        });
        
        return await response.json();
    }
    
    async createAPIKey(name, permissions, ipWhitelist) {
        const response = await fetch(`${this.baseURL}/api/v1/core/security/api-keys`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.token}`
            },
            body: JSON.stringify({
                name,
                permissions,
                ip_whitelist: ipWhitelist
            })
        });
        
        return await response.json();
    }
}

// 使用示例
const api = new CoreAPI('https://api.example.com');

// 登录
api.login('<EMAIL>', 'password123', '1234', 'captcha_uuid')
    .then(result => {
        if (result.code === 0) {
            console.log('登录成功:', result.data.user_info);
            
            // 获取用户信息
            return api.getUserProfile();
        } else {
            throw new Error(result.message);
        }
    })
    .then(profile => {
        console.log('用户信息:', profile.data);
    })
    .catch(error => {
        console.error('操作失败:', error);
    });
```
