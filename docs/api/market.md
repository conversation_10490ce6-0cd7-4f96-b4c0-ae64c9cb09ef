# Market Service API

Market Service 负责提供市场行情数据，包括实时价格、深度数据、K线数据等。

## 基础信息

- **服务名称**: Market Service
- **默认端口**: 8082
- **API 前缀**: `/api/v1/market`
- **认证方式**: 公开接口无需认证

## 基础行情

### 获取服务器时间

**接口地址**: `GET /api/v1/market/time`

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "server_time": 1701421200000,
    "timezone": "UTC"
  }
}
```

### 获取交易对信息

**接口地址**: `GET /api/v1/market/symbols`

**查询参数**:
- `status`: 状态筛选 (active/inactive)

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "symbols": [
      {
        "symbol": "BTCUSDT",
        "base_currency": "BTC",
        "quote_currency": "USDT",
        "status": "active",
        "price_precision": 2,
        "amount_precision": 6,
        "min_amount": "0.000001",
        "max_amount": "1000",
        "min_notional": "10",
        "tick_size": "0.01",
        "step_size": "0.000001"
      },
      {
        "symbol": "ETHUSDT",
        "base_currency": "ETH",
        "quote_currency": "USDT",
        "status": "active",
        "price_precision": 2,
        "amount_precision": 5,
        "min_amount": "0.00001",
        "max_amount": "10000",
        "min_notional": "10",
        "tick_size": "0.01",
        "step_size": "0.00001"
      }
    ]
  }
}
```

### 获取最新价格

**接口地址**: `GET /api/v1/market/ticker/price`

**查询参数**:
- `symbol`: 交易对 (可选，不传返回所有)

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "symbol": "BTCUSDT",
      "price": "42350.50",
      "timestamp": 1701421200000
    },
    {
      "symbol": "ETHUSDT",
      "price": "2580.75",
      "timestamp": 1701421200000
    }
  ]
}
```

### 获取24小时统计

**接口地址**: `GET /api/v1/market/ticker/24hr`

**查询参数**:
- `symbol`: 交易对 (可选，不传返回所有)

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "symbol": "BTCUSDT",
      "price_change": "1250.50",
      "price_change_percent": "3.04",
      "weighted_avg_price": "41800.25",
      "prev_close_price": "41100.00",
      "last_price": "42350.50",
      "bid_price": "42349.50",
      "ask_price": "42351.00",
      "open_price": "41100.00",
      "high_price": "42500.00",
      "low_price": "40800.00",
      "volume": "1234.567890",
      "quote_volume": "51623456.78",
      "open_time": 1701334800000,
      "close_time": 1701421200000,
      "count": 125678
    }
  ]
}
```

### 获取最优挂单

**接口地址**: `GET /api/v1/market/ticker/book`

**查询参数**:
- `symbol`: 交易对 (可选，不传返回所有)

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "symbol": "BTCUSDT",
      "bid_price": "42349.50",
      "bid_qty": "0.125000",
      "ask_price": "42351.00",
      "ask_qty": "0.089000",
      "timestamp": 1701421200000
    }
  ]
}
```

## 深度数据

### 获取订单簿深度

**接口地址**: `GET /api/v1/market/depth`

**查询参数**:
- `symbol`: 交易对 (必填)
- `limit`: 返回条数 (可选，默认100，可选值: 5, 10, 20, 50, 100, 500, 1000)

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "symbol": "BTCUSDT",
    "last_update_id": 1234567890,
    "timestamp": 1701421200000,
    "bids": [
      ["42349.50", "0.125000"],
      ["42349.00", "0.250000"],
      ["42348.50", "0.180000"]
    ],
    "asks": [
      ["42351.00", "0.089000"],
      ["42351.50", "0.156000"],
      ["42352.00", "0.234000"]
    ]
  }
}
```

## K线数据

### 获取K线数据

**接口地址**: `GET /api/v1/market/klines`

**查询参数**:
- `symbol`: 交易对 (必填)
- `interval`: 时间间隔 (必填)
- `start_time`: 开始时间 (可选)
- `end_time`: 结束时间 (可选)
- `limit`: 返回条数 (可选，默认500，最大1000)

**时间间隔说明**:
- `1m`, `3m`, `5m`, `15m`, `30m` - 分钟线
- `1h`, `2h`, `4h`, `6h`, `8h`, `12h` - 小时线
- `1d`, `3d` - 日线
- `1w` - 周线
- `1M` - 月线

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "symbol": "BTCUSDT",
    "interval": "1h",
    "klines": [
      [
        1701417600000,  // 开盘时间
        "42100.00",     // 开盘价
        "42250.00",     // 最高价
        "42050.00",     // 最低价
        "42200.00",     // 收盘价
        "125.456789",   // 成交量
        1701421199999,  // 收盘时间
        "5289567.89",   // 成交额
        1234,           // 成交笔数
        "62.123456",    // 主动买入成交量
        "2623456.78"    // 主动买入成交额
      ]
    ]
  }
}
```

## 成交数据

### 获取最近成交

**接口地址**: `GET /api/v1/market/trades`

**查询参数**:
- `symbol`: 交易对 (必填)
- `limit`: 返回条数 (可选，默认500，最大1000)

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "symbol": "BTCUSDT",
    "trades": [
      {
        "id": "trade_123456",
        "price": "42350.50",
        "qty": "0.001000",
        "quote_qty": "42.35050",
        "time": 1701421200000,
        "is_buyer_maker": false
      },
      {
        "id": "trade_123455",
        "price": "42349.00",
        "qty": "0.005000",
        "quote_qty": "211.74500",
        "time": 1701421195000,
        "is_buyer_maker": true
      }
    ]
  }
}
```

### 获取历史成交

**接口地址**: `GET /api/v1/market/historical-trades`

**查询参数**:
- `symbol`: 交易对 (必填)
- `limit`: 返回条数 (可选，默认500，最大1000)
- `from_id`: 从指定成交ID开始 (可选)

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "symbol": "BTCUSDT",
    "trades": [
      {
        "id": "trade_123456",
        "price": "42350.50",
        "qty": "0.001000",
        "quote_qty": "42.35050",
        "time": 1701421200000,
        "is_buyer_maker": false
      }
    ]
  }
}
```

## 聚合成交

### 获取聚合成交数据

**接口地址**: `GET /api/v1/market/agg-trades`

**查询参数**:
- `symbol`: 交易对 (必填)
- `from_id`: 从指定聚合成交ID开始 (可选)
- `start_time`: 开始时间 (可选)
- `end_time`: 结束时间 (可选)
- `limit`: 返回条数 (可选，默认500，最大1000)

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "symbol": "BTCUSDT",
    "agg_trades": [
      {
        "agg_id": "agg_123456",
        "price": "42350.50",
        "qty": "0.125000",
        "first_id": "trade_123450",
        "last_id": "trade_123456",
        "timestamp": 1701421200000,
        "is_buyer_maker": false
      }
    ]
  }
}
```

## 市场统计

### 获取24小时价格变动统计

**接口地址**: `GET /api/v1/market/ticker/price-change`

**查询参数**:
- `symbol`: 交易对 (可选，不传返回所有)

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "symbol": "BTCUSDT",
      "price_change": "1250.50",
      "price_change_percent": "3.04",
      "open_price": "41100.00",
      "high_price": "42500.00",
      "low_price": "40800.00",
      "last_price": "42350.50",
      "volume": "1234.567890",
      "quote_volume": "51623456.78",
      "open_time": 1701334800000,
      "close_time": 1701421200000
    }
  ]
}
```

### 获取交易量排行

**接口地址**: `GET /api/v1/market/ticker/volume`

**查询参数**:
- `limit`: 返回条数 (可选，默认10，最大50)
- `sort`: 排序方式 (volume/quote_volume，默认volume)

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "symbol": "BTCUSDT",
      "volume": "1234.567890",
      "quote_volume": "51623456.78",
      "rank": 1
    },
    {
      "symbol": "ETHUSDT",
      "volume": "5678.901234",
      "quote_volume": "14567890.12",
      "rank": 2
    }
  ]
}
```

## 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 30001 | 交易对不存在 | 检查交易对是否正确 |
| 30002 | 时间间隔不支持 | 使用支持的时间间隔 |
| 30003 | 时间范围过大 | 缩小查询时间范围 |
| 30004 | 限制参数超出范围 | 调整limit参数 |
| 30005 | 开始时间大于结束时间 | 检查时间参数 |
| 30006 | 数据暂时不可用 | 稍后重试 |

## 示例代码

### Go 示例

```go
package main

import (
    "encoding/json"
    "fmt"
    "io/ioutil"
    "net/http"
    "net/url"
)

type MarketClient struct {
    BaseURL string
}

type TickerResponse struct {
    Code    int    `json:"code"`
    Message string `json:"message"`
    Data    []struct {
        Symbol                string `json:"symbol"`
        PriceChange          string `json:"price_change"`
        PriceChangePercent   string `json:"price_change_percent"`
        WeightedAvgPrice     string `json:"weighted_avg_price"`
        PrevClosePrice       string `json:"prev_close_price"`
        LastPrice            string `json:"last_price"`
        BidPrice             string `json:"bid_price"`
        AskPrice             string `json:"ask_price"`
        OpenPrice            string `json:"open_price"`
        HighPrice            string `json:"high_price"`
        LowPrice             string `json:"low_price"`
        Volume               string `json:"volume"`
        QuoteVolume          string `json:"quote_volume"`
        OpenTime             int64  `json:"open_time"`
        CloseTime            int64  `json:"close_time"`
        Count                int    `json:"count"`
    } `json:"data"`
}

type KlineResponse struct {
    Code    int    `json:"code"`
    Message string `json:"message"`
    Data    struct {
        Symbol   string      `json:"symbol"`
        Interval string      `json:"interval"`
        Klines   [][]interface{} `json:"klines"`
    } `json:"data"`
}

func (c *MarketClient) Get24hrTicker(symbol string) (*TickerResponse, error) {
    u := c.BaseURL + "/api/v1/market/ticker/24hr"
    if symbol != "" {
        u += "?symbol=" + url.QueryEscape(symbol)
    }
    
    resp, err := http.Get(u)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()
    
    body, err := ioutil.ReadAll(resp.Body)
    if err != nil {
        return nil, err
    }
    
    var tickerResp TickerResponse
    err = json.Unmarshal(body, &tickerResp)
    if err != nil {
        return nil, err
    }
    
    return &tickerResp, nil
}

func (c *MarketClient) GetKlines(symbol, interval string, limit int) (*KlineResponse, error) {
    u := fmt.Sprintf("%s/api/v1/market/klines?symbol=%s&interval=%s&limit=%d",
        c.BaseURL, url.QueryEscape(symbol), url.QueryEscape(interval), limit)
    
    resp, err := http.Get(u)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()
    
    body, err := ioutil.ReadAll(resp.Body)
    if err != nil {
        return nil, err
    }
    
    var klineResp KlineResponse
    err = json.Unmarshal(body, &klineResp)
    if err != nil {
        return nil, err
    }
    
    return &klineResp, nil
}

func main() {
    client := &MarketClient{
        BaseURL: "https://api.example.com",
    }
    
    // 获取24小时统计
    ticker, err := client.Get24hrTicker("BTCUSDT")
    if err != nil {
        fmt.Printf("Get ticker failed: %v\n", err)
        return
    }
    
    if ticker.Code == 0 && len(ticker.Data) > 0 {
        data := ticker.Data[0]
        fmt.Printf("Symbol: %s\n", data.Symbol)
        fmt.Printf("Last Price: %s\n", data.LastPrice)
        fmt.Printf("24h Change: %s (%s%%)\n", data.PriceChange, data.PriceChangePercent)
        fmt.Printf("24h Volume: %s\n", data.Volume)
    }
    
    // 获取K线数据
    klines, err := client.GetKlines("BTCUSDT", "1h", 10)
    if err != nil {
        fmt.Printf("Get klines failed: %v\n", err)
        return
    }
    
    if klines.Code == 0 {
        fmt.Printf("\nK线数据 (%s %s):\n", klines.Data.Symbol, klines.Data.Interval)
        for i, kline := range klines.Data.Klines {
            fmt.Printf("K线 %d: 开盘=%v, 最高=%v, 最低=%v, 收盘=%v, 成交量=%v\n",
                i+1, kline[1], kline[2], kline[3], kline[4], kline[5])
        }
    }
}
```

### JavaScript 示例

```javascript
const axios = require('axios');

class MarketAPI {
    constructor(baseURL) {
        this.baseURL = baseURL;
    }
    
    async get24hrTicker(symbol = null) {
        let url = `${this.baseURL}/api/v1/market/ticker/24hr`;
        if (symbol) {
            url += `?symbol=${symbol}`;
        }
        
        try {
            const response = await axios.get(url);
            return response.data;
        } catch (error) {
            throw error;
        }
    }
    
    async getDepth(symbol, limit = 100) {
        const url = `${this.baseURL}/api/v1/market/depth?symbol=${symbol}&limit=${limit}`;
        
        try {
            const response = await axios.get(url);
            return response.data;
        } catch (error) {
            throw error;
        }
    }
    
    async getKlines(symbol, interval, limit = 500) {
        const url = `${this.baseURL}/api/v1/market/klines?symbol=${symbol}&interval=${interval}&limit=${limit}`;
        
        try {
            const response = await axios.get(url);
            return response.data;
        } catch (error) {
            throw error;
        }
    }
    
    async getTrades(symbol, limit = 500) {
        const url = `${this.baseURL}/api/v1/market/trades?symbol=${symbol}&limit=${limit}`;
        
        try {
            const response = await axios.get(url);
            return response.data;
        } catch (error) {
            throw error;
        }
    }
}

// 使用示例
const marketAPI = new MarketAPI('https://api.example.com');

async function main() {
    try {
        // 获取24小时统计
        const ticker = await marketAPI.get24hrTicker('BTCUSDT');
        if (ticker.code === 0 && ticker.data.length > 0) {
            const data = ticker.data[0];
            console.log(`${data.symbol}:`);
            console.log(`  当前价格: ${data.last_price}`);
            console.log(`  24h涨跌: ${data.price_change} (${data.price_change_percent}%)`);
            console.log(`  24h成交量: ${data.volume}`);
        }
        
        // 获取深度数据
        const depth = await marketAPI.getDepth('BTCUSDT', 10);
        if (depth.code === 0) {
            console.log('\n深度数据:');
            console.log('买盘:', depth.data.bids.slice(0, 3));
            console.log('卖盘:', depth.data.asks.slice(0, 3));
        }
        
        // 获取K线数据
        const klines = await marketAPI.getKlines('BTCUSDT', '1h', 5);
        if (klines.code === 0) {
            console.log('\nK线数据:');
            klines.data.klines.forEach((kline, index) => {
                console.log(`K线 ${index + 1}: 开盘=${kline[1]}, 收盘=${kline[4]}, 成交量=${kline[5]}`);
            });
        }
        
        // 获取最近成交
        const trades = await marketAPI.getTrades('BTCUSDT', 5);
        if (trades.code === 0) {
            console.log('\n最近成交:');
            trades.data.trades.forEach((trade, index) => {
                console.log(`成交 ${index + 1}: 价格=${trade.price}, 数量=${trade.qty}, 时间=${new Date(trade.time)}`);
            });
        }
        
    } catch (error) {
        console.error('API调用失败:', error.message);
    }
}

main();
```
