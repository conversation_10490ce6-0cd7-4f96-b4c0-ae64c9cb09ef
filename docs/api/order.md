# Order Service API

Order Service 负责处理所有订单相关的操作，包括下单、撤单、查询订单等功能。

## 基础信息

- **服务名称**: Order Service
- **默认端口**: 8081
- **API 前缀**: `/api/v1/order`
- **认证方式**: JWT Token + API Key

## 订单管理

### 下单

**接口地址**: `POST /api/v1/order/place`

**请求头**:
```
Authorization: Bearer access_token
X-API-KEY: your_api_key
X-SIGNATURE: signature
X-TIMESTAMP: timestamp
```

**请求参数**:
```json
{
  "symbol": "BTCUSDT",
  "side": "buy",
  "type": "limit",
  "amount": "0.001",
  "price": "50000",
  "time_in_force": "GTC",
  "client_order_id": "my_order_001"
}
```

**参数说明**:
- `symbol`: 交易对
- `side`: 买卖方向 (buy/sell)
- `type`: 订单类型 (limit/market/stop_loss/stop_loss_limit/take_profit/take_profit_limit)
- `amount`: 交易数量
- `price`: 价格 (限价单必填)
- `time_in_force`: 有效期 (GTC/IOC/FOK)
- `client_order_id`: 客户端订单ID (可选)

**响应示例**:
```json
{
  "code": 0,
  "message": "订单创建成功",
  "data": {
    "order_id": "1234567890",
    "client_order_id": "my_order_001",
    "symbol": "BTCUSDT",
    "side": "buy",
    "type": "limit",
    "amount": "0.001",
    "price": "50000",
    "status": "NEW",
    "time_in_force": "GTC",
    "created_at": "2023-12-01T10:00:00Z",
    "updated_at": "2023-12-01T10:00:00Z"
  }
}
```

### 撤销订单

**接口地址**: `DELETE /api/v1/order/cancel`

**请求头**:
```
Authorization: Bearer access_token
X-API-KEY: your_api_key
X-SIGNATURE: signature
X-TIMESTAMP: timestamp
```

**请求参数**:
```json
{
  "symbol": "BTCUSDT",
  "order_id": "1234567890"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "订单撤销成功",
  "data": {
    "order_id": "1234567890",
    "symbol": "BTCUSDT",
    "status": "CANCELED",
    "canceled_at": "2023-12-01T10:05:00Z"
  }
}
```

### 批量撤销订单

**接口地址**: `DELETE /api/v1/order/cancel-all`

**请求头**:
```
Authorization: Bearer access_token
X-API-KEY: your_api_key
X-SIGNATURE: signature
X-TIMESTAMP: timestamp
```

**请求参数**:
```json
{
  "symbol": "BTCUSDT"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "批量撤销成功",
  "data": {
    "symbol": "BTCUSDT",
    "canceled_count": 5,
    "canceled_orders": [
      "1234567890",
      "1234567891",
      "1234567892"
    ]
  }
}
```

### 查询订单

**接口地址**: `GET /api/v1/order/{order_id}`

**请求头**:
```
Authorization: Bearer access_token
X-API-KEY: your_api_key
X-SIGNATURE: signature
X-TIMESTAMP: timestamp
```

**路径参数**:
- `order_id`: 订单ID

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "order_id": "1234567890",
    "client_order_id": "my_order_001",
    "symbol": "BTCUSDT",
    "side": "buy",
    "type": "limit",
    "amount": "0.001",
    "price": "50000",
    "filled_amount": "0.0005",
    "remaining_amount": "0.0005",
    "status": "PARTIALLY_FILLED",
    "time_in_force": "GTC",
    "avg_price": "49950",
    "fee": "0.00000005",
    "fee_currency": "BTC",
    "created_at": "2023-12-01T10:00:00Z",
    "updated_at": "2023-12-01T10:02:00Z",
    "trades": [
      {
        "trade_id": "trade_001",
        "amount": "0.0005",
        "price": "49950",
        "fee": "0.00000005",
        "fee_currency": "BTC",
        "timestamp": "2023-12-01T10:02:00Z"
      }
    ]
  }
}
```

### 查询当前订单

**接口地址**: `GET /api/v1/order/open`

**请求头**:
```
Authorization: Bearer access_token
X-API-KEY: your_api_key
X-SIGNATURE: signature
X-TIMESTAMP: timestamp
```

**查询参数**:
- `symbol`: 交易对 (可选)
- `side`: 买卖方向 (可选)
- `type`: 订单类型 (可选)
- `page`: 页码 (默认1)
- `limit`: 每页数量 (默认20，最大100)

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "orders": [
      {
        "order_id": "1234567890",
        "client_order_id": "my_order_001",
        "symbol": "BTCUSDT",
        "side": "buy",
        "type": "limit",
        "amount": "0.001",
        "price": "50000",
        "filled_amount": "0",
        "remaining_amount": "0.001",
        "status": "NEW",
        "time_in_force": "GTC",
        "created_at": "2023-12-01T10:00:00Z",
        "updated_at": "2023-12-01T10:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 1,
      "pages": 1
    }
  }
}
```

### 查询历史订单

**接口地址**: `GET /api/v1/order/history`

**请求头**:
```
Authorization: Bearer access_token
X-API-KEY: your_api_key
X-SIGNATURE: signature
X-TIMESTAMP: timestamp
```

**查询参数**:
- `symbol`: 交易对 (可选)
- `side`: 买卖方向 (可选)
- `type`: 订单类型 (可选)
- `status`: 订单状态 (可选)
- `start_time`: 开始时间 (可选)
- `end_time`: 结束时间 (可选)
- `page`: 页码 (默认1)
- `limit`: 每页数量 (默认20，最大100)

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "orders": [
      {
        "order_id": "1234567889",
        "client_order_id": "my_order_000",
        "symbol": "BTCUSDT",
        "side": "buy",
        "type": "limit",
        "amount": "0.001",
        "price": "49000",
        "filled_amount": "0.001",
        "remaining_amount": "0",
        "status": "FILLED",
        "time_in_force": "GTC",
        "avg_price": "49000",
        "fee": "0.00000049",
        "fee_currency": "BTC",
        "created_at": "2023-11-30T15:00:00Z",
        "updated_at": "2023-11-30T15:01:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 1,
      "pages": 1
    }
  }
}
```

## 成交记录

### 查询成交记录

**接口地址**: `GET /api/v1/order/trades`

**请求头**:
```
Authorization: Bearer access_token
X-API-KEY: your_api_key
X-SIGNATURE: signature
X-TIMESTAMP: timestamp
```

**查询参数**:
- `symbol`: 交易对 (可选)
- `order_id`: 订单ID (可选)
- `start_time`: 开始时间 (可选)
- `end_time`: 结束时间 (可选)
- `page`: 页码 (默认1)
- `limit`: 每页数量 (默认20，最大100)

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "trades": [
      {
        "trade_id": "trade_001",
        "order_id": "1234567889",
        "symbol": "BTCUSDT",
        "side": "buy",
        "amount": "0.001",
        "price": "49000",
        "fee": "0.00000049",
        "fee_currency": "BTC",
        "is_maker": true,
        "timestamp": "2023-11-30T15:01:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 1,
      "pages": 1
    }
  }
}
```

## 订单状态说明

| 状态 | 说明 |
|------|------|
| NEW | 新建订单，等待撮合 |
| PARTIALLY_FILLED | 部分成交 |
| FILLED | 完全成交 |
| CANCELED | 已撤销 |
| REJECTED | 已拒绝 |
| EXPIRED | 已过期 |

## 订单类型说明

| 类型 | 说明 |
|------|------|
| limit | 限价单 |
| market | 市价单 |
| stop_loss | 止损单 |
| stop_loss_limit | 止损限价单 |
| take_profit | 止盈单 |
| take_profit_limit | 止盈限价单 |

## 时间有效性说明

| 类型 | 说明 |
|------|------|
| GTC | Good Till Cancel (一直有效直到撤销) |
| IOC | Immediate Or Cancel (立即成交或撤销) |
| FOK | Fill Or Kill (全部成交或撤销) |

## 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 20001 | 交易对不存在 | 检查交易对是否正确 |
| 20002 | 交易对已暂停交易 | 等待交易恢复 |
| 20003 | 订单数量不符合要求 | 检查最小/最大交易数量 |
| 20004 | 订单价格不符合要求 | 检查价格精度和范围 |
| 20005 | 余额不足 | 充值或减少交易数量 |
| 20006 | 订单不存在 | 检查订单ID是否正确 |
| 20007 | 订单已成交无法撤销 | 订单已完成 |
| 20008 | 订单已撤销 | 订单已被撤销 |
| 20009 | 超过最大订单数量限制 | 撤销部分订单后重试 |
| 20010 | 价格偏离市场价格过多 | 调整订单价格 |
| 20011 | 订单金额低于最小限制 | 增加订单金额 |
| 20012 | 系统维护中 | 等待维护完成 |

## 示例代码

### Go 示例

```go
package main

import (
    "bytes"
    "crypto/hmac"
    "crypto/sha256"
    "encoding/hex"
    "encoding/json"
    "fmt"
    "io/ioutil"
    "net/http"
    "strconv"
    "time"
)

type OrderClient struct {
    BaseURL   string
    APIKey    string
    SecretKey string
}

type PlaceOrderRequest struct {
    Symbol        string `json:"symbol"`
    Side          string `json:"side"`
    Type          string `json:"type"`
    Amount        string `json:"amount"`
    Price         string `json:"price,omitempty"`
    TimeInForce   string `json:"time_in_force,omitempty"`
    ClientOrderID string `json:"client_order_id,omitempty"`
}

type OrderResponse struct {
    Code    int    `json:"code"`
    Message string `json:"message"`
    Data    struct {
        OrderID       string `json:"order_id"`
        ClientOrderID string `json:"client_order_id"`
        Symbol        string `json:"symbol"`
        Side          string `json:"side"`
        Type          string `json:"type"`
        Amount        string `json:"amount"`
        Price         string `json:"price"`
        Status        string `json:"status"`
        TimeInForce   string `json:"time_in_force"`
        CreatedAt     string `json:"created_at"`
        UpdatedAt     string `json:"updated_at"`
    } `json:"data"`
}

func (c *OrderClient) sign(message string) string {
    h := hmac.New(sha256.New, []byte(c.SecretKey))
    h.Write([]byte(message))
    return hex.EncodeToString(h.Sum(nil))
}

func (c *OrderClient) PlaceOrder(req PlaceOrderRequest) (*OrderResponse, error) {
    url := c.BaseURL + "/api/v1/order/place"
    
    body, _ := json.Marshal(req)
    timestamp := strconv.FormatInt(time.Now().UnixMilli(), 10)
    
    // 生成签名
    signString := timestamp + "POST" + "/api/v1/order/place" + string(body)
    signature := c.sign(signString)
    
    httpReq, _ := http.NewRequest("POST", url, bytes.NewBuffer(body))
    httpReq.Header.Set("Content-Type", "application/json")
    httpReq.Header.Set("X-API-KEY", c.APIKey)
    httpReq.Header.Set("X-SIGNATURE", signature)
    httpReq.Header.Set("X-TIMESTAMP", timestamp)
    
    client := &http.Client{}
    resp, err := client.Do(httpReq)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()
    
    respBody, _ := ioutil.ReadAll(resp.Body)
    
    var orderResp OrderResponse
    err = json.Unmarshal(respBody, &orderResp)
    if err != nil {
        return nil, err
    }
    
    return &orderResp, nil
}

func main() {
    client := &OrderClient{
        BaseURL:   "https://api.example.com",
        APIKey:    "your_api_key",
        SecretKey: "your_secret_key",
    }
    
    order := PlaceOrderRequest{
        Symbol:      "BTCUSDT",
        Side:        "buy",
        Type:        "limit",
        Amount:      "0.001",
        Price:       "50000",
        TimeInForce: "GTC",
    }
    
    resp, err := client.PlaceOrder(order)
    if err != nil {
        fmt.Printf("Place order failed: %v\n", err)
        return
    }
    
    if resp.Code == 0 {
        fmt.Printf("Order placed successfully: %s\n", resp.Data.OrderID)
    } else {
        fmt.Printf("Place order failed: %s\n", resp.Message)
    }
}
```

### JavaScript 示例

```javascript
const crypto = require('crypto');
const axios = require('axios');

class OrderAPI {
    constructor(baseURL, apiKey, secretKey) {
        this.baseURL = baseURL;
        this.apiKey = apiKey;
        this.secretKey = secretKey;
    }
    
    sign(message) {
        return crypto
            .createHmac('sha256', this.secretKey)
            .update(message)
            .digest('hex');
    }
    
    async placeOrder(order) {
        const timestamp = Date.now().toString();
        const path = '/api/v1/order/place';
        const body = JSON.stringify(order);
        
        const signString = timestamp + 'POST' + path + body;
        const signature = this.sign(signString);
        
        const config = {
            method: 'POST',
            url: this.baseURL + path,
            headers: {
                'Content-Type': 'application/json',
                'X-API-KEY': this.apiKey,
                'X-SIGNATURE': signature,
                'X-TIMESTAMP': timestamp
            },
            data: body
        };
        
        try {
            const response = await axios(config);
            return response.data;
        } catch (error) {
            throw error;
        }
    }
    
    async cancelOrder(symbol, orderId) {
        const timestamp = Date.now().toString();
        const path = '/api/v1/order/cancel';
        const body = JSON.stringify({ symbol, order_id: orderId });
        
        const signString = timestamp + 'DELETE' + path + body;
        const signature = this.sign(signString);
        
        const config = {
            method: 'DELETE',
            url: this.baseURL + path,
            headers: {
                'Content-Type': 'application/json',
                'X-API-KEY': this.apiKey,
                'X-SIGNATURE': signature,
                'X-TIMESTAMP': timestamp
            },
            data: body
        };
        
        try {
            const response = await axios(config);
            return response.data;
        } catch (error) {
            throw error;
        }
    }
    
    async getOpenOrders(symbol = null) {
        const timestamp = Date.now().toString();
        const path = '/api/v1/order/open';
        const query = symbol ? `?symbol=${symbol}` : '';
        
        const signString = timestamp + 'GET' + path + query;
        const signature = this.sign(signString);
        
        const config = {
            method: 'GET',
            url: this.baseURL + path + query,
            headers: {
                'X-API-KEY': this.apiKey,
                'X-SIGNATURE': signature,
                'X-TIMESTAMP': timestamp
            }
        };
        
        try {
            const response = await axios(config);
            return response.data;
        } catch (error) {
            throw error;
        }
    }
}

// 使用示例
const orderAPI = new OrderAPI(
    'https://api.example.com',
    'your_api_key',
    'your_secret_key'
);

// 下单
orderAPI.placeOrder({
    symbol: 'BTCUSDT',
    side: 'buy',
    type: 'limit',
    amount: '0.001',
    price: '50000',
    time_in_force: 'GTC'
}).then(result => {
    console.log('下单结果:', result);
    
    if (result.code === 0) {
        const orderId = result.data.order_id;
        console.log('订单ID:', orderId);
        
        // 查询当前订单
        return orderAPI.getOpenOrders('BTCUSDT');
    }
}).then(orders => {
    console.log('当前订单:', orders);
}).catch(error => {
    console.error('操作失败:', error);
});
```
