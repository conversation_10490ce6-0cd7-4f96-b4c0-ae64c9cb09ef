# WebSocket API

WebSocket API 提供实时数据推送服务，包括行情数据、订单更新、账户变动等。

## 基础信息

- **连接地址**: `wss://api.example.com/ws`
- **协议**: WebSocket
- **数据格式**: JSON
- **心跳间隔**: 30秒

## 连接管理

### 建立连接

```javascript
const ws = new WebSocket('wss://api.example.com/ws');

ws.onopen = function(event) {
    console.log('WebSocket连接已建立');
};

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('收到消息:', data);
};

ws.onclose = function(event) {
    console.log('WebSocket连接已关闭');
};

ws.onerror = function(error) {
    console.error('WebSocket错误:', error);
};
```

### 心跳机制

客户端需要定期发送心跳消息保持连接：

```json
{
  "method": "PING",
  "id": 1
}
```

服务器响应：
```json
{
  "method": "PONG",
  "id": 1
}
```

### 认证

对于需要认证的私有数据流，需要先进行认证：

```json
{
  "method": "AUTH",
  "params": {
    "api_key": "your_api_key",
    "signature": "signature",
    "timestamp": *************
  },
  "id": 1
}
```

认证成功响应：
```json
{
  "method": "AUTH",
  "result": "success",
  "id": 1
}
```

## 公共数据流

### 订阅管理

#### 订阅数据流

```json
{
  "method": "SUBSCRIBE",
  "params": ["btcusdt@ticker", "ethusdt@depth"],
  "id": 1
}
```

#### 取消订阅

```json
{
  "method": "UNSUBSCRIBE",
  "params": ["btcusdt@ticker"],
  "id": 2
}
```

#### 查询订阅列表

```json
{
  "method": "LIST_SUBSCRIPTIONS",
  "id": 3
}
```

响应：
```json
{
  "method": "LIST_SUBSCRIPTIONS",
  "result": ["btcusdt@ticker", "ethusdt@depth"],
  "id": 3
}
```

### 实时价格推送

**订阅**: `{symbol}@ticker`

**示例**: `btcusdt@ticker`

**推送数据**:
```json
{
  "stream": "btcusdt@ticker",
  "data": {
    "symbol": "BTCUSDT",
    "price_change": "1250.50",
    "price_change_percent": "3.04",
    "weighted_avg_price": "41800.25",
    "prev_close_price": "41100.00",
    "last_price": "42350.50",
    "bid_price": "42349.50",
    "ask_price": "42351.00",
    "open_price": "41100.00",
    "high_price": "42500.00",
    "low_price": "40800.00",
    "volume": "1234.567890",
    "quote_volume": "51623456.78",
    "open_time": 1701334800000,
    "close_time": *************,
    "count": 125678,
    "timestamp": *************
  }
}
```

### 深度数据推送

**订阅**: `{symbol}@depth` 或 `{symbol}@depth{levels}`

**示例**: `btcusdt@depth` 或 `btcusdt@depth20`

**推送数据**:
```json
{
  "stream": "btcusdt@depth",
  "data": {
    "symbol": "BTCUSDT",
    "last_update_id": **********,
    "bids": [
      ["42349.50", "0.125000"],
      ["42349.00", "0.250000"],
      ["42348.50", "0.180000"]
    ],
    "asks": [
      ["42351.00", "0.089000"],
      ["42351.50", "0.156000"],
      ["42352.00", "0.234000"]
    ],
    "timestamp": *************
  }
}
```

### K线数据推送

**订阅**: `{symbol}@kline_{interval}`

**示例**: `btcusdt@kline_1m`

**推送数据**:
```json
{
  "stream": "btcusdt@kline_1m",
  "data": {
    "symbol": "BTCUSDT",
    "kline": {
      "open_time": *************,
      "close_time": 1701421259999,
      "symbol": "BTCUSDT",
      "interval": "1m",
      "open_price": "42300.00",
      "close_price": "42350.50",
      "high_price": "42380.00",
      "low_price": "42290.00",
      "volume": "12.345678",
      "quote_volume": "522456.78",
      "trade_count": 156,
      "taker_buy_volume": "6.123456",
      "taker_buy_quote_volume": "259123.45",
      "is_closed": false
    },
    "timestamp": 1701421230000
  }
}
```

### 成交数据推送

**订阅**: `{symbol}@trade`

**示例**: `btcusdt@trade`

**推送数据**:
```json
{
  "stream": "btcusdt@trade",
  "data": {
    "symbol": "BTCUSDT",
    "trade_id": "trade_123456",
    "price": "42350.50",
    "quantity": "0.001000",
    "buyer_order_id": "order_buy_123",
    "seller_order_id": "order_sell_456",
    "trade_time": *************,
    "is_buyer_maker": false
  }
}
```

### 聚合成交推送

**订阅**: `{symbol}@aggTrade`

**示例**: `btcusdt@aggTrade`

**推送数据**:
```json
{
  "stream": "btcusdt@aggTrade",
  "data": {
    "symbol": "BTCUSDT",
    "agg_trade_id": "agg_123456",
    "price": "42350.50",
    "quantity": "0.125000",
    "first_trade_id": "trade_123450",
    "last_trade_id": "trade_123456",
    "trade_time": *************,
    "is_buyer_maker": false
  }
}
```

## 私有数据流

### 账户更新推送

**订阅**: `account` (需要认证)

**推送数据**:
```json
{
  "stream": "account",
  "data": {
    "event_type": "account_update",
    "event_time": *************,
    "balances": [
      {
        "asset": "BTC",
        "free": "0.********",
        "locked": "0.********"
      },
      {
        "asset": "USDT",
        "free": "1234.********",
        "locked": "100.********"
      }
    ]
  }
}
```

### 订单更新推送

**订阅**: `orders` (需要认证)

**推送数据**:
```json
{
  "stream": "orders",
  "data": {
    "event_type": "order_update",
    "event_time": *************,
    "symbol": "BTCUSDT",
    "order_id": "**********",
    "client_order_id": "my_order_001",
    "side": "buy",
    "order_type": "limit",
    "time_in_force": "GTC",
    "quantity": "0.001000",
    "price": "50000.00",
    "stop_price": "0.00",
    "iceberg_quantity": "0.00",
    "order_list_id": -1,
    "original_client_order_id": "",
    "execution_type": "NEW",
    "order_status": "NEW",
    "order_reject_reason": "NONE",
    "order_id": "**********",
    "last_executed_quantity": "0.********",
    "cumulative_filled_quantity": "0.********",
    "last_executed_price": "0.00",
    "commission_amount": "0",
    "commission_asset": null,
    "transaction_time": *************,
    "trade_id": -1,
    "is_order_working": true,
    "is_trade_maker_side": false
  }
}
```

### 成交更新推送

**订阅**: `trades` (需要认证)

**推送数据**:
```json
{
  "stream": "trades",
  "data": {
    "event_type": "trade_update",
    "event_time": *************,
    "symbol": "BTCUSDT",
    "trade_id": "trade_123456",
    "order_id": "**********",
    "side": "buy",
    "quantity": "0.001000",
    "price": "42350.50",
    "commission": "0.00000042",
    "commission_asset": "BTC",
    "trade_time": *************,
    "is_buyer": true,
    "is_maker": false
  }
}
```

## 组合数据流

### 多数据流订阅

可以同时订阅多个数据流：

```json
{
  "method": "SUBSCRIBE",
  "params": [
    "btcusdt@ticker",
    "btcusdt@depth20",
    "btcusdt@kline_1m",
    "btcusdt@trade",
    "ethusdt@ticker"
  ],
  "id": 1
}
```

### 全市场数据流

**订阅**: `!ticker@arr` (所有交易对的ticker数据)

**推送数据**:
```json
{
  "stream": "!ticker@arr",
  "data": [
    {
      "symbol": "BTCUSDT",
      "price_change": "1250.50",
      "price_change_percent": "3.04",
      "last_price": "42350.50",
      "volume": "1234.567890",
      "timestamp": *************
    },
    {
      "symbol": "ETHUSDT",
      "price_change": "-45.25",
      "price_change_percent": "-1.72",
      "last_price": "2580.75",
      "volume": "5678.901234",
      "timestamp": *************
    }
  ]
}
```

## 错误处理

### 错误响应格式

```json
{
  "error": {
    "code": 40001,
    "message": "Invalid symbol"
  },
  "id": 1
}
```

### 常见错误码

| 错误码 | 说明 |
|--------|------|
| 40001 | 无效的交易对 |
| 40002 | 无效的数据流名称 |
| 40003 | 认证失败 |
| 40004 | 权限不足 |
| 40005 | 订阅数量超限 |
| 40006 | 连接频率过高 |

## 限制说明

- 每个连接最多可订阅 50 个数据流
- 每个IP最多可建立 5 个WebSocket连接
- 心跳超时时间为 60 秒
- 消息发送频率限制为每秒 100 条

## 示例代码

### JavaScript 示例

```javascript
class SpotWebSocket {
    constructor(url) {
        this.url = url;
        this.ws = null;
        this.subscriptions = new Set();
        this.authenticated = false;
        this.messageId = 1;
        this.callbacks = new Map();
    }
    
    connect() {
        return new Promise((resolve, reject) => {
            this.ws = new WebSocket(this.url);
            
            this.ws.onopen = () => {
                console.log('WebSocket连接已建立');
                this.startHeartbeat();
                resolve();
            };
            
            this.ws.onmessage = (event) => {
                this.handleMessage(JSON.parse(event.data));
            };
            
            this.ws.onclose = () => {
                console.log('WebSocket连接已关闭');
                this.stopHeartbeat();
            };
            
            this.ws.onerror = (error) => {
                console.error('WebSocket错误:', error);
                reject(error);
            };
        });
    }
    
    handleMessage(data) {
        if (data.stream) {
            // 数据推送
            this.onData(data.stream, data.data);
        } else if (data.id) {
            // 响应消息
            const callback = this.callbacks.get(data.id);
            if (callback) {
                callback(data);
                this.callbacks.delete(data.id);
            }
        } else if (data.method === 'PONG') {
            // 心跳响应
            console.log('收到心跳响应');
        }
    }
    
    send(message) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        }
    }
    
    sendWithCallback(message, callback) {
        const id = this.messageId++;
        message.id = id;
        this.callbacks.set(id, callback);
        this.send(message);
    }
    
    subscribe(streams, callback) {
        const message = {
            method: 'SUBSCRIBE',
            params: Array.isArray(streams) ? streams : [streams]
        };
        
        this.sendWithCallback(message, (response) => {
            if (response.result === null) {
                message.params.forEach(stream => {
                    this.subscriptions.add(stream);
                });
                console.log('订阅成功:', message.params);
            } else {
                console.error('订阅失败:', response.error);
            }
            if (callback) callback(response);
        });
    }
    
    unsubscribe(streams, callback) {
        const message = {
            method: 'UNSUBSCRIBE',
            params: Array.isArray(streams) ? streams : [streams]
        };
        
        this.sendWithCallback(message, (response) => {
            if (response.result === null) {
                message.params.forEach(stream => {
                    this.subscriptions.delete(stream);
                });
                console.log('取消订阅成功:', message.params);
            } else {
                console.error('取消订阅失败:', response.error);
            }
            if (callback) callback(response);
        });
    }
    
    authenticate(apiKey, signature, timestamp, callback) {
        const message = {
            method: 'AUTH',
            params: {
                api_key: apiKey,
                signature: signature,
                timestamp: timestamp
            }
        };
        
        this.sendWithCallback(message, (response) => {
            if (response.result === 'success') {
                this.authenticated = true;
                console.log('认证成功');
            } else {
                console.error('认证失败:', response.error);
            }
            if (callback) callback(response);
        });
    }
    
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            this.send({ method: 'PING' });
        }, 30000);
    }
    
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }
    
    onData(stream, data) {
        // 重写此方法处理数据推送
        console.log(`收到数据 [${stream}]:`, data);
    }
    
    close() {
        this.stopHeartbeat();
        if (this.ws) {
            this.ws.close();
        }
    }
}

// 使用示例
const ws = new SpotWebSocket('wss://api.example.com/ws');

// 重写数据处理方法
ws.onData = function(stream, data) {
    switch (stream) {
        case 'btcusdt@ticker':
            console.log('BTC价格更新:', data.last_price);
            break;
        case 'btcusdt@depth':
            console.log('BTC深度更新, 最佳买价:', data.bids[0][0]);
            break;
        case 'btcusdt@kline_1m':
            console.log('BTC 1分钟K线更新:', data.kline.close_price);
            break;
        case 'orders':
            console.log('订单更新:', data.order_status);
            break;
        default:
            console.log(`未处理的数据流 [${stream}]:`, data);
    }
};

// 连接并订阅
ws.connect().then(() => {
    // 订阅公共数据流
    ws.subscribe([
        'btcusdt@ticker',
        'btcusdt@depth20',
        'btcusdt@kline_1m',
        'btcusdt@trade'
    ]);
    
    // 如果需要私有数据，先认证
    // ws.authenticate(apiKey, signature, timestamp, () => {
    //     ws.subscribe(['account', 'orders', 'trades']);
    // });
}).catch(error => {
    console.error('连接失败:', error);
});

// 5分钟后关闭连接
setTimeout(() => {
    ws.close();
}, 5 * 60 * 1000);
```

### Go 示例

```go
package main

import (
    "encoding/json"
    "fmt"
    "log"
    "net/url"
    "os"
    "os/signal"
    "time"

    "github.com/gorilla/websocket"
)

type WebSocketClient struct {
    conn          *websocket.Conn
    subscriptions map[string]bool
    authenticated bool
    messageID     int
}

type Message struct {
    Method string      `json:"method,omitempty"`
    Params interface{} `json:"params,omitempty"`
    ID     int         `json:"id,omitempty"`
    Stream string      `json:"stream,omitempty"`
    Data   interface{} `json:"data,omitempty"`
    Result interface{} `json:"result,omitempty"`
    Error  *Error      `json:"error,omitempty"`
}

type Error struct {
    Code    int    `json:"code"`
    Message string `json:"message"`
}

func NewWebSocketClient() *WebSocketClient {
    return &WebSocketClient{
        subscriptions: make(map[string]bool),
        messageID:     1,
    }
}

func (c *WebSocketClient) Connect(urlStr string) error {
    u, err := url.Parse(urlStr)
    if err != nil {
        return err
    }

    conn, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
    if err != nil {
        return err
    }

    c.conn = conn
    go c.readMessages()
    go c.heartbeat()

    return nil
}

func (c *WebSocketClient) readMessages() {
    defer c.conn.Close()
    
    for {
        _, message, err := c.conn.ReadMessage()
        if err != nil {
            log.Printf("读取消息错误: %v", err)
            break
        }

        var msg Message
        if err := json.Unmarshal(message, &msg); err != nil {
            log.Printf("解析消息错误: %v", err)
            continue
        }

        c.handleMessage(&msg)
    }
}

func (c *WebSocketClient) handleMessage(msg *Message) {
    if msg.Stream != "" {
        // 数据推送
        c.onData(msg.Stream, msg.Data)
    } else if msg.Method == "PONG" {
        // 心跳响应
        log.Println("收到心跳响应")
    } else if msg.ID > 0 {
        // 响应消息
        if msg.Error != nil {
            log.Printf("请求错误 [ID:%d]: %s", msg.ID, msg.Error.Message)
        } else {
            log.Printf("请求成功 [ID:%d]: %v", msg.ID, msg.Result)
        }
    }
}

func (c *WebSocketClient) onData(stream string, data interface{}) {
    switch stream {
    case "btcusdt@ticker":
        log.Printf("BTC价格更新: %v", data)
    case "btcusdt@depth":
        log.Printf("BTC深度更新: %v", data)
    default:
        log.Printf("收到数据 [%s]: %v", stream, data)
    }
}

func (c *WebSocketClient) send(msg *Message) error {
    data, err := json.Marshal(msg)
    if err != nil {
        return err
    }

    return c.conn.WriteMessage(websocket.TextMessage, data)
}

func (c *WebSocketClient) Subscribe(streams []string) error {
    msg := &Message{
        Method: "SUBSCRIBE",
        Params: streams,
        ID:     c.messageID,
    }
    c.messageID++

    for _, stream := range streams {
        c.subscriptions[stream] = true
    }

    return c.send(msg)
}

func (c *WebSocketClient) heartbeat() {
    ticker := time.NewTicker(30 * time.Second)
    defer ticker.Stop()

    for {
        select {
        case <-ticker.C:
            msg := &Message{
                Method: "PING",
                ID:     c.messageID,
            }
            c.messageID++
            
            if err := c.send(msg); err != nil {
                log.Printf("发送心跳失败: %v", err)
                return
            }
        }
    }
}

func (c *WebSocketClient) Close() error {
    return c.conn.Close()
}

func main() {
    client := NewWebSocketClient()

    // 连接WebSocket
    if err := client.Connect("wss://api.example.com/ws"); err != nil {
        log.Fatal("连接失败:", err)
    }

    // 订阅数据流
    streams := []string{
        "btcusdt@ticker",
        "btcusdt@depth20",
        "btcusdt@kline_1m",
    }

    if err := client.Subscribe(streams); err != nil {
        log.Fatal("订阅失败:", err)
    }

    // 等待中断信号
    interrupt := make(chan os.Signal, 1)
    signal.Notify(interrupt, os.Interrupt)

    <-interrupt
    log.Println("收到中断信号，正在关闭连接...")

    client.Close()
}
```
