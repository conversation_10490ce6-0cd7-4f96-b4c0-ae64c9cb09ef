# 系统架构设计

## 整体架构

### 1. 架构概览

Spot-Server 采用微服务架构设计，将不同的业务功能拆分为独立的服务模块，每个服务都可以独立部署、扩展和维护。

```mermaid
graph TB
    subgraph "客户端层"
        A[Web客户端]
        B[移动客户端]
        C[第三方API]
    end
    
    subgraph "接入层"
        D[负载均衡器]
        E[API网关]
    end
    
    subgraph "业务服务层"
        F[Core Service<br/>核心服务]
        G[Order Service<br/>订单服务]
        H[Market Service<br/>行情服务]
        I[Limit Service<br/>限价服务]
        J[KLine Service<br/>K线服务]
        K[Closing Service<br/>清算服务]
        L[Push Service<br/>推送服务]
    end
    
    subgraph "数据层"
        M[MySQL集群]
        N[Redis集群]
        O[RabbitMQ集群]
    end
    
    subgraph "基础设施层"
        P[服务发现<br/>Consul]
        Q[监控系统<br/>Prometheus]
        R[日志系统<br/>ELK]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    E --> F
    E --> G
    E --> H
    F --> I
    F --> J
    F --> K
    F --> L
    G --> M
    G --> N
    H --> N
    H --> O
    I --> M
    I --> N
    J --> M
    J --> N
    K --> M
    K --> N
    L --> N
    L --> O
    
    F --> P
    G --> P
    H --> P
    F --> Q
    G --> Q
    H --> Q
    F --> R
    G --> R
    H --> R
```

### 2. 服务拆分原则

#### 业务边界清晰
- **Core Service**: 用户管理、权限控制、基础配置
- **Order Service**: 订单处理、撮合引擎、交易执行
- **Market Service**: 行情数据接入、处理和分发
- **Limit Service**: 限价单管理、条件触发
- **KLine Service**: K线数据生成和存储
- **Closing Service**: 交易清算、资金结算
- **Push Service**: 实时消息推送

#### 数据一致性
- 每个服务拥有独立的数据库
- 通过事件驱动保证最终一致性
- 使用分布式事务处理跨服务操作

#### 服务自治
- 独立部署和扩展
- 独立的技术栈选择
- 独立的开发团队

## 技术架构

### 1. 技术栈选择

#### 编程语言
- **Go**: 主要开发语言，高并发性能优秀
- **JavaScript**: 前端开发
- **SQL**: 数据库查询

#### 框架和库
- **Gin**: HTTP Web框架
- **GORM**: ORM框架
- **RPCX**: RPC通信框架
- **Gorilla WebSocket**: WebSocket支持
- **Zap**: 结构化日志
- **Prometheus**: 监控指标

#### 数据存储
- **MySQL**: 主要业务数据存储
- **Redis**: 缓存和会话存储
- **RabbitMQ**: 消息队列

#### 基础设施
- **Docker**: 容器化部署
- **Kubernetes**: 容器编排
- **Consul**: 服务发现
- **Nginx**: 负载均衡

### 2. 数据流架构

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as API网关
    participant Core as 核心服务
    participant Order as 订单服务
    participant Market as 行情服务
    participant MQ as 消息队列
    participant DB as 数据库
    participant Cache as 缓存
    
    Client->>Gateway: 下单请求
    Gateway->>Core: 用户验证
    Core->>Cache: 检查用户状态
    Cache-->>Core: 用户信息
    Core-->>Gateway: 验证通过
    Gateway->>Order: 创建订单
    Order->>DB: 保存订单
    Order->>MQ: 发布订单事件
    MQ->>Market: 订单通知
    Market->>Cache: 更新行情数据
    Order-->>Gateway: 订单创建成功
    Gateway-->>Client: 返回结果
```

### 3. 服务间通信

#### 同步通信
- **HTTP/REST**: 外部API接口
- **gRPC**: 内部服务间调用
- **RPC**: 高性能内部通信

#### 异步通信
- **消息队列**: 事件驱动架构
- **WebSocket**: 实时数据推送
- **事件总线**: 服务解耦

## 数据架构

### 1. 数据分层

#### 数据接入层
- 外部交易所API数据接入
- 用户交易数据接入
- 系统配置数据接入

#### 数据处理层
- 实时数据处理
- 批量数据处理
- 数据清洗和转换

#### 数据存储层
- 热数据存储 (Redis)
- 温数据存储 (MySQL)
- 冷数据存储 (归档系统)

#### 数据服务层
- 数据查询服务
- 数据分析服务
- 数据导出服务

### 2. 数据库设计

#### 分库分表策略
```sql
-- 用户表按用户ID分片
CREATE TABLE users_0001 (
    id BIGINT PRIMARY KEY,
    username VARCHAR(50),
    email VARCHAR(100),
    created_at TIMESTAMP
);

-- 订单表按交易对分片
CREATE TABLE orders_btcusdt (
    id BIGINT PRIMARY KEY,
    user_id BIGINT,
    symbol VARCHAR(20),
    side ENUM('buy', 'sell'),
    amount DECIMAL(20,8),
    price DECIMAL(20,8),
    status VARCHAR(20),
    created_at TIMESTAMP
);

-- 成交表按时间分区
CREATE TABLE trades (
    id BIGINT PRIMARY KEY,
    order_id BIGINT,
    symbol VARCHAR(20),
    amount DECIMAL(20,8),
    price DECIMAL(20,8),
    timestamp TIMESTAMP
) PARTITION BY RANGE (UNIX_TIMESTAMP(timestamp)) (
    PARTITION p202301 VALUES LESS THAN (UNIX_TIMESTAMP('2023-02-01')),
    PARTITION p202302 VALUES LESS THAN (UNIX_TIMESTAMP('2023-03-01'))
);
```

#### 读写分离
```go
type DatabaseConfig struct {
    Master DatabaseConnection `yaml:"master"`
    Slaves []DatabaseConnection `yaml:"slaves"`
}

type DatabaseManager struct {
    master *sql.DB
    slaves []*sql.DB
    current int
}

func (dm *DatabaseManager) GetReadDB() *sql.DB {
    // 轮询选择从库
    db := dm.slaves[dm.current%len(dm.slaves)]
    dm.current++
    return db
}

func (dm *DatabaseManager) GetWriteDB() *sql.DB {
    return dm.master
}
```

### 3. 缓存架构

#### 多级缓存
```go
type CacheLayer struct {
    L1 *sync.Map      // 本地缓存
    L2 *redis.Client  // Redis缓存
    L3 *sql.DB        // 数据库
}

// 缓存策略
func (c *CacheLayer) Get(key string) (interface{}, error) {
    // L1缓存 - 最快访问
    if value, ok := c.L1.Load(key); ok {
        return value, nil
    }
    
    // L2缓存 - 网络访问
    value, err := c.L2.Get(key).Result()
    if err == nil {
        c.L1.Store(key, value)
        return value, nil
    }
    
    // L3数据库 - 最慢访问
    value, err = c.queryFromDB(key)
    if err != nil {
        return nil, err
    }
    
    // 回写缓存
    c.L2.Set(key, value, time.Hour)
    c.L1.Store(key, value)
    
    return value, nil
}
```

#### 缓存更新策略
- **Cache Aside**: 应用程序管理缓存
- **Write Through**: 写入时同步更新缓存
- **Write Behind**: 异步更新缓存
- **Refresh Ahead**: 预先刷新缓存

## 安全架构

### 1. 认证和授权

#### JWT Token 认证
```go
type JWTClaims struct {
    UserID   string `json:"user_id"`
    Username string `json:"username"`
    Role     string `json:"role"`
    jwt.StandardClaims
}

func GenerateToken(userID, username, role string) (string, error) {
    claims := JWTClaims{
        UserID:   userID,
        Username: username,
        Role:     role,
        StandardClaims: jwt.StandardClaims{
            ExpiresAt: time.Now().Add(24 * time.Hour).Unix(),
            Issuer:    "spot-server",
        },
    }
    
    token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
    return token.SignedString([]byte(secretKey))
}
```

#### API Key 认证
```go
type APIKeyAuth struct {
    APIKey    string `json:"api_key"`
    SecretKey string `json:"secret_key"`
    Signature string `json:"signature"`
    Timestamp int64  `json:"timestamp"`
}

func ValidateAPIKey(auth APIKeyAuth, body []byte) error {
    // 检查时间戳
    if time.Now().Unix()-auth.Timestamp > 300 {
        return errors.New("request expired")
    }
    
    // 验证签名
    signString := fmt.Sprintf("%d%s", auth.Timestamp, string(body))
    expectedSig := hmacSHA256(signString, auth.SecretKey)
    
    if auth.Signature != expectedSig {
        return errors.New("invalid signature")
    }
    
    return nil
}
```

### 2. 数据安全

#### 敏感数据加密
```go
func EncryptSensitiveData(data string) (string, error) {
    block, err := aes.NewCipher([]byte(encryptionKey))
    if err != nil {
        return "", err
    }
    
    gcm, err := cipher.NewGCM(block)
    if err != nil {
        return "", err
    }
    
    nonce := make([]byte, gcm.NonceSize())
    if _, err = io.ReadFull(rand.Reader, nonce); err != nil {
        return "", err
    }
    
    ciphertext := gcm.Seal(nonce, nonce, []byte(data), nil)
    return base64.StdEncoding.EncodeToString(ciphertext), nil
}
```

#### 数据脱敏
```go
func MaskSensitiveInfo(data interface{}) interface{} {
    switch v := data.(type) {
    case string:
        if isEmail(v) {
            return maskEmail(v)
        }
        if isPhone(v) {
            return maskPhone(v)
        }
    case map[string]interface{}:
        for key, value := range v {
            if isSensitiveField(key) {
                v[key] = MaskSensitiveInfo(value)
            }
        }
    }
    return data
}
```

### 3. 网络安全

#### 限流和防护
```go
type RateLimiter struct {
    limiter *rate.Limiter
    clients map[string]*rate.Limiter
    mutex   sync.RWMutex
}

func (rl *RateLimiter) Allow(clientID string) bool {
    rl.mutex.RLock()
    limiter, exists := rl.clients[clientID]
    rl.mutex.RUnlock()
    
    if !exists {
        rl.mutex.Lock()
        limiter = rate.NewLimiter(rate.Limit(100), 200) // 100 req/s, burst 200
        rl.clients[clientID] = limiter
        rl.mutex.Unlock()
    }
    
    return limiter.Allow()
}
```

#### HTTPS 和 TLS
```go
func StartHTTPSServer() {
    cert, err := tls.LoadX509KeyPair("server.crt", "server.key")
    if err != nil {
        log.Fatal(err)
    }
    
    tlsConfig := &tls.Config{
        Certificates: []tls.Certificate{cert},
        MinVersion:   tls.VersionTLS12,
        CipherSuites: []uint16{
            tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
            tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305,
            tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
        },
    }
    
    server := &http.Server{
        Addr:      ":443",
        TLSConfig: tlsConfig,
    }
    
    log.Fatal(server.ListenAndServeTLS("", ""))
}
```

## 性能架构

### 1. 高并发设计

#### 连接池管理
```go
type ConnectionPool struct {
    pool    chan *sql.DB
    maxConn int
    minConn int
}

func NewConnectionPool(maxConn, minConn int) *ConnectionPool {
    pool := &ConnectionPool{
        pool:    make(chan *sql.DB, maxConn),
        maxConn: maxConn,
        minConn: minConn,
    }
    
    // 初始化最小连接数
    for i := 0; i < minConn; i++ {
        conn := createConnection()
        pool.pool <- conn
    }
    
    return pool
}

func (p *ConnectionPool) Get() *sql.DB {
    select {
    case conn := <-p.pool:
        return conn
    default:
        return createConnection()
    }
}

func (p *ConnectionPool) Put(conn *sql.DB) {
    select {
    case p.pool <- conn:
    default:
        conn.Close()
    }
}
```

#### 异步处理
```go
type AsyncProcessor struct {
    workers   int
    jobQueue  chan Job
    quit      chan bool
}

func (p *AsyncProcessor) Start() {
    for i := 0; i < p.workers; i++ {
        go p.worker()
    }
}

func (p *AsyncProcessor) worker() {
    for {
        select {
        case job := <-p.jobQueue:
            job.Process()
        case <-p.quit:
            return
        }
    }
}

func (p *AsyncProcessor) Submit(job Job) {
    p.jobQueue <- job
}
```

### 2. 缓存优化

#### 预热策略
```go
func WarmupCache() {
    // 预加载热点数据
    symbols := getActiveSymbols()
    for _, symbol := range symbols {
        go func(s string) {
            // 预加载深度数据
            depth := getDepthFromDB(s)
            cache.Set(fmt.Sprintf("depth:%s", s), depth, time.Hour)
            
            // 预加载K线数据
            klines := getKlinesFromDB(s, "1m", 1000)
            cache.Set(fmt.Sprintf("klines:%s:1m", s), klines, time.Hour)
        }(symbol)
    }
}
```

#### 缓存穿透防护
```go
func GetWithBloomFilter(key string) (interface{}, error) {
    // 检查布隆过滤器
    if !bloomFilter.Test([]byte(key)) {
        return nil, errors.New("key not exists")
    }
    
    // 从缓存获取
    value, err := cache.Get(key)
    if err == nil {
        return value, nil
    }
    
    // 从数据库获取
    value, err = database.Get(key)
    if err != nil {
        return nil, err
    }
    
    // 更新缓存和布隆过滤器
    cache.Set(key, value, time.Hour)
    bloomFilter.Add([]byte(key))
    
    return value, nil
}
```

## 可扩展性设计

### 1. 水平扩展

#### 服务扩展
```yaml
# kubernetes deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: order-service
spec:
  replicas: 5  # 可根据负载调整
  selector:
    matchLabels:
      app: order-service
  template:
    spec:
      containers:
      - name: order-service
        image: spot-server/order:latest
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
```

#### 数据库扩展
```go
// 分片路由
func GetShardDB(userID string) *sql.DB {
    hash := crc32.ChecksumIEEE([]byte(userID))
    shardIndex := hash % uint32(len(shardDBs))
    return shardDBs[shardIndex]
}

// 读写分离
func GetDB(operation string) *sql.DB {
    if operation == "read" {
        return getSlaveDB()
    }
    return getMasterDB()
}
```

### 2. 垂直扩展

#### 资源优化
```go
// 内存优化
func OptimizeMemory() {
    // 设置GC参数
    debug.SetGCPercent(100)
    debug.SetMemoryLimit(8 << 30) // 8GB
    
    // 对象池复用
    var bufferPool = sync.Pool{
        New: func() interface{} {
            return make([]byte, 1024)
        },
    }
}

// CPU优化
func OptimizeCPU() {
    // 设置GOMAXPROCS
    runtime.GOMAXPROCS(runtime.NumCPU())
    
    // 使用CPU亲和性
    // 具体实现依赖于操作系统
}
```

这个架构设计文档涵盖了系统的整体架构、技术选型、数据架构、安全架构、性能架构和可扩展性设计等方面，为开发和运维团队提供了全面的技术指导。
