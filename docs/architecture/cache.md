# 缓存架构设计

## 缓存策略概览

### 1. 缓存层次结构
```
┌─────────────────────────────────────────────────────────┐
│                   应用层缓存                             │
├─────────────────────────────────────────────────────────┤
│  L1: 本地缓存 (内存)     │  L2: 分布式缓存 (Redis)      │
│  • 进程内缓存            │  • 集群缓存                  │
│  • 响应时间: < 1ms       │  • 响应时间: < 10ms          │
│  • 容量: 100MB-1GB       │  • 容量: 10GB-100GB          │
│  • 生存时间: 5-30分钟     │  • 生存时间: 1小时-1天        │
└─────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────▼─────────────────────────────┐
│                   数据存储层                             │
├─────────────────────────────────────────────────────────┤
│  L3: 数据库 (MySQL)      │  L4: 归档存储 (对象存储)      │
│  • 持久化存储            │  • 冷数据存储                │
│  • 响应时间: 10-100ms    │  • 响应时间: 100ms-1s        │
│  • 容量: 1TB-10TB        │  • 容量: 无限制               │
└─────────────────────────────────────────────────────────┘
```

### 2. 缓存分类
- **热点数据缓存**: 高频访问的数据
- **会话缓存**: 用户会话和状态信息
- **计算结果缓存**: 复杂计算的结果
- **静态数据缓存**: 配置信息和元数据

## Redis 集群架构

### 1. 集群拓扑
```
┌─────────────────────────────────────────────────────────┐
│                Redis Cluster                            │
├─────────────────┬─────────────────┬─────────────────────┤
│   Master-1      │   Master-2      │   Master-3          │
│   Slots:        │   Slots:        │   Slots:            │
│   0-5460        │   5461-10922    │   10923-16383       │
│       │         │       │         │       │             │
│   Slave-1       │   Slave-2       │   Slave-3           │
└─────────────────┴─────────────────┴─────────────────────┘

┌─────────────────────────────────────────────────────────┐
│              Sentinel 高可用架构                         │
├─────────────────┬─────────────────┬─────────────────────┤
│   Sentinel-1    │   Sentinel-2    │   Sentinel-3        │
│   监控Master    │   监控Master    │   监控Master        │
│   故障检测      │   故障检测      │   故障检测          │
│   自动切换      │   自动切换      │   自动切换          │
└─────────────────┴─────────────────┴─────────────────────┘
```

### 2. 分片策略
```go
// 一致性哈希分片
type ConsistentHash struct {
    nodes map[uint32]string
    keys  []uint32
}

func (ch *ConsistentHash) GetNode(key string) string {
    hash := crc32.ChecksumIEEE([]byte(key))
    idx := sort.Search(len(ch.keys), func(i int) bool {
        return ch.keys[i] >= hash
    })
    if idx == len(ch.keys) {
        idx = 0
    }
    return ch.nodes[ch.keys[idx]]
}

// 业务分片策略
func GetRedisKey(business, key string) string {
    switch business {
    case "user":
        return fmt.Sprintf("user:%s", key)
    case "order":
        return fmt.Sprintf("order:%s", key)
    case "market":
        return fmt.Sprintf("market:%s", key)
    default:
        return fmt.Sprintf("default:%s", key)
    }
}
```

## 缓存数据结构设计

### 1. 用户相关缓存
```go
// 用户信息缓存
type UserCache struct {
    UserID    string    `json:"user_id"`
    Username  string    `json:"username"`
    Email     string    `json:"email"`
    Status    string    `json:"status"`
    KYCLevel  int       `json:"kyc_level"`
    VIPLevel  int       `json:"vip_level"`
    UpdatedAt time.Time `json:"updated_at"`
}

// Redis Key: user:info:{user_id}
// TTL: 30分钟
// 数据结构: Hash

// 用户会话缓存
type SessionCache struct {
    UserID    string    `json:"user_id"`
    Token     string    `json:"token"`
    LoginIP   string    `json:"login_ip"`
    LoginTime time.Time `json:"login_time"`
    ExpiresAt time.Time `json:"expires_at"`
}

// Redis Key: session:{token}
// TTL: 24小时
// 数据结构: Hash
```

### 2. 订单相关缓存
```go
// 活跃订单缓存
type ActiveOrderCache struct {
    OrderID     string          `json:"order_id"`
    UserID      string          `json:"user_id"`
    Symbol      string          `json:"symbol"`
    Side        string          `json:"side"`
    Type        string          `json:"type"`
    Amount      decimal.Decimal `json:"amount"`
    Price       decimal.Decimal `json:"price"`
    Status      string          `json:"status"`
    CreatedAt   time.Time       `json:"created_at"`
}

// Redis Key: order:active:{user_id}
// TTL: 1小时
// 数据结构: Sorted Set (按创建时间排序)

// 订单统计缓存
type OrderStatsCache struct {
    Symbol      string `json:"symbol"`
    TotalOrders int64  `json:"total_orders"`
    TotalVolume string `json:"total_volume"`
    AvgPrice    string `json:"avg_price"`
    UpdatedAt   int64  `json:"updated_at"`
}

// Redis Key: order:stats:{symbol}:24h
// TTL: 5分钟
// 数据结构: Hash
```

### 3. 行情相关缓存
```go
// 实时价格缓存
type TickerCache struct {
    Symbol             string          `json:"symbol"`
    LastPrice          decimal.Decimal `json:"last_price"`
    PriceChange        decimal.Decimal `json:"price_change"`
    PriceChangePercent decimal.Decimal `json:"price_change_percent"`
    HighPrice          decimal.Decimal `json:"high_price"`
    LowPrice           decimal.Decimal `json:"low_price"`
    Volume             decimal.Decimal `json:"volume"`
    QuoteVolume        decimal.Decimal `json:"quote_volume"`
    Timestamp          int64           `json:"timestamp"`
}

// Redis Key: ticker:{symbol}
// TTL: 10秒
// 数据结构: Hash

// 深度数据缓存
type DepthCache struct {
    Symbol       string      `json:"symbol"`
    LastUpdateID int64       `json:"last_update_id"`
    Bids         [][]string  `json:"bids"`
    Asks         [][]string  `json:"asks"`
    Timestamp    int64       `json:"timestamp"`
}

// Redis Key: depth:{symbol}:{level}
// TTL: 5秒
// 数据结构: Hash

// K线数据缓存
type KlineCache struct {
    Symbol    string `json:"symbol"`
    Interval  string `json:"interval"`
    OpenTime  int64  `json:"open_time"`
    CloseTime int64  `json:"close_time"`
    Open      string `json:"open"`
    High      string `json:"high"`
    Low       string `json:"low"`
    Close     string `json:"close"`
    Volume    string `json:"volume"`
}

// Redis Key: kline:{symbol}:{interval}
// TTL: 1分钟
// 数据结构: List (最新1000条)
```

### 4. 系统配置缓存
```go
// 系统配置缓存
type ConfigCache struct {
    Key         string      `json:"key"`
    Value       interface{} `json:"value"`
    Type        string      `json:"type"`
    Description string      `json:"description"`
    UpdatedAt   time.Time   `json:"updated_at"`
}

// Redis Key: config:{key}
// TTL: 1小时
// 数据结构: Hash

// 交易对配置缓存
type SymbolConfigCache struct {
    Symbol          string          `json:"symbol"`
    BaseCurrency    string          `json:"base_currency"`
    QuoteCurrency   string          `json:"quote_currency"`
    Status          string          `json:"status"`
    PricePrecision  int             `json:"price_precision"`
    AmountPrecision int             `json:"amount_precision"`
    MinAmount       decimal.Decimal `json:"min_amount"`
    MaxAmount       decimal.Decimal `json:"max_amount"`
    MinNotional     decimal.Decimal `json:"min_notional"`
    MakerFee        decimal.Decimal `json:"maker_fee"`
    TakerFee        decimal.Decimal `json:"taker_fee"`
}

// Redis Key: symbol:config:{symbol}
// TTL: 1小时
// 数据结构: Hash
```

## 缓存操作策略

### 1. Cache-Aside 模式
```go
type CacheAsidePattern struct {
    cache    *redis.Client
    database *sql.DB
}

func (c *CacheAsidePattern) Get(key string) (interface{}, error) {
    // 1. 先从缓存获取
    cached, err := c.cache.Get(key).Result()
    if err == nil {
        return cached, nil
    }
    
    // 2. 缓存未命中，从数据库获取
    data, err := c.queryFromDB(key)
    if err != nil {
        return nil, err
    }
    
    // 3. 写入缓存
    c.cache.Set(key, data, time.Hour)
    
    return data, nil
}

func (c *CacheAsidePattern) Update(key string, data interface{}) error {
    // 1. 更新数据库
    if err := c.updateDB(key, data); err != nil {
        return err
    }
    
    // 2. 删除缓存 (让下次读取时重新加载)
    c.cache.Del(key)
    
    return nil
}
```

### 2. Write-Through 模式
```go
type WriteThroughPattern struct {
    cache    *redis.Client
    database *sql.DB
}

func (w *WriteThroughPattern) Set(key string, data interface{}) error {
    // 1. 同时写入缓存和数据库
    if err := w.writeToCache(key, data); err != nil {
        return err
    }
    
    if err := w.writeToDB(key, data); err != nil {
        // 回滚缓存
        w.cache.Del(key)
        return err
    }
    
    return nil
}
```

### 3. Write-Behind 模式
```go
type WriteBehindPattern struct {
    cache     *redis.Client
    database  *sql.DB
    writeQueue chan WriteOperation
}

type WriteOperation struct {
    Key  string
    Data interface{}
    Op   string // "insert", "update", "delete"
}

func (w *WriteBehindPattern) Set(key string, data interface{}) error {
    // 1. 立即写入缓存
    if err := w.cache.Set(key, data, time.Hour).Err(); err != nil {
        return err
    }
    
    // 2. 异步写入数据库
    w.writeQueue <- WriteOperation{
        Key:  key,
        Data: data,
        Op:   "update",
    }
    
    return nil
}

func (w *WriteBehindPattern) processWrites() {
    for op := range w.writeQueue {
        switch op.Op {
        case "insert":
            w.insertToDB(op.Key, op.Data)
        case "update":
            w.updateDB(op.Key, op.Data)
        case "delete":
            w.deleteFromDB(op.Key)
        }
    }
}
```

## 缓存预热策略

### 1. 启动时预热
```go
func WarmupCache() error {
    log.Info("开始缓存预热...")
    
    // 预热热点交易对配置
    symbols := []string{"BTCUSDT", "ETHUSDT", "BNBUSDT"}
    for _, symbol := range symbols {
        if err := warmupSymbolConfig(symbol); err != nil {
            log.Error("预热交易对配置失败", zap.String("symbol", symbol), zap.Error(err))
        }
    }
    
    // 预热系统配置
    if err := warmupSystemConfig(); err != nil {
        log.Error("预热系统配置失败", zap.Error(err))
    }
    
    // 预热热点用户数据
    if err := warmupHotUsers(); err != nil {
        log.Error("预热热点用户失败", zap.Error(err))
    }
    
    log.Info("缓存预热完成")
    return nil
}

func warmupSymbolConfig(symbol string) error {
    config, err := getSymbolConfigFromDB(symbol)
    if err != nil {
        return err
    }
    
    key := fmt.Sprintf("symbol:config:%s", symbol)
    return cache.HMSet(key, map[string]interface{}{
        "symbol":           config.Symbol,
        "base_currency":    config.BaseCurrency,
        "quote_currency":   config.QuoteCurrency,
        "status":           config.Status,
        "price_precision":  config.PricePrecision,
        "amount_precision": config.AmountPrecision,
        "min_amount":       config.MinAmount.String(),
        "max_amount":       config.MaxAmount.String(),
        "min_notional":     config.MinNotional.String(),
        "maker_fee":        config.MakerFee.String(),
        "taker_fee":        config.TakerFee.String(),
    }).Err()
}
```

### 2. 定时预热
```go
func StartScheduledWarmup() {
    ticker := time.NewTicker(30 * time.Minute)
    go func() {
        for range ticker.C {
            // 预热热点数据
            warmupHotData()
        }
    }()
}

func warmupHotData() {
    // 预热活跃交易对的深度数据
    activeSymbols := getActiveSymbols()
    for _, symbol := range activeSymbols {
        warmupDepthData(symbol)
    }
    
    // 预热热门K线数据
    intervals := []string{"1m", "5m", "15m", "1h", "1d"}
    for _, symbol := range activeSymbols {
        for _, interval := range intervals {
            warmupKlineData(symbol, interval)
        }
    }
}
```

## 缓存失效策略

### 1. TTL 过期策略
```go
// 不同类型数据的TTL设置
var CacheTTL = map[string]time.Duration{
    "user:info":      30 * time.Minute,  // 用户信息
    "session":        24 * time.Hour,    // 用户会话
    "ticker":         10 * time.Second,  // 实时价格
    "depth":          5 * time.Second,   // 深度数据
    "kline":          1 * time.Minute,   // K线数据
    "order:active":   1 * time.Hour,     // 活跃订单
    "config":         1 * time.Hour,     // 系统配置
    "symbol:config":  1 * time.Hour,     // 交易对配置
}

func SetCacheWithTTL(key, value string, dataType string) error {
    ttl, exists := CacheTTL[dataType]
    if !exists {
        ttl = 1 * time.Hour // 默认TTL
    }
    
    return cache.Set(key, value, ttl).Err()
}
```

### 2. 主动失效策略
```go
// 数据更新时主动删除相关缓存
func InvalidateUserCache(userID string) error {
    keys := []string{
        fmt.Sprintf("user:info:%s", userID),
        fmt.Sprintf("user:profile:%s", userID),
        fmt.Sprintf("user:settings:%s", userID),
    }
    
    return cache.Del(keys...).Err()
}

func InvalidateOrderCache(userID, symbol string) error {
    keys := []string{
        fmt.Sprintf("order:active:%s", userID),
        fmt.Sprintf("order:stats:%s:24h", symbol),
        fmt.Sprintf("user:orders:%s", userID),
    }
    
    return cache.Del(keys...).Err()
}

// 使用Redis的发布订阅实现缓存失效通知
func PublishCacheInvalidation(pattern string) error {
    return cache.Publish("cache:invalidate", pattern).Err()
}

func SubscribeCacheInvalidation() {
    pubsub := cache.Subscribe("cache:invalidate")
    defer pubsub.Close()
    
    for msg := range pubsub.Channel() {
        pattern := msg.Payload
        
        // 根据模式删除匹配的缓存键
        keys, err := cache.Keys(pattern).Result()
        if err != nil {
            log.Error("获取缓存键失败", zap.Error(err))
            continue
        }
        
        if len(keys) > 0 {
            cache.Del(keys...)
            log.Info("删除缓存", zap.Strings("keys", keys))
        }
    }
}
```

## 缓存穿透防护

### 1. 布隆过滤器
```go
type BloomFilter struct {
    bitSet []bool
    size   uint
    hash   []hash.Hash
}

func NewBloomFilter(size uint, hashCount int) *BloomFilter {
    return &BloomFilter{
        bitSet: make([]bool, size),
        size:   size,
        hash:   make([]hash.Hash, hashCount),
    }
}

func (bf *BloomFilter) Add(data []byte) {
    for _, h := range bf.hash {
        h.Reset()
        h.Write(data)
        index := binary.BigEndian.Uint32(h.Sum(nil)) % uint32(bf.size)
        bf.bitSet[index] = true
    }
}

func (bf *BloomFilter) Test(data []byte) bool {
    for _, h := range bf.hash {
        h.Reset()
        h.Write(data)
        index := binary.BigEndian.Uint32(h.Sum(nil)) % uint32(bf.size)
        if !bf.bitSet[index] {
            return false
        }
    }
    return true
}

// 在查询前使用布隆过滤器检查
func GetWithBloomFilter(key string) (interface{}, error) {
    // 1. 布隆过滤器检查
    if !bloomFilter.Test([]byte(key)) {
        return nil, errors.New("key not exists")
    }
    
    // 2. 缓存查询
    cached, err := cache.Get(key).Result()
    if err == nil {
        return cached, nil
    }
    
    // 3. 数据库查询
    data, err := queryFromDB(key)
    if err != nil {
        return nil, err
    }
    
    // 4. 更新缓存和布隆过滤器
    cache.Set(key, data, time.Hour)
    bloomFilter.Add([]byte(key))
    
    return data, nil
}
```

### 2. 空值缓存
```go
func GetWithNullCache(key string) (interface{}, error) {
    // 1. 从缓存获取
    cached, err := cache.Get(key).Result()
    if err == nil {
        if cached == "NULL" {
            return nil, errors.New("not found")
        }
        return cached, nil
    }
    
    // 2. 从数据库获取
    data, err := queryFromDB(key)
    if err != nil {
        // 缓存空值，防止穿透
        cache.Set(key, "NULL", 5*time.Minute)
        return nil, err
    }
    
    // 3. 缓存正常值
    cache.Set(key, data, time.Hour)
    return data, nil
}
```

## 缓存雪崩防护

### 1. 随机TTL
```go
func SetCacheWithRandomTTL(key, value string, baseTTL time.Duration) error {
    // 在基础TTL上增加随机时间，避免同时过期
    randomTTL := baseTTL + time.Duration(rand.Intn(300))*time.Second
    return cache.Set(key, value, randomTTL).Err()
}
```

### 2. 互斥锁
```go
var mutex sync.Mutex
var rebuilding = make(map[string]bool)

func GetWithMutex(key string) (interface{}, error) {
    // 1. 尝试从缓存获取
    cached, err := cache.Get(key).Result()
    if err == nil {
        return cached, nil
    }
    
    // 2. 检查是否正在重建
    mutex.Lock()
    if rebuilding[key] {
        mutex.Unlock()
        // 等待重建完成
        time.Sleep(100 * time.Millisecond)
        return GetWithMutex(key)
    }
    rebuilding[key] = true
    mutex.Unlock()
    
    // 3. 重建缓存
    defer func() {
        mutex.Lock()
        delete(rebuilding, key)
        mutex.Unlock()
    }()
    
    data, err := queryFromDB(key)
    if err != nil {
        return nil, err
    }
    
    cache.Set(key, data, time.Hour)
    return data, nil
}
```

## 监控和指标

### 1. 缓存指标
```go
type CacheMetrics struct {
    HitCount    int64 `json:"hit_count"`
    MissCount   int64 `json:"miss_count"`
    HitRate     float64 `json:"hit_rate"`
    AvgLatency  float64 `json:"avg_latency"`
    ErrorCount  int64 `json:"error_count"`
}

func (m *CacheMetrics) RecordHit() {
    atomic.AddInt64(&m.HitCount, 1)
    m.updateHitRate()
}

func (m *CacheMetrics) RecordMiss() {
    atomic.AddInt64(&m.MissCount, 1)
    m.updateHitRate()
}

func (m *CacheMetrics) updateHitRate() {
    total := m.HitCount + m.MissCount
    if total > 0 {
        m.HitRate = float64(m.HitCount) / float64(total)
    }
}
```

### 2. 性能监控
```go
func MonitorCachePerformance() {
    ticker := time.NewTicker(1 * time.Minute)
    go func() {
        for range ticker.C {
            // 收集Redis性能指标
            info, err := cache.Info("stats").Result()
            if err != nil {
                log.Error("获取Redis统计信息失败", zap.Error(err))
                continue
            }
            
            // 解析并上报指标
            parseAndReportMetrics(info)
        }
    }()
}
```

这个缓存架构设计提供了完整的缓存策略和实现方案，确保系统的高性能和高可用性。
