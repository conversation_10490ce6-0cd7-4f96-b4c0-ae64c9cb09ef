# 数据库设计

## 设计原则

### 1. 数据库设计原则
- **规范化设计**: 遵循第三范式，减少数据冗余
- **性能优化**: 合理使用索引，优化查询性能
- **扩展性**: 支持水平分片和垂直分区
- **一致性**: 保证数据的完整性和一致性
- **安全性**: 敏感数据加密存储

### 2. 分库分表策略
- **垂直分库**: 按业务模块分离数据库
- **水平分表**: 按数据量和访问模式分片
- **读写分离**: 主从复制分离读写压力
- **冷热分离**: 历史数据归档存储

## 数据库架构

### 1. 整体架构
```
┌─────────────────────────────────────────────────────────┐
│                    应用层                                │
├─────────────────────────────────────────────────────────┤
│  Core Service │ Order Service │ Market Service │ ...     │
└─────────┬───────────┬─────────────┬─────────────┬────────┘
          │           │             │             │
┌─────────▼───────────▼─────────────▼─────────────▼────────┐
│                   数据访问层                             │
├─────────────────────────────────────────────────────────┤
│  Connection Pool │ ORM Framework │ Cache Layer │ ...     │
└─────────┬───────────┬─────────────┬─────────────┬────────┘
          │           │             │             │
┌─────────▼───────────▼─────────────▼─────────────▼────────┐
│                   数据存储层                             │
├─────────────────────────────────────────────────────────┤
│  MySQL Cluster │ Redis Cluster │ InfluxDB │ ClickHouse  │
└─────────────────────────────────────────────────────────┘
```

### 2. 分库策略
```
┌─────────────────┬─────────────────┬─────────────────┐
│   core_db       │   order_db      │   market_db     │
├─────────────────┼─────────────────┼─────────────────┤
│ • users         │ • orders        │ • symbols       │
│ • roles         │ • trades        │ • tickers       │
│ • permissions   │ • positions     │ • klines        │
│ • configs       │ • balances      │ • depths        │
│ • notifications │ • transactions  │ • trade_data    │
└─────────────────┴─────────────────┴─────────────────┘

┌─────────────────┬─────────────────┬─────────────────┐
│   limit_db      │   closing_db    │   task_db       │
├─────────────────┼─────────────────┼─────────────────┤
│ • limit_orders  │ • settlements   │ • scheduled_jobs│
│ • conditions    │ • clearing_logs │ • job_logs      │
│ • triggers      │ • fee_records   │ • cron_tasks    │
└─────────────────┴─────────────────┴─────────────────┘
```

## 核心表设计

### 1. 用户相关表

#### users 用户表
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(32) UNIQUE NOT NULL COMMENT '用户ID',
    username VARCHAR(100) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(255) UNIQUE COMMENT '邮箱',
    phone VARCHAR(20) UNIQUE COMMENT '手机号',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    salt VARCHAR(32) NOT NULL COMMENT '密码盐值',
    nickname VARCHAR(100) COMMENT '昵称',
    avatar VARCHAR(500) COMMENT '头像URL',
    real_name VARCHAR(100) COMMENT '真实姓名',
    id_card VARCHAR(50) COMMENT '身份证号',
    kyc_level TINYINT DEFAULT 0 COMMENT 'KYC等级',
    vip_level TINYINT DEFAULT 0 COMMENT 'VIP等级',
    status ENUM('active', 'inactive', 'suspended', 'deleted') DEFAULT 'active',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```

#### user_profiles 用户资料表
```sql
CREATE TABLE user_profiles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(32) NOT NULL,
    language VARCHAR(10) DEFAULT 'zh-CN' COMMENT '语言设置',
    timezone VARCHAR(50) DEFAULT 'Asia/Shanghai' COMMENT '时区设置',
    currency VARCHAR(10) DEFAULT 'USD' COMMENT '默认货币',
    notification_email BOOLEAN DEFAULT TRUE COMMENT '邮件通知',
    notification_sms BOOLEAN DEFAULT TRUE COMMENT '短信通知',
    notification_push BOOLEAN DEFAULT TRUE COMMENT '推送通知',
    two_factor_enabled BOOLEAN DEFAULT FALSE COMMENT '双因子认证',
    two_factor_secret VARCHAR(32) COMMENT '双因子密钥',
    api_trading_enabled BOOLEAN DEFAULT FALSE COMMENT 'API交易权限',
    withdrawal_enabled BOOLEAN DEFAULT TRUE COMMENT '提现权限',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_user_id (user_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户资料表';
```

#### api_keys API密钥表
```sql
CREATE TABLE api_keys (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    key_id VARCHAR(32) UNIQUE NOT NULL COMMENT 'API Key ID',
    user_id VARCHAR(32) NOT NULL,
    name VARCHAR(100) NOT NULL COMMENT 'API Key名称',
    api_key VARCHAR(64) UNIQUE NOT NULL COMMENT 'API Key',
    secret_key VARCHAR(64) NOT NULL COMMENT 'Secret Key',
    permissions JSON COMMENT '权限列表',
    ip_whitelist JSON COMMENT 'IP白名单',
    status ENUM('active', 'inactive', 'deleted') DEFAULT 'active',
    last_used_at TIMESTAMP NULL COMMENT '最后使用时间',
    expires_at TIMESTAMP NULL COMMENT '过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_api_key (api_key),
    INDEX idx_status (status),
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API密钥表';
```

### 2. 订单相关表

#### orders 订单表 (分表)
```sql
CREATE TABLE orders_btcusdt (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_id VARCHAR(32) UNIQUE NOT NULL COMMENT '订单ID',
    user_id VARCHAR(32) NOT NULL COMMENT '用户ID',
    client_order_id VARCHAR(64) COMMENT '客户端订单ID',
    symbol VARCHAR(20) NOT NULL COMMENT '交易对',
    side ENUM('buy', 'sell') NOT NULL COMMENT '买卖方向',
    type ENUM('limit', 'market', 'stop_loss', 'stop_loss_limit', 'take_profit', 'take_profit_limit') NOT NULL COMMENT '订单类型',
    amount DECIMAL(20,8) NOT NULL COMMENT '订单数量',
    price DECIMAL(20,8) COMMENT '订单价格',
    stop_price DECIMAL(20,8) COMMENT '止损价格',
    filled_amount DECIMAL(20,8) DEFAULT 0 COMMENT '已成交数量',
    remaining_amount DECIMAL(20,8) NOT NULL COMMENT '剩余数量',
    avg_price DECIMAL(20,8) DEFAULT 0 COMMENT '平均成交价格',
    status ENUM('NEW', 'PARTIALLY_FILLED', 'FILLED', 'CANCELED', 'REJECTED', 'EXPIRED') DEFAULT 'NEW',
    time_in_force ENUM('GTC', 'IOC', 'FOK') DEFAULT 'GTC' COMMENT '有效期类型',
    fee DECIMAL(20,8) DEFAULT 0 COMMENT '手续费',
    fee_currency VARCHAR(10) COMMENT '手续费币种',
    source VARCHAR(20) DEFAULT 'web' COMMENT '订单来源',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_symbol (symbol),
    INDEX idx_status (status),
    INDEX idx_side (side),
    INDEX idx_type (type),
    INDEX idx_created_at (created_at),
    INDEX idx_user_symbol_status (user_id, symbol, status),
    INDEX idx_symbol_side_price (symbol, side, price)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表-BTCUSDT';
```

#### trades 成交表 (分区表)
```sql
CREATE TABLE trades (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    trade_id VARCHAR(32) UNIQUE NOT NULL COMMENT '成交ID',
    order_id VARCHAR(32) NOT NULL COMMENT '订单ID',
    user_id VARCHAR(32) NOT NULL COMMENT '用户ID',
    symbol VARCHAR(20) NOT NULL COMMENT '交易对',
    side ENUM('buy', 'sell') NOT NULL COMMENT '买卖方向',
    amount DECIMAL(20,8) NOT NULL COMMENT '成交数量',
    price DECIMAL(20,8) NOT NULL COMMENT '成交价格',
    quote_amount DECIMAL(20,8) NOT NULL COMMENT '成交金额',
    fee DECIMAL(20,8) DEFAULT 0 COMMENT '手续费',
    fee_currency VARCHAR(10) COMMENT '手续费币种',
    is_maker BOOLEAN DEFAULT FALSE COMMENT '是否为挂单方',
    trade_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '成交时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_order_id (order_id),
    INDEX idx_user_id (user_id),
    INDEX idx_symbol (symbol),
    INDEX idx_trade_time (trade_time),
    INDEX idx_user_symbol_time (user_id, symbol, trade_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成交表'
PARTITION BY RANGE (UNIX_TIMESTAMP(trade_time)) (
    PARTITION p202301 VALUES LESS THAN (UNIX_TIMESTAMP('2023-02-01')),
    PARTITION p202302 VALUES LESS THAN (UNIX_TIMESTAMP('2023-03-01')),
    PARTITION p202303 VALUES LESS THAN (UNIX_TIMESTAMP('2023-04-01')),
    PARTITION p202304 VALUES LESS THAN (UNIX_TIMESTAMP('2023-05-01')),
    PARTITION p202305 VALUES LESS THAN (UNIX_TIMESTAMP('2023-06-01')),
    PARTITION p202306 VALUES LESS THAN (UNIX_TIMESTAMP('2023-07-01')),
    PARTITION p202307 VALUES LESS THAN (UNIX_TIMESTAMP('2023-08-01')),
    PARTITION p202308 VALUES LESS THAN (UNIX_TIMESTAMP('2023-09-01')),
    PARTITION p202309 VALUES LESS THAN (UNIX_TIMESTAMP('2023-10-01')),
    PARTITION p202310 VALUES LESS THAN (UNIX_TIMESTAMP('2023-11-01')),
    PARTITION p202311 VALUES LESS THAN (UNIX_TIMESTAMP('2023-12-01')),
    PARTITION p202312 VALUES LESS THAN (UNIX_TIMESTAMP('2024-01-01'))
);
```

### 3. 资产相关表

#### balances 余额表
```sql
CREATE TABLE balances (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(32) NOT NULL,
    currency VARCHAR(10) NOT NULL COMMENT '币种',
    available DECIMAL(20,8) DEFAULT 0 COMMENT '可用余额',
    frozen DECIMAL(20,8) DEFAULT 0 COMMENT '冻结余额',
    total DECIMAL(20,8) GENERATED ALWAYS AS (available + frozen) COMMENT '总余额',
    version BIGINT DEFAULT 0 COMMENT '版本号(乐观锁)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_user_currency (user_id, currency),
    INDEX idx_user_id (user_id),
    INDEX idx_currency (currency)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='余额表';
```

#### balance_logs 余额变动日志表
```sql
CREATE TABLE balance_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    log_id VARCHAR(32) UNIQUE NOT NULL COMMENT '日志ID',
    user_id VARCHAR(32) NOT NULL,
    currency VARCHAR(10) NOT NULL COMMENT '币种',
    type ENUM('deposit', 'withdraw', 'trade', 'fee', 'transfer', 'bonus', 'penalty') NOT NULL COMMENT '变动类型',
    amount DECIMAL(20,8) NOT NULL COMMENT '变动金额',
    balance_before DECIMAL(20,8) NOT NULL COMMENT '变动前余额',
    balance_after DECIMAL(20,8) NOT NULL COMMENT '变动后余额',
    frozen_before DECIMAL(20,8) DEFAULT 0 COMMENT '变动前冻结',
    frozen_after DECIMAL(20,8) DEFAULT 0 COMMENT '变动后冻结',
    reference_id VARCHAR(32) COMMENT '关联ID(订单ID/交易ID等)',
    reference_type VARCHAR(20) COMMENT '关联类型',
    description VARCHAR(500) COMMENT '描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_currency (currency),
    INDEX idx_type (type),
    INDEX idx_reference (reference_id, reference_type),
    INDEX idx_created_at (created_at),
    INDEX idx_user_currency_time (user_id, currency, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='余额变动日志表';
```

### 4. 行情相关表

#### symbols 交易对表
```sql
CREATE TABLE symbols (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    symbol VARCHAR(20) UNIQUE NOT NULL COMMENT '交易对',
    base_currency VARCHAR(10) NOT NULL COMMENT '基础货币',
    quote_currency VARCHAR(10) NOT NULL COMMENT '计价货币',
    status ENUM('active', 'inactive', 'delisted') DEFAULT 'active',
    price_precision TINYINT DEFAULT 8 COMMENT '价格精度',
    amount_precision TINYINT DEFAULT 8 COMMENT '数量精度',
    min_amount DECIMAL(20,8) NOT NULL COMMENT '最小交易数量',
    max_amount DECIMAL(20,8) NOT NULL COMMENT '最大交易数量',
    min_notional DECIMAL(20,8) NOT NULL COMMENT '最小交易金额',
    tick_size DECIMAL(20,8) NOT NULL COMMENT '价格步长',
    step_size DECIMAL(20,8) NOT NULL COMMENT '数量步长',
    maker_fee DECIMAL(6,4) DEFAULT 0.001 COMMENT 'Maker手续费率',
    taker_fee DECIMAL(6,4) DEFAULT 0.001 COMMENT 'Taker手续费率',
    trading_enabled BOOLEAN DEFAULT TRUE COMMENT '是否允许交易',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_base_currency (base_currency),
    INDEX idx_quote_currency (quote_currency),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='交易对表';
```

#### klines K线表 (时序数据库)
```sql
-- 使用InfluxDB存储K线数据
-- 表结构示例 (InfluxDB Line Protocol)
-- klines,symbol=BTCUSDT,interval=1m open=50000,high=50100,low=49900,close=50050,volume=1.23456789 1640995200000000000
```

### 5. 系统配置表

#### system_configs 系统配置表
```sql
CREATE TABLE system_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
    description VARCHAR(500) COMMENT '配置描述',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_config_key (config_key),
    INDEX idx_is_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';
```

## 索引优化策略

### 1. 主键索引
- 使用自增ID作为主键
- 业务ID使用唯一索引
- 避免使用UUID作为主键

### 2. 复合索引
```sql
-- 订单查询优化
INDEX idx_user_symbol_status (user_id, symbol, status)
INDEX idx_symbol_side_price (symbol, side, price)
INDEX idx_created_at_status (created_at, status)

-- 成交查询优化
INDEX idx_user_symbol_time (user_id, symbol, trade_time)
INDEX idx_symbol_time (symbol, trade_time)

-- 余额查询优化
INDEX idx_user_currency_time (user_id, currency, created_at)
```

### 3. 分区索引
```sql
-- 按时间分区的表，每个分区都有独立的索引
ALTER TABLE trades 
ADD INDEX idx_partition_symbol_time (symbol, trade_time) LOCAL;
```

## 数据分片策略

### 1. 水平分片
```sql
-- 用户表按用户ID分片
users_0001: user_id % 1000 = 0-99
users_0002: user_id % 1000 = 100-199
...
users_0010: user_id % 1000 = 900-999

-- 订单表按交易对分片
orders_btcusdt: symbol = 'BTCUSDT'
orders_ethusdt: symbol = 'ETHUSDT'
orders_others: 其他交易对
```

### 2. 垂直分片
```sql
-- 用户基础信息表
users_basic: id, user_id, username, email, status

-- 用户扩展信息表
users_profile: user_id, real_name, id_card, kyc_level

-- 用户设置表
users_settings: user_id, language, timezone, notifications
```

## 数据一致性保证

### 1. 事务处理
```sql
-- 下单事务
START TRANSACTION;
-- 1. 检查余额
SELECT available FROM balances WHERE user_id = ? AND currency = ? FOR UPDATE;
-- 2. 冻结资金
UPDATE balances SET available = available - ?, frozen = frozen + ? WHERE user_id = ? AND currency = ?;
-- 3. 创建订单
INSERT INTO orders (...) VALUES (...);
-- 4. 记录余额变动
INSERT INTO balance_logs (...) VALUES (...);
COMMIT;
```

### 2. 乐观锁
```sql
-- 使用版本号实现乐观锁
UPDATE balances 
SET available = ?, frozen = ?, version = version + 1 
WHERE user_id = ? AND currency = ? AND version = ?;
```

### 3. 分布式事务
```go
// 使用Saga模式处理分布式事务
type OrderSaga struct {
    steps []SagaStep
}

func (s *OrderSaga) Execute() error {
    for i, step := range s.steps {
        if err := step.Execute(); err != nil {
            // 回滚已执行的步骤
            for j := i - 1; j >= 0; j-- {
                s.steps[j].Compensate()
            }
            return err
        }
    }
    return nil
}
```

## 性能优化

### 1. 查询优化
```sql
-- 使用覆盖索引
SELECT order_id, status, created_at 
FROM orders 
WHERE user_id = ? AND symbol = ? 
ORDER BY created_at DESC 
LIMIT 20;

-- 避免SELECT *
SELECT id, order_id, amount, price, status 
FROM orders 
WHERE user_id = ?;

-- 使用LIMIT限制结果集
SELECT * FROM trades 
WHERE symbol = ? 
ORDER BY trade_time DESC 
LIMIT 100;
```

### 2. 连接池优化
```go
// 数据库连接池配置
db.SetMaxOpenConns(100)        // 最大连接数
db.SetMaxIdleConns(20)         // 最大空闲连接数
db.SetConnMaxLifetime(time.Hour) // 连接最大生存时间
```

### 3. 读写分离
```go
type DatabaseManager struct {
    master *sql.DB
    slaves []*sql.DB
}

func (dm *DatabaseManager) GetReadDB() *sql.DB {
    // 轮询选择从库
    return dm.slaves[rand.Intn(len(dm.slaves))]
}

func (dm *DatabaseManager) GetWriteDB() *sql.DB {
    return dm.master
}
```

## 数据备份策略

### 1. 备份方案
```bash
# 全量备份 (每日)
mysqldump --single-transaction --routines --triggers \
  --master-data=2 --all-databases > backup_full_$(date +%Y%m%d).sql

# 增量备份 (每小时)
mysqlbinlog --start-datetime="2023-12-01 10:00:00" \
  --stop-datetime="2023-12-01 11:00:00" \
  mysql-bin.000001 > backup_inc_$(date +%Y%m%d_%H).sql
```

### 2. 恢复方案
```bash
# 恢复全量备份
mysql < backup_full_20231201.sql

# 恢复增量备份
mysql < backup_inc_20231201_10.sql
mysql < backup_inc_20231201_11.sql
```

## 监控指标

### 1. 性能指标
- 查询响应时间
- 连接数使用率
- 慢查询统计
- 锁等待时间

### 2. 业务指标
- 订单创建速度
- 成交处理延迟
- 余额更新频率
- 数据一致性检查

这个数据库设计文档提供了完整的数据库架构和表结构设计，为系统的稳定运行和高性能提供了坚实的数据基础。
