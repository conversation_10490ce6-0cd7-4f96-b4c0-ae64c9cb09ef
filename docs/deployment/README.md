# 部署指南

## 部署架构

### 生产环境架构
```
                    ┌─────────────────┐
                    │   Load Balancer │
                    │    (Nginx)      │
                    └─────────┬───────┘
                              │
                    ┌─────────▼───────┐
                    │   API Gateway   │
                    └─────────┬───────┘
                              │
        ┌─────────────────────┼─────────────────────┐
        │                     │                     │
┌───────▼───────┐    ┌────────▼────────┐    ┌──────▼──────┐
│  Core Service │    │  Order Service  │    │Market Service│
│   (3 nodes)   │    │   (5 nodes)     │    │  (2 nodes)  │
└───────┬───────┘    └────────┬────────┘    └──────┬──────┘
        │                     │                     │
        └─────────────────────┼─────────────────────┘
                              │
    ┌─────────────────────────┼─────────────────────────┐
    │                         │                         │
┌───▼────┐  ┌────▼────┐  ┌───▼────┐  ┌────▼────┐  ┌───▼────┐
│ Limit  │  │ KLine   │  │Closing │  │  Push   │  │  Task  │
│Service │  │ Service │  │Service │  │ Service │  │Service │
└────────┘  └─────────┘  └────────┘  └─────────┘  └────────┘
```

### 基础设施
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   MySQL Cluster │    │  Redis Cluster  │    │ RabbitMQ Cluster│
│   (Master/Slave)│    │   (3 nodes)     │    │   (3 nodes)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Consul        │    │   Prometheus    │    │   ELK Stack     │
│   (3 nodes)     │    │   Monitoring    │    │   Logging       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 环境准备

### 系统要求
- **操作系统**: CentOS 7+, Ubuntu 18.04+
- **CPU**: 4核心以上
- **内存**: 8GB以上
- **磁盘**: 100GB以上 SSD
- **网络**: 千兆网卡

### 软件依赖
- Docker 20.10+
- Docker Compose 1.29+
- Kubernetes 1.20+ (可选)
- Nginx 1.18+

## Docker 部署

### 1. 准备 Docker 镜像

#### Dockerfile
```dockerfile
# 多阶段构建
FROM golang:1.18-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main pkg/core/main.go

# 运行阶段
FROM alpine:latest

RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/

COPY --from=builder /app/main .
COPY --from=builder /app/config ./config

EXPOSE 8080
CMD ["./main"]
```

#### 构建脚本 (deploy/_build.sh)
```bash
#!/bin/bash

set -e

# 配置
REGISTRY="your-registry.com"
PROJECT="spot-server"
VERSION=${1:-latest}

# 服务列表
SERVICES=("core" "order" "market" "limit" "kline" "closing" "planorder" "push" "task")

echo "Building images for version: $VERSION"

for service in "${SERVICES[@]}"; do
    echo "Building $service..."
    
    # 构建镜像
    docker build \
        --build-arg SERVICE=$service \
        -t $REGISTRY/$PROJECT/$service:$VERSION \
        -f deploy/Dockerfile.$service .
    
    # 推送镜像
    docker push $REGISTRY/$PROJECT/$service:$VERSION
    
    echo "$service build completed"
done

echo "All images built successfully!"
```

### 2. Docker Compose 部署

#### docker-compose.yml
```yaml
version: '3.8'

services:
  # 基础设施
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: spot_server
      MYSQL_USER: spot_user
      MYSQL_PASSWORD: spotpassword
    volumes:
      - mysql_data:/var/lib/mysql
      - ./deploy/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"
    networks:
      - spot-network

  redis:
    image: redis:6.2-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - spot-network

  rabbitmq:
    image: rabbitmq:3.9-management
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin123
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    networks:
      - spot-network

  # 应用服务
  core:
    image: ${REGISTRY}/spot-server/core:${VERSION}
    environment:
      - CONFIG_PATH=/app/config/config.yaml
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
    ports:
      - "8080:8080"
    depends_on:
      - mysql
      - redis
      - rabbitmq
    networks:
      - spot-network
    restart: unless-stopped

  order:
    image: ${REGISTRY}/spot-server/order:${VERSION}
    environment:
      - CONFIG_PATH=/app/config/config.yaml
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
    ports:
      - "8081:8081"
    depends_on:
      - mysql
      - redis
      - rabbitmq
    networks:
      - spot-network
    restart: unless-stopped

  market:
    image: ${REGISTRY}/spot-server/market:${VERSION}
    environment:
      - CONFIG_PATH=/app/config/config.yaml
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
    ports:
      - "8082:8082"
    depends_on:
      - redis
      - rabbitmq
    networks:
      - spot-network
    restart: unless-stopped

  # Nginx 负载均衡
  nginx:
    image: nginx:alpine
    volumes:
      - ./deploy/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./deploy/nginx/ssl:/etc/nginx/ssl
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - core
      - order
      - market
    networks:
      - spot-network
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
  rabbitmq_data:

networks:
  spot-network:
    driver: bridge
```

#### 部署脚本 (deploy/_deploy.sh)
```bash
#!/bin/bash

set -e

ENVIRONMENT=${1:-development}
VERSION=${2:-latest}

echo "Deploying to $ENVIRONMENT environment with version $VERSION"

# 设置环境变量
export REGISTRY="your-registry.com"
export VERSION=$VERSION

case $ENVIRONMENT in
    "development")
        echo "Deploying to development environment..."
        docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
        ;;
    "staging")
        echo "Deploying to staging environment..."
        docker-compose -f docker-compose.yml -f docker-compose.staging.yml up -d
        ;;
    "production")
        echo "Deploying to production environment..."
        docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
        ;;
    *)
        echo "Unknown environment: $ENVIRONMENT"
        exit 1
        ;;
esac

echo "Deployment completed!"

# 健康检查
echo "Performing health checks..."
sleep 30

services=("core:8080" "order:8081" "market:8082")
for service in "${services[@]}"; do
    name=${service%:*}
    port=${service#*:}
    
    if curl -f http://localhost:$port/health > /dev/null 2>&1; then
        echo "✓ $name service is healthy"
    else
        echo "✗ $name service is not responding"
    fi
done
```

## Kubernetes 部署

### 1. 命名空间
```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: spot-server
```

### 2. 配置管理
```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: spot-config
  namespace: spot-server
data:
  config.yaml: |
    server:
      port: 8080
      mode: production
    database:
      host: mysql-service
      port: 3306
      username: spot_user
      password: spotpassword
      database: spot_server
    redis:
      default:
        address: redis-service:6379
        password: ""
        db_nums: [0, 1, 2]
    mq:
      url: amqp://admin:admin123@rabbitmq-service:5672/
```

### 3. 数据库部署
```yaml
# mysql-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mysql
  namespace: spot-server
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mysql
  template:
    metadata:
      labels:
        app: mysql
    spec:
      containers:
      - name: mysql
        image: mysql:8.0
        env:
        - name: MYSQL_ROOT_PASSWORD
          value: "rootpassword"
        - name: MYSQL_DATABASE
          value: "spot_server"
        - name: MYSQL_USER
          value: "spot_user"
        - name: MYSQL_PASSWORD
          value: "spotpassword"
        ports:
        - containerPort: 3306
        volumeMounts:
        - name: mysql-storage
          mountPath: /var/lib/mysql
      volumes:
      - name: mysql-storage
        persistentVolumeClaim:
          claimName: mysql-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: mysql-service
  namespace: spot-server
spec:
  selector:
    app: mysql
  ports:
  - port: 3306
    targetPort: 3306
```

### 4. 应用部署
```yaml
# core-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: core-service
  namespace: spot-server
spec:
  replicas: 3
  selector:
    matchLabels:
      app: core-service
  template:
    metadata:
      labels:
        app: core-service
    spec:
      containers:
      - name: core
        image: your-registry.com/spot-server/core:latest
        ports:
        - containerPort: 8080
        env:
        - name: CONFIG_PATH
          value: "/app/config/config.yaml"
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      volumes:
      - name: config-volume
        configMap:
          name: spot-config

---
apiVersion: v1
kind: Service
metadata:
  name: core-service
  namespace: spot-server
spec:
  selector:
    app: core-service
  ports:
  - port: 80
    targetPort: 8080
  type: ClusterIP
```

### 5. Ingress 配置
```yaml
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: spot-ingress
  namespace: spot-server
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - api.yourspot.com
    secretName: spot-tls
  rules:
  - host: api.yourspot.com
    http:
      paths:
      - path: /api/v1/core
        pathType: Prefix
        backend:
          service:
            name: core-service
            port:
              number: 80
      - path: /api/v1/order
        pathType: Prefix
        backend:
          service:
            name: order-service
            port:
              number: 80
```

## 监控和日志

### 1. Prometheus 监控
```yaml
# prometheus-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
    scrape_configs:
    - job_name: 'spot-services'
      static_configs:
      - targets: ['core-service:8080', 'order-service:8081']
      metrics_path: /metrics
```

### 2. 日志收集 (Fluentd)
```yaml
# fluentd-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluentd-config
data:
  fluent.conf: |
    <source>
      @type tail
      path /var/log/containers/*spot-server*.log
      pos_file /var/log/fluentd-containers.log.pos
      tag kubernetes.*
      format json
    </source>
    
    <match kubernetes.**>
      @type elasticsearch
      host elasticsearch-service
      port 9200
      index_name spot-logs
    </match>
```

## 安全配置

### 1. 网络安全
```yaml
# network-policy.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: spot-network-policy
  namespace: spot-server
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: kube-system
```

### 2. RBAC 配置
```yaml
# rbac.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: spot-service-account
  namespace: spot-server

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: spot-role
  namespace: spot-server
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: spot-role-binding
  namespace: spot-server
subjects:
- kind: ServiceAccount
  name: spot-service-account
  namespace: spot-server
roleRef:
  kind: Role
  name: spot-role
  apiGroup: rbac.authorization.k8s.io
```

## 备份和恢复

### 1. 数据库备份
```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/mysql"
DB_NAME="spot_server"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -h mysql-service -u root -p$MYSQL_ROOT_PASSWORD $DB_NAME > $BACKUP_DIR/spot_server_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/spot_server_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

echo "Database backup completed: spot_server_$DATE.sql.gz"
```

### 2. Redis 备份
```bash
#!/bin/bash
# redis-backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/redis"

mkdir -p $BACKUP_DIR

# 备份Redis数据
redis-cli -h redis-service BGSAVE
sleep 10
kubectl cp redis-pod:/data/dump.rdb $BACKUP_DIR/redis_$DATE.rdb

echo "Redis backup completed: redis_$DATE.rdb"
```

## 故障排查

### 常见问题

1. **服务启动失败**
```bash
# 查看Pod状态
kubectl get pods -n spot-server

# 查看Pod日志
kubectl logs -f pod-name -n spot-server

# 查看Pod详细信息
kubectl describe pod pod-name -n spot-server
```

2. **数据库连接失败**
```bash
# 测试数据库连接
kubectl exec -it mysql-pod -n spot-server -- mysql -u root -p

# 检查网络连通性
kubectl exec -it core-pod -n spot-server -- ping mysql-service
```

3. **性能问题**
```bash
# 查看资源使用情况
kubectl top pods -n spot-server

# 查看节点资源
kubectl top nodes
```

## 升级策略

### 滚动更新
```bash
# 更新镜像
kubectl set image deployment/core-service core=your-registry.com/spot-server/core:v2.0.0 -n spot-server

# 查看更新状态
kubectl rollout status deployment/core-service -n spot-server

# 回滚到上一版本
kubectl rollout undo deployment/core-service -n spot-server
```

### 蓝绿部署
```bash
# 部署新版本到绿色环境
kubectl apply -f green-deployment.yaml

# 切换流量
kubectl patch service core-service -p '{"spec":{"selector":{"version":"green"}}}'

# 清理蓝色环境
kubectl delete -f blue-deployment.yaml
```
