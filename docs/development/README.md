# 开发指南

## 开发环境搭建

### 系统要求
- Go 1.18 或更高版本
- MySQL 5.7 或更高版本
- Redis 6.0 或更高版本
- RabbitMQ 3.8 或更高版本
- Git

### 安装 Go 环境
```bash
# 下载并安装 Go
wget https://golang.org/dl/go1.18.linux-amd64.tar.gz
sudo tar -C /usr/local -xzf go1.18.linux-amd64.tar.gz

# 设置环境变量
echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
echo 'export GOPATH=$HOME/go' >> ~/.bashrc
echo 'export GO111MODULE=on' >> ~/.bashrc
source ~/.bashrc
```

### 安装数据库

#### MySQL
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server

# CentOS/RHEL
sudo yum install mysql-server

# 启动服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 创建数据库
mysql -u root -p
CREATE DATABASE spot_server;
CREATE USER 'spot_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON spot_server.* TO 'spot_user'@'localhost';
FLUSH PRIVILEGES;
```

#### Redis
```bash
# Ubuntu/Debian
sudo apt install redis-server

# CentOS/RHEL
sudo yum install redis

# 启动服务
sudo systemctl start redis
sudo systemctl enable redis
```

#### RabbitMQ
```bash
# Ubuntu/Debian
sudo apt install rabbitmq-server

# CentOS/RHEL
sudo yum install rabbitmq-server

# 启动服务
sudo systemctl start rabbitmq-server
sudo systemctl enable rabbitmq-server

# 启用管理插件
sudo rabbitmq-plugins enable rabbitmq_management
```

### 克隆项目
```bash
git clone <repository-url>
cd spot-server
```

### 安装依赖
```bash
go mod download
```

### 配置文件设置
```bash
# 复制配置文件模板
cp config/config.example.yaml config/config.yaml

# 编辑配置文件
vim config/config.yaml
```

配置文件示例：
```yaml
# 服务配置
server:
  port: 8080
  mode: debug

# 数据库配置
database:
  host: localhost
  port: 3306
  username: spot_user
  password: your_password
  database: spot_server
  charset: utf8mb4
  max_idle_conns: 10
  max_open_conns: 100

# Redis配置
redis:
  default:
    address: localhost:6379
    password: ""
    db_nums: [0, 1, 2]
    pool_size: 20
    use_tls: false

# 消息队列配置
mq:
  url: amqp://guest:guest@localhost:5672/
  exchange: spot_exchange
  queue_prefix: spot_

# 日志配置
log:
  level: debug
  file: logs/app.log
  only_file: false

# RPC配置
rpc:
  discovery: consul://localhost:8500
  local_name: spot-server
```

## 项目结构

```
spot-server/
├── cmd/                    # 应用程序入口
├── pkg/                    # 核心业务包
│   ├── core/              # 核心服务
│   ├── order/             # 订单服务
│   ├── market/            # 行情服务
│   ├── limit/             # 限价服务
│   ├── kline/             # K线服务
│   ├── closing/           # 清算服务
│   ├── planorder/         # 计划订单服务
│   ├── push/              # 推送服务
│   └── task/              # 任务服务
├── libs/                  # 公共库
│   ├── cache/             # 缓存操作
│   ├── database/          # 数据库操作
│   ├── conf/              # 配置管理
│   ├── log/               # 日志系统
│   ├── messagequeue/      # 消息队列
│   ├── proto/             # 协议定义
│   └── utils/             # 工具函数
├── config/                # 配置文件
├── docs/                  # 文档
├── deploy/                # 部署脚本
├── test/                  # 测试文件
├── go.mod                 # Go模块文件
├── go.sum                 # Go依赖校验文件
└── README.md              # 项目说明
```

## 代码规范

### 命名规范
- **包名**: 使用小写字母，简短且有意义
- **文件名**: 使用小写字母和下划线
- **变量名**: 使用驼峰命名法
- **常量名**: 使用大写字母和下划线
- **函数名**: 使用驼峰命名法，公开函数首字母大写

```go
// 好的示例
package order

const (
    ORDER_STATUS_NEW = "NEW"
    ORDER_STATUS_FILLED = "FILLED"
)

type OrderService struct {
    orderRepo OrderRepository
}

func (s *OrderService) PlaceOrder(req *PlaceOrderRequest) (*Order, error) {
    // 实现逻辑
}

// 私有函数
func (s *OrderService) validateOrder(order *Order) error {
    // 验证逻辑
}
```

### 错误处理
```go
// 使用自定义错误类型
type OrderError struct {
    Code    int
    Message string
    Err     error
}

func (e *OrderError) Error() string {
    return fmt.Sprintf("order error [%d]: %s", e.Code, e.Message)
}

// 错误处理示例
func (s *OrderService) PlaceOrder(req *PlaceOrderRequest) (*Order, error) {
    if err := s.validateOrder(req); err != nil {
        return nil, &OrderError{
            Code:    1001,
            Message: "invalid order parameters",
            Err:     err,
        }
    }
    
    order, err := s.orderRepo.Create(req)
    if err != nil {
        log.Error("failed to create order", zap.Error(err))
        return nil, err
    }
    
    return order, nil
}
```

### 日志规范
```go
import (
    "go.uber.org/zap"
    "spot/libs/log"
)

// 使用结构化日志
log.Info("order placed successfully",
    zap.String("order_id", order.ID),
    zap.String("symbol", order.Symbol),
    zap.String("side", order.Side),
    zap.String("user_id", order.UserID),
)

log.Error("failed to process order",
    zap.String("order_id", order.ID),
    zap.Error(err),
)
```

### 配置管理
```go
// 使用配置结构体
type Config struct {
    Server   ServerConfig   `yaml:"server"`
    Database DatabaseConfig `yaml:"database"`
    Redis    RedisConfig    `yaml:"redis"`
}

type ServerConfig struct {
    Port int    `yaml:"port"`
    Mode string `yaml:"mode"`
}

// 配置初始化
func InitConfig() *Config {
    var config Config
    if err := conf.LoadConfig("config/config.yaml", &config); err != nil {
        log.Fatal("failed to load config", zap.Error(err))
    }
    return &config
}
```

## 测试指南

### 单元测试
```go
// order_service_test.go
package order

import (
    "testing"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
)

type MockOrderRepository struct {
    mock.Mock
}

func (m *MockOrderRepository) Create(req *PlaceOrderRequest) (*Order, error) {
    args := m.Called(req)
    return args.Get(0).(*Order), args.Error(1)
}

func TestOrderService_PlaceOrder(t *testing.T) {
    // 准备测试数据
    mockRepo := new(MockOrderRepository)
    service := NewOrderService(mockRepo)
    
    req := &PlaceOrderRequest{
        Symbol: "BTCUSDT",
        Side:   "buy",
        Amount: "0.001",
        Price:  "50000",
    }
    
    expectedOrder := &Order{
        ID:     "123456",
        Symbol: "BTCUSDT",
        Side:   "buy",
        Amount: "0.001",
        Price:  "50000",
        Status: "NEW",
    }
    
    // 设置mock期望
    mockRepo.On("Create", req).Return(expectedOrder, nil)
    
    // 执行测试
    order, err := service.PlaceOrder(req)
    
    // 验证结果
    assert.NoError(t, err)
    assert.Equal(t, expectedOrder.ID, order.ID)
    assert.Equal(t, expectedOrder.Symbol, order.Symbol)
    
    // 验证mock调用
    mockRepo.AssertExpectations(t)
}
```

### 集成测试
```go
// integration_test.go
package integration

import (
    "testing"
    "spot/libs/database"
    "spot/libs/cache"
    "spot/pkg/order"
)

func TestOrderIntegration(t *testing.T) {
    // 初始化测试数据库
    database.InitTestDB()
    cache.InitTestRedis()
    
    // 创建服务实例
    orderService := order.NewOrderService(
        order.NewOrderRepository(database.TestDB()),
    )
    
    // 执行集成测试
    req := &order.PlaceOrderRequest{
        UserID: "user123",
        Symbol: "BTCUSDT",
        Side:   "buy",
        Amount: "0.001",
        Price:  "50000",
    }
    
    order, err := orderService.PlaceOrder(req)
    assert.NoError(t, err)
    assert.NotEmpty(t, order.ID)
    
    // 清理测试数据
    defer database.CleanTestDB()
}
```

### 运行测试
```bash
# 运行所有测试
go test ./...

# 运行特定包的测试
go test ./pkg/order

# 运行测试并显示覆盖率
go test -cover ./...

# 生成覆盖率报告
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out -o coverage.html
```

## 性能优化

### 数据库优化
```go
// 使用连接池
func InitDB() *sql.DB {
    db, err := sql.Open("mysql", dsn)
    if err != nil {
        log.Fatal("failed to connect database", zap.Error(err))
    }
    
    // 设置连接池参数
    db.SetMaxIdleConns(10)
    db.SetMaxOpenConns(100)
    db.SetConnMaxLifetime(time.Hour)
    
    return db
}

// 使用预编译语句
func (r *OrderRepository) GetOrderByID(id string) (*Order, error) {
    stmt, err := r.db.Prepare("SELECT * FROM orders WHERE id = ?")
    if err != nil {
        return nil, err
    }
    defer stmt.Close()
    
    var order Order
    err = stmt.QueryRow(id).Scan(&order.ID, &order.Symbol, &order.Side)
    if err != nil {
        return nil, err
    }
    
    return &order, nil
}
```

### 缓存优化
```go
// 使用Redis缓存
func (s *OrderService) GetOrder(id string) (*Order, error) {
    // 先从缓存获取
    cacheKey := fmt.Sprintf("order:%s", id)
    if cached, err := cache.Get(cacheKey); err == nil {
        var order Order
        if err := json.Unmarshal([]byte(cached), &order); err == nil {
            return &order, nil
        }
    }
    
    // 缓存未命中，从数据库获取
    order, err := s.orderRepo.GetByID(id)
    if err != nil {
        return nil, err
    }
    
    // 写入缓存
    if data, err := json.Marshal(order); err == nil {
        cache.Set(cacheKey, string(data), time.Hour)
    }
    
    return order, nil
}
```

### 并发优化
```go
// 使用goroutine池
type WorkerPool struct {
    workers    int
    jobQueue   chan Job
    workerPool chan chan Job
    quit       chan bool
}

func NewWorkerPool(workers int) *WorkerPool {
    return &WorkerPool{
        workers:    workers,
        jobQueue:   make(chan Job, 1000),
        workerPool: make(chan chan Job, workers),
        quit:       make(chan bool),
    }
}

func (p *WorkerPool) Start() {
    for i := 0; i < p.workers; i++ {
        worker := NewWorker(p.workerPool)
        worker.Start()
    }
    
    go p.dispatch()
}

func (p *WorkerPool) Submit(job Job) {
    p.jobQueue <- job
}
```

## 调试技巧

### 使用pprof进行性能分析
```go
import _ "net/http/pprof"

func main() {
    // 启动pprof服务
    go func() {
        log.Println(http.ListenAndServe("localhost:6060", nil))
    }()
    
    // 应用程序逻辑
}
```

访问性能分析：
```bash
# CPU分析
go tool pprof http://localhost:6060/debug/pprof/profile

# 内存分析
go tool pprof http://localhost:6060/debug/pprof/heap

# goroutine分析
go tool pprof http://localhost:6060/debug/pprof/goroutine
```

### 日志调试
```go
// 使用不同日志级别
log.Debug("debug information", zap.Any("data", data))
log.Info("operation completed", zap.String("operation", "place_order"))
log.Warn("potential issue detected", zap.String("issue", "high_latency"))
log.Error("operation failed", zap.Error(err))
```

## 部署指南

### 本地开发部署
```bash
# 启动依赖服务
docker-compose up -d mysql redis rabbitmq

# 启动应用服务
go run pkg/core/main.go &
go run pkg/order/main.go &
go run pkg/market/main.go &
```

### 生产环境部署
```bash
# 编译
make build

# 部署
./deploy/_deploy.sh production
```

## 常见问题

### Q: 如何添加新的交易所支持？
A: 在 `libs/` 目录下创建新的交易所包，实现标准接口，然后在 `pkg/market/` 中注册。

### Q: 如何扩展新的订单类型？
A: 在 `libs/define/` 中定义新的订单类型常量，在 `pkg/order/` 中实现相应的处理逻辑。

### Q: 如何优化系统性能？
A: 使用缓存、数据库连接池、goroutine池等技术，具体参考性能优化章节。

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 更多资源

- [Go 官方文档](https://golang.org/doc/)
- [Gin 框架文档](https://gin-gonic.com/docs/)
- [GORM 文档](https://gorm.io/docs/)
- [Redis Go 客户端](https://github.com/go-redis/redis)
