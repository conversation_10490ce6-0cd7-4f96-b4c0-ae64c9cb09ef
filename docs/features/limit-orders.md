# 限价订单功能

## 功能概述

限价订单功能是Spot-Server交易系统的核心组件之一，提供智能化的条件订单执行服务。支持多种触发条件、灵活的执行策略和完善的风险控制机制。

## 核心功能

### 1. 订单类型

#### 止损订单 (Stop Loss)
当市场价格达到或超过止损价格时，自动触发市价卖出订单。

```go
type StopLossOrder struct {
    BaseOrder
    StopPrice    decimal.Decimal `json:"stop_price"`    // 止损价格
    TriggerType  string          `json:"trigger_type"`  // 触发类型: "last_price", "mark_price"
    WorkingType  string          `json:"working_type"`  // 工作类型: "mark_price", "contract_price"
    TimeInForce  string          `json:"time_in_force"` // 有效期: "GTC", "IOC", "FOK"
}

func (slo *StopLossOrder) ShouldTrigger(currentPrice decimal.Decimal) bool {
    if slo.Side == "sell" {
        // 卖出止损：当前价格 <= 止损价格时触发
        return currentPrice.LessThanOrEqual(slo.StopPrice)
    } else {
        // 买入止损：当前价格 >= 止损价格时触发
        return currentPrice.GreaterThanOrEqual(slo.StopPrice)
    }
}
```

#### 止盈订单 (Take Profit)
当市场价格达到或超过止盈价格时，自动触发市价卖出订单。

```go
type TakeProfitOrder struct {
    BaseOrder
    TakeProfitPrice decimal.Decimal `json:"take_profit_price"` // 止盈价格
    TriggerType     string          `json:"trigger_type"`      // 触发类型
    WorkingType     string          `json:"working_type"`      // 工作类型
}

func (tpo *TakeProfitOrder) ShouldTrigger(currentPrice decimal.Decimal) bool {
    if tpo.Side == "sell" {
        // 卖出止盈：当前价格 >= 止盈价格时触发
        return currentPrice.GreaterThanOrEqual(tpo.TakeProfitPrice)
    } else {
        // 买入止盈：当前价格 <= 止盈价格时触发
        return currentPrice.LessThanOrEqual(tpo.TakeProfitPrice)
    }
}
```

#### 止损限价订单 (Stop Loss Limit)
当价格触发止损条件时，提交限价订单而非市价订单。

```go
type StopLossLimitOrder struct {
    BaseOrder
    StopPrice   decimal.Decimal `json:"stop_price"`   // 止损触发价格
    LimitPrice  decimal.Decimal `json:"limit_price"`  // 限价订单价格
    TriggerType string          `json:"trigger_type"` // 触发类型
}

func (sllo *StopLossLimitOrder) CreateLimitOrder() *LimitOrder {
    return &LimitOrder{
        BaseOrder: BaseOrder{
            Symbol:    sllo.Symbol,
            Side:      sllo.Side,
            Amount:    sllo.Amount,
            UserID:    sllo.UserID,
            Type:      "limit",
        },
        Price: sllo.LimitPrice,
    }
}
```

#### 追踪止损订单 (Trailing Stop)
动态调整止损价格，跟随有利价格变动。

```go
type TrailingStopOrder struct {
    BaseOrder
    CallbackRate    decimal.Decimal `json:"callback_rate"`    // 回调比例 (0.01 = 1%)
    ActivationPrice decimal.Decimal `json:"activation_price"` // 激活价格
    CurrentStopPrice decimal.Decimal `json:"current_stop_price"` // 当前止损价格
    HighestPrice    decimal.Decimal `json:"highest_price"`    // 最高价格记录
    LowestPrice     decimal.Decimal `json:"lowest_price"`     // 最低价格记录
    IsActivated     bool            `json:"is_activated"`     // 是否已激活
}

func (tso *TrailingStopOrder) UpdateStopPrice(currentPrice decimal.Decimal) {
    // 检查是否达到激活条件
    if !tso.IsActivated {
        if tso.Side == "sell" && currentPrice.GreaterThanOrEqual(tso.ActivationPrice) {
            tso.IsActivated = true
            tso.HighestPrice = currentPrice
            tso.CurrentStopPrice = currentPrice.Mul(decimal.NewFromFloat(1).Sub(tso.CallbackRate))
        } else if tso.Side == "buy" && currentPrice.LessThanOrEqual(tso.ActivationPrice) {
            tso.IsActivated = true
            tso.LowestPrice = currentPrice
            tso.CurrentStopPrice = currentPrice.Mul(decimal.NewFromFloat(1).Add(tso.CallbackRate))
        }
        return
    }
    
    // 更新追踪止损价格
    if tso.Side == "sell" {
        if currentPrice.GreaterThan(tso.HighestPrice) {
            tso.HighestPrice = currentPrice
            newStopPrice := currentPrice.Mul(decimal.NewFromFloat(1).Sub(tso.CallbackRate))
            if newStopPrice.GreaterThan(tso.CurrentStopPrice) {
                tso.CurrentStopPrice = newStopPrice
            }
        }
    } else {
        if currentPrice.LessThan(tso.LowestPrice) {
            tso.LowestPrice = currentPrice
            newStopPrice := currentPrice.Mul(decimal.NewFromFloat(1).Add(tso.CallbackRate))
            if newStopPrice.LessThan(tso.CurrentStopPrice) {
                tso.CurrentStopPrice = newStopPrice
            }
        }
    }
}

func (tso *TrailingStopOrder) ShouldTrigger(currentPrice decimal.Decimal) bool {
    if !tso.IsActivated {
        return false
    }
    
    if tso.Side == "sell" {
        return currentPrice.LessThanOrEqual(tso.CurrentStopPrice)
    } else {
        return currentPrice.GreaterThanOrEqual(tso.CurrentStopPrice)
    }
}
```

### 2. 触发机制

#### 价格监控服务
```go
type PriceMonitor struct {
    limitOrderManager *LimitOrderManager
    priceFeeds        map[string]chan decimal.Decimal
    subscribers       map[string][]chan PriceUpdate
    mutex             sync.RWMutex
}

type PriceUpdate struct {
    Symbol    string          `json:"symbol"`
    Price     decimal.Decimal `json:"price"`
    PriceType string          `json:"price_type"` // "last", "mark", "index"
    Timestamp time.Time       `json:"timestamp"`
}

func (pm *PriceMonitor) Start() {
    for symbol := range pm.priceFeeds {
        go pm.monitorSymbol(symbol)
    }
}

func (pm *PriceMonitor) monitorSymbol(symbol string) {
    priceFeed := pm.priceFeeds[symbol]
    
    for price := range priceFeed {
        update := PriceUpdate{
            Symbol:    symbol,
            Price:     price,
            PriceType: "last",
            Timestamp: time.Now(),
        }
        
        // 通知限价订单管理器
        pm.limitOrderManager.OnPriceUpdate(update)
        
        // 通知其他订阅者
        pm.notifySubscribers(symbol, update)
    }
}

func (pm *PriceMonitor) notifySubscribers(symbol string, update PriceUpdate) {
    pm.mutex.RLock()
    subscribers := pm.subscribers[symbol]
    pm.mutex.RUnlock()
    
    for _, subscriber := range subscribers {
        select {
        case subscriber <- update:
        default:
            // 非阻塞发送，避免慢消费者影响系统
        }
    }
}
```

#### 限价订单管理器
```go
type LimitOrderManager struct {
    activeOrders    map[string][]LimitOrder // symbol -> orders
    orderIndex      map[string]LimitOrder   // orderID -> order
    triggerEngine   *TriggerEngine
    orderProcessor  *OrderProcessor
    cache          *redis.Client
    db             *gorm.DB
    mutex          sync.RWMutex
}

func (lom *LimitOrderManager) AddLimitOrder(order LimitOrder) error {
    // 1. 验证订单
    if err := lom.validateLimitOrder(order); err != nil {
        return err
    }
    
    // 2. 保存到数据库
    if err := lom.saveLimitOrder(order); err != nil {
        return err
    }
    
    // 3. 添加到内存索引
    lom.mutex.Lock()
    lom.activeOrders[order.Symbol] = append(lom.activeOrders[order.Symbol], order)
    lom.orderIndex[order.OrderID] = order
    lom.mutex.Unlock()
    
    // 4. 缓存订单
    lom.cacheLimitOrder(order)
    
    log.Info("限价订单已添加", 
        zap.String("order_id", order.OrderID),
        zap.String("symbol", order.Symbol),
        zap.String("type", order.Type))
    
    return nil
}

func (lom *LimitOrderManager) OnPriceUpdate(update PriceUpdate) {
    lom.mutex.RLock()
    orders := lom.activeOrders[update.Symbol]
    lom.mutex.RUnlock()
    
    if len(orders) == 0 {
        return
    }
    
    // 检查每个订单是否应该触发
    var triggeredOrders []LimitOrder
    
    for _, order := range orders {
        if lom.shouldTriggerOrder(order, update) {
            triggeredOrders = append(triggeredOrders, order)
        }
    }
    
    // 处理触发的订单
    for _, order := range triggeredOrders {
        go lom.triggerOrder(order, update)
    }
}

func (lom *LimitOrderManager) shouldTriggerOrder(order LimitOrder, update PriceUpdate) bool {
    switch order.Type {
    case "stop_loss":
        return order.(*StopLossOrder).ShouldTrigger(update.Price)
    case "take_profit":
        return order.(*TakeProfitOrder).ShouldTrigger(update.Price)
    case "stop_loss_limit":
        return order.(*StopLossLimitOrder).ShouldTrigger(update.Price)
    case "trailing_stop":
        trailingOrder := order.(*TrailingStopOrder)
        trailingOrder.UpdateStopPrice(update.Price)
        return trailingOrder.ShouldTrigger(update.Price)
    default:
        return false
    }
}

func (lom *LimitOrderManager) triggerOrder(order LimitOrder, update PriceUpdate) {
    // 1. 移除活跃订单
    lom.removeLimitOrder(order.OrderID)
    
    // 2. 创建市价或限价订单
    marketOrder, err := lom.createTriggeredOrder(order, update)
    if err != nil {
        log.Error("创建触发订单失败", 
            zap.String("order_id", order.OrderID),
            zap.Error(err))
        return
    }
    
    // 3. 提交订单到交易引擎
    if err := lom.orderProcessor.ProcessOrder(marketOrder); err != nil {
        log.Error("处理触发订单失败", 
            zap.String("order_id", order.OrderID),
            zap.String("triggered_order_id", marketOrder.OrderID),
            zap.Error(err))
        return
    }
    
    // 4. 更新限价订单状态
    lom.updateLimitOrderStatus(order.OrderID, "triggered", marketOrder.OrderID)
    
    log.Info("限价订单已触发", 
        zap.String("limit_order_id", order.OrderID),
        zap.String("triggered_order_id", marketOrder.OrderID),
        zap.String("trigger_price", update.Price.String()))
}
```

### 3. 风险控制

#### 价格偏离检查
```go
type RiskController struct {
    config *RiskConfig
    priceService *PriceService
}

type RiskConfig struct {
    MaxPriceDeviation   decimal.Decimal `json:"max_price_deviation"`   // 最大价格偏离 (5%)
    MaxOrderValue       decimal.Decimal `json:"max_order_value"`       // 最大订单价值
    MaxDailyTriggers    int             `json:"max_daily_triggers"`    // 每日最大触发次数
    MinTriggerInterval  time.Duration   `json:"min_trigger_interval"`  // 最小触发间隔
}

func (rc *RiskController) ValidateLimitOrder(order LimitOrder) error {
    // 1. 检查价格偏离
    if err := rc.checkPriceDeviation(order); err != nil {
        return err
    }
    
    // 2. 检查订单价值
    if err := rc.checkOrderValue(order); err != nil {
        return err
    }
    
    // 3. 检查触发频率
    if err := rc.checkTriggerFrequency(order); err != nil {
        return err
    }
    
    return nil
}

func (rc *RiskController) checkPriceDeviation(order LimitOrder) error {
    currentPrice, err := rc.priceService.GetCurrentPrice(order.Symbol)
    if err != nil {
        return err
    }
    
    var triggerPrice decimal.Decimal
    switch order.Type {
    case "stop_loss":
        triggerPrice = order.(*StopLossOrder).StopPrice
    case "take_profit":
        triggerPrice = order.(*TakeProfitOrder).TakeProfitPrice
    case "stop_loss_limit":
        triggerPrice = order.(*StopLossLimitOrder).StopPrice
    default:
        return nil
    }
    
    deviation := triggerPrice.Sub(currentPrice).Div(currentPrice).Abs()
    if deviation.GreaterThan(rc.config.MaxPriceDeviation) {
        return fmt.Errorf("触发价格偏离当前价格过大: %.2f%%", 
            deviation.Mul(decimal.NewFromInt(100)).InexactFloat64())
    }
    
    return nil
}

func (rc *RiskController) checkOrderValue(order LimitOrder) error {
    orderValue := order.Amount.Mul(order.Price)
    if orderValue.GreaterThan(rc.config.MaxOrderValue) {
        return errors.New("订单价值超过限制")
    }
    
    return nil
}

func (rc *RiskController) checkTriggerFrequency(order LimitOrder) error {
    // 检查用户今日触发次数
    today := time.Now().Format("2006-01-02")
    key := fmt.Sprintf("limit_order_triggers:%s:%s", order.UserID, today)
    
    count, err := rc.cache.Get(key).Int()
    if err != nil && err != redis.Nil {
        return err
    }
    
    if count >= rc.config.MaxDailyTriggers {
        return errors.New("今日触发次数已达上限")
    }
    
    return nil
}
```

#### 滑点保护
```go
type SlippageProtection struct {
    maxSlippage decimal.Decimal
    priceService *PriceService
}

func (sp *SlippageProtection) CheckSlippage(order LimitOrder, triggerPrice decimal.Decimal) error {
    currentPrice, err := sp.priceService.GetCurrentPrice(order.Symbol)
    if err != nil {
        return err
    }
    
    // 计算滑点
    slippage := currentPrice.Sub(triggerPrice).Div(triggerPrice).Abs()
    
    if slippage.GreaterThan(sp.maxSlippage) {
        return fmt.Errorf("滑点过大: %.2f%%, 最大允许: %.2f%%",
            slippage.Mul(decimal.NewFromInt(100)).InexactFloat64(),
            sp.maxSlippage.Mul(decimal.NewFromInt(100)).InexactFloat64())
    }
    
    return nil
}
```

### 4. 订单执行策略

#### 智能执行算法
```go
type ExecutionStrategy interface {
    Execute(order LimitOrder, triggerPrice decimal.Decimal) (*Order, error)
}

// TWAP执行策略 (时间加权平均价格)
type TWAPStrategy struct {
    timeWindow   time.Duration
    sliceCount   int
    orderProcessor *OrderProcessor
}

func (ts *TWAPStrategy) Execute(order LimitOrder, triggerPrice decimal.Decimal) (*Order, error) {
    sliceAmount := order.Amount.Div(decimal.NewFromInt(int64(ts.sliceCount)))
    sliceInterval := ts.timeWindow / time.Duration(ts.sliceCount)
    
    var executedOrders []*Order
    
    for i := 0; i < ts.sliceCount; i++ {
        sliceOrder := &Order{
            UserID:  order.UserID,
            Symbol:  order.Symbol,
            Side:    order.Side,
            Type:    "market",
            Amount:  sliceAmount,
        }
        
        if err := ts.orderProcessor.ProcessOrder(sliceOrder); err != nil {
            log.Error("TWAP切片执行失败", zap.Error(err))
            continue
        }
        
        executedOrders = append(executedOrders, sliceOrder)
        
        if i < ts.sliceCount-1 {
            time.Sleep(sliceInterval)
        }
    }
    
    // 返回聚合订单信息
    return ts.aggregateOrders(executedOrders), nil
}

// VWAP执行策略 (成交量加权平均价格)
type VWAPStrategy struct {
    volumeProfile map[time.Duration]decimal.Decimal
    orderProcessor *OrderProcessor
}

func (vs *VWAPStrategy) Execute(order LimitOrder, triggerPrice decimal.Decimal) (*Order, error) {
    totalVolume := decimal.Zero
    for _, volume := range vs.volumeProfile {
        totalVolume = totalVolume.Add(volume)
    }
    
    var executedOrders []*Order
    
    for timeSlot, expectedVolume := range vs.volumeProfile {
        ratio := expectedVolume.Div(totalVolume)
        sliceAmount := order.Amount.Mul(ratio)
        
        sliceOrder := &Order{
            UserID:  order.UserID,
            Symbol:  order.Symbol,
            Side:    order.Side,
            Type:    "market",
            Amount:  sliceAmount,
        }
        
        if err := vs.orderProcessor.ProcessOrder(sliceOrder); err != nil {
            log.Error("VWAP切片执行失败", zap.Error(err))
            continue
        }
        
        executedOrders = append(executedOrders, sliceOrder)
        time.Sleep(timeSlot)
    }
    
    return vs.aggregateOrders(executedOrders), nil
}
```

### 5. 性能优化

#### 内存索引优化
```go
type OptimizedLimitOrderManager struct {
    // 按价格区间索引订单
    priceRangeIndex map[string]*IntervalTree // symbol -> price intervals
    
    // 按触发时间索引订单
    timeIndex *TimeBasedIndex
    
    // 用户订单索引
    userOrderIndex map[string][]string // userID -> orderIDs
    
    // 订单类型索引
    typeIndex map[string][]string // orderType -> orderIDs
}

type IntervalTree struct {
    root *IntervalNode
}

type IntervalNode struct {
    low, high decimal.Decimal
    orders    []string // orderIDs
    left, right *IntervalNode
}

func (it *IntervalTree) Query(price decimal.Decimal) []string {
    return it.queryNode(it.root, price)
}

func (it *IntervalTree) queryNode(node *IntervalNode, price decimal.Decimal) []string {
    if node == nil {
        return nil
    }
    
    var result []string
    
    // 检查当前节点
    if price.GreaterThanOrEqual(node.low) && price.LessThanOrEqual(node.high) {
        result = append(result, node.orders...)
    }
    
    // 递归查询子节点
    if node.left != nil && price.LessThanOrEqual(node.left.high) {
        result = append(result, it.queryNode(node.left, price)...)
    }
    
    if node.right != nil && price.GreaterThanOrEqual(node.right.low) {
        result = append(result, it.queryNode(node.right, price)...)
    }
    
    return result
}
```

#### 批量处理优化
```go
type BatchProcessor struct {
    batchSize    int
    batchTimeout time.Duration
    orderQueue   chan LimitOrder
    processor    *LimitOrderManager
}

func (bp *BatchProcessor) Start() {
    go func() {
        batch := make([]LimitOrder, 0, bp.batchSize)
        timer := time.NewTimer(bp.batchTimeout)
        
        for {
            select {
            case order := <-bp.orderQueue:
                batch = append(batch, order)
                
                if len(batch) >= bp.batchSize {
                    bp.processBatch(batch)
                    batch = batch[:0]
                    timer.Reset(bp.batchTimeout)
                }
                
            case <-timer.C:
                if len(batch) > 0 {
                    bp.processBatch(batch)
                    batch = batch[:0]
                }
                timer.Reset(bp.batchTimeout)
            }
        }
    }()
}

func (bp *BatchProcessor) processBatch(orders []LimitOrder) {
    // 按交易对分组
    symbolGroups := make(map[string][]LimitOrder)
    for _, order := range orders {
        symbolGroups[order.Symbol] = append(symbolGroups[order.Symbol], order)
    }
    
    // 并行处理每个交易对的订单
    var wg sync.WaitGroup
    for symbol, symbolOrders := range symbolGroups {
        wg.Add(1)
        go func(s string, orders []LimitOrder) {
            defer wg.Done()
            bp.processSymbolOrders(s, orders)
        }(symbol, symbolOrders)
    }
    
    wg.Wait()
}
```

## API接口

### 限价订单管理接口
```
POST   /api/v1/limit-orders           # 创建限价订单
GET    /api/v1/limit-orders           # 获取限价订单列表
GET    /api/v1/limit-orders/:id       # 获取限价订单详情
PUT    /api/v1/limit-orders/:id       # 修改限价订单
DELETE /api/v1/limit-orders/:id       # 取消限价订单
```

### 订单历史接口
```
GET    /api/v1/limit-orders/history   # 获取历史记录
GET    /api/v1/limit-orders/triggers  # 获取触发记录
```

## 配置说明

### 限价订单配置
```yaml
limit_orders:
  enabled: true
  max_active_orders_per_user: 100
  max_active_orders_per_symbol: 1000
  
  risk_control:
    max_price_deviation: 0.05  # 5%
    max_order_value: 1000000
    max_daily_triggers: 50
    min_trigger_interval: 1s
  
  execution:
    default_strategy: "market"
    slippage_protection: true
    max_slippage: 0.01  # 1%
  
  performance:
    batch_size: 100
    batch_timeout: 10ms
    price_check_interval: 100ms
```

### 监控配置
```yaml
monitoring:
  metrics:
    - active_limit_orders
    - triggered_orders_per_minute
    - average_trigger_latency
    - price_deviation_alerts
  
  alerts:
    high_trigger_rate: 1000/min
    high_latency: 100ms
    price_deviation: 5%
```

这个限价订单功能文档提供了完整的条件订单系统实现方案，包括多种订单类型、智能触发机制、风险控制和性能优化等核心功能。
