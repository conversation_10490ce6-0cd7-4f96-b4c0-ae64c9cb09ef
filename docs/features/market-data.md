# 市场数据功能

## 功能概述

市场数据功能负责收集、处理和分发实时市场数据，包括价格行情、深度数据、K线数据、成交数据等。系统支持多数据源接入、实时数据处理和高频数据分发。

## 核心功能

### 1. 数据源管理

#### 外部数据源
```go
type DataSource interface {
    Connect() error
    Subscribe(symbols []string) error
    GetTicker(symbol string) (*Ticker, error)
    GetDepth(symbol string, limit int) (*Depth, error)
    GetTrades(symbol string, limit int) ([]*Trade, error)
    Close() error
}

// Binance数据源实现
type BinanceDataSource struct {
    apiKey    string
    secretKey string
    wsConn    *websocket.Conn
    restClient *http.Client
    symbols   map[string]bool
    callbacks map[string]func(interface{})
}

func (bds *BinanceDataSource) Connect() error {
    // 建立WebSocket连接
    conn, _, err := websocket.DefaultDialer.Dial("wss://stream.binance.com:9443/ws", nil)
    if err != nil {
        return err
    }
    
    bds.wsConn = conn
    
    // 启动消息处理协程
    go bds.handleMessages()
    
    return nil
}

func (bds *BinanceDataSource) Subscribe(symbols []string) error {
    streams := make([]string, 0, len(symbols)*3)
    
    for _, symbol := range symbols {
        symbol = strings.ToLower(symbol)
        streams = append(streams, 
            fmt.Sprintf("%s@ticker", symbol),
            fmt.Sprintf("%s@depth20", symbol),
            fmt.Sprintf("%s@trade", symbol),
        )
        bds.symbols[symbol] = true
    }
    
    subscribeMsg := map[string]interface{}{
        "method": "SUBSCRIBE",
        "params": streams,
        "id":     time.Now().Unix(),
    }
    
    return bds.wsConn.WriteJSON(subscribeMsg)
}

func (bds *BinanceDataSource) handleMessages() {
    for {
        var msg map[string]interface{}
        if err := bds.wsConn.ReadJSON(&msg); err != nil {
            log.Error("读取WebSocket消息失败", zap.Error(err))
            break
        }
        
        if stream, ok := msg["stream"].(string); ok {
            bds.processStreamData(stream, msg["data"])
        }
    }
}
```

#### 数据源聚合
```go
type DataAggregator struct {
    sources   map[string]DataSource
    weights   map[string]float64
    cache     *redis.Client
    publisher *EventPublisher
}

func (da *DataAggregator) AddDataSource(name string, source DataSource, weight float64) {
    da.sources[name] = source
    da.weights[name] = weight
}

func (da *DataAggregator) GetAggregatedTicker(symbol string) (*Ticker, error) {
    var tickers []*WeightedTicker
    
    // 从所有数据源获取ticker
    for name, source := range da.sources {
        ticker, err := source.GetTicker(symbol)
        if err != nil {
            log.Warn("获取ticker失败", zap.String("source", name), zap.Error(err))
            continue
        }
        
        tickers = append(tickers, &WeightedTicker{
            Ticker: ticker,
            Weight: da.weights[name],
        })
    }
    
    if len(tickers) == 0 {
        return nil, errors.New("无可用数据源")
    }
    
    // 加权平均计算
    return da.calculateWeightedAverage(tickers), nil
}

func (da *DataAggregator) calculateWeightedAverage(tickers []*WeightedTicker) *Ticker {
    var totalWeight float64
    var weightedPrice, weightedVolume decimal.Decimal
    
    for _, wt := range tickers {
        weight := decimal.NewFromFloat(wt.Weight)
        totalWeight += wt.Weight
        
        weightedPrice = weightedPrice.Add(wt.Ticker.LastPrice.Mul(weight))
        weightedVolume = weightedVolume.Add(wt.Ticker.Volume.Mul(weight))
    }
    
    totalWeightDecimal := decimal.NewFromFloat(totalWeight)
    
    return &Ticker{
        Symbol:    tickers[0].Ticker.Symbol,
        LastPrice: weightedPrice.Div(totalWeightDecimal),
        Volume:    weightedVolume.Div(totalWeightDecimal),
        Timestamp: time.Now(),
    }
}
```

### 2. 实时行情处理

#### Ticker数据处理
```go
type Ticker struct {
    Symbol             string          `json:"symbol"`
    LastPrice          decimal.Decimal `json:"last_price"`
    PriceChange        decimal.Decimal `json:"price_change"`
    PriceChangePercent decimal.Decimal `json:"price_change_percent"`
    WeightedAvgPrice   decimal.Decimal `json:"weighted_avg_price"`
    PrevClosePrice     decimal.Decimal `json:"prev_close_price"`
    BidPrice           decimal.Decimal `json:"bid_price"`
    AskPrice           decimal.Decimal `json:"ask_price"`
    OpenPrice          decimal.Decimal `json:"open_price"`
    HighPrice          decimal.Decimal `json:"high_price"`
    LowPrice           decimal.Decimal `json:"low_price"`
    Volume             decimal.Decimal `json:"volume"`
    QuoteVolume        decimal.Decimal `json:"quote_volume"`
    OpenTime           time.Time       `json:"open_time"`
    CloseTime          time.Time       `json:"close_time"`
    Count              int64           `json:"count"`
    Timestamp          time.Time       `json:"timestamp"`
}

type TickerProcessor struct {
    cache     *redis.Client
    db        *gorm.DB
    publisher *EventPublisher
    stats     *TickerStats
}

func (tp *TickerProcessor) ProcessTicker(ticker *Ticker) error {
    // 1. 验证数据
    if err := tp.validateTicker(ticker); err != nil {
        return err
    }
    
    // 2. 计算统计数据
    tp.calculateStats(ticker)
    
    // 3. 缓存数据
    if err := tp.cacheTicker(ticker); err != nil {
        log.Error("缓存ticker失败", zap.Error(err))
    }
    
    // 4. 发布事件
    tp.publisher.PublishTickerUpdate(ticker)
    
    // 5. 更新数据库 (异步)
    go tp.saveTicker(ticker)
    
    return nil
}

func (tp *TickerProcessor) calculateStats(ticker *Ticker) {
    // 计算24小时统计
    stats := tp.stats.Get24HourStats(ticker.Symbol)
    
    // 更新价格变化
    if !stats.OpenPrice.IsZero() {
        ticker.PriceChange = ticker.LastPrice.Sub(stats.OpenPrice)
        ticker.PriceChangePercent = ticker.PriceChange.Div(stats.OpenPrice).Mul(decimal.NewFromInt(100))
    }
    
    // 更新最高最低价
    if ticker.LastPrice.GreaterThan(stats.HighPrice) {
        stats.HighPrice = ticker.LastPrice
    }
    if ticker.LastPrice.LessThan(stats.LowPrice) || stats.LowPrice.IsZero() {
        stats.LowPrice = ticker.LastPrice
    }
    
    // 更新成交量
    stats.Volume = stats.Volume.Add(ticker.Volume)
    stats.QuoteVolume = stats.QuoteVolume.Add(ticker.QuoteVolume)
    
    ticker.HighPrice = stats.HighPrice
    ticker.LowPrice = stats.LowPrice
    ticker.Volume = stats.Volume
    ticker.QuoteVolume = stats.QuoteVolume
}

func (tp *TickerProcessor) cacheTicker(ticker *Ticker) error {
    key := fmt.Sprintf("ticker:%s", ticker.Symbol)
    data, err := json.Marshal(ticker)
    if err != nil {
        return err
    }
    
    return tp.cache.Set(key, data, 10*time.Second).Err()
}
```

#### 深度数据处理
```go
type Depth struct {
    Symbol       string     `json:"symbol"`
    LastUpdateID int64      `json:"last_update_id"`
    Bids         [][]string `json:"bids"` // [price, quantity]
    Asks         [][]string `json:"asks"` // [price, quantity]
    Timestamp    time.Time  `json:"timestamp"`
}

type DepthProcessor struct {
    cache     *redis.Client
    publisher *EventPublisher
    merger    *DepthMerger
}

func (dp *DepthProcessor) ProcessDepth(depth *Depth) error {
    // 1. 验证数据
    if err := dp.validateDepth(depth); err != nil {
        return err
    }
    
    // 2. 合并深度数据
    mergedDepth := dp.merger.MergeDepth(depth)
    
    // 3. 缓存不同级别的深度
    levels := []int{5, 10, 20, 50, 100}
    for _, level := range levels {
        limitedDepth := dp.limitDepth(mergedDepth, level)
        if err := dp.cacheDepth(limitedDepth, level); err != nil {
            log.Error("缓存深度数据失败", zap.Error(err))
        }
    }
    
    // 4. 发布事件
    dp.publisher.PublishDepthUpdate(mergedDepth)
    
    return nil
}

func (dp *DepthProcessor) limitDepth(depth *Depth, level int) *Depth {
    limited := &Depth{
        Symbol:       depth.Symbol,
        LastUpdateID: depth.LastUpdateID,
        Timestamp:    depth.Timestamp,
    }
    
    // 限制买单深度
    if len(depth.Bids) > level {
        limited.Bids = depth.Bids[:level]
    } else {
        limited.Bids = depth.Bids
    }
    
    // 限制卖单深度
    if len(depth.Asks) > level {
        limited.Asks = depth.Asks[:level]
    } else {
        limited.Asks = depth.Asks
    }
    
    return limited
}

func (dp *DepthProcessor) cacheDepth(depth *Depth, level int) error {
    key := fmt.Sprintf("depth:%s:%d", depth.Symbol, level)
    data, err := json.Marshal(depth)
    if err != nil {
        return err
    }
    
    return dp.cache.Set(key, data, 5*time.Second).Err()
}
```

### 3. K线数据生成

#### K线数据结构
```go
type Kline struct {
    Symbol           string          `json:"symbol"`
    Interval         string          `json:"interval"`
    OpenTime         time.Time       `json:"open_time"`
    CloseTime        time.Time       `json:"close_time"`
    Open             decimal.Decimal `json:"open"`
    High             decimal.Decimal `json:"high"`
    Low              decimal.Decimal `json:"low"`
    Close            decimal.Decimal `json:"close"`
    Volume           decimal.Decimal `json:"volume"`
    QuoteVolume      decimal.Decimal `json:"quote_volume"`
    TradeCount       int64           `json:"trade_count"`
    TakerBuyVolume   decimal.Decimal `json:"taker_buy_volume"`
    TakerBuyQuoteVolume decimal.Decimal `json:"taker_buy_quote_volume"`
    IsClosed         bool            `json:"is_closed"`
}

type KlineGenerator struct {
    intervals map[string]time.Duration
    cache     *redis.Client
    influxDB  *influxdb.Client
    publisher *EventPublisher
}

func NewKlineGenerator() *KlineGenerator {
    return &KlineGenerator{
        intervals: map[string]time.Duration{
            "1m":  1 * time.Minute,
            "5m":  5 * time.Minute,
            "15m": 15 * time.Minute,
            "30m": 30 * time.Minute,
            "1h":  1 * time.Hour,
            "4h":  4 * time.Hour,
            "1d":  24 * time.Hour,
            "1w":  7 * 24 * time.Hour,
        },
    }
}

func (kg *KlineGenerator) ProcessTrade(trade *Trade) error {
    // 为每个时间间隔生成K线
    for interval, duration := range kg.intervals {
        if err := kg.updateKline(trade, interval, duration); err != nil {
            log.Error("更新K线失败", 
                zap.String("symbol", trade.Symbol),
                zap.String("interval", interval),
                zap.Error(err))
        }
    }
    
    return nil
}

func (kg *KlineGenerator) updateKline(trade *Trade, interval string, duration time.Duration) error {
    // 计算K线时间窗口
    openTime := kg.calculateOpenTime(trade.Timestamp, duration)
    closeTime := openTime.Add(duration).Add(-time.Millisecond)
    
    key := fmt.Sprintf("kline:%s:%s:%d", trade.Symbol, interval, openTime.Unix())
    
    // 获取当前K线数据
    kline, err := kg.getCurrentKline(key, trade.Symbol, interval, openTime, closeTime)
    if err != nil {
        return err
    }
    
    // 更新K线数据
    kg.updateKlineWithTrade(kline, trade)
    
    // 缓存K线数据
    if err := kg.cacheKline(key, kline); err != nil {
        return err
    }
    
    // 发布K线更新事件
    kg.publisher.PublishKlineUpdate(kline)
    
    // 如果K线已关闭，保存到时序数据库
    if kline.IsClosed {
        go kg.saveKlineToInfluxDB(kline)
    }
    
    return nil
}

func (kg *KlineGenerator) updateKlineWithTrade(kline *Kline, trade *Trade) {
    // 更新开盘价 (第一笔交易)
    if kline.Open.IsZero() {
        kline.Open = trade.Price
    }
    
    // 更新收盘价 (最新价格)
    kline.Close = trade.Price
    
    // 更新最高价
    if trade.Price.GreaterThan(kline.High) || kline.High.IsZero() {
        kline.High = trade.Price
    }
    
    // 更新最低价
    if trade.Price.LessThan(kline.Low) || kline.Low.IsZero() {
        kline.Low = trade.Price
    }
    
    // 更新成交量
    kline.Volume = kline.Volume.Add(trade.Amount)
    kline.QuoteVolume = kline.QuoteVolume.Add(trade.QuoteAmount)
    kline.TradeCount++
    
    // 更新主动买入量
    if trade.IsBuyerMaker {
        kline.TakerBuyVolume = kline.TakerBuyVolume.Add(trade.Amount)
        kline.TakerBuyQuoteVolume = kline.TakerBuyQuoteVolume.Add(trade.QuoteAmount)
    }
    
    // 检查K线是否已关闭
    kline.IsClosed = time.Now().After(kline.CloseTime)
}

func (kg *KlineGenerator) calculateOpenTime(timestamp time.Time, duration time.Duration) time.Time {
    // 计算K线开始时间
    switch duration {
    case 1 * time.Minute:
        return timestamp.Truncate(time.Minute)
    case 5 * time.Minute:
        return timestamp.Truncate(5 * time.Minute)
    case 15 * time.Minute:
        return timestamp.Truncate(15 * time.Minute)
    case 30 * time.Minute:
        return timestamp.Truncate(30 * time.Minute)
    case 1 * time.Hour:
        return timestamp.Truncate(time.Hour)
    case 4 * time.Hour:
        return timestamp.Truncate(4 * time.Hour)
    case 24 * time.Hour:
        return time.Date(timestamp.Year(), timestamp.Month(), timestamp.Day(), 0, 0, 0, 0, timestamp.Location())
    case 7 * 24 * time.Hour:
        // 周K线从周一开始
        weekday := int(timestamp.Weekday())
        if weekday == 0 {
            weekday = 7 // 周日为7
        }
        daysToMonday := weekday - 1
        monday := timestamp.AddDate(0, 0, -daysToMonday)
        return time.Date(monday.Year(), monday.Month(), monday.Day(), 0, 0, 0, 0, monday.Location())
    default:
        return timestamp.Truncate(duration)
    }
}
```

### 4. 数据分发

#### WebSocket推送
```go
type WebSocketHub struct {
    clients    map[*WebSocketClient]bool
    register   chan *WebSocketClient
    unregister chan *WebSocketClient
    broadcast  chan []byte
    subscriptions map[string]map[*WebSocketClient]bool
    mutex      sync.RWMutex
}

type WebSocketClient struct {
    hub        *WebSocketHub
    conn       *websocket.Conn
    send       chan []byte
    userID     string
    subscriptions map[string]bool
}

func (h *WebSocketHub) Run() {
    for {
        select {
        case client := <-h.register:
            h.clients[client] = true
            log.Info("WebSocket客户端连接", zap.String("user_id", client.userID))
            
        case client := <-h.unregister:
            if _, ok := h.clients[client]; ok {
                delete(h.clients, client)
                close(client.send)
                h.removeClientSubscriptions(client)
                log.Info("WebSocket客户端断开", zap.String("user_id", client.userID))
            }
            
        case message := <-h.broadcast:
            for client := range h.clients {
                select {
                case client.send <- message:
                default:
                    close(client.send)
                    delete(h.clients, client)
                }
            }
        }
    }
}

func (h *WebSocketHub) Subscribe(client *WebSocketClient, stream string) {
    h.mutex.Lock()
    defer h.mutex.Unlock()
    
    if h.subscriptions[stream] == nil {
        h.subscriptions[stream] = make(map[*WebSocketClient]bool)
    }
    
    h.subscriptions[stream][client] = true
    client.subscriptions[stream] = true
}

func (h *WebSocketHub) PublishToStream(stream string, data interface{}) {
    h.mutex.RLock()
    clients := h.subscriptions[stream]
    h.mutex.RUnlock()
    
    if len(clients) == 0 {
        return
    }
    
    message := map[string]interface{}{
        "stream": stream,
        "data":   data,
    }
    
    jsonData, err := json.Marshal(message)
    if err != nil {
        log.Error("序列化WebSocket消息失败", zap.Error(err))
        return
    }
    
    for client := range clients {
        select {
        case client.send <- jsonData:
        default:
            close(client.send)
            delete(h.clients, client)
            h.removeClientSubscriptions(client)
        }
    }
}
```

#### 事件发布
```go
type EventPublisher struct {
    wsHub     *WebSocketHub
    mqClient  *rabbitmq.Client
    cache     *redis.Client
}

func (ep *EventPublisher) PublishTickerUpdate(ticker *Ticker) {
    // WebSocket推送
    stream := fmt.Sprintf("%s@ticker", strings.ToLower(ticker.Symbol))
    ep.wsHub.PublishToStream(stream, ticker)
    
    // 消息队列发布
    ep.publishToMQ("ticker.update", ticker)
    
    // Redis发布订阅
    ep.publishToRedis("ticker:"+ticker.Symbol, ticker)
}

func (ep *EventPublisher) PublishDepthUpdate(depth *Depth) {
    stream := fmt.Sprintf("%s@depth", strings.ToLower(depth.Symbol))
    ep.wsHub.PublishToStream(stream, depth)
    
    ep.publishToMQ("depth.update", depth)
    ep.publishToRedis("depth:"+depth.Symbol, depth)
}

func (ep *EventPublisher) PublishKlineUpdate(kline *Kline) {
    stream := fmt.Sprintf("%s@kline_%s", strings.ToLower(kline.Symbol), kline.Interval)
    ep.wsHub.PublishToStream(stream, kline)
    
    ep.publishToMQ("kline.update", kline)
    ep.publishToRedis("kline:"+kline.Symbol+":"+kline.Interval, kline)
}

func (ep *EventPublisher) publishToMQ(routingKey string, data interface{}) {
    jsonData, err := json.Marshal(data)
    if err != nil {
        log.Error("序列化MQ消息失败", zap.Error(err))
        return
    }
    
    err = ep.mqClient.Publish("market.data", routingKey, jsonData)
    if err != nil {
        log.Error("发布MQ消息失败", zap.Error(err))
    }
}

func (ep *EventPublisher) publishToRedis(channel string, data interface{}) {
    jsonData, err := json.Marshal(data)
    if err != nil {
        log.Error("序列化Redis消息失败", zap.Error(err))
        return
    }
    
    err = ep.cache.Publish(channel, jsonData).Err()
    if err != nil {
        log.Error("发布Redis消息失败", zap.Error(err))
    }
}
```

### 5. 数据存储

#### 时序数据库存储
```go
type InfluxDBStorage struct {
    client   influxdb2.Client
    writeAPI influxdb2api.WriteAPI
    bucket   string
    org      string
}

func (ids *InfluxDBStorage) SaveKline(kline *Kline) error {
    // 创建数据点
    point := influxdb2.NewPointWithMeasurement("klines").
        AddTag("symbol", kline.Symbol).
        AddTag("interval", kline.Interval).
        AddField("open", kline.Open.InexactFloat64()).
        AddField("high", kline.High.InexactFloat64()).
        AddField("low", kline.Low.InexactFloat64()).
        AddField("close", kline.Close.InexactFloat64()).
        AddField("volume", kline.Volume.InexactFloat64()).
        AddField("quote_volume", kline.QuoteVolume.InexactFloat64()).
        AddField("trade_count", kline.TradeCount).
        AddField("taker_buy_volume", kline.TakerBuyVolume.InexactFloat64()).
        AddField("taker_buy_quote_volume", kline.TakerBuyQuoteVolume.InexactFloat64()).
        SetTime(kline.OpenTime)
    
    // 写入数据
    ids.writeAPI.WritePoint(point)
    
    return nil
}

func (ids *InfluxDBStorage) SaveTicker(ticker *Ticker) error {
    point := influxdb2.NewPointWithMeasurement("tickers").
        AddTag("symbol", ticker.Symbol).
        AddField("last_price", ticker.LastPrice.InexactFloat64()).
        AddField("price_change", ticker.PriceChange.InexactFloat64()).
        AddField("price_change_percent", ticker.PriceChangePercent.InexactFloat64()).
        AddField("high_price", ticker.HighPrice.InexactFloat64()).
        AddField("low_price", ticker.LowPrice.InexactFloat64()).
        AddField("volume", ticker.Volume.InexactFloat64()).
        AddField("quote_volume", ticker.QuoteVolume.InexactFloat64()).
        AddField("count", ticker.Count).
        SetTime(ticker.Timestamp)
    
    ids.writeAPI.WritePoint(point)
    
    return nil
}

func (ids *InfluxDBStorage) GetKlines(symbol, interval string, startTime, endTime time.Time, limit int) ([]*Kline, error) {
    query := fmt.Sprintf(`
        from(bucket: "%s")
        |> range(start: %s, stop: %s)
        |> filter(fn: (r) => r._measurement == "klines")
        |> filter(fn: (r) => r.symbol == "%s")
        |> filter(fn: (r) => r.interval == "%s")
        |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
        |> sort(columns: ["_time"])
        |> limit(n: %d)
    `, ids.bucket, startTime.Format(time.RFC3339), endTime.Format(time.RFC3339), symbol, interval, limit)
    
    result, err := ids.client.QueryAPI(ids.org).Query(context.Background(), query)
    if err != nil {
        return nil, err
    }
    
    var klines []*Kline
    for result.Next() {
        record := result.Record()
        
        kline := &Kline{
            Symbol:    record.ValueByKey("symbol").(string),
            Interval:  record.ValueByKey("interval").(string),
            OpenTime:  record.Time(),
            Open:      decimal.NewFromFloat(record.ValueByKey("open").(float64)),
            High:      decimal.NewFromFloat(record.ValueByKey("high").(float64)),
            Low:       decimal.NewFromFloat(record.ValueByKey("low").(float64)),
            Close:     decimal.NewFromFloat(record.ValueByKey("close").(float64)),
            Volume:    decimal.NewFromFloat(record.ValueByKey("volume").(float64)),
            QuoteVolume: decimal.NewFromFloat(record.ValueByKey("quote_volume").(float64)),
            TradeCount: record.ValueByKey("trade_count").(int64),
        }
        
        klines = append(klines, kline)
    }
    
    return klines, nil
}
```

## API接口

### 市场数据接口
```
GET    /api/v1/ticker/:symbol         # 获取24小时价格统计
GET    /api/v1/ticker                 # 获取所有交易对价格统计
GET    /api/v1/depth/:symbol          # 获取订单簿深度
GET    /api/v1/trades/:symbol         # 获取最近成交记录
GET    /api/v1/klines/:symbol         # 获取K线数据
GET    /api/v1/exchangeInfo           # 获取交易规则和交易对信息
```

### WebSocket数据流
```
wss://api.example.com/ws/stream

# 订阅格式
{
  "method": "SUBSCRIBE",
  "params": [
    "btcusdt@ticker",
    "btcusdt@depth20",
    "btcusdt@kline_1m",
    "btcusdt@trade"
  ],
  "id": 1
}
```

## 配置说明

### 数据源配置
```yaml
data_sources:
  binance:
    enabled: true
    weight: 0.4
    api_key: "your_api_key"
    secret_key: "your_secret_key"
    ws_url: "wss://stream.binance.com:9443/ws"
    rest_url: "https://api.binance.com"
  
  huobi:
    enabled: true
    weight: 0.3
    ws_url: "wss://api.huobi.pro/ws"
    rest_url: "https://api.huobi.pro"
  
  okex:
    enabled: true
    weight: 0.3
    ws_url: "wss://ws.okex.com:8443/ws/v5/public"
    rest_url: "https://www.okex.com"
```

### 存储配置
```yaml
storage:
  influxdb:
    url: "http://localhost:8086"
    token: "your_token"
    org: "your_org"
    bucket: "market_data"
    
  redis:
    addr: "localhost:6379"
    password: ""
    db: 0
    
  cache_ttl:
    ticker: 10s
    depth: 5s
    kline: 60s
    trade: 30s
```

这个市场数据功能文档提供了完整的市场数据处理方案，包括数据收集、处理、存储和分发的全流程实现。
