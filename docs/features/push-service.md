# 推送服务功能

## 功能概述

推送服务是Spot-Server的重要组件，负责向用户实时推送交易通知、价格提醒、系统公告等各类消息。支持多种推送渠道，包括WebSocket、邮件、短信、移动端推送等。

## 核心功能

### 1. 多渠道推送

#### 推送渠道管理
```go
type PushChannel interface {
    Send(message *PushMessage) error
    GetChannelType() string
    IsAvailable() bool
    GetConfig() *ChannelConfig
}

type PushMessage struct {
    MessageID   string                 `json:"message_id"`
    UserID      string                 `json:"user_id"`
    Type        string                 `json:"type"`
    Title       string                 `json:"title"`
    Content     string                 `json:"content"`
    Data        map[string]interface{} `json:"data"`
    Priority    int                    `json:"priority"`
    Channels    []string               `json:"channels"`
    ScheduleAt  *time.Time             `json:"schedule_at"`
    ExpiresAt   *time.Time             `json:"expires_at"`
    CreatedAt   time.Time              `json:"created_at"`
}

type ChannelConfig struct {
    Enabled     bool                   `json:"enabled"`
    RateLimit   int                    `json:"rate_limit"`
    RetryCount  int                    `json:"retry_count"`
    Timeout     time.Duration          `json:"timeout"`
    Templates   map[string]string      `json:"templates"`
    Settings    map[string]interface{} `json:"settings"`
}
```

#### WebSocket推送
```go
type WebSocketPushChannel struct {
    hub         *WebSocketHub
    connections map[string]*WebSocketConnection
    mutex       sync.RWMutex
}

type WebSocketConnection struct {
    UserID     string
    Conn       *websocket.Conn
    Send       chan []byte
    LastActive time.Time
    Subscriptions map[string]bool
}

func (wpc *WebSocketPushChannel) Send(message *PushMessage) error {
    wpc.mutex.RLock()
    conn, exists := wpc.connections[message.UserID]
    wpc.mutex.RUnlock()
    
    if !exists {
        return fmt.Errorf("用户 %s 未连接WebSocket", message.UserID)
    }
    
    // 构造WebSocket消息
    wsMessage := map[string]interface{}{
        "type":       "notification",
        "message_id": message.MessageID,
        "title":      message.Title,
        "content":    message.Content,
        "data":       message.Data,
        "timestamp":  time.Now().Unix(),
    }
    
    jsonData, err := json.Marshal(wsMessage)
    if err != nil {
        return err
    }
    
    // 发送消息
    select {
    case conn.Send <- jsonData:
        return nil
    case <-time.After(5 * time.Second):
        return errors.New("WebSocket发送超时")
    }
}

func (wpc *WebSocketPushChannel) HandleConnection(userID string, conn *websocket.Conn) {
    wsConn := &WebSocketConnection{
        UserID:        userID,
        Conn:          conn,
        Send:          make(chan []byte, 256),
        LastActive:    time.Now(),
        Subscriptions: make(map[string]bool),
    }
    
    wpc.mutex.Lock()
    wpc.connections[userID] = wsConn
    wpc.mutex.Unlock()
    
    // 启动发送协程
    go wpc.writePump(wsConn)
    
    // 启动接收协程
    go wpc.readPump(wsConn)
}

func (wpc *WebSocketPushChannel) writePump(conn *WebSocketConnection) {
    ticker := time.NewTicker(54 * time.Second)
    defer func() {
        ticker.Stop()
        conn.Conn.Close()
        wpc.removeConnection(conn.UserID)
    }()
    
    for {
        select {
        case message, ok := <-conn.Send:
            conn.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
            if !ok {
                conn.Conn.WriteMessage(websocket.CloseMessage, []byte{})
                return
            }
            
            if err := conn.Conn.WriteMessage(websocket.TextMessage, message); err != nil {
                log.Error("WebSocket写入失败", zap.Error(err))
                return
            }
            
        case <-ticker.C:
            conn.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
            if err := conn.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
                return
            }
        }
    }
}
```

#### 邮件推送
```go
type EmailPushChannel struct {
    smtpConfig  *SMTPConfig
    templates   *TemplateManager
    rateLimiter *RateLimiter
}

type SMTPConfig struct {
    Host     string `json:"host"`
    Port     int    `json:"port"`
    Username string `json:"username"`
    Password string `json:"password"`
    From     string `json:"from"`
    UseTLS   bool   `json:"use_tls"`
}

func (epc *EmailPushChannel) Send(message *PushMessage) error {
    // 检查发送频率限制
    if !epc.rateLimiter.Allow(message.UserID) {
        return errors.New("邮件发送频率超限")
    }
    
    // 获取用户邮箱
    userEmail, err := epc.getUserEmail(message.UserID)
    if err != nil {
        return err
    }
    
    // 渲染邮件模板
    emailContent, err := epc.renderEmailTemplate(message)
    if err != nil {
        return err
    }
    
    // 发送邮件
    return epc.sendEmail(userEmail, message.Title, emailContent)
}

func (epc *EmailPushChannel) renderEmailTemplate(message *PushMessage) (string, error) {
    templateName := fmt.Sprintf("email_%s", message.Type)
    template, err := epc.templates.GetTemplate(templateName)
    if err != nil {
        // 使用默认模板
        template, err = epc.templates.GetTemplate("email_default")
        if err != nil {
            return "", err
        }
    }
    
    data := map[string]interface{}{
        "Title":   message.Title,
        "Content": message.Content,
        "Data":    message.Data,
        "Time":    time.Now().Format("2006-01-02 15:04:05"),
    }
    
    return template.Render(data)
}

func (epc *EmailPushChannel) sendEmail(to, subject, content string) error {
    auth := smtp.PlainAuth("", epc.smtpConfig.Username, epc.smtpConfig.Password, epc.smtpConfig.Host)
    
    msg := fmt.Sprintf("To: %s\r\nSubject: %s\r\nContent-Type: text/html; charset=UTF-8\r\n\r\n%s",
        to, subject, content)
    
    addr := fmt.Sprintf("%s:%d", epc.smtpConfig.Host, epc.smtpConfig.Port)
    
    if epc.smtpConfig.UseTLS {
        return epc.sendEmailTLS(addr, auth, epc.smtpConfig.From, []string{to}, []byte(msg))
    }
    
    return smtp.SendMail(addr, auth, epc.smtpConfig.From, []string{to}, []byte(msg))
}
```

#### 短信推送
```go
type SMSPushChannel struct {
    provider    SMSProvider
    rateLimiter *RateLimiter
    templates   *TemplateManager
}

type SMSProvider interface {
    SendSMS(phone, content string) error
    GetProviderName() string
    IsAvailable() bool
}

// 阿里云短信服务实现
type AliyunSMSProvider struct {
    accessKeyID     string
    accessKeySecret string
    signName        string
    endpoint        string
}

func (asp *AliyunSMSProvider) SendSMS(phone, content string) error {
    client, err := dysmsapi.NewClientWithAccessKey("cn-hangzhou", asp.accessKeyID, asp.accessKeySecret)
    if err != nil {
        return err
    }
    
    request := dysmsapi.CreateSendSmsRequest()
    request.Scheme = "https"
    request.PhoneNumbers = phone
    request.SignName = asp.signName
    request.TemplateCode = "SMS_TEMPLATE_CODE"
    request.TemplateParam = content
    
    response, err := client.SendSms(request)
    if err != nil {
        return err
    }
    
    if response.Code != "OK" {
        return fmt.Errorf("短信发送失败: %s", response.Message)
    }
    
    return nil
}

func (spc *SMSPushChannel) Send(message *PushMessage) error {
    // 检查发送频率限制
    if !spc.rateLimiter.Allow(message.UserID) {
        return errors.New("短信发送频率超限")
    }
    
    // 获取用户手机号
    userPhone, err := spc.getUserPhone(message.UserID)
    if err != nil {
        return err
    }
    
    // 渲染短信模板
    smsContent, err := spc.renderSMSTemplate(message)
    if err != nil {
        return err
    }
    
    // 发送短信
    return spc.provider.SendSMS(userPhone, smsContent)
}
```

#### 移动端推送
```go
type MobilePushChannel struct {
    apnsClient   *apns2.Client
    fcmClient    *fcm.Client
    deviceTokens map[string]*DeviceToken
    mutex        sync.RWMutex
}

type DeviceToken struct {
    UserID    string    `json:"user_id"`
    Token     string    `json:"token"`
    Platform  string    `json:"platform"` // "ios", "android"
    AppID     string    `json:"app_id"`
    UpdatedAt time.Time `json:"updated_at"`
}

func (mpc *MobilePushChannel) Send(message *PushMessage) error {
    mpc.mutex.RLock()
    deviceToken, exists := mpc.deviceTokens[message.UserID]
    mpc.mutex.RUnlock()
    
    if !exists {
        return fmt.Errorf("用户 %s 未注册设备Token", message.UserID)
    }
    
    switch deviceToken.Platform {
    case "ios":
        return mpc.sendAPNS(deviceToken, message)
    case "android":
        return mpc.sendFCM(deviceToken, message)
    default:
        return fmt.Errorf("不支持的平台: %s", deviceToken.Platform)
    }
}

func (mpc *MobilePushChannel) sendAPNS(deviceToken *DeviceToken, message *PushMessage) error {
    notification := &apns2.Notification{}
    notification.DeviceToken = deviceToken.Token
    notification.Topic = deviceToken.AppID
    notification.Payload = []byte(fmt.Sprintf(`{
        "aps": {
            "alert": {
                "title": "%s",
                "body": "%s"
            },
            "badge": 1,
            "sound": "default"
        },
        "custom_data": %s
    }`, message.Title, message.Content, mpc.marshalData(message.Data)))
    
    res, err := mpc.apnsClient.Push(notification)
    if err != nil {
        return err
    }
    
    if res.StatusCode != 200 {
        return fmt.Errorf("APNS推送失败: %s", res.Reason)
    }
    
    return nil
}

func (mpc *MobilePushChannel) sendFCM(deviceToken *DeviceToken, message *PushMessage) error {
    msg := &fcm.Message{
        To: deviceToken.Token,
        Notification: &fcm.Notification{
            Title: message.Title,
            Body:  message.Content,
        },
        Data: mpc.convertDataToStringMap(message.Data),
    }
    
    response, err := mpc.fcmClient.Send(msg)
    if err != nil {
        return err
    }
    
    if response.Failure > 0 {
        return fmt.Errorf("FCM推送失败: %v", response.Results)
    }
    
    return nil
}
```

### 2. 消息类型管理

#### 交易通知
```go
type TradeNotificationHandler struct {
    pushService *PushService
    templates   *TemplateManager
}

func (tnh *TradeNotificationHandler) HandleTradeExecuted(trade *Trade) {
    // 买方通知
    buyMessage := &PushMessage{
        MessageID: uuid.New().String(),
        UserID:    trade.BuyUserID,
        Type:      "trade_executed",
        Title:     "交易成交通知",
        Content:   fmt.Sprintf("您的买单已成交，成交价格: %s, 成交数量: %s", trade.Price.String(), trade.Amount.String()),
        Data: map[string]interface{}{
            "trade_id":     trade.TradeID,
            "order_id":     trade.BuyOrderID,
            "symbol":       trade.Symbol,
            "side":         "buy",
            "amount":       trade.Amount.String(),
            "price":        trade.Price.String(),
            "quote_amount": trade.QuoteAmount.String(),
            "fee":          trade.BuyFee.String(),
        },
        Priority:  2,
        Channels:  []string{"websocket", "mobile"},
        CreatedAt: time.Now(),
    }
    
    // 卖方通知
    sellMessage := &PushMessage{
        MessageID: uuid.New().String(),
        UserID:    trade.SellUserID,
        Type:      "trade_executed",
        Title:     "交易成交通知",
        Content:   fmt.Sprintf("您的卖单已成交，成交价格: %s, 成交数量: %s", trade.Price.String(), trade.Amount.String()),
        Data: map[string]interface{}{
            "trade_id":     trade.TradeID,
            "order_id":     trade.SellOrderID,
            "symbol":       trade.Symbol,
            "side":         "sell",
            "amount":       trade.Amount.String(),
            "price":        trade.Price.String(),
            "quote_amount": trade.QuoteAmount.String(),
            "fee":          trade.SellFee.String(),
        },
        Priority:  2,
        Channels:  []string{"websocket", "mobile"},
        CreatedAt: time.Now(),
    }
    
    // 发送通知
    tnh.pushService.SendMessage(buyMessage)
    tnh.pushService.SendMessage(sellMessage)
}
```

#### 价格提醒
```go
type PriceAlertHandler struct {
    pushService   *PushService
    alertManager  *AlertManager
    priceMonitor  *PriceMonitor
}

type PriceAlert struct {
    AlertID     string          `json:"alert_id"`
    UserID      string          `json:"user_id"`
    Symbol      string          `json:"symbol"`
    Condition   string          `json:"condition"` // "above", "below", "change"
    TargetPrice decimal.Decimal `json:"target_price"`
    ChangeRate  decimal.Decimal `json:"change_rate"`
    IsActive    bool            `json:"is_active"`
    CreatedAt   time.Time       `json:"created_at"`
    TriggeredAt *time.Time      `json:"triggered_at"`
}

func (pah *PriceAlertHandler) CheckPriceAlerts(symbol string, currentPrice decimal.Decimal) {
    alerts := pah.alertManager.GetActiveAlerts(symbol)
    
    for _, alert := range alerts {
        if pah.shouldTriggerAlert(alert, currentPrice) {
            pah.triggerPriceAlert(alert, currentPrice)
        }
    }
}

func (pah *PriceAlertHandler) shouldTriggerAlert(alert *PriceAlert, currentPrice decimal.Decimal) bool {
    switch alert.Condition {
    case "above":
        return currentPrice.GreaterThanOrEqual(alert.TargetPrice)
    case "below":
        return currentPrice.LessThanOrEqual(alert.TargetPrice)
    case "change":
        // 计算价格变化率
        changeRate := currentPrice.Sub(alert.TargetPrice).Div(alert.TargetPrice).Abs()
        return changeRate.GreaterThanOrEqual(alert.ChangeRate)
    default:
        return false
    }
}

func (pah *PriceAlertHandler) triggerPriceAlert(alert *PriceAlert, currentPrice decimal.Decimal) {
    message := &PushMessage{
        MessageID: uuid.New().String(),
        UserID:    alert.UserID,
        Type:      "price_alert",
        Title:     "价格提醒",
        Content:   fmt.Sprintf("%s 价格已达到您设置的提醒条件，当前价格: %s", alert.Symbol, currentPrice.String()),
        Data: map[string]interface{}{
            "alert_id":      alert.AlertID,
            "symbol":        alert.Symbol,
            "condition":     alert.Condition,
            "target_price":  alert.TargetPrice.String(),
            "current_price": currentPrice.String(),
        },
        Priority:  3,
        Channels:  []string{"websocket", "mobile", "email"},
        CreatedAt: time.Now(),
    }
    
    pah.pushService.SendMessage(message)
    
    // 标记提醒已触发
    now := time.Now()
    alert.TriggeredAt = &now
    alert.IsActive = false
    pah.alertManager.UpdateAlert(alert)
}
```

#### 系统公告
```go
type SystemAnnouncementHandler struct {
    pushService *PushService
    userService *UserService
}

type SystemAnnouncement struct {
    AnnouncementID string                 `json:"announcement_id"`
    Title          string                 `json:"title"`
    Content        string                 `json:"content"`
    Type           string                 `json:"type"` // "maintenance", "feature", "security"
    Priority       int                    `json:"priority"`
    TargetUsers    []string               `json:"target_users"`
    TargetGroups   []string               `json:"target_groups"`
    Channels       []string               `json:"channels"`
    ScheduleAt     *time.Time             `json:"schedule_at"`
    ExpiresAt      *time.Time             `json:"expires_at"`
    Data           map[string]interface{} `json:"data"`
    CreatedAt      time.Time              `json:"created_at"`
}

func (sah *SystemAnnouncementHandler) BroadcastAnnouncement(announcement *SystemAnnouncement) error {
    // 获取目标用户列表
    targetUsers, err := sah.getTargetUsers(announcement)
    if err != nil {
        return err
    }
    
    // 批量发送通知
    var wg sync.WaitGroup
    semaphore := make(chan struct{}, 100) // 限制并发数
    
    for _, userID := range targetUsers {
        wg.Add(1)
        go func(uid string) {
            defer wg.Done()
            semaphore <- struct{}{}
            defer func() { <-semaphore }()
            
            message := &PushMessage{
                MessageID: uuid.New().String(),
                UserID:    uid,
                Type:      "system_announcement",
                Title:     announcement.Title,
                Content:   announcement.Content,
                Data:      announcement.Data,
                Priority:  announcement.Priority,
                Channels:  announcement.Channels,
                ScheduleAt: announcement.ScheduleAt,
                ExpiresAt: announcement.ExpiresAt,
                CreatedAt: time.Now(),
            }
            
            if err := sah.pushService.SendMessage(message); err != nil {
                log.Error("发送系统公告失败", 
                    zap.String("user_id", uid),
                    zap.String("announcement_id", announcement.AnnouncementID),
                    zap.Error(err))
            }
        }(userID)
    }
    
    wg.Wait()
    
    log.Info("系统公告发送完成", 
        zap.String("announcement_id", announcement.AnnouncementID),
        zap.Int("target_users", len(targetUsers)))
    
    return nil
}

func (sah *SystemAnnouncementHandler) getTargetUsers(announcement *SystemAnnouncement) ([]string, error) {
    var targetUsers []string
    
    // 指定用户
    targetUsers = append(targetUsers, announcement.TargetUsers...)
    
    // 用户组
    for _, group := range announcement.TargetGroups {
        groupUsers, err := sah.userService.GetUsersByGroup(group)
        if err != nil {
            return nil, err
        }
        targetUsers = append(targetUsers, groupUsers...)
    }
    
    // 去重
    userSet := make(map[string]bool)
    var uniqueUsers []string
    for _, userID := range targetUsers {
        if !userSet[userID] {
            userSet[userID] = true
            uniqueUsers = append(uniqueUsers, userID)
        }
    }
    
    return uniqueUsers, nil
}
```

### 3. 推送服务核心

#### 推送服务管理器
```go
type PushService struct {
    channels       map[string]PushChannel
    messageQueue   chan *PushMessage
    scheduler      *MessageScheduler
    rateLimiter    *RateLimiter
    retryManager   *RetryManager
    analytics      *PushAnalytics
    db             *gorm.DB
    cache          *redis.Client
}

func (ps *PushService) SendMessage(message *PushMessage) error {
    // 1. 验证消息
    if err := ps.validateMessage(message); err != nil {
        return err
    }
    
    // 2. 检查用户推送设置
    userSettings, err := ps.getUserPushSettings(message.UserID)
    if err != nil {
        return err
    }
    
    // 3. 过滤推送渠道
    enabledChannels := ps.filterEnabledChannels(message.Channels, userSettings)
    if len(enabledChannels) == 0 {
        return errors.New("没有可用的推送渠道")
    }
    
    // 4. 保存消息记录
    if err := ps.saveMessage(message); err != nil {
        log.Error("保存推送消息失败", zap.Error(err))
    }
    
    // 5. 发送到消息队列
    select {
    case ps.messageQueue <- message:
        return nil
    case <-time.After(5 * time.Second):
        return errors.New("消息队列已满")
    }
}

func (ps *PushService) processMessages() {
    for message := range ps.messageQueue {
        ps.processMessage(message)
    }
}

func (ps *PushService) processMessage(message *PushMessage) {
    // 检查是否为定时消息
    if message.ScheduleAt != nil && message.ScheduleAt.After(time.Now()) {
        ps.scheduler.ScheduleMessage(message)
        return
    }
    
    // 检查消息是否过期
    if message.ExpiresAt != nil && message.ExpiresAt.Before(time.Now()) {
        log.Warn("消息已过期", zap.String("message_id", message.MessageID))
        return
    }
    
    // 获取用户推送设置
    userSettings, err := ps.getUserPushSettings(message.UserID)
    if err != nil {
        log.Error("获取用户推送设置失败", zap.Error(err))
        return
    }
    
    // 过滤推送渠道
    enabledChannels := ps.filterEnabledChannels(message.Channels, userSettings)
    
    // 并发发送到各个渠道
    var wg sync.WaitGroup
    for _, channelName := range enabledChannels {
        if channel, exists := ps.channels[channelName]; exists {
            wg.Add(1)
            go func(ch PushChannel, msg *PushMessage) {
                defer wg.Done()
                ps.sendToChannel(ch, msg)
            }(channel, message)
        }
    }
    
    wg.Wait()
}

func (ps *PushService) sendToChannel(channel PushChannel, message *PushMessage) {
    channelType := channel.GetChannelType()
    
    // 检查渠道可用性
    if !channel.IsAvailable() {
        log.Warn("推送渠道不可用", zap.String("channel", channelType))
        return
    }
    
    // 检查发送频率限制
    if !ps.rateLimiter.AllowChannel(message.UserID, channelType) {
        log.Warn("推送频率超限", 
            zap.String("user_id", message.UserID),
            zap.String("channel", channelType))
        return
    }
    
    // 发送消息
    startTime := time.Now()
    err := channel.Send(message)
    duration := time.Since(startTime)
    
    // 记录发送结果
    ps.analytics.RecordSend(channelType, message.Type, err == nil, duration)
    
    if err != nil {
        log.Error("推送发送失败", 
            zap.String("channel", channelType),
            zap.String("message_id", message.MessageID),
            zap.Error(err))
        
        // 加入重试队列
        ps.retryManager.AddRetry(channel, message, err)
    } else {
        log.Info("推送发送成功", 
            zap.String("channel", channelType),
            zap.String("message_id", message.MessageID),
            zap.Duration("duration", duration))
        
        // 更新消息状态
        ps.updateMessageStatus(message.MessageID, channelType, "sent")
    }
}
```

## API接口

### 推送管理接口
```
POST   /api/v1/push/send               # 发送推送消息
GET    /api/v1/push/messages           # 获取推送消息列表
GET    /api/v1/push/messages/:id       # 获取推送消息详情
DELETE /api/v1/push/messages/:id       # 删除推送消息
```

### 用户设置接口
```
GET    /api/v1/user/push-settings      # 获取推送设置
PUT    /api/v1/user/push-settings      # 更新推送设置
POST   /api/v1/user/device-token       # 注册设备Token
DELETE /api/v1/user/device-token       # 删除设备Token
```

### 价格提醒接口
```
POST   /api/v1/price-alerts            # 创建价格提醒
GET    /api/v1/price-alerts            # 获取价格提醒列表
PUT    /api/v1/price-alerts/:id        # 更新价格提醒
DELETE /api/v1/price-alerts/:id        # 删除价格提醒
```

## 配置说明

### 推送服务配置
```yaml
push_service:
  enabled: true
  queue_size: 10000
  worker_count: 10
  
  channels:
    websocket:
      enabled: true
      max_connections: 10000
      heartbeat_interval: 30s
    
    email:
      enabled: true
      smtp_host: "smtp.example.com"
      smtp_port: 587
      username: "<EMAIL>"
      password: "password"
      use_tls: true
      rate_limit: 100  # 每小时
    
    sms:
      enabled: true
      provider: "aliyun"
      access_key_id: "your_access_key"
      access_key_secret: "your_secret"
      sign_name: "交易平台"
      rate_limit: 10   # 每小时
    
    mobile:
      enabled: true
      apns_key_file: "/path/to/apns.p8"
      apns_key_id: "your_key_id"
      apns_team_id: "your_team_id"
      fcm_server_key: "your_fcm_key"
      rate_limit: 1000 # 每小时
```

### 消息模板配置
```yaml
templates:
  trade_executed:
    title: "交易成交通知"
    email: "templates/trade_executed_email.html"
    sms: "您的{{.Side}}单已成交，价格{{.Price}}，数量{{.Amount}}"
    mobile: "交易成交：{{.Symbol}} {{.Side}} {{.Amount}}@{{.Price}}"
  
  price_alert:
    title: "价格提醒"
    email: "templates/price_alert_email.html"
    sms: "{{.Symbol}}价格提醒：当前价格{{.CurrentPrice}}"
    mobile: "价格提醒：{{.Symbol}} {{.CurrentPrice}}"
```

这个推送服务功能文档提供了完整的多渠道推送系统实现方案，包括WebSocket、邮件、短信、移动端推送等多种推送方式，以及交易通知、价格提醒、系统公告等多种消息类型的处理。
