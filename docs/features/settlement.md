# 清算结算功能

## 功能概述

清算结算功能是Spot-Server交易系统的核心财务模块，负责处理交易后的资金清算、手续费计算、资产转移和财务对账等关键业务。确保交易系统的资金安全和账务准确。

## 核心功能

### 1. 实时清算

#### 交易清算引擎
```go
type SettlementEngine struct {
    db              *gorm.DB
    cache           *redis.Client
    balanceManager  *BalanceManager
    feeCalculator   *FeeCalculator
    eventPublisher  *EventPublisher
    riskController  *RiskController
    auditLogger     *AuditLogger
}

type TradeSettlement struct {
    TradeID         string          `json:"trade_id"`
    BuyOrderID      string          `json:"buy_order_id"`
    SellOrderID     string          `json:"sell_order_id"`
    BuyUserID       string          `json:"buy_user_id"`
    SellUserID      string          `json:"sell_user_id"`
    Symbol          string          `json:"symbol"`
    Amount          decimal.Decimal `json:"amount"`
    Price           decimal.Decimal `json:"price"`
    QuoteAmount     decimal.Decimal `json:"quote_amount"`
    BuyFee          decimal.Decimal `json:"buy_fee"`
    SellFee         decimal.Decimal `json:"sell_fee"`
    BuyFeeCurrency  string          `json:"buy_fee_currency"`
    SellFeeCurrency string          `json:"sell_fee_currency"`
    SettlementTime  time.Time       `json:"settlement_time"`
    Status          string          `json:"status"`
}

func (se *SettlementEngine) ProcessTradeSettlement(trade *Trade) error {
    // 1. 创建清算记录
    settlement := &TradeSettlement{
        TradeID:        trade.TradeID,
        BuyOrderID:     trade.BuyOrderID,
        SellOrderID:    trade.SellOrderID,
        BuyUserID:      trade.BuyUserID,
        SellUserID:     trade.SellUserID,
        Symbol:         trade.Symbol,
        Amount:         trade.Amount,
        Price:          trade.Price,
        QuoteAmount:    trade.QuoteAmount,
        SettlementTime: time.Now(),
        Status:         "processing",
    }
    
    // 2. 计算手续费
    if err := se.calculateFees(settlement); err != nil {
        return fmt.Errorf("计算手续费失败: %w", err)
    }
    
    // 3. 执行资金转移
    if err := se.executeSettlement(settlement); err != nil {
        return fmt.Errorf("执行清算失败: %w", err)
    }
    
    // 4. 更新清算状态
    settlement.Status = "completed"
    
    // 5. 保存清算记录
    if err := se.saveSettlement(settlement); err != nil {
        return fmt.Errorf("保存清算记录失败: %w", err)
    }
    
    // 6. 发布清算事件
    se.eventPublisher.PublishSettlementEvent(settlement)
    
    // 7. 记录审计日志
    se.auditLogger.LogSettlement(settlement)
    
    return nil
}

func (se *SettlementEngine) executeSettlement(settlement *TradeSettlement) error {
    return se.db.Transaction(func(tx *gorm.DB) error {
        // 买方清算：减少计价货币，增加基础货币
        if err := se.settleBuyer(tx, settlement); err != nil {
            return err
        }
        
        // 卖方清算：减少基础货币，增加计价货币
        if err := se.settleSeller(tx, settlement); err != nil {
            return err
        }
        
        // 手续费处理
        if err := se.settleFees(tx, settlement); err != nil {
            return err
        }
        
        return nil
    })
}

func (se *SettlementEngine) settleBuyer(tx *gorm.DB, settlement *TradeSettlement) error {
    baseCurrency, quoteCurrency := se.parseSymbol(settlement.Symbol)
    
    // 减少买方冻结的计价货币
    totalCost := settlement.QuoteAmount.Add(settlement.BuyFee)
    if err := se.balanceManager.UpdateBalanceWithTx(tx, settlement.BuyUserID, quoteCurrency, 
        decimal.Zero, totalCost.Neg()); err != nil {
        return err
    }
    
    // 增加买方基础货币
    if err := se.balanceManager.UpdateBalanceWithTx(tx, settlement.BuyUserID, baseCurrency, 
        settlement.Amount, decimal.Zero); err != nil {
        return err
    }
    
    // 记录余额变动
    se.logBalanceChange(settlement.BuyUserID, quoteCurrency, totalCost.Neg(), "trade_settlement")
    se.logBalanceChange(settlement.BuyUserID, baseCurrency, settlement.Amount, "trade_settlement")
    
    return nil
}

func (se *SettlementEngine) settleSeller(tx *gorm.DB, settlement *TradeSettlement) error {
    baseCurrency, quoteCurrency := se.parseSymbol(settlement.Symbol)
    
    // 减少卖方冻结的基础货币
    if err := se.balanceManager.UpdateBalanceWithTx(tx, settlement.SellUserID, baseCurrency, 
        decimal.Zero, settlement.Amount.Neg()); err != nil {
        return err
    }
    
    // 增加卖方计价货币（扣除手续费）
    netAmount := settlement.QuoteAmount.Sub(settlement.SellFee)
    if err := se.balanceManager.UpdateBalanceWithTx(tx, settlement.SellUserID, quoteCurrency, 
        netAmount, decimal.Zero); err != nil {
        return err
    }
    
    // 记录余额变动
    se.logBalanceChange(settlement.SellUserID, baseCurrency, settlement.Amount.Neg(), "trade_settlement")
    se.logBalanceChange(settlement.SellUserID, quoteCurrency, netAmount, "trade_settlement")
    
    return nil
}
```

#### 手续费计算
```go
type FeeCalculator struct {
    feeRates map[string]*FeeRate
    vipRates map[int]*VIPFeeRate
    cache    *redis.Client
}

type FeeRate struct {
    Symbol     string          `json:"symbol"`
    MakerRate  decimal.Decimal `json:"maker_rate"`
    TakerRate  decimal.Decimal `json:"taker_rate"`
    MinFee     decimal.Decimal `json:"min_fee"`
    MaxFee     decimal.Decimal `json:"max_fee"`
}

type VIPFeeRate struct {
    Level     int             `json:"level"`
    Discount  decimal.Decimal `json:"discount"` // 折扣率 0.9 = 10%折扣
}

func (fc *FeeCalculator) CalculateTradeFee(userID, symbol string, amount, price decimal.Decimal, isMaker bool) (decimal.Decimal, string, error) {
    // 1. 获取基础费率
    feeRate, err := fc.getFeeRate(symbol)
    if err != nil {
        return decimal.Zero, "", err
    }
    
    // 2. 确定费率类型
    var rate decimal.Decimal
    if isMaker {
        rate = feeRate.MakerRate
    } else {
        rate = feeRate.TakerRate
    }
    
    // 3. 获取VIP折扣
    discount, err := fc.getVIPDiscount(userID)
    if err != nil {
        log.Warn("获取VIP折扣失败", zap.String("user_id", userID), zap.Error(err))
        discount = decimal.NewFromFloat(1.0) // 无折扣
    }
    
    // 4. 计算手续费
    tradeValue := amount.Mul(price)
    fee := tradeValue.Mul(rate).Mul(discount)
    
    // 5. 应用最小/最大手续费限制
    if fee.LessThan(feeRate.MinFee) {
        fee = feeRate.MinFee
    }
    if fee.GreaterThan(feeRate.MaxFee) && !feeRate.MaxFee.IsZero() {
        fee = feeRate.MaxFee
    }
    
    // 6. 确定手续费币种
    feeCurrency := fc.getFeeCurrency(symbol, isMaker)
    
    return fee, feeCurrency, nil
}

func (fc *FeeCalculator) getVIPDiscount(userID string) (decimal.Decimal, error) {
    // 从缓存获取用户VIP等级
    cacheKey := fmt.Sprintf("user:vip:%s", userID)
    vipLevel, err := fc.cache.Get(cacheKey).Int()
    if err != nil {
        // 从数据库查询
        vipLevel, err = fc.getUserVIPLevel(userID)
        if err != nil {
            return decimal.NewFromFloat(1.0), err
        }
        
        // 缓存VIP等级
        fc.cache.Set(cacheKey, vipLevel, time.Hour)
    }
    
    if vipRate, exists := fc.vipRates[vipLevel]; exists {
        return vipRate.Discount, nil
    }
    
    return decimal.NewFromFloat(1.0), nil
}
```

### 2. 批量清算

#### 定时批量清算
```go
type BatchSettlement struct {
    engine    *SettlementEngine
    batchSize int
    interval  time.Duration
    queue     chan *Trade
}

func (bs *BatchSettlement) Start() {
    ticker := time.NewTicker(bs.interval)
    defer ticker.Stop()
    
    batch := make([]*Trade, 0, bs.batchSize)
    
    for {
        select {
        case trade := <-bs.queue:
            batch = append(batch, trade)
            
            if len(batch) >= bs.batchSize {
                bs.processBatch(batch)
                batch = batch[:0]
            }
            
        case <-ticker.C:
            if len(batch) > 0 {
                bs.processBatch(batch)
                batch = batch[:0]
            }
        }
    }
}

func (bs *BatchSettlement) processBatch(trades []*Trade) {
    log.Info("开始批量清算", zap.Int("count", len(trades)))
    
    // 按用户分组以优化数据库操作
    userGroups := bs.groupTradesByUser(trades)
    
    var wg sync.WaitGroup
    for userID, userTrades := range userGroups {
        wg.Add(1)
        go func(uid string, trades []*Trade) {
            defer wg.Done()
            bs.processUserTrades(uid, trades)
        }(userID, userTrades)
    }
    
    wg.Wait()
    
    log.Info("批量清算完成", zap.Int("count", len(trades)))
}

func (bs *BatchSettlement) groupTradesByUser(trades []*Trade) map[string][]*Trade {
    userGroups := make(map[string][]*Trade)
    
    for _, trade := range trades {
        userGroups[trade.BuyUserID] = append(userGroups[trade.BuyUserID], trade)
        if trade.SellUserID != trade.BuyUserID {
            userGroups[trade.SellUserID] = append(userGroups[trade.SellUserID], trade)
        }
    }
    
    return userGroups
}
```

### 3. 资金对账

#### 日终对账
```go
type ReconciliationService struct {
    db              *gorm.DB
    cache           *redis.Client
    balanceManager  *BalanceManager
    reportGenerator *ReportGenerator
}

type ReconciliationReport struct {
    Date                time.Time                    `json:"date"`
    TotalTrades         int64                        `json:"total_trades"`
    TotalVolume         decimal.Decimal              `json:"total_volume"`
    TotalFees           decimal.Decimal              `json:"total_fees"`
    BalanceDiscrepancies []BalanceDiscrepancy        `json:"balance_discrepancies"`
    CurrencyBalances    map[string]CurrencyBalance   `json:"currency_balances"`
    Status              string                       `json:"status"`
    CreatedAt           time.Time                    `json:"created_at"`
}

type BalanceDiscrepancy struct {
    UserID          string          `json:"user_id"`
    Currency        string          `json:"currency"`
    ExpectedBalance decimal.Decimal `json:"expected_balance"`
    ActualBalance   decimal.Decimal `json:"actual_balance"`
    Difference      decimal.Decimal `json:"difference"`
}

type CurrencyBalance struct {
    Currency      string          `json:"currency"`
    TotalBalance  decimal.Decimal `json:"total_balance"`
    AvailableBalance decimal.Decimal `json:"available_balance"`
    FrozenBalance decimal.Decimal `json:"frozen_balance"`
}

func (rs *ReconciliationService) RunDailyReconciliation(date time.Time) (*ReconciliationReport, error) {
    log.Info("开始日终对账", zap.Time("date", date))
    
    report := &ReconciliationReport{
        Date:      date,
        Status:    "processing",
        CreatedAt: time.Now(),
    }
    
    // 1. 统计交易数据
    if err := rs.calculateTradeStatistics(report, date); err != nil {
        return nil, fmt.Errorf("计算交易统计失败: %w", err)
    }
    
    // 2. 检查余额一致性
    if err := rs.checkBalanceConsistency(report, date); err != nil {
        return nil, fmt.Errorf("检查余额一致性失败: %w", err)
    }
    
    // 3. 计算货币总量
    if err := rs.calculateCurrencyTotals(report); err != nil {
        return nil, fmt.Errorf("计算货币总量失败: %w", err)
    }
    
    // 4. 生成对账报告
    if err := rs.generateReconciliationReport(report); err != nil {
        return nil, fmt.Errorf("生成对账报告失败: %w", err)
    }
    
    report.Status = "completed"
    
    // 5. 保存对账结果
    if err := rs.saveReconciliationReport(report); err != nil {
        return nil, fmt.Errorf("保存对账报告失败: %w", err)
    }
    
    log.Info("日终对账完成", 
        zap.Time("date", date),
        zap.Int64("total_trades", report.TotalTrades),
        zap.Int("discrepancies", len(report.BalanceDiscrepancies)))
    
    return report, nil
}

func (rs *ReconciliationService) checkBalanceConsistency(report *ReconciliationReport, date time.Time) error {
    // 获取所有用户的余额变动记录
    var balanceChanges []BalanceChange
    if err := rs.db.Where("DATE(created_at) = ?", date.Format("2006-01-02")).
        Find(&balanceChanges).Error; err != nil {
        return err
    }
    
    // 按用户和货币分组计算预期余额
    expectedBalances := rs.calculateExpectedBalances(balanceChanges)
    
    // 获取实际余额
    actualBalances, err := rs.balanceManager.GetAllUserBalances()
    if err != nil {
        return err
    }
    
    // 比较预期余额和实际余额
    var discrepancies []BalanceDiscrepancy
    for userID, currencies := range expectedBalances {
        for currency, expectedBalance := range currencies {
            actualBalance := actualBalances[userID][currency]
            
            if !expectedBalance.Equal(actualBalance) {
                discrepancies = append(discrepancies, BalanceDiscrepancy{
                    UserID:          userID,
                    Currency:        currency,
                    ExpectedBalance: expectedBalance,
                    ActualBalance:   actualBalance,
                    Difference:      actualBalance.Sub(expectedBalance),
                })
            }
        }
    }
    
    report.BalanceDiscrepancies = discrepancies
    
    return nil
}
```

### 4. 清算风控

#### 清算风险监控
```go
type SettlementRiskController struct {
    config          *RiskConfig
    alertManager    *AlertManager
    balanceMonitor  *BalanceMonitor
}

type RiskConfig struct {
    MaxDailyVolume        decimal.Decimal `json:"max_daily_volume"`
    MaxSingleTradeValue   decimal.Decimal `json:"max_single_trade_value"`
    MaxBalanceDeviation   decimal.Decimal `json:"max_balance_deviation"`
    AlertThresholds       AlertThresholds `json:"alert_thresholds"`
}

type AlertThresholds struct {
    HighVolumeThreshold   decimal.Decimal `json:"high_volume_threshold"`
    LargeTradeThreshold   decimal.Decimal `json:"large_trade_threshold"`
    BalanceAnomalyThreshold decimal.Decimal `json:"balance_anomaly_threshold"`
}

func (src *SettlementRiskController) ValidateSettlement(settlement *TradeSettlement) error {
    // 1. 检查交易金额
    if err := src.checkTradeValue(settlement); err != nil {
        return err
    }
    
    // 2. 检查日交易量
    if err := src.checkDailyVolume(settlement); err != nil {
        return err
    }
    
    // 3. 检查余额异常
    if err := src.checkBalanceAnomaly(settlement); err != nil {
        return err
    }
    
    // 4. 检查用户风险等级
    if err := src.checkUserRiskLevel(settlement); err != nil {
        return err
    }
    
    return nil
}

func (src *SettlementRiskController) checkTradeValue(settlement *TradeSettlement) error {
    tradeValue := settlement.QuoteAmount
    
    if tradeValue.GreaterThan(src.config.MaxSingleTradeValue) {
        // 发送高额交易告警
        src.alertManager.SendAlert("large_trade", map[string]interface{}{
            "trade_id":    settlement.TradeID,
            "trade_value": tradeValue.String(),
            "buy_user":    settlement.BuyUserID,
            "sell_user":   settlement.SellUserID,
        })
        
        return fmt.Errorf("交易金额超过限制: %s", tradeValue.String())
    }
    
    if tradeValue.GreaterThan(src.config.AlertThresholds.LargeTradeThreshold) {
        // 发送交易监控告警
        src.alertManager.SendAlert("trade_monitoring", map[string]interface{}{
            "trade_id":    settlement.TradeID,
            "trade_value": tradeValue.String(),
        })
    }
    
    return nil
}

func (src *SettlementRiskController) MonitorSettlementHealth() {
    ticker := time.NewTicker(1 * time.Minute)
    defer ticker.Stop()
    
    for range ticker.C {
        // 检查清算队列积压
        queueLength := src.getSettlementQueueLength()
        if queueLength > 1000 {
            src.alertManager.SendAlert("settlement_queue_backlog", map[string]interface{}{
                "queue_length": queueLength,
            })
        }
        
        // 检查清算延迟
        avgLatency := src.getAverageSettlementLatency()
        if avgLatency > 5*time.Second {
            src.alertManager.SendAlert("settlement_high_latency", map[string]interface{}{
                "avg_latency": avgLatency.String(),
            })
        }
        
        // 检查失败率
        failureRate := src.getSettlementFailureRate()
        if failureRate > 0.01 { // 1%
            src.alertManager.SendAlert("settlement_high_failure_rate", map[string]interface{}{
                "failure_rate": failureRate,
            })
        }
    }
}
```

### 5. 清算报表

#### 财务报表生成
```go
type ReportGenerator struct {
    db              *gorm.DB
    templateEngine  *TemplateEngine
    exportService   *ExportService
}

type FinancialReport struct {
    ReportType      string                 `json:"report_type"`
    Period          string                 `json:"period"`
    StartDate       time.Time              `json:"start_date"`
    EndDate         time.Time              `json:"end_date"`
    TradingSummary  TradingSummary         `json:"trading_summary"`
    FeeSummary      FeeSummary             `json:"fee_summary"`
    BalanceSummary  BalanceSummary         `json:"balance_summary"`
    UserStatistics  UserStatistics         `json:"user_statistics"`
    GeneratedAt     time.Time              `json:"generated_at"`
}

type TradingSummary struct {
    TotalTrades     int64           `json:"total_trades"`
    TotalVolume     decimal.Decimal `json:"total_volume"`
    TotalValue      decimal.Decimal `json:"total_value"`
    TopSymbols      []SymbolStats   `json:"top_symbols"`
}

type FeeSummary struct {
    TotalFees       decimal.Decimal `json:"total_fees"`
    MakerFees       decimal.Decimal `json:"maker_fees"`
    TakerFees       decimal.Decimal `json:"taker_fees"`
    FeesBySymbol    []SymbolFees    `json:"fees_by_symbol"`
}

func (rg *ReportGenerator) GenerateDailyReport(date time.Time) (*FinancialReport, error) {
    report := &FinancialReport{
        ReportType:  "daily",
        Period:      date.Format("2006-01-02"),
        StartDate:   date,
        EndDate:     date.Add(24 * time.Hour),
        GeneratedAt: time.Now(),
    }
    
    // 1. 生成交易汇总
    if err := rg.generateTradingSummary(report); err != nil {
        return nil, err
    }
    
    // 2. 生成手续费汇总
    if err := rg.generateFeeSummary(report); err != nil {
        return nil, err
    }
    
    // 3. 生成余额汇总
    if err := rg.generateBalanceSummary(report); err != nil {
        return nil, err
    }
    
    // 4. 生成用户统计
    if err := rg.generateUserStatistics(report); err != nil {
        return nil, err
    }
    
    return report, nil
}

func (rg *ReportGenerator) ExportReport(report *FinancialReport, format string) ([]byte, error) {
    switch format {
    case "pdf":
        return rg.exportService.ExportToPDF(report)
    case "excel":
        return rg.exportService.ExportToExcel(report)
    case "csv":
        return rg.exportService.ExportToCSV(report)
    default:
        return nil, fmt.Errorf("不支持的导出格式: %s", format)
    }
}
```

## API接口

### 清算管理接口
```
GET    /api/v1/settlements              # 获取清算记录
GET    /api/v1/settlements/:id          # 获取清算详情
POST   /api/v1/settlements/reconcile    # 手动对账
GET    /api/v1/settlements/reports      # 获取清算报表
```

### 手续费管理接口
```
GET    /api/v1/fees/rates               # 获取手续费率
PUT    /api/v1/fees/rates               # 更新手续费率
GET    /api/v1/fees/statistics          # 获取手续费统计
```

## 配置说明

### 清算配置
```yaml
settlement:
  enabled: true
  real_time: true
  batch_size: 1000
  batch_interval: 1s
  
  risk_control:
    max_daily_volume: 100000000
    max_single_trade_value: 1000000
    max_balance_deviation: 0.01
  
  reconciliation:
    daily_reconciliation: true
    reconciliation_time: "23:59:00"
    auto_fix_minor_discrepancies: true
    discrepancy_threshold: 0.0001
  
  fees:
    default_maker_rate: 0.001
    default_taker_rate: 0.001
    min_fee: 0.00000001
    max_fee: 1000
```

### 报表配置
```yaml
reports:
  daily_report: true
  weekly_report: true
  monthly_report: true
  
  export_formats:
    - pdf
    - excel
    - csv
  
  auto_email: true
  email_recipients:
    - <EMAIL>
    - <EMAIL>
```

这个清算结算功能文档提供了完整的财务清算系统实现方案，包括实时清算、批量处理、资金对账、风险控制和报表生成等核心功能。
