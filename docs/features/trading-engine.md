# 交易引擎功能

## 功能概述

交易引擎是Spot-Server的核心组件，负责处理所有的交易订单、撮合交易、管理订单簿等关键功能。采用高性能的内存撮合引擎，支持多种订单类型和交易策略。

## 核心功能

### 1. 订单管理

#### 订单类型
- **限价单 (Limit Order)**: 指定价格的买卖订单
- **市价单 (Market Order)**: 按市场最优价格立即成交
- **止损单 (Stop Loss)**: 价格达到止损点时触发的市价单
- **止损限价单 (Stop Loss Limit)**: 价格达到止损点时触发的限价单
- **止盈单 (Take Profit)**: 价格达到止盈点时触发的市价单
- **止盈限价单 (Take Profit Limit)**: 价格达到止盈点时触发的限价单

#### 订单状态
```go
const (
    OrderStatusNew            = "NEW"              // 新建
    OrderStatusPartiallyFilled = "PARTIALLY_FILLED" // 部分成交
    OrderStatusFilled         = "FILLED"           // 完全成交
    OrderStatusCanceled       = "CANCELED"         // 已取消
    OrderStatusRejected       = "REJECTED"         // 已拒绝
    OrderStatusExpired        = "EXPIRED"          // 已过期
)

type Order struct {
    OrderID         string          `json:"order_id"`
    UserID          string          `json:"user_id"`
    ClientOrderID   string          `json:"client_order_id"`
    Symbol          string          `json:"symbol"`
    Side            string          `json:"side"` // "buy" or "sell"
    Type            string          `json:"type"`
    Amount          decimal.Decimal `json:"amount"`
    Price           decimal.Decimal `json:"price"`
    StopPrice       decimal.Decimal `json:"stop_price"`
    FilledAmount    decimal.Decimal `json:"filled_amount"`
    RemainingAmount decimal.Decimal `json:"remaining_amount"`
    AvgPrice        decimal.Decimal `json:"avg_price"`
    Status          string          `json:"status"`
    TimeInForce     string          `json:"time_in_force"`
    Fee             decimal.Decimal `json:"fee"`
    FeeCurrency     string          `json:"fee_currency"`
    Source          string          `json:"source"`
    CreatedAt       time.Time       `json:"created_at"`
    UpdatedAt       time.Time       `json:"updated_at"`
}
```

#### 订单验证
```go
type OrderValidator struct {
    symbolConfig *SymbolConfig
    userBalance  *UserBalance
}

func (v *OrderValidator) ValidateOrder(order *Order) error {
    // 1. 验证交易对
    if !v.symbolConfig.IsActive(order.Symbol) {
        return errors.New("交易对未激活")
    }
    
    // 2. 验证订单数量
    if order.Amount.LessThan(v.symbolConfig.MinAmount) {
        return errors.New("订单数量低于最小限制")
    }
    
    if order.Amount.GreaterThan(v.symbolConfig.MaxAmount) {
        return errors.New("订单数量超过最大限制")
    }
    
    // 3. 验证价格精度
    if !v.validatePricePrecision(order.Price, v.symbolConfig.PricePrecision) {
        return errors.New("价格精度不正确")
    }
    
    // 4. 验证数量精度
    if !v.validateAmountPrecision(order.Amount, v.symbolConfig.AmountPrecision) {
        return errors.New("数量精度不正确")
    }
    
    // 5. 验证最小交易金额
    notional := order.Amount.Mul(order.Price)
    if notional.LessThan(v.symbolConfig.MinNotional) {
        return errors.New("交易金额低于最小限制")
    }
    
    // 6. 验证用户余额
    if err := v.validateBalance(order); err != nil {
        return err
    }
    
    return nil
}

func (v *OrderValidator) validateBalance(order *Order) error {
    if order.Side == "buy" {
        // 买单需要足够的计价货币
        required := order.Amount.Mul(order.Price)
        if v.userBalance.GetAvailable(v.symbolConfig.QuoteCurrency).LessThan(required) {
            return errors.New("余额不足")
        }
    } else {
        // 卖单需要足够的基础货币
        if v.userBalance.GetAvailable(v.symbolConfig.BaseCurrency).LessThan(order.Amount) {
            return errors.New("余额不足")
        }
    }
    
    return nil
}
```

### 2. 撮合引擎

#### 订单簿结构
```go
type OrderBook struct {
    Symbol    string
    Bids      *PriceLevel // 买单队列 (价格从高到低)
    Asks      *PriceLevel // 卖单队列 (价格从低到高)
    LastPrice decimal.Decimal
    mutex     sync.RWMutex
}

type PriceLevel struct {
    Price  decimal.Decimal
    Orders []*Order
    Volume decimal.Decimal
}

func NewOrderBook(symbol string) *OrderBook {
    return &OrderBook{
        Symbol: symbol,
        Bids:   NewPriceLevel(),
        Asks:   NewPriceLevel(),
    }
}

func (ob *OrderBook) AddOrder(order *Order) {
    ob.mutex.Lock()
    defer ob.mutex.Unlock()
    
    if order.Side == "buy" {
        ob.Bids.AddOrder(order)
    } else {
        ob.Asks.AddOrder(order)
    }
}

func (ob *OrderBook) RemoveOrder(orderID string) {
    ob.mutex.Lock()
    defer ob.mutex.Unlock()
    
    ob.Bids.RemoveOrder(orderID)
    ob.Asks.RemoveOrder(orderID)
}
```

#### 撮合算法
```go
type MatchingEngine struct {
    orderBooks map[string]*OrderBook
    tradeQueue chan *Trade
    mutex      sync.RWMutex
}

func (me *MatchingEngine) ProcessOrder(order *Order) ([]*Trade, error) {
    me.mutex.Lock()
    defer me.mutex.Unlock()
    
    orderBook := me.getOrderBook(order.Symbol)
    var trades []*Trade
    
    if order.Type == "market" {
        trades = me.matchMarketOrder(orderBook, order)
    } else if order.Type == "limit" {
        trades = me.matchLimitOrder(orderBook, order)
    }
    
    // 如果订单未完全成交且不是市价单，加入订单簿
    if order.RemainingAmount.GreaterThan(decimal.Zero) && order.Type != "market" {
        orderBook.AddOrder(order)
    }
    
    return trades, nil
}

func (me *MatchingEngine) matchLimitOrder(orderBook *OrderBook, order *Order) []*Trade {
    var trades []*Trade
    
    if order.Side == "buy" {
        // 买单与卖单撮合
        for orderBook.Asks.HasOrders() && order.RemainingAmount.GreaterThan(decimal.Zero) {
            bestAsk := orderBook.Asks.GetBestPrice()
            if order.Price.LessThan(bestAsk) {
                break // 价格不匹配
            }
            
            trade := me.executeTrade(order, orderBook.Asks.GetBestOrder(), bestAsk)
            trades = append(trades, trade)
            
            // 更新订单状态
            me.updateOrderAfterTrade(order, trade)
            me.updateOrderAfterTrade(orderBook.Asks.GetBestOrder(), trade)
        }
    } else {
        // 卖单与买单撮合
        for orderBook.Bids.HasOrders() && order.RemainingAmount.GreaterThan(decimal.Zero) {
            bestBid := orderBook.Bids.GetBestPrice()
            if order.Price.GreaterThan(bestBid) {
                break // 价格不匹配
            }
            
            trade := me.executeTrade(orderBook.Bids.GetBestOrder(), order, bestBid)
            trades = append(trades, trade)
            
            // 更新订单状态
            me.updateOrderAfterTrade(order, trade)
            me.updateOrderAfterTrade(orderBook.Bids.GetBestOrder(), trade)
        }
    }
    
    return trades
}

func (me *MatchingEngine) executeTrade(buyOrder, sellOrder *Order, price decimal.Decimal) *Trade {
    // 计算成交数量 (取两个订单剩余数量的最小值)
    tradeAmount := decimal.Min(buyOrder.RemainingAmount, sellOrder.RemainingAmount)
    
    trade := &Trade{
        TradeID:     me.generateTradeID(),
        Symbol:      buyOrder.Symbol,
        BuyOrderID:  buyOrder.OrderID,
        SellOrderID: sellOrder.OrderID,
        BuyUserID:   buyOrder.UserID,
        SellUserID:  sellOrder.UserID,
        Amount:      tradeAmount,
        Price:       price,
        QuoteAmount: tradeAmount.Mul(price),
        Timestamp:   time.Now(),
    }
    
    // 计算手续费
    trade.BuyFee = me.calculateFee(trade.QuoteAmount, buyOrder.UserID, false)
    trade.SellFee = me.calculateFee(trade.Amount, sellOrder.UserID, true)
    
    return trade
}
```

### 3. 风险控制

#### 风险检查
```go
type RiskManager struct {
    config *RiskConfig
    cache  *redis.Client
}

type RiskConfig struct {
    MaxOrderValue     decimal.Decimal `json:"max_order_value"`
    MaxDailyVolume    decimal.Decimal `json:"max_daily_volume"`
    MaxPositionRatio  decimal.Decimal `json:"max_position_ratio"`
    PriceDeviationMax decimal.Decimal `json:"price_deviation_max"`
}

func (rm *RiskManager) CheckOrderRisk(order *Order) error {
    // 1. 检查订单金额限制
    orderValue := order.Amount.Mul(order.Price)
    if orderValue.GreaterThan(rm.config.MaxOrderValue) {
        return errors.New("订单金额超过限制")
    }
    
    // 2. 检查日交易量限制
    dailyVolume := rm.getUserDailyVolume(order.UserID)
    if dailyVolume.Add(orderValue).GreaterThan(rm.config.MaxDailyVolume) {
        return errors.New("日交易量超过限制")
    }
    
    // 3. 检查价格偏离度
    if err := rm.checkPriceDeviation(order); err != nil {
        return err
    }
    
    // 4. 检查持仓比例
    if err := rm.checkPositionRatio(order); err != nil {
        return err
    }
    
    return nil
}

func (rm *RiskManager) checkPriceDeviation(order *Order) error {
    if order.Type != "limit" {
        return nil
    }
    
    // 获取最新市场价格
    marketPrice := rm.getMarketPrice(order.Symbol)
    if marketPrice.IsZero() {
        return nil // 新交易对没有市场价格
    }
    
    // 计算价格偏离度
    deviation := order.Price.Sub(marketPrice).Div(marketPrice).Abs()
    if deviation.GreaterThan(rm.config.PriceDeviationMax) {
        return errors.New("订单价格偏离市场价格过大")
    }
    
    return nil
}

func (rm *RiskManager) checkPositionRatio(order *Order) error {
    // 获取用户总资产
    totalAssets := rm.getUserTotalAssets(order.UserID)
    if totalAssets.IsZero() {
        return nil
    }
    
    // 计算订单占总资产比例
    orderValue := order.Amount.Mul(order.Price)
    ratio := orderValue.Div(totalAssets)
    
    if ratio.GreaterThan(rm.config.MaxPositionRatio) {
        return errors.New("单笔订单占总资产比例过高")
    }
    
    return nil
}
```

#### 异常监控
```go
type AnomalyDetector struct {
    patterns map[string]*TradingPattern
    alerts   chan *Alert
}

type TradingPattern struct {
    UserID          string
    OrderCount      int64
    TotalVolume     decimal.Decimal
    AvgOrderSize    decimal.Decimal
    PriceRange      decimal.Decimal
    TimeWindow      time.Duration
    LastUpdateTime  time.Time
}

func (ad *AnomalyDetector) DetectAnomaly(order *Order) {
    pattern := ad.getOrCreatePattern(order.UserID)
    pattern.Update(order)
    
    // 检测异常模式
    if ad.isHighFrequencyTrading(pattern) {
        ad.sendAlert("high_frequency_trading", order.UserID)
    }
    
    if ad.isWashTrading(pattern) {
        ad.sendAlert("wash_trading", order.UserID)
    }
    
    if ad.isPumpAndDump(pattern) {
        ad.sendAlert("pump_and_dump", order.UserID)
    }
}

func (ad *AnomalyDetector) isHighFrequencyTrading(pattern *TradingPattern) bool {
    // 检测高频交易：短时间内大量小额订单
    if pattern.TimeWindow < 1*time.Minute && pattern.OrderCount > 100 {
        avgSize := pattern.TotalVolume.Div(decimal.NewFromInt(pattern.OrderCount))
        if avgSize.LessThan(decimal.NewFromFloat(100)) {
            return true
        }
    }
    return false
}
```

### 4. 订单执行

#### 订单处理流程
```go
type OrderProcessor struct {
    validator      *OrderValidator
    matchingEngine *MatchingEngine
    riskManager    *RiskManager
    balanceManager *BalanceManager
    eventPublisher *EventPublisher
}

func (op *OrderProcessor) ProcessOrder(order *Order) error {
    // 1. 订单验证
    if err := op.validator.ValidateOrder(order); err != nil {
        return op.rejectOrder(order, err.Error())
    }
    
    // 2. 风险检查
    if err := op.riskManager.CheckOrderRisk(order); err != nil {
        return op.rejectOrder(order, err.Error())
    }
    
    // 3. 冻结资金
    if err := op.balanceManager.FreezeBalance(order); err != nil {
        return op.rejectOrder(order, err.Error())
    }
    
    // 4. 订单撮合
    trades, err := op.matchingEngine.ProcessOrder(order)
    if err != nil {
        // 撮合失败，解冻资金
        op.balanceManager.UnfreezeBalance(order)
        return op.rejectOrder(order, err.Error())
    }
    
    // 5. 处理成交
    for _, trade := range trades {
        if err := op.processTrade(trade); err != nil {
            log.Error("处理成交失败", zap.Error(err))
        }
    }
    
    // 6. 更新订单状态
    op.updateOrderStatus(order)
    
    // 7. 发布事件
    op.eventPublisher.PublishOrderEvent(order)
    
    return nil
}

func (op *OrderProcessor) processTrade(trade *Trade) error {
    // 1. 更新用户余额
    if err := op.balanceManager.ProcessTrade(trade); err != nil {
        return err
    }
    
    // 2. 记录成交
    if err := op.saveTrade(trade); err != nil {
        return err
    }
    
    // 3. 发布成交事件
    op.eventPublisher.PublishTradeEvent(trade)
    
    return nil
}
```

#### 余额管理
```go
type BalanceManager struct {
    db    *gorm.DB
    cache *redis.Client
}

func (bm *BalanceManager) FreezeBalance(order *Order) error {
    var currency string
    var amount decimal.Decimal
    
    if order.Side == "buy" {
        currency = order.Symbol[strings.Index(order.Symbol, "/")+1:] // 计价货币
        amount = order.Amount.Mul(order.Price)
    } else {
        currency = order.Symbol[:strings.Index(order.Symbol, "/")] // 基础货币
        amount = order.Amount
    }
    
    // 使用数据库事务确保原子性
    return bm.db.Transaction(func(tx *gorm.DB) error {
        var balance UserBalance
        if err := tx.Where("user_id = ? AND currency = ?", order.UserID, currency).
            First(&balance).Error; err != nil {
            return err
        }
        
        if balance.Available.LessThan(amount) {
            return errors.New("余额不足")
        }
        
        // 更新余额
        balance.Available = balance.Available.Sub(amount)
        balance.Frozen = balance.Frozen.Add(amount)
        
        return tx.Save(&balance).Error
    })
}

func (bm *BalanceManager) ProcessTrade(trade *Trade) error {
    return bm.db.Transaction(func(tx *gorm.DB) error {
        // 处理买方余额
        if err := bm.processBuyerBalance(tx, trade); err != nil {
            return err
        }
        
        // 处理卖方余额
        if err := bm.processSellerBalance(tx, trade); err != nil {
            return err
        }
        
        return nil
    })
}

func (bm *BalanceManager) processBuyerBalance(tx *gorm.DB, trade *Trade) error {
    // 买方：减少计价货币冻结，增加基础货币可用
    quoteCurrency := trade.Symbol[strings.Index(trade.Symbol, "/")+1:]
    baseCurrency := trade.Symbol[:strings.Index(trade.Symbol, "/")]
    
    // 减少计价货币冻结
    quoteAmount := trade.QuoteAmount.Add(trade.BuyFee)
    if err := bm.updateBalance(tx, trade.BuyUserID, quoteCurrency, decimal.Zero, quoteAmount.Neg()); err != nil {
        return err
    }
    
    // 增加基础货币可用
    baseAmount := trade.Amount
    if err := bm.updateBalance(tx, trade.BuyUserID, baseCurrency, baseAmount, decimal.Zero); err != nil {
        return err
    }
    
    return nil
}
```

### 5. 性能优化

#### 内存订单簿
```go
type MemoryOrderBook struct {
    symbol    string
    bids      *skiplist.SkipList // 使用跳表实现高效的价格排序
    asks      *skiplist.SkipList
    orderMap  map[string]*Order   // 订单ID到订单的映射
    lastPrice decimal.Decimal
    mutex     sync.RWMutex
}

func NewMemoryOrderBook(symbol string) *MemoryOrderBook {
    return &MemoryOrderBook{
        symbol:   symbol,
        bids:     skiplist.New(skiplist.Desc), // 买单按价格降序
        asks:     skiplist.New(skiplist.Asc),  // 卖单按价格升序
        orderMap: make(map[string]*Order),
    }
}

func (mob *MemoryOrderBook) AddOrder(order *Order) {
    mob.mutex.Lock()
    defer mob.mutex.Unlock()
    
    mob.orderMap[order.OrderID] = order
    
    if order.Side == "buy" {
        mob.bids.Set(order.Price.String(), order)
    } else {
        mob.asks.Set(order.Price.String(), order)
    }
}

func (mob *MemoryOrderBook) GetDepth(levels int) *Depth {
    mob.mutex.RLock()
    defer mob.mutex.RUnlock()
    
    depth := &Depth{
        Symbol: mob.symbol,
        Bids:   make([][]string, 0, levels),
        Asks:   make([][]string, 0, levels),
    }
    
    // 获取买单深度
    bidIter := mob.bids.Iterator()
    for i := 0; i < levels && bidIter.Next(); i++ {
        price := bidIter.Key().(string)
        volume := mob.calculateVolumeAtPrice(mob.bids, price)
        depth.Bids = append(depth.Bids, []string{price, volume.String()})
    }
    
    // 获取卖单深度
    askIter := mob.asks.Iterator()
    for i := 0; i < levels && askIter.Next(); i++ {
        price := askIter.Key().(string)
        volume := mob.calculateVolumeAtPrice(mob.asks, price)
        depth.Asks = append(depth.Asks, []string{price, volume.String()})
    }
    
    return depth
}
```

#### 批量处理
```go
type BatchProcessor struct {
    orderQueue chan *Order
    batchSize  int
    timeout    time.Duration
    processor  *OrderProcessor
}

func (bp *BatchProcessor) Start() {
    go func() {
        batch := make([]*Order, 0, bp.batchSize)
        timer := time.NewTimer(bp.timeout)
        
        for {
            select {
            case order := <-bp.orderQueue:
                batch = append(batch, order)
                
                if len(batch) >= bp.batchSize {
                    bp.processBatch(batch)
                    batch = batch[:0]
                    timer.Reset(bp.timeout)
                }
                
            case <-timer.C:
                if len(batch) > 0 {
                    bp.processBatch(batch)
                    batch = batch[:0]
                }
                timer.Reset(bp.timeout)
            }
        }
    }()
}

func (bp *BatchProcessor) processBatch(orders []*Order) {
    // 并行处理订单
    var wg sync.WaitGroup
    for _, order := range orders {
        wg.Add(1)
        go func(o *Order) {
            defer wg.Done()
            bp.processor.ProcessOrder(o)
        }(order)
    }
    wg.Wait()
}
```

## API接口

### 订单管理接口
```
POST   /api/v1/orders                # 创建订单
GET    /api/v1/orders                # 获取订单列表
GET    /api/v1/orders/:id            # 获取订单详情
DELETE /api/v1/orders/:id            # 取消订单
DELETE /api/v1/orders                # 批量取消订单
```

### 成交查询接口
```
GET    /api/v1/trades               # 获取成交记录
GET    /api/v1/trades/:id           # 获取成交详情
```

### 市场数据接口
```
GET    /api/v1/depth/:symbol        # 获取订单簿深度
GET    /api/v1/ticker/:symbol       # 获取24小时统计
GET    /api/v1/trades/:symbol       # 获取最近成交
```

## 配置说明

### 交易引擎配置
```yaml
trading:
  matching_engine:
    type: "memory"  # memory, redis, database
    batch_size: 100
    batch_timeout: 10ms
  
  risk_management:
    max_order_value: 1000000
    max_daily_volume: 10000000
    max_position_ratio: 0.5
    price_deviation_max: 0.1
  
  order_book:
    depth_levels: 20
    cache_ttl: 1s
    snapshot_interval: 10s
```

### 性能配置
```yaml
performance:
  order_queue_size: 10000
  worker_count: 10
  batch_processing: true
  memory_order_book: true
  
  cache:
    order_book_ttl: 1s
    ticker_ttl: 5s
    depth_ttl: 1s
```

这个交易引擎功能文档提供了完整的交易系统实现方案，包括订单管理、撮合引擎、风险控制等核心功能。
