# 用户管理功能

## 功能概述

用户管理是Spot-Server的核心功能模块，负责用户的注册、认证、授权、资料管理等全生命周期管理。该模块采用现代化的安全设计，支持多种认证方式和精细化的权限控制。

## 核心功能

### 1. 用户注册

#### 注册方式
- **邮箱注册**: 使用邮箱地址注册账户
- **手机号注册**: 使用手机号码注册账户
- **第三方登录**: 支持Google、GitHub等第三方OAuth登录

#### 注册流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as API网关
    participant C as Core Service
    participant D as 数据库
    participant E as 邮件服务

    U->>F: 填写注册信息
    F->>A: POST /api/v1/auth/register
    A->>C: 转发注册请求
    C->>C: 验证输入参数
    C->>D: 检查用户是否存在
    D-->>C: 返回检查结果
    C->>C: 生成用户ID和密码哈希
    C->>D: 创建用户记录
    D-->>C: 返回创建结果
    C->>E: 发送验证邮件
    E-->>C: 发送成功
    C-->>A: 返回注册结果
    A-->>F: 返回响应
    F-->>U: 显示注册成功
```

#### 注册验证
```go
type RegisterRequest struct {
    Username string `json:"username" binding:"required,min=3,max=50"`
    Email    string `json:"email" binding:"required,email"`
    Phone    string `json:"phone" binding:"omitempty,phone"`
    Password string `json:"password" binding:"required,min=8,max=128"`
    Code     string `json:"code" binding:"required,len=6"`
    CodeType string `json:"code_type" binding:"required,oneof=email sms"`
}

func (r *RegisterRequest) Validate() error {
    // 密码强度验证
    if !isStrongPassword(r.Password) {
        return errors.New("密码强度不足")
    }
    
    // 用户名格式验证
    if !isValidUsername(r.Username) {
        return errors.New("用户名格式不正确")
    }
    
    return nil
}

func isStrongPassword(password string) bool {
    // 至少8位，包含大小写字母、数字和特殊字符
    hasUpper := regexp.MustCompile(`[A-Z]`).MatchString(password)
    hasLower := regexp.MustCompile(`[a-z]`).MatchString(password)
    hasNumber := regexp.MustCompile(`[0-9]`).MatchString(password)
    hasSpecial := regexp.MustCompile(`[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]`).MatchString(password)
    
    return len(password) >= 8 && hasUpper && hasLower && hasNumber && hasSpecial
}
```

### 2. 用户认证

#### 认证方式
- **密码认证**: 用户名/邮箱 + 密码
- **双因子认证**: TOTP (Time-based One-Time Password)
- **API Key认证**: 用于程序化访问
- **JWT Token**: 无状态的会话管理

#### 登录流程
```go
type LoginRequest struct {
    Username string `json:"username" binding:"required"`
    Password string `json:"password" binding:"required"`
    TotpCode string `json:"totp_code" binding:"omitempty,len=6"`
    Remember bool   `json:"remember"`
}

type LoginResponse struct {
    AccessToken  string    `json:"access_token"`
    RefreshToken string    `json:"refresh_token"`
    ExpiresAt    time.Time `json:"expires_at"`
    User         UserInfo  `json:"user"`
}

func (s *AuthService) Login(req *LoginRequest) (*LoginResponse, error) {
    // 1. 验证用户凭据
    user, err := s.validateCredentials(req.Username, req.Password)
    if err != nil {
        return nil, err
    }
    
    // 2. 检查账户状态
    if user.Status != "active" {
        return nil, errors.New("账户已被禁用")
    }
    
    // 3. 验证双因子认证
    if user.TwoFactorEnabled {
        if req.TotpCode == "" {
            return nil, errors.New("需要双因子认证码")
        }
        
        if !s.verifyTOTP(user.TwoFactorSecret, req.TotpCode) {
            return nil, errors.New("双因子认证码错误")
        }
    }
    
    // 4. 生成JWT Token
    accessToken, err := s.generateAccessToken(user)
    if err != nil {
        return nil, err
    }
    
    refreshToken, err := s.generateRefreshToken(user)
    if err != nil {
        return nil, err
    }
    
    // 5. 记录登录日志
    s.recordLoginLog(user.UserID, req)
    
    return &LoginResponse{
        AccessToken:  accessToken,
        RefreshToken: refreshToken,
        ExpiresAt:    time.Now().Add(24 * time.Hour),
        User:         s.buildUserInfo(user),
    }, nil
}
```

#### JWT Token管理
```go
type JWTClaims struct {
    UserID   string   `json:"user_id"`
    Username string   `json:"username"`
    Roles    []string `json:"roles"`
    jwt.StandardClaims
}

func (s *AuthService) generateAccessToken(user *User) (string, error) {
    claims := &JWTClaims{
        UserID:   user.UserID,
        Username: user.Username,
        Roles:    user.Roles,
        StandardClaims: jwt.StandardClaims{
            ExpiresAt: time.Now().Add(24 * time.Hour).Unix(),
            IssuedAt:  time.Now().Unix(),
            Issuer:    "spot-server",
            Subject:   user.UserID,
        },
    }
    
    token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
    return token.SignedString([]byte(s.jwtSecret))
}

func (s *AuthService) validateToken(tokenString string) (*JWTClaims, error) {
    token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
        return []byte(s.jwtSecret), nil
    })
    
    if err != nil {
        return nil, err
    }
    
    if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
        return claims, nil
    }
    
    return nil, errors.New("无效的token")
}
```

### 3. 双因子认证 (2FA)

#### TOTP实现
```go
import "github.com/pquerna/otp/totp"

func (s *AuthService) EnableTwoFactor(userID string) (*TwoFactorSetup, error) {
    user, err := s.getUserByID(userID)
    if err != nil {
        return nil, err
    }
    
    // 生成TOTP密钥
    key, err := totp.Generate(totp.GenerateOpts{
        Issuer:      "Spot-Server",
        AccountName: user.Email,
        SecretSize:  32,
    })
    if err != nil {
        return nil, err
    }
    
    // 生成二维码
    qrCode, err := s.generateQRCode(key.URL())
    if err != nil {
        return nil, err
    }
    
    // 临时保存密钥（用户确认后正式启用）
    s.cache.Set(fmt.Sprintf("2fa_setup:%s", userID), key.Secret(), 10*time.Minute)
    
    return &TwoFactorSetup{
        Secret: key.Secret(),
        QRCode: qrCode,
        URL:    key.URL(),
    }, nil
}

func (s *AuthService) ConfirmTwoFactor(userID, code string) error {
    // 获取临时密钥
    secret, err := s.cache.Get(fmt.Sprintf("2fa_setup:%s", userID)).Result()
    if err != nil {
        return errors.New("设置已过期，请重新开始")
    }
    
    // 验证TOTP码
    if !totp.Validate(code, secret) {
        return errors.New("验证码错误")
    }
    
    // 启用双因子认证
    err = s.updateUserTwoFactor(userID, secret, true)
    if err != nil {
        return err
    }
    
    // 清除临时密钥
    s.cache.Del(fmt.Sprintf("2fa_setup:%s", userID))
    
    return nil
}

func (s *AuthService) verifyTOTP(secret, code string) bool {
    return totp.Validate(code, secret)
}
```

### 4. 用户资料管理

#### 基础资料
```go
type UserProfile struct {
    UserID      string    `json:"user_id"`
    Username    string    `json:"username"`
    Email       string    `json:"email"`
    Phone       string    `json:"phone"`
    Nickname    string    `json:"nickname"`
    Avatar      string    `json:"avatar"`
    RealName    string    `json:"real_name"`
    IDCard      string    `json:"id_card"`
    Birthday    time.Time `json:"birthday"`
    Gender      string    `json:"gender"`
    Country     string    `json:"country"`
    Language    string    `json:"language"`
    Timezone    string    `json:"timezone"`
    Currency    string    `json:"currency"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}

func (s *UserService) UpdateProfile(userID string, req *UpdateProfileRequest) error {
    // 验证权限
    if !s.canUpdateProfile(userID, req) {
        return errors.New("无权限修改此资料")
    }
    
    // 验证数据
    if err := req.Validate(); err != nil {
        return err
    }
    
    // 更新数据库
    err := s.db.Model(&UserProfile{}).
        Where("user_id = ?", userID).
        Updates(req.ToMap()).Error
    if err != nil {
        return err
    }
    
    // 清除缓存
    s.cache.Del(fmt.Sprintf("user:profile:%s", userID))
    
    return nil
}
```

#### 头像上传
```go
func (s *UserService) UploadAvatar(userID string, file multipart.File, header *multipart.FileHeader) (string, error) {
    // 验证文件类型
    if !s.isValidImageType(header.Header.Get("Content-Type")) {
        return "", errors.New("不支持的图片格式")
    }
    
    // 验证文件大小 (最大2MB)
    if header.Size > 2*1024*1024 {
        return "", errors.New("文件大小超过限制")
    }
    
    // 生成文件名
    ext := filepath.Ext(header.Filename)
    filename := fmt.Sprintf("avatar/%s/%s%s", userID, uuid.New().String(), ext)
    
    // 上传到对象存储
    url, err := s.storage.Upload(filename, file)
    if err != nil {
        return "", err
    }
    
    // 更新用户头像
    err = s.db.Model(&UserProfile{}).
        Where("user_id = ?", userID).
        Update("avatar", url).Error
    if err != nil {
        return "", err
    }
    
    return url, nil
}
```

### 5. KYC身份认证

#### 认证等级
```go
const (
    KYCLevel0 = 0 // 未认证
    KYCLevel1 = 1 // 基础认证 (姓名+身份证)
    KYCLevel2 = 2 // 高级认证 (人脸识别)
    KYCLevel3 = 3 // 企业认证
)

type KYCRequest struct {
    Level      int    `json:"level" binding:"required,oneof=1 2 3"`
    RealName   string `json:"real_name" binding:"required"`
    IDCard     string `json:"id_card" binding:"required"`
    IDCardFront string `json:"id_card_front"` // 身份证正面照片
    IDCardBack  string `json:"id_card_back"`  // 身份证背面照片
    SelfiePhoto string `json:"selfie_photo"`  // 自拍照片
    CompanyName string `json:"company_name"`  // 企业名称
    BusinessLicense string `json:"business_license"` // 营业执照
}

func (s *KYCService) SubmitKYC(userID string, req *KYCRequest) error {
    // 检查当前KYC状态
    user, err := s.getUserByID(userID)
    if err != nil {
        return err
    }
    
    if user.KYCLevel >= req.Level {
        return errors.New("已通过该等级认证")
    }
    
    // 验证身份证号码
    if !s.validateIDCard(req.IDCard) {
        return errors.New("身份证号码格式错误")
    }
    
    // 创建KYC申请记录
    kycRecord := &KYCRecord{
        UserID:      userID,
        Level:       req.Level,
        RealName:    req.RealName,
        IDCard:      req.IDCard,
        Status:      "pending",
        SubmittedAt: time.Now(),
    }
    
    // 根据等级进行不同的验证
    switch req.Level {
    case KYCLevel1:
        err = s.processLevel1KYC(kycRecord, req)
    case KYCLevel2:
        err = s.processLevel2KYC(kycRecord, req)
    case KYCLevel3:
        err = s.processLevel3KYC(kycRecord, req)
    }
    
    if err != nil {
        return err
    }
    
    // 保存记录
    return s.db.Create(kycRecord).Error
}

func (s *KYCService) processLevel1KYC(record *KYCRecord, req *KYCRequest) error {
    // 身份证OCR识别
    ocrResult, err := s.ocrService.RecognizeIDCard(req.IDCardFront, req.IDCardBack)
    if err != nil {
        return err
    }
    
    // 验证OCR结果与提交信息是否一致
    if ocrResult.Name != req.RealName || ocrResult.IDNumber != req.IDCard {
        return errors.New("身份证信息与提交信息不符")
    }
    
    record.OCRResult = ocrResult
    record.Status = "approved"
    
    return nil
}

func (s *KYCService) processLevel2KYC(record *KYCRecord, req *KYCRequest) error {
    // 先进行Level1认证
    if err := s.processLevel1KYC(record, req); err != nil {
        return err
    }
    
    // 人脸识别验证
    faceResult, err := s.faceService.VerifyFace(req.SelfiePhoto, req.IDCardFront)
    if err != nil {
        return err
    }
    
    if faceResult.Similarity < 0.8 {
        record.Status = "rejected"
        record.RejectReason = "人脸识别相似度不足"
        return errors.New("人脸识别验证失败")
    }
    
    record.FaceResult = faceResult
    
    return nil
}
```

### 6. 权限管理

#### RBAC权限模型
```go
type Role struct {
    ID          int64       `json:"id"`
    Name        string      `json:"name"`
    Description string      `json:"description"`
    Permissions []Permission `json:"permissions" gorm:"many2many:role_permissions;"`
    CreatedAt   time.Time   `json:"created_at"`
    UpdatedAt   time.Time   `json:"updated_at"`
}

type Permission struct {
    ID          int64     `json:"id"`
    Name        string    `json:"name"`
    Resource    string    `json:"resource"`
    Action      string    `json:"action"`
    Description string    `json:"description"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}

type UserRole struct {
    UserID string `json:"user_id"`
    RoleID int64  `json:"role_id"`
}

func (s *AuthService) CheckPermission(userID, resource, action string) bool {
    // 获取用户角色
    roles, err := s.getUserRoles(userID)
    if err != nil {
        return false
    }
    
    // 检查权限
    for _, role := range roles {
        for _, permission := range role.Permissions {
            if permission.Resource == resource && permission.Action == action {
                return true
            }
            
            // 支持通配符权限
            if permission.Resource == "*" || permission.Action == "*" {
                return true
            }
        }
    }
    
    return false
}

// 权限中间件
func (s *AuthService) RequirePermission(resource, action string) gin.HandlerFunc {
    return func(c *gin.Context) {
        userID := c.GetString("user_id")
        if userID == "" {
            c.JSON(401, gin.H{"error": "未登录"})
            c.Abort()
            return
        }
        
        if !s.CheckPermission(userID, resource, action) {
            c.JSON(403, gin.H{"error": "权限不足"})
            c.Abort()
            return
        }
        
        c.Next()
    }
}
```

### 7. 安全功能

#### 登录安全
```go
type SecurityService struct {
    cache    *redis.Client
    db       *gorm.DB
    maxAttempts int
    lockDuration time.Duration
}

func (s *SecurityService) CheckLoginAttempts(username, ip string) error {
    // 检查用户名登录尝试次数
    userKey := fmt.Sprintf("login_attempts:user:%s", username)
    userAttempts, _ := s.cache.Get(userKey).Int()
    
    // 检查IP登录尝试次数
    ipKey := fmt.Sprintf("login_attempts:ip:%s", ip)
    ipAttempts, _ := s.cache.Get(ipKey).Int()
    
    if userAttempts >= s.maxAttempts {
        return errors.New("账户已被锁定，请稍后再试")
    }
    
    if ipAttempts >= s.maxAttempts*3 {
        return errors.New("IP已被限制，请稍后再试")
    }
    
    return nil
}

func (s *SecurityService) RecordFailedLogin(username, ip string) {
    userKey := fmt.Sprintf("login_attempts:user:%s", username)
    ipKey := fmt.Sprintf("login_attempts:ip:%s", ip)
    
    // 增加失败次数
    s.cache.Incr(userKey)
    s.cache.Incr(ipKey)
    
    // 设置过期时间
    s.cache.Expire(userKey, s.lockDuration)
    s.cache.Expire(ipKey, s.lockDuration)
}

func (s *SecurityService) ClearFailedLogin(username, ip string) {
    userKey := fmt.Sprintf("login_attempts:user:%s", username)
    ipKey := fmt.Sprintf("login_attempts:ip:%s", ip)
    
    s.cache.Del(userKey, ipKey)
}
```

#### 设备管理
```go
type UserDevice struct {
    ID           int64     `json:"id"`
    UserID       string    `json:"user_id"`
    DeviceID     string    `json:"device_id"`
    DeviceName   string    `json:"device_name"`
    DeviceType   string    `json:"device_type"`
    OS           string    `json:"os"`
    Browser      string    `json:"browser"`
    IP           string    `json:"ip"`
    Location     string    `json:"location"`
    IsTrusted    bool      `json:"is_trusted"`
    LastActiveAt time.Time `json:"last_active_at"`
    CreatedAt    time.Time `json:"created_at"`
}

func (s *SecurityService) RegisterDevice(userID string, req *DeviceInfo) error {
    device := &UserDevice{
        UserID:       userID,
        DeviceID:     s.generateDeviceID(req),
        DeviceName:   req.DeviceName,
        DeviceType:   req.DeviceType,
        OS:           req.OS,
        Browser:      req.Browser,
        IP:           req.IP,
        Location:     s.getLocationByIP(req.IP),
        IsTrusted:    false,
        LastActiveAt: time.Now(),
        CreatedAt:    time.Now(),
    }
    
    return s.db.Create(device).Error
}

func (s *SecurityService) CheckDeviceTrust(userID, deviceID string) bool {
    var device UserDevice
    err := s.db.Where("user_id = ? AND device_id = ?", userID, deviceID).First(&device).Error
    if err != nil {
        return false
    }
    
    return device.IsTrusted
}
```

## API接口

### 认证相关接口
```
POST   /api/v1/auth/register          # 用户注册
POST   /api/v1/auth/login             # 用户登录
POST   /api/v1/auth/logout            # 用户登出
POST   /api/v1/auth/refresh           # 刷新Token
POST   /api/v1/auth/forgot-password   # 忘记密码
POST   /api/v1/auth/reset-password    # 重置密码
```

### 用户资料接口
```
GET    /api/v1/user/profile           # 获取用户资料
PUT    /api/v1/user/profile           # 更新用户资料
POST   /api/v1/user/avatar            # 上传头像
PUT    /api/v1/user/password          # 修改密码
```

### 安全设置接口
```
POST   /api/v1/user/2fa/enable        # 启用双因子认证
POST   /api/v1/user/2fa/confirm       # 确认双因子认证
POST   /api/v1/user/2fa/disable       # 禁用双因子认证
GET    /api/v1/user/devices           # 获取设备列表
DELETE /api/v1/user/devices/:id       # 删除设备
```

### KYC认证接口
```
POST   /api/v1/user/kyc/submit        # 提交KYC申请
GET    /api/v1/user/kyc/status        # 获取KYC状态
GET    /api/v1/user/kyc/history       # 获取KYC历史
```

## 配置说明

### 安全配置
```yaml
security:
  password:
    min_length: 8
    require_uppercase: true
    require_lowercase: true
    require_numbers: true
    require_symbols: true
  
  login:
    max_attempts: 5
    lock_duration: 30m
    session_timeout: 24h
  
  jwt:
    secret: "your-jwt-secret"
    access_token_ttl: 24h
    refresh_token_ttl: 7d
  
  2fa:
    issuer: "Spot-Server"
    secret_size: 32
    window: 1
```

### 功能开关
```yaml
features:
  registration:
    enabled: true
    email_verification: true
    phone_verification: false
  
  kyc:
    enabled: true
    auto_approve_level1: false
    require_level2_for_trading: true
  
  2fa:
    enabled: true
    required_for_withdrawal: true
    required_for_api: false
```

这个用户管理功能文档提供了完整的用户生命周期管理方案，包括注册、认证、权限、安全等各个方面的详细实现。
