# 运维指南

## 系统监控

### 1. 服务健康检查

#### 健康检查端点
每个服务都提供标准的健康检查端点：

```bash
# 核心服务健康检查
curl http://localhost:8080/health

# 订单服务健康检查
curl http://localhost:8081/health

# 行情服务健康检查
curl http://localhost:8082/health
```

健康检查响应格式：
```json
{
  "status": "healthy",
  "timestamp": "2023-12-01T10:00:00Z",
  "version": "v1.0.0",
  "checks": {
    "database": "healthy",
    "redis": "healthy",
    "mq": "healthy"
  }
}
```

#### 自动化健康检查脚本
```bash
#!/bin/bash
# health-check.sh

SERVICES=(
    "core:8080"
    "order:8081"
    "market:8082"
    "limit:8083"
    "kline:8084"
    "closing:8085"
    "planorder:8086"
    "push:8087"
    "task:8088"
)

for service in "${SERVICES[@]}"; do
    name=${service%:*}
    port=${service#*:}
    
    if curl -f -s http://localhost:$port/health > /dev/null; then
        echo "✓ $name service is healthy"
    else
        echo "✗ $name service is unhealthy"
        # 发送告警
        send_alert "$name service is down"
    fi
done
```

### 2. 性能监控

#### Prometheus 指标
```go
// 在应用中添加监控指标
package metrics

import (
    "github.com/prometheus/client_golang/prometheus"
    "github.com/prometheus/client_golang/prometheus/promauto"
)

var (
    // HTTP请求计数器
    httpRequestsTotal = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "http_requests_total",
            Help: "Total number of HTTP requests",
        },
        []string{"method", "endpoint", "status"},
    )
    
    // HTTP请求延迟
    httpRequestDuration = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "http_request_duration_seconds",
            Help: "HTTP request duration in seconds",
        },
        []string{"method", "endpoint"},
    )
    
    // 订单处理指标
    orderProcessed = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "orders_processed_total",
            Help: "Total number of orders processed",
        },
        []string{"symbol", "side", "status"},
    )
    
    // 数据库连接池指标
    dbConnections = promauto.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "db_connections",
            Help: "Number of database connections",
        },
        []string{"state"},
    )
)

// 记录HTTP请求
func RecordHTTPRequest(method, endpoint, status string, duration float64) {
    httpRequestsTotal.WithLabelValues(method, endpoint, status).Inc()
    httpRequestDuration.WithLabelValues(method, endpoint).Observe(duration)
}
```

#### Grafana 仪表板配置
```json
{
  "dashboard": {
    "title": "Spot Server Monitoring",
    "panels": [
      {
        "title": "HTTP Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{method}} {{endpoint}}"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "Order Processing Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(orders_processed_total[5m])",
            "legendFormat": "{{symbol}} {{side}}"
          }
        ]
      }
    ]
  }
}
```

### 3. 告警配置

#### Prometheus 告警规则
```yaml
# alerts.yml
groups:
- name: spot-server-alerts
  rules:
  - alert: ServiceDown
    expr: up == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Service {{ $labels.instance }} is down"
      description: "Service {{ $labels.instance }} has been down for more than 1 minute"

  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "High error rate on {{ $labels.instance }}"
      description: "Error rate is {{ $value }} errors per second"

  - alert: HighResponseTime
    expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High response time on {{ $labels.instance }}"
      description: "95th percentile response time is {{ $value }} seconds"

  - alert: DatabaseConnectionHigh
    expr: db_connections{state="active"} / db_connections{state="total"} > 0.8
    for: 3m
    labels:
      severity: warning
    annotations:
      summary: "Database connection usage is high"
      description: "Database connection usage is {{ $value }}%"
```

#### AlertManager 配置
```yaml
# alertmanager.yml
global:
  smtp_smarthost: 'smtp.gmail.com:587'
  smtp_from: '<EMAIL>'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'

receivers:
- name: 'web.hook'
  email_configs:
  - to: '<EMAIL>'
    subject: 'Spot Server Alert: {{ .GroupLabels.alertname }}'
    body: |
      {{ range .Alerts }}
      Alert: {{ .Annotations.summary }}
      Description: {{ .Annotations.description }}
      {{ end }}
  
  webhook_configs:
  - url: 'http://slack-webhook-url'
    send_resolved: true
```

## 日志管理

### 1. 日志格式标准化

#### 结构化日志配置
```go
// 日志配置
package log

import (
    "go.uber.org/zap"
    "go.uber.org/zap/zapcore"
)

func InitLogger(level string, filename string) *zap.Logger {
    config := zap.NewProductionConfig()
    config.Level = zap.NewAtomicLevelAt(getLogLevel(level))
    config.OutputPaths = []string{filename, "stdout"}
    config.ErrorOutputPaths = []string{filename, "stderr"}
    
    // 自定义编码器
    config.EncoderConfig = zapcore.EncoderConfig{
        TimeKey:        "timestamp",
        LevelKey:       "level",
        NameKey:        "logger",
        CallerKey:      "caller",
        MessageKey:     "message",
        StacktraceKey:  "stacktrace",
        LineEnding:     zapcore.DefaultLineEnding,
        EncodeLevel:    zapcore.LowercaseLevelEncoder,
        EncodeTime:     zapcore.ISO8601TimeEncoder,
        EncodeDuration: zapcore.SecondsDurationEncoder,
        EncodeCaller:   zapcore.ShortCallerEncoder,
    }
    
    logger, _ := config.Build()
    return logger
}

// 业务日志记录
func LogOrderEvent(logger *zap.Logger, orderID, userID, symbol, side, status string, amount, price float64) {
    logger.Info("order_event",
        zap.String("event_type", "order"),
        zap.String("order_id", orderID),
        zap.String("user_id", userID),
        zap.String("symbol", symbol),
        zap.String("side", side),
        zap.String("status", status),
        zap.Float64("amount", amount),
        zap.Float64("price", price),
        zap.Time("timestamp", time.Now()),
    )
}
```

### 2. 日志收集和分析

#### ELK Stack 配置

##### Filebeat 配置
```yaml
# filebeat.yml
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /var/log/spot-server/*.log
  fields:
    service: spot-server
  fields_under_root: true
  multiline.pattern: '^\d{4}-\d{2}-\d{2}'
  multiline.negate: true
  multiline.match: after

output.elasticsearch:
  hosts: ["elasticsearch:9200"]
  index: "spot-server-logs-%{+yyyy.MM.dd}"

processors:
- add_host_metadata:
    when.not.contains.tags: forwarded
```

##### Logstash 配置
```ruby
# logstash.conf
input {
  beats {
    port => 5044
  }
}

filter {
  if [service] == "spot-server" {
    json {
      source => "message"
    }
    
    date {
      match => [ "timestamp", "ISO8601" ]
    }
    
    if [event_type] == "order" {
      mutate {
        add_tag => ["order_event"]
      }
    }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "spot-server-logs-%{+YYYY.MM.dd}"
  }
}
```

##### Kibana 仪表板
```json
{
  "version": "7.15.0",
  "objects": [
    {
      "id": "spot-server-dashboard",
      "type": "dashboard",
      "attributes": {
        "title": "Spot Server Logs Dashboard",
        "panelsJSON": "[{\"version\":\"7.15.0\",\"gridData\":{\"x\":0,\"y\":0,\"w\":24,\"h\":15},\"panelIndex\":\"1\",\"embeddableConfig\":{},\"panelRefName\":\"panel_1\"}]"
      }
    }
  ]
}
```

### 3. 日志轮转和清理

#### Logrotate 配置
```bash
# /etc/logrotate.d/spot-server
/var/log/spot-server/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 spot-server spot-server
    postrotate
        /bin/kill -USR1 $(cat /var/run/spot-server.pid 2>/dev/null) 2>/dev/null || true
    endscript
}
```

#### 自动清理脚本
```bash
#!/bin/bash
# log-cleanup.sh

LOG_DIR="/var/log/spot-server"
RETENTION_DAYS=30

# 删除超过保留期的日志文件
find $LOG_DIR -name "*.log.gz" -mtime +$RETENTION_DAYS -delete

# 删除超过保留期的错误日志
find $LOG_DIR -name "error*.log" -mtime +$RETENTION_DAYS -delete

echo "Log cleanup completed. Removed files older than $RETENTION_DAYS days."
```

## 性能调优

### 1. 应用层优化

#### Go 应用优化
```go
// 连接池优化
func InitDB() *sql.DB {
    db, err := sql.Open("mysql", dsn)
    if err != nil {
        log.Fatal(err)
    }
    
    // 连接池配置
    db.SetMaxOpenConns(100)        // 最大连接数
    db.SetMaxIdleConns(20)         // 最大空闲连接数
    db.SetConnMaxLifetime(time.Hour) // 连接最大生存时间
    
    return db
}

// Redis 连接池优化
func InitRedis() *redis.Client {
    return redis.NewClient(&redis.Options{
        Addr:         "localhost:6379",
        PoolSize:     50,              // 连接池大小
        MinIdleConns: 10,              // 最小空闲连接数
        MaxRetries:   3,               // 最大重试次数
        DialTimeout:  5 * time.Second, // 连接超时
        ReadTimeout:  3 * time.Second, // 读取超时
        WriteTimeout: 3 * time.Second, // 写入超时
    })
}

// 内存优化
func OptimizeGC() {
    // 设置GC目标百分比
    debug.SetGCPercent(100)
    
    // 设置内存限制
    debug.SetMemoryLimit(8 << 30) // 8GB
}
```

#### 缓存策略优化
```go
// 多级缓存
type CacheManager struct {
    l1Cache *sync.Map          // 本地缓存
    l2Cache *redis.Client      // Redis缓存
    l3Cache *sql.DB           // 数据库
}

func (c *CacheManager) Get(key string) (interface{}, error) {
    // L1缓存查找
    if value, ok := c.l1Cache.Load(key); ok {
        return value, nil
    }
    
    // L2缓存查找
    value, err := c.l2Cache.Get(key).Result()
    if err == nil {
        // 写入L1缓存
        c.l1Cache.Store(key, value)
        return value, nil
    }
    
    // L3数据库查找
    value, err = c.queryFromDB(key)
    if err != nil {
        return nil, err
    }
    
    // 写入缓存
    c.l2Cache.Set(key, value, time.Hour)
    c.l1Cache.Store(key, value)
    
    return value, nil
}
```

### 2. 数据库优化

#### MySQL 优化配置
```ini
# my.cnf
[mysqld]
# 基础配置
max_connections = 1000
max_connect_errors = 10000
table_open_cache = 2048
max_allowed_packet = 16M

# InnoDB 配置
innodb_buffer_pool_size = 4G
innodb_log_file_size = 256M
innodb_log_buffer_size = 16M
innodb_flush_log_at_trx_commit = 2
innodb_file_per_table = 1

# 查询缓存
query_cache_type = 1
query_cache_size = 256M
query_cache_limit = 2M

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
```

#### 索引优化
```sql
-- 订单表索引优化
CREATE INDEX idx_user_symbol_status ON orders(user_id, symbol, status);
CREATE INDEX idx_symbol_side_price ON orders(symbol, side, price);
CREATE INDEX idx_created_at ON orders(created_at);

-- 成交表索引优化
CREATE INDEX idx_order_id ON trades(order_id);
CREATE INDEX idx_symbol_timestamp ON trades(symbol, timestamp);

-- 分区表优化
ALTER TABLE trades PARTITION BY RANGE (UNIX_TIMESTAMP(timestamp)) (
    PARTITION p202301 VALUES LESS THAN (UNIX_TIMESTAMP('2023-02-01')),
    PARTITION p202302 VALUES LESS THAN (UNIX_TIMESTAMP('2023-03-01')),
    PARTITION p202303 VALUES LESS THAN (UNIX_TIMESTAMP('2023-04-01'))
);
```

### 3. Redis 优化

#### Redis 配置优化
```conf
# redis.conf
# 内存配置
maxmemory 4gb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

# 网络配置
tcp-keepalive 300
timeout 0

# 客户端配置
maxclients 10000

# 慢日志配置
slowlog-log-slower-than 10000
slowlog-max-len 128
```

#### Redis 集群配置
```bash
# 启动Redis集群
redis-server --port 7000 --cluster-enabled yes --cluster-config-file nodes-7000.conf
redis-server --port 7001 --cluster-enabled yes --cluster-config-file nodes-7001.conf
redis-server --port 7002 --cluster-enabled yes --cluster-config-file nodes-7002.conf

# 创建集群
redis-cli --cluster create 127.0.0.1:7000 127.0.0.1:7001 127.0.0.1:7002 --cluster-replicas 0
```

## 故障排查

### 1. 常见问题诊断

#### 服务无响应
```bash
# 检查进程状态
ps aux | grep spot-server

# 检查端口占用
netstat -tlnp | grep :8080

# 检查系统资源
top
free -h
df -h

# 检查网络连接
ss -tulpn | grep :8080
```

#### 数据库连接问题
```bash
# 检查MySQL状态
systemctl status mysql

# 检查连接数
mysql -e "SHOW STATUS LIKE 'Threads_connected';"

# 检查慢查询
mysql -e "SHOW VARIABLES LIKE 'slow_query_log';"

# 分析慢查询日志
mysqldumpslow /var/log/mysql/slow.log
```

#### Redis 连接问题
```bash
# 检查Redis状态
redis-cli ping

# 检查连接数
redis-cli info clients

# 检查内存使用
redis-cli info memory

# 检查慢查询
redis-cli slowlog get 10
```

### 2. 性能问题排查

#### CPU 使用率高
```bash
# 查看CPU使用情况
top -p $(pgrep spot-server)

# 使用pprof分析
go tool pprof http://localhost:6060/debug/pprof/profile

# 查看goroutine
go tool pprof http://localhost:6060/debug/pprof/goroutine
```

#### 内存泄漏排查
```bash
# 内存使用分析
go tool pprof http://localhost:6060/debug/pprof/heap

# 查看内存分配
go tool pprof http://localhost:6060/debug/pprof/allocs
```

#### 网络延迟问题
```bash
# 网络延迟测试
ping target-host

# 路由跟踪
traceroute target-host

# 网络连接状态
ss -i
```

### 3. 应急处理流程

#### 服务故障处理
```bash
#!/bin/bash
# emergency-restart.sh

SERVICE_NAME="spot-server"
LOG_FILE="/var/log/spot-server/emergency.log"

echo "$(date): Emergency restart initiated" >> $LOG_FILE

# 停止服务
systemctl stop $SERVICE_NAME

# 等待进程完全停止
sleep 10

# 检查进程是否还在运行
if pgrep $SERVICE_NAME > /dev/null; then
    echo "$(date): Force killing process" >> $LOG_FILE
    pkill -9 $SERVICE_NAME
fi

# 清理临时文件
rm -f /tmp/spot-server-*

# 启动服务
systemctl start $SERVICE_NAME

# 检查服务状态
if systemctl is-active --quiet $SERVICE_NAME; then
    echo "$(date): Service restarted successfully" >> $LOG_FILE
else
    echo "$(date): Service restart failed" >> $LOG_FILE
    exit 1
fi
```

#### 数据库故障切换
```bash
#!/bin/bash
# db-failover.sh

MASTER_HOST="db-master"
SLAVE_HOST="db-slave"
CONFIG_FILE="/etc/spot-server/config.yaml"

# 检查主库状态
if ! mysql -h $MASTER_HOST -e "SELECT 1" > /dev/null 2>&1; then
    echo "Master database is down, switching to slave"
    
    # 更新配置文件
    sed -i "s/host: $MASTER_HOST/host: $SLAVE_HOST/g" $CONFIG_FILE
    
    # 重启应用服务
    systemctl restart spot-server
    
    # 发送告警
    send_alert "Database failover completed"
fi
```

## 备份和恢复

### 1. 数据备份策略

#### 自动备份脚本
```bash
#!/bin/bash
# backup-all.sh

BACKUP_DIR="/backup/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# 数据库备份
mysqldump --single-transaction --routines --triggers spot_server > $BACKUP_DIR/database.sql

# Redis备份
redis-cli BGSAVE
cp /var/lib/redis/dump.rdb $BACKUP_DIR/

# 配置文件备份
tar -czf $BACKUP_DIR/config.tar.gz /etc/spot-server/

# 日志备份
tar -czf $BACKUP_DIR/logs.tar.gz /var/log/spot-server/

# 上传到云存储
aws s3 cp $BACKUP_DIR s3://backup-bucket/spot-server/ --recursive

echo "Backup completed: $BACKUP_DIR"
```

### 2. 灾难恢复

#### 恢复流程
```bash
#!/bin/bash
# disaster-recovery.sh

BACKUP_DATE=$1
BACKUP_DIR="/backup/$BACKUP_DATE"

if [ ! -d "$BACKUP_DIR" ]; then
    echo "Backup directory not found: $BACKUP_DIR"
    exit 1
fi

# 停止所有服务
systemctl stop spot-server

# 恢复数据库
mysql spot_server < $BACKUP_DIR/database.sql

# 恢复Redis
systemctl stop redis
cp $BACKUP_DIR/dump.rdb /var/lib/redis/
systemctl start redis

# 恢复配置文件
tar -xzf $BACKUP_DIR/config.tar.gz -C /

# 启动服务
systemctl start spot-server

echo "Disaster recovery completed from backup: $BACKUP_DATE"
```
