package address

import (
	"errors"

	"github.com/btcsuite/btcutil/base58"
)

var VERSION_BYTE = map[string]map[string]byte{
	"bitcoincash": {"P2PKH": 0, "P2SH": 5},
	"bchtest":     {"P2PKH": 111, "P2SH": 196},
}

func ConvertBchToLegacyAddress(bchAddress string) (string, error) {
	valid, prefix, values := isBchAddress(bchAddress)
	if !valid {
		return "", errors.New("illegal bch address")
	}

	data, err := convertBits(values, 5, 8, false)
	if err != nil {
		return "", err
	}
	if len(data) != 21 {
		return "", errors.New("incorrect data length")
	}

	tp := ""
	switch data[0] {
	case 0x00:
		tp = "P2PKH"
	case 0x08:
		tp = "P2SH"
	}

	if tp != "P2PKH" && tp != "P2SH" {
		return "", errors.New("illegal bch address type")
	}

	if prefix != "bitcoincash" && prefix != "bchtest" {
		return "", errors.New("illegal bch address prefix")
	}

	var versionByte = VERSION_BYTE[prefix][tp]
	return base58.CheckEncode(data[1:21], versionByte), nil
}

func convertBits(data []byte, fromBits uint, tobits uint, pad bool) ([]byte, error) {
	// General power-of-2 base conversion.
	var uintArr []uint
	for _, i := range data {
		uintArr = append(uintArr, uint(i))
	}
	acc := uint(0)
	bits := uint(0)
	var ret []uint
	maxv := uint((1 << tobits) - 1)
	maxAcc := uint((1 << (fromBits + tobits - 1)) - 1)
	for _, value := range uintArr {
		acc = ((acc << fromBits) | value) & maxAcc
		bits += fromBits
		for bits >= tobits {
			bits -= tobits
			ret = append(ret, (acc>>bits)&maxv)
		}
	}
	if pad {
		if bits > 0 {
			ret = append(ret, (acc<<(tobits-bits))&maxv)
		}
	} else if bits >= fromBits || ((acc<<(tobits-bits))&maxv) != 0 {
		return []byte{}, errors.New("encoding padding error")
	}
	var dataArr []byte
	for _, i := range ret {
		dataArr = append(dataArr, byte(i))
	}
	return dataArr, nil
}
