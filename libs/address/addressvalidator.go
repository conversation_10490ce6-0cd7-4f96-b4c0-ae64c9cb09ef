package address

import (
	"bytes"
	"crypto/sha256"
	"encoding/binary"
	"encoding/hex"
	"errors"
	"math/big"
	"regexp"
	"strings"

	"github.com/btcsuite/btcutil/base58"
	"golang.org/x/crypto/ripemd160"
)

//bitcoin like coins-----------------------------------------------------------
var bitcoinLikeAddressVersions = map[string]map[string][]string{"btc": {"main": {"00", "05"}, "test": {"6F", "C4"}},
	"bch":  {"main": {"00", "05"}, "test": {"6F", "C4"}},
	"bcx":  {"main": {"4B", "3F"}, "test": {"41", "C4"}},
	"bcd":  {"main": {"00", "05"}, "test": {"6F", "C4"}},
	"btg":  {"main": {"26", "17"}, "test": {"6F", "C4"}},
	"sbtc": {"main": {"00", "05"}, "test": {"6F", "C4"}},
	"ltc":  {"main": {"30", "05"}, "test": {"6F", "C4"}},
	"doge": {"main": {"1E", "16"}, "test": {"71", "C4"}},
	"dash": {"main": {"4C", "10"}, "test": {"8C", "13"}},
	"ppc":  {"main": {"37", "75"}, "test": {"6F", "C4"}},
	"nmc":  {"main": {"34", "0D"}, "test": {"6F", "C4"}}}

func typeOf(addr string) (bool, string) {

	r, errRegx := regexp.Compile(`[^1-9A-HJ-NP-Za-km-z]`)

	if errRegx != nil || len(r.FindStringSubmatch(addr)) > 0 {
		return false, ""
	}

	decoded := decodeAddress(addr)

	if len(decoded) != 50 {
		return false, ""
	}

	check := decoded[:len(decoded)-8]

	hexCheck, _ := hex.DecodeString(check)

	buf := new(bytes.Buffer)
	binary.Write(buf, binary.LittleEndian, hexCheck)

	check = strings.ToUpper(hex.EncodeToString(sha256Bytes(sha256Bytes(buf.Bytes()))))[:8]

	version := decoded[:2]

	return check == decoded[len(decoded)-8:], version
}

func sha256Bytes(bytesToSha []byte) []byte {

	buf := new(bytes.Buffer)
	binary.Write(buf, binary.LittleEndian, sha256.Sum256(bytesToSha))

	return buf.Bytes()
}

func isValidBitcoinLikeAddress(addr string, coin string, version string) bool {
	if coin == "bch" && (strings.HasPrefix(addr, "bitcoincash:") || strings.HasPrefix(addr, "bchtest:")) {
		if version == "main" && !strings.HasPrefix(addr, "bitcoincash:") {
			return false
		} else if version == "test" && !strings.HasPrefix(addr, "bchtest:") {
			return false
		}
		valid, _, _ := isBchAddress(addr)
		return valid
	}

	valid, versionAddr := typeOf(addr)

	if !valid {
		return false
	}

	if version != "main" && version != "test" {
		version = "main"
	}

	return inSlice(versionAddr, bitcoinLikeAddressVersions[coin][version])
}

func inSlice(versionAddr string, valids []string) bool {
	for _, element := range valids {
		if versionAddr == element {
			return true
		}
	}

	return false
}

func decodeAddress(data string) string {
	charsetB58 := "**********************************************************"

	raw := new(big.Int)
	current := new(big.Int)
	for i := 0; i < len(data); i++ {
		raw.Mul(raw, new(big.Int).SetInt64(int64(58)))
		raw.Add(raw, current.SetInt64(int64(strings.Index(charsetB58, data[i:i+1]))))
	}

	charsetHex := "0123456789ABCDEF"
	hexa := ""
	dv := new(big.Int)
	rem := new(big.Int)
	sixteen := new(big.Int).SetInt64(int64(16))
	for {
		dv.DivMod(raw, sixteen, rem)
		hexa += charsetHex[int(rem.Int64()) : int(rem.Int64())+1]

		raw = dv
		if raw.Sign() <= 0 {
			break
		}
	}

	withPadding := strrev(hexa)
	for i := 0; i < len(data) && data[i:i+1] == "1"; i++ {
		withPadding = "00" + withPadding
	}

	if len(withPadding)%2 != 0 {
		withPadding = "0" + withPadding
	}

	return withPadding
}

func strrev(s string) string {
	runes := []rune(s)
	for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
		runes[i], runes[j] = runes[j], runes[i]
	}

	return string(runes)
}

//bch-----------------------------------------------------------
/**
 * The cashaddr character set for decoding.
 */
var CHARSET_REV = [128]int8{
	-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
	-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
	-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, -1, 10, 17, 21, 20, 26, 30, 7,
	5, -1, -1, -1, -1, -1, -1, -1, 29, -1, 24, 13, 25, 9, 8, 23, -1, 18, 22,
	31, 27, 19, -1, 1, 0, 3, 16, 11, 28, 12, 14, 6, 4, 2, -1, -1, -1, -1,
	-1, -1, 29, -1, 24, 13, 25, 9, 8, 23, -1, 18, 22, 31, 27, 19, -1, 1, 0,
	3, 16, 11, 28, 12, 14, 6, 4, 2, -1, -1, -1, -1, -1,
}

/**
 * Concatenate two byte arrays.
 */
func Cat(x, y []byte) []byte {
	return append(x, y...)
}

/**
 * This function will compute what 8 5-bit values to XOR into the last 8 input
 * values, in order to make the checksum 0. These 8 values are packed together
 * in a single 40-bit integer. The higher bits correspond to earlier values.
 */
func PolyMod(v []byte) uint64 {
	/**
	 * The input is interpreted as a list of coefficients of a polynomial over F
	 * = GF(32), with an implicit 1 in front. If the input is [v0,v1,v2,v3,v4],
	 * that polynomial is v(x) = 1*x^5 + v0*x^4 + v1*x^3 + v2*x^2 + v3*x + v4.
	 * The implicit 1 guarantees that [v0,v1,v2,...] has a distinct checksum
	 * from [0,v0,v1,v2,...].
	 *
	 * The output is a 40-bit integer whose 5-bit groups are the coefficients of
	 * the remainder of v(x) mod g(x), where g(x) is the cashaddr generator, x^8
	 * + {19}*x^7 + {3}*x^6 + {25}*x^5 + {11}*x^4 + {25}*x^3 + {3}*x^2 + {19}*x
	 * + {1}. g(x) is chosen in such a way that the resulting code is a BCH
	 * code, guaranteeing detection of up to 4 errors within a window of 1025
	 * characters. Among the various possible BCH codes, one was selected to in
	 * fact guarantee detection of up to 5 errors within a window of 160
	 * characters and 6 erros within a window of 126 characters. In addition,
	 * the code guarantee the detection of a burst of up to 8 errors.
	 *
	 * Note that the coefficients are elements of GF(32), here represented as
	 * decimal numbers between {}. In this finite field, addition is just XOR of
	 * the corresponding numbers. For example, {27} + {13} = {27 ^ 13} = {22}.
	 * Multiplication is more complicated, and requires treating the bits of
	 * values themselves as coefficients of a polynomial over a smaller field,
	 * GF(2), and multiplying those polynomials mod a^5 + a^3 + 1. For example,
	 * {5} * {26} = (a^2 + 1) * (a^4 + a^3 + a) = (a^4 + a^3 + a) * a^2 + (a^4 +
	 * a^3 + a) = a^6 + a^5 + a^4 + a = a^3 + 1 (mod a^5 + a^3 + 1) = {9}.
	 *
	 * During the course of the loop below, `c` contains the bitpacked
	 * coefficients of the polynomial constructed from just the values of v that
	 * were processed so far, mod g(x). In the above example, `c` initially
	 * corresponds to 1 mod (x), and after processing 2 inputs of v, it
	 * corresponds to x^2 + v0*x + v1 mod g(x). As 1 mod g(x) = 1, that is the
	 * starting value for `c`.
	 */
	c := uint64(1)
	for _, d := range v {
		/**
		 * We want to update `c` to correspond to a polynomial with one extra
		 * term. If the initial value of `c` consists of the coefficients of
		 * c(x) = f(x) mod g(x), we modify it to correspond to
		 * c'(x) = (f(x) * x + d) mod g(x), where d is the next input to
		 * process.
		 *
		 * Simplifying:
		 * c'(x) = (f(x) * x + d) mod g(x)
		 *         ((f(x) mod g(x)) * x + d) mod g(x)
		 *         (c(x) * x + d) mod g(x)
		 * If c(x) = c0*x^5 + c1*x^4 + c2*x^3 + c3*x^2 + c4*x + c5, we want to
		 * compute
		 * c'(x) = (c0*x^5 + c1*x^4 + c2*x^3 + c3*x^2 + c4*x + c5) * x + d
		 *                                                             mod g(x)
		 *       = c0*x^6 + c1*x^5 + c2*x^4 + c3*x^3 + c4*x^2 + c5*x + d
		 *                                                             mod g(x)
		 *       = c0*(x^6 mod g(x)) + c1*x^5 + c2*x^4 + c3*x^3 + c4*x^2 +
		 *                                                             c5*x + d
		 * If we call (x^6 mod g(x)) = k(x), this can be written as
		 * c'(x) = (c1*x^5 + c2*x^4 + c3*x^3 + c4*x^2 + c5*x + d) + c0*k(x)
		 */

		// First, determine the value of c0:
		c0 := byte(c >> 35)

		// Then compute c1*x^5 + c2*x^4 + c3*x^3 + c4*x^2 + c5*x + d:
		c = ((c & 0x07ffffffff) << 5) ^ uint64(d)

		// Finally, for each set bit n in c0, conditionally add {2^n}k(x):
		if c0&0x01 > 0 {
			// k(x) = {19}*x^7 + {3}*x^6 + {25}*x^5 + {11}*x^4 + {25}*x^3 +
			//        {3}*x^2 + {19}*x + {1}
			c ^= 0x98f2bc8e61
		}

		if c0&0x02 > 0 {
			// {2}k(x) = {15}*x^7 + {6}*x^6 + {27}*x^5 + {22}*x^4 + {27}*x^3 +
			//           {6}*x^2 + {15}*x + {2}
			c ^= 0x79b76d99e2
		}

		if c0&0x04 > 0 {
			// {4}k(x) = {30}*x^7 + {12}*x^6 + {31}*x^5 + {5}*x^4 + {31}*x^3 +
			//           {12}*x^2 + {30}*x + {4}
			c ^= 0xf33e5fb3c4
		}

		if c0&0x08 > 0 {
			// {8}k(x) = {21}*x^7 + {24}*x^6 + {23}*x^5 + {10}*x^4 + {23}*x^3 +
			//           {24}*x^2 + {21}*x + {8}
			c ^= 0xae2eabe2a8
		}

		if c0&0x10 > 0 {
			// {16}k(x) = {3}*x^7 + {25}*x^6 + {7}*x^5 + {20}*x^4 + {7}*x^3 +
			//            {25}*x^2 + {3}*x + {16}
			c ^= 0x1e4f43e470
		}
	}

	/**
	 * PolyMod computes what value to xor into the final values to make the
	 * checksum 0. However, if we required that the checksum was 0, it would be
	 * the case that appending a 0 to a valid list of values would result in a
	 * new valid list. For that reason, cashaddr requires the resulting checksum
	 * to be 1 instead.
	 */
	return c ^ 1
}

/**
 * Convert to lower case.
 *
 * Assume the input is a character.
 */
func LowerCase(c byte) byte {
	// ASCII black magic.
	return c | 0x20
}

/**
 * Expand the address prefix for the checksum computation.
 */
func ExpandPrefix(prefix string) []byte {
	ret := make([]byte, len(prefix)+1)
	for i := 0; i < len(prefix); i++ {
		ret[i] = byte(prefix[i]) & 0x1f
	}

	ret[len(prefix)] = 0
	return ret
}

/**
 * Verify a checksum.
 */
func VerifyChecksum(prefix string, payload []byte) bool {
	return PolyMod(Cat(ExpandPrefix(prefix), payload)) == 0
}

func isBchAddress(str string) (bool, string, []byte) {
	// Go over the string and do some sanity checks.
	lower, upper := false, false
	prefixSize := 0
	for i := 0; i < len(str); i++ {
		c := byte(str[i])
		if c >= 'a' && c <= 'z' {
			lower = true
			continue
		}

		if c >= 'A' && c <= 'Z' {
			upper = true
			continue
		}

		if c >= '0' && c <= '9' {
			// We cannot have numbers in the prefix.
			if prefixSize == 0 {
				return false, "", nil
			}

			continue
		}

		if c == ':' {
			// The separator must not be the first character, and there must not
			// be 2 separators.
			if i == 0 || prefixSize != 0 {
				return false, "", nil
			}

			prefixSize = i
			continue
		}

		// We have an unexpected character.
		return false, "", nil
	}

	// We must have a prefix and a data part and we can't have both uppercase
	// and lowercase.
	if prefixSize == 0 {
		return false, "", nil
	}

	if upper && lower {
		return false, "", nil
	}

	// Get the prefix.
	var prefix string
	for i := 0; i < prefixSize; i++ {
		prefix += string(LowerCase(str[i]))
	}

	// Decode values.
	valuesSize := len(str) - 1 - prefixSize
	values := make([]byte, valuesSize)
	for i := 0; i < valuesSize; i++ {
		c := byte(str[i+prefixSize+1])
		// We have an invalid char in there.
		if c > 127 || CHARSET_REV[c] == -1 {
			return false, "", nil
		}

		values[i] = byte(CHARSET_REV[c])
	}

	// Verify the checksum.
	if !VerifyChecksum(prefix, values) {
		return false, "", nil
	}

	return true, prefix, values[:len(values)-8]
}

//bitcoin like coins check-----------------------------------------------------------
func isBitcoinLikeAddress(coin string) bool {
	_, ok := bitcoinLikeAddressVersions[coin]
	if !ok {
		return false
	}
	return true
}

//eth-----------------------------------------------------------

const AddressLength = 20

// hasHexPrefix validates str begins with '0x' or '0X'.
func hasHexPrefix(str string) bool {
	return len(str) >= 2 && str[0] == '0' && (str[1] == 'x' || str[1] == 'X')
}

// isHexCharacter returns bool of c being a valid hexadecimal.
func isHexCharacter(c byte) bool {
	return ('0' <= c && c <= '9') || ('a' <= c && c <= 'f') || ('A' <= c && c <= 'F')
}

// isHex validates whether each byte is valid hexadecimal string.
func isHex(str string) bool {
	if len(str)%2 != 0 {
		return false
	}
	for _, c := range []byte(str) {
		if !isHexCharacter(c) {
			return false
		}
	}
	return true
}

func IsEthAddress(s string) bool {
	if hasHexPrefix(s) {
		s = s[2:]
	}
	return len(s) == 2*AddressLength && isHex(s)
}

//zec

type zecNetID struct {
	AddressPubKeyHash []byte
	AddressScriptHash []byte
	ZAddress          []byte
}

var zecNetIDs = map[string]zecNetID{
	"main": {[]byte{0x1c, 0xb8}, []byte{0x1c, 0xbd}, []byte{0x16, 0x9a}},
	"test": {[]byte{0x1d, 0x25}, []byte{0x1c, 0xba}, []byte{0x16, 0xb6}},
}

func checksum(input []byte) (cksum [4]byte) {
	h := sha256.Sum256(input)
	h2 := sha256.Sum256(h[:])
	copy(cksum[:], h2[:4])
	return
}

func checkDecode(input string) (result []byte, version []byte, err error) {
	decoded := base58.Decode(input)
	if len(decoded) < 5 {
		return nil, nil, errors.New("checksum mismatch")
	}
	version = append(version, decoded[0:2]...)
	var cksum [4]byte
	copy(cksum[:], decoded[len(decoded)-4:])
	if checksum(decoded[:len(decoded)-4]) != cksum {
		return nil, nil, errors.New("invalid format")
	}
	payload := decoded[2 : len(decoded)-4]
	result = append(result, payload...)
	return
}

func IsValidZECAddress(addr string, version string) bool {
	checkID, ok := zecNetIDs[version]
	if !ok {
		return false
	}

	// Switch on decoded length to determine the type.
	decoded, netID, err := checkDecode(addr)
	if err != nil {
		return false
	}
	switch len(decoded) {
	case ripemd160.Size: // P2PKH or P2SH
		isP2PKH := bytes.Equal(netID, checkID.AddressPubKeyHash)
		isP2SH := bytes.Equal(netID, checkID.AddressScriptHash)
		if isP2PKH && isP2SH {
			return false
		} else if isP2PKH || isP2SH {
			return true
		}
		return false

	default:
		if bytes.Equal(netID, checkID.ZAddress) {
			return true
		}
		return false
	}
}

//lsk-----------------------------------------------------------
func IsValidLSKAddress(v string) bool {
	re := regexp.MustCompile("^\\d+L$")
	return re.MatchString(v)
}

//main check-----------------------------------------------------------
func CheckAddress(addr string, coin string, version string, shareAddress string) bool {
	if isBitcoinLikeAddress(coin) {
		return isValidBitcoinLikeAddress(addr, coin, version)
	}
	if coin == "eth" || shareAddress == "eth" || coin == "qb" {
		return IsEthAddress(addr)
	}
	if coin == "lsk" {
		return IsValidLSKAddress(addr)
	}
	if coin == "zec" {
		return IsValidZECAddress(addr, version)
	}
	return true
}
