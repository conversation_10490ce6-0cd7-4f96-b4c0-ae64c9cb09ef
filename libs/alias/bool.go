package alias

import (
	"fmt"
	"reflect"
	"strconv"
)

// NumBool 重新实现bool,可用于数据库实例结构体,非0为真
type NumBool bool

func (nb NumBool) Bool() bool {
	return nb == true
}

func (nb NumBool) String() string {
	return strconv.FormatBool(bool(nb))
}

// MarshalJSON implements the json.Marshaler interface.
func (nb NumBool) MarshalJSON() ([]byte, error) {
	if nb {
		return []byte("true"), nil
	}
	return []byte("false"), nil
}

// UnmarshalJSON implements the json.Unmarshaler interface.
func (nb *NumBool) UnmarshalJSON(data []byte) error {
	// Ignore null, like in the main JSON package.
	if string(data) == "null" {
		return nil
	}
	b, err := strconv.ParseBool(string(data))
	if err != nil {
		// 尝试转为数字,非0为真
		n, e := strconv.ParseFloat(string(data), 64)
		if e != nil {
			// 返回无法转换为bool的错误
			return err
		}
		b = n != 0
	}

	*nb = NumBool(b)
	return nil
}

// <PERSON><PERSON> implements the Scanner interface.
func (nb *NumBool) Scan(value any) error {
	if value == nil {
		return nil
	}

	var vs string
	switch v := value.(type) {
	case int, uint, int8, uint8, int16, uint16, int32, uint32, int64, uint64, float32, float64:
		// 判断是否是该类型的0值,如果是,则是false,反之为true
		*nb = reflect.DeepEqual(v, reflect.Zero(reflect.TypeOf(v)).Interface()) != true
		return nil
	case string:
		vs = v
	case []byte:
		vs = string(v)
	default:
		return fmt.Errorf("sql/driver: couldn't convert %v (%T) into type bool", value, value)
	}

	// 字符串类型处理
	f, err := strconv.ParseFloat(vs, 64)
	if err != nil {
		b, err := strconv.ParseBool(vs)
		if err != nil {
			return fmt.Errorf("sql/driver: couldn't convert string %q into type bool", vs)
		}
		*nb = NumBool(b)
		return nil
	}
	*nb = f != 0
	return nil
}
