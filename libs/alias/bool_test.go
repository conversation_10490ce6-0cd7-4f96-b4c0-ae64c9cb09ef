package alias

import (
	"encoding/json"
	"testing"
)

func TestNumBool_String(t *testing.T) {
	list := []NumBool{
		true,
		false,
	}

	for _, nb := range list {
		t.Log(nb.String())
	}
}

func TestNumBool_String2(t *testing.T) {
	list := []struct{
		State NumBool `json:"state"`
	}{
		{State: true},
		{State: false},
	}

	for _, nb := range list {
		t.Logf("%+v", nb)
	}
}

func TestNumBool_MarshalJSON(t *testing.T) {
	list := []struct{
		State NumBool `json:"state"`
	}{
		{State: true},
		{State: false},
	}

	for _, nb := range list {
		b, err := json.Marshal(&nb)
		t.Logf("%s, %v", b, err)
	}
}

func TestNumBool_MarshalJSON2(t *testing.T) {
	list := []struct{
		State NumBool `json:"state,omitempty"`
	}{
		{State: true},
		{State: false},
	}

	for _, nb := range list {
		b, err := json.Marshal(&nb)
		t.Logf("%s, %v", b, err)
	}
}

func TestNumBool_UnmarshalJSON(t *testing.T) {
	list := [][]byte{
		[]byte(`{"state":true}`),
		[]byte(`{"state":false}`),
		[]byte(`{"state":0}`),
		[]byte(`{"state":10000}`),
		[]byte(`{"state":-1}`),
		[]byte(`{"state":123.123}`),
	}

	type Demo struct{
		State NumBool `json:"state"`
	}

	for _, b := range list {
		var nb Demo
		err := json.Unmarshal(b, &nb)
		t.Logf("%+v, %v", nb, err)
	}
}

func TestNumBool_Scan(t *testing.T) {
	list := []any{
		true,
		false,
		uint(100),
		int8(10),
		int16(-23),
		0,
		"false",
		"true",
		[]byte("true"),
		[]byte("false"),
		12.345,
	}

	for _, x := range list {
		var nb NumBool
		err := nb.Scan(x)
		t.Logf("%v -> %s, %v", x, nb, err)
	}
}