package alias

import (
	"strconv"
	"time"
)

type UnixTime time.Time

func (c UnixTime) String() string {
	return time.Time(c).String()
}

// MarshalJSON implements the json.Marshaler interface.
func (c UnixTime) MarshalJSON() ([]byte, error) {
	var b [10]byte
	strconv.AppendInt(b[:0], time.Time(c).Unix(), 10)
	return b[0:], nil
}

// UnmarshalJSON implements the json.Unmarshaler interface.
func (c *UnixTime) UnmarshalJSON(data []byte) error {
	// Ignore null, like in the main JSON package.
	if string(data) == "null" {
		return nil
	}
	n, e := strconv.ParseInt(string(data), 10, 64)
	if e != nil {
		return e
	}
	*c = UnixTime(time.Unix(n, 0))
	return nil
}