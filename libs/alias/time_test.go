package alias

import (
	"encoding/json"
	"testing"
	"time"
)

func TestUnixTime_String(t *testing.T) {
	ut := UnixTime(time.Now())
	t.Logf("%s", ut)
}

func TestUnixTime_MarshalJSON(t *testing.T) {
	type Demo struct {
		ID int `json:"id"`
		Ts UnixTime `json:"ts"`
	}

	ut := Demo{
		ID: 100,
		Ts: UnixTime(time.Now()),
	}

	b, err := json.Marshal(&ut)
	t.Logf("%s, %v", b, err)
}

func TestUnixTime_UnmarshalJSON(t *testing.T) {
	b := `{"id":100,"ts":1653374988}`

	type Demo struct {
		ID int `json:"id"`
		Ts UnixTime `json:"ts"`
	}
	var ut Demo
	err := json.Unmarshal([]byte(b), &ut)
	t.Logf("%+v, %v", ut, err)
}