package biance

import (
	"fmt"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"net/http"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/proto"
	"spot/libs/utils"
	"strings"
	"time"
)

//https://api.binance.com/api/v3/ticker/price
//现货价格

//{"symbol":"GASBTC","price":"0.00004750"}
const (
	bApi     = "https://api.binance.com/api/v3/"
	depthUrl = bApi + "depth?symbol=%v&limit=%v"
	url      = bApi + "ticker/price"
	_name    = "binance"
)

func GetTickers(codes map[string]struct{}) (list []proto.MarketTrade) {
	b, err := utils.GetUrl(nil, url)
	//log.Info(string(b))
	var tickers []tick
	err = json.Unmarshal(b, &tickers)
	if err != nil {
		return
	}
	if len(tickers) == 0 {
		return
	}

	for _, v := range tickers {
		if _, ok := codes[v.Code]; ok {
			mk := proto.MarketTrade{
				Symbol:   v.Code,
				DealTime: time.Now().Unix(),
				Price:    v.Price,
				Ts:       time.Now(),
				Source:   _name,
			}
			list = append(list, mk)
		}
	}
	return
}

//{"symbol":"GASBTC","price":"0.00004750"}
type tick struct {
	Code  string          `json:"symbol"`
	Price decimal.Decimal `json:"price"`
}

//默认 100; 最大 5000. 可选值:[5, 10, 20, 50, 100, 500, 1000, 5000]
func GetSpotDepth(client *http.Client, code string, count int) (data *ResultDepth) {
	code = strings.Replace(code, "/", "", -1)
	u := fmt.Sprintf(depthUrl, code, count)
	log.Info(u)
	b, err := utils.GetUrl(client, u)
	if err != nil {
		log.Error("biance get depth fail", zap.Error(err), zap.String("code", code))
		fmt.Errorf("%v", err)
		return
	}
	d := new(ResultDepth)
	err = json.Unmarshal(b, d)
	if err != nil {
		log.Error("GetSpotDepth json unmarshal fail", zap.Error(err), zap.Error(err), zap.String("code", code))
		return
	}
	data = d
	return
}
