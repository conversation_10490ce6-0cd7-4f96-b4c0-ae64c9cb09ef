/*
@Time : 2019-12-17 19:06
<AUTHOR> mocha
@File : client_test
*/
package biance

import (
	"fmt"
	"github.com/gorilla/websocket"
	"io/ioutil"
	"spot/libs/convert"
	"testing"
)

func TestClient(te *testing.T) {
	w, rsp, err := websocket.DefaultDialer.Dial(_wsMarket, nil)
	if err != nil {
		fmt.Println(err)
	}
	b, err := ioutil.ReadAll(rsp.Body)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(convert.Bytes2Str(b))

	go func() {
		for {
			w.SetPingHandler(func(appData string) error {
				//te.Log(appData)
				w.WriteMessage(websocket.PongMessage, nil)
				return nil
			})
			t, b, err := w.ReadMessage()
			//d, _ := zip.UnzipByte(b)
			te.Log(t, convert.Bytes2Str(b), err)
		}
	}()

	go func() {
		str := `{"method": "SUBSCRIBE","params":["aaveusdt@trade"],"id": 1}`
		w.WriteMessage(websocket.TextMessage, convert.Str2Bytes(str))

		//time.Sleep(1 * time.Minute)
		//t = model.SubSymbolArg{ContractCode: "eth/usdt"}
		//goto RE
	}()
	select {}

}
