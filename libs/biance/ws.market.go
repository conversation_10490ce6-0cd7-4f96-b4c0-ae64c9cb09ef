/*
@Time : 2/7/20 3:31 下午
<AUTHOR> mocha
@File : main
*/
package biance

import (
	"context"
	"encoding/json"
	"math/rand"

	"github.com/gorilla/websocket"
	"go.uber.org/zap"
	"spot/libs/log"
	"spot/libs/wsc"
)

const (
	_wsMarket = "wss://stream.binance.com:9443/ws/stream"
	_sub      = "SUBSCRIBE"
	_unSub    = "UNSUBSCRIBE"
)

//entries

type MarketWsClient struct {
	cm         *wsc.ClientManager
	wsPoint    string
	topics     []string
	msgHandler func([]byte)

	ctx    context.Context
	cancel context.CancelFunc
}

type Config struct {
	WsPoint     string
	dialer      *websocket.Dialer
	timeSeconds int64
}

func (c *MarketWsClient) dealMessage(message wsc.Message) {
	if message.Err != nil {
		log.Error("收到错误消息", zap.Any("data", message))
		return
	}

	if c.msgHandler != nil {
		c.msgHandler(message.Msg)
	}

}

func NewMarketWsClient(config *Config, f func([]byte)) *MarketWsClient {
	c := &MarketWsClient{
		wsPoint:    _wsMarket,
		topics:     nil,
		msgHandler: f,
	}
	cm := wsc.New(config.WsPoint, c.dealMessage, c.RestartSub)
	cm.Start()
	c.cm = cm
	return c
}

func (c *MarketWsClient) RestartSub() {
	log.Info("币安开始重新订阅合约数据", zap.Any("data", c.topics))
	c.Subscript(c.topics)
}

func (c *MarketWsClient) Subscript(topics []string) {
	if c == nil {
		return
	}
	log.Infof("sub topics:%v", topics)
	c.topics = topics
	if c.cancel != nil {
		c.cancel()
	}

	c.ctx, c.cancel = context.WithCancel(context.Background())
	ctx := c.ctx

	d := BasicData{Method: _sub, Params: c.topics, Id: rand.Int63n(10) + 1}
	b, err := json.Marshal(d)
	if err != nil {
		log.Errorf("Marshal fail,%v", err)
		return
	}
	c.cm.Send(ctx, b)
}

func (c *MarketWsClient) unSubscript() {
	if c == nil {
		return
	}
	log.Infof("huobi unSub topics:%v", c.topics)

	ctx := c.ctx

	d := BasicData{Method: _unSub, Params: c.topics}
	b, err := json.Marshal(d)
	if err != nil {
		log.Errorf("Marshal fail,%v", err)
		return
	}
	c.cm.Send(ctx, b)
}

//{
//"method": "SUBSCRIBE",
//"params":
//[
//"btcusdt@aggTrade",
//"btcusdt@depth"
//],
//"id": 1
//}
type BasicData struct {
	Method string   `json:"method"`
	Params []string `json:"params"`
	Id     int64    `json:"id"`
}

type ResultPayload struct {
	Event  string `json:"e"`
	Time   int64  `json:"E"`
	Symbol string `json:"s"`
}

//{
//"e": "trade",     // 事件类型
//"E": 123456789,   // 事件时间
//"s": "BNBBTC",    // 交易对
//"t": 12345,       // 交易ID
//"p": "0.001",     // 成交价格
//"q": "100",       // 成交数量
//"b": 88,          // 买方的订单ID
//"a": 50,          // 卖方的订单ID
//"T": 123456785,   // 成交时间
//"m": true,        // 买方是否是做市方。如true，则此次成交是一个主动卖出单，否则是一个主动买入单。
//"M": true         // 请忽略该字段
//}
type ResultTicker struct {
	ResultPayload
	AId      int64  `json:"a"` //归集Id
	Price    string `json:"p"` //成交价格
	Amount   string `json:"q"` //成交数量
	DealTime int64  `json:"T"` //成交时间
	MType    bool   `json:"m"`
	M        bool   `json:"M"`
}

type ResultDepth struct {
	LastUpdateID int64      `json:"lastUpdateId"`
	Bids         [][]string `json:"bids"`
	Asks         [][]string `json:"asks"`
}
