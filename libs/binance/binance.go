package binance

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"github.com/gorilla/websocket"
)

const (
	Url       = "https://www.binance.com"
	TestUrl   = "https://testnet.binance.vision"
	WsAddress = "wss://stream.binance.com/ws"
	//WsAddress     = "wss://stream.binance.com:9443/ws"
	TestWsAddress = "wss://testnet.binance.vision/ws"
)

type BinanceClient struct {
	client          *client
	dialer          *websocket.Dialer
	multiWs         *MarketWsClient
	privateCallFunc func([]byte)
}

func NewBinanceClient(url, ws, apikey, secret string) *BinanceClient {
	return &BinanceClient{
		client: &client{
			window: 5000,
			apikey: apikey,
			secret: secret,
			client: http.DefaultClient,
			url:    url,
			ws:     ws,
		},
		dialer: websocket.DefaultDialer,
	}
}

func NewBinanceClientWindow(apikey, secret string, window int) (*BinanceClient, error) {
	if window <= 0 {
		return nil, fmt.Errorf("window value is invalid")
	}
	return &BinanceClient{
		client: &client{
			window: window,
			apikey: apikey,
			secret: secret,
			client: http.DefaultClient,
		},
		dialer: websocket.DefaultDialer,
	}, nil
}

// General endpoints

// Ping tests connectivity to the Rest API
func (client *BinanceClient) Ping() error {
	_, err := client.client.do(http.MethodGet, "api/v1/ping", nil, false, false)
	return err
}

// Time tests connectivity to the Rest API and get the current server time
func (client *BinanceClient) Time() (*ServerTime, error) {
	res, err := client.client.do(http.MethodGet, "api/v1/time", nil, false, false)
	if err != nil {
		return nil, err
	}
	serverTime := &ServerTime{}
	return serverTime, json.Unmarshal(res, serverTime)
}

// Market Data endpoints
// Depth retrieves the order book for the given symbol
func (client *BinanceClient) Depth(opts *DepthOpts) (*Depth, error) {
	if opts == nil {
		return nil, fmt.Errorf("opts is nil")
	}
	if opts.Limit == 0 || opts.Limit > 100 {
		opts.Limit = 100
	}
	res, err := client.client.do(http.MethodGet, "api/v1/depth", opts, false, false)
	if err != nil {
		return nil, err
	}
	depth := &Depth{}
	return depth, json.Unmarshal(res, &depth)
}

// AggregatedTrades gets compressed, aggregate trades.
// Trades that fill at the time, from the same order, with the same price will have the quantity aggregated
// Remark: If both startTime and endTime are sent, limit should not be sent AND the distance between startTime and endTime must be less than 24 hours.
// Remark: If frondId, startTime, and endTime are not sent, the most recent aggregate trades will be returned.
func (client *BinanceClient) AggregatedTrades(opts *AggregatedTradeOpts) ([]*AggregatedTrade, error) {
	if opts == nil {
		return nil, fmt.Errorf("opts is nil")
	}
	if opts.Limit == 0 || opts.Limit > 500 {
		opts.Limit = 500
	}
	res, err := client.client.do(http.MethodGet, "api/v1/aggTrades", opts, false, false)
	if err != nil {
		return nil, err
	}
	trades := []*AggregatedTrade{}
	return trades, json.Unmarshal(res, &trades)
}

// Klines returns kline/candlestick bars for a symbol. Klines are uniquely identified by their open time
func (client *BinanceClient) Klines(opts *KlinesOpts) ([]*Klines, error) {
	if opts == nil {
		return nil, fmt.Errorf("opts is nil")
	}
	if opts.Symbol == "" || opts.Interval == "" {
		return nil, fmt.Errorf("symbol or interval are missing")
	}
	if opts.Limit == 0 || opts.Limit > 500 {
		opts.Limit = 500
	}
	res, err := client.client.do(http.MethodGet, "api/v1/klines", opts, false, false)
	if err != nil {
		return nil, err
	}
	klines := []*Klines{}
	return klines, json.Unmarshal(res, &klines)

}

// Ticker returns 24 hour price change statistics
func (client *BinanceClient) Ticker(opts *TickerOpts) (*TickerStats, error) {
	if opts == nil {
		return nil, fmt.Errorf("opts is nil")
	}
	res, err := client.client.do(http.MethodGet, "api/v1/ticker/24hr", opts, false, false)
	if err != nil {
		return nil, err
	}
	tickerStats := &TickerStats{}
	return tickerStats, json.Unmarshal(res, tickerStats)
}

// Prices calculates the latest price for all symbols
func (client *BinanceClient) Prices() ([]*SymbolPrice, error) {
	res, err := client.client.do(http.MethodGet, "api/v1/ticker/allPrices", nil, false, false)
	if err != nil {
		return nil, err
	}
	prices := []*SymbolPrice{}
	return prices, json.Unmarshal(res, &prices)
}

// AllBookTickers returns best price/qty on the order book for all symbols
func (client *BinanceClient) AllBookTickers() ([]*BookTicker, error) {
	res, err := client.client.do(http.MethodGet, "api/v1/ticker/allBookTickers", nil, false, false)
	if err != nil {
		return nil, err
	}
	resp := []*BookTicker{}
	return resp, json.Unmarshal(res, &resp)
}

// Signed endpoints, associated with an account

// NewOrder sends in a new order
func (client *BinanceClient) NewOrder(opts *NewOrderOpts) (*NewOrder, error) {
	if opts == nil {
		return nil, fmt.Errorf("opts is nil")
	}
	res, err := client.client.do(http.MethodPost, "api/v3/order", opts, true, false)
	if err != nil {
		return nil, err
	}
	resp := &NewOrder{}
	return resp, json.Unmarshal(res, resp)
}

// NewOrderTest tests new order creation and signature/recvWindow long. Creates and validates a new order but does not send it into the matching engine
func (client *BinanceClient) NewOrderTest(opts *NewOrderOpts) error {
	if opts == nil {
		return fmt.Errorf("opts is nil")
	}
	_, err := client.client.do(http.MethodPost, "api/v3/order/test", opts, true, false)
	return err
}

// QueryOrder checks an order's status
func (client *BinanceClient) QueryOrder(opts *QueryOrderOpts) (*QueryOrder, error) {
	if opts == nil {
		return nil, fmt.Errorf("opts is nil")
	}
	if opts.OrderID < 0 && opts.OrigClientOrderId == "" {
		return nil, fmt.Errorf("order id must be set")
	}
	res, err := client.client.do(http.MethodGet, "api/v3/order", opts, true, false)
	if err != nil {
		return nil, err
	}
	resp := &QueryOrder{}
	return resp, json.Unmarshal(res, resp)
}

// CancelOrder cancel an active order
func (client *BinanceClient) CancelOrder(opts *CancelOrderOpts) (*CancelOrder, error) {
	if opts == nil {
		return nil, fmt.Errorf("opts is nil")
	}
	if opts.OrderID < 0 {
		return nil, fmt.Errorf("order id must be set")
	}
	res, err := client.client.do(http.MethodDelete, "api/v3/order", opts, true, false)
	if err != nil {
		return nil, err
	}
	resp := &CancelOrder{}
	return resp, json.Unmarshal(res, resp)
}

// OpenOrders get all open orders on a symbol
func (client *BinanceClient) OpenOrders(opts *OpenOrdersOpts) ([]*QueryOrder, error) {
	if opts == nil {
		return nil, fmt.Errorf("opts is nil")
	}
	res, err := client.client.do(http.MethodGet, "api/v3/openOrders", opts, true, false)
	if err != nil {
		return nil, err
	}
	resp := []*QueryOrder{}
	return resp, json.Unmarshal(res, &resp)
}

// AllOrders get all account orders; active, canceled, or filled
func (client *BinanceClient) AllOrders(opts *AllOrdersOpts) ([]*QueryOrder, error) {
	if opts == nil {
		return nil, fmt.Errorf("opts is nil")
	}
	if opts.Limit == 0 {
		opts.Limit = 500
	}
	res, err := client.client.do(http.MethodGet, "api/v3/allOrders", opts, true, false)
	if err != nil {
		return nil, err
	}
	resp := []*QueryOrder{}
	return resp, json.Unmarshal(res, &resp)
}

// Account get current account information
func (client *BinanceClient) Account() (*AccountInfo, error) {
	res, err := client.client.do(http.MethodGet, "api/v3/account", nil, true, false)
	if err != nil {
		return nil, err
	}
	resp := &AccountInfo{}
	return resp, json.Unmarshal(res, &resp)
}

// Trades get trades for a specific account and symbol
func (client *BinanceClient) Trades(opts *TradesOpts) (*Trades, error) {
	if opts == nil {
		return nil, fmt.Errorf("opts is nil")
	}
	if opts.Limit == 0 || opts.Limit > 500 {
		opts.Limit = 500
	}
	res, err := client.client.do(http.MethodGet, "api/v3/myTrades", opts, true, false)
	if err != nil {
		return nil, err
	}
	resp := &Trades{}
	return resp, json.Unmarshal(res, &resp)
}

func (client *BinanceClient) ExchangeInfo() (*ExchangeInfo, error) {
	res, err := client.client.do(http.MethodGet, "api/v1/exchangeInfo", nil, false, false)
	if err != nil {
		return nil, err
	}
	resp := &ExchangeInfo{}
	return resp, json.Unmarshal(res, &resp)
}

// User stream endpoint

// Datastream starts a new user datastream
func (client *BinanceClient) DataStream() (string, error) {
	res, err := client.client.do(http.MethodPost, "api/v1/userDataStream", nil, false, true)
	if err != nil {
		return "", err
	}

	resp := &Datastream{}
	return resp.ListenKey, json.Unmarshal(res, &resp)
}

// DataStreamKeepAlive pings the datastream key to prevent timeout
func (client *BinanceClient) DataStreamKeepAlive(listenKey string) error {
	_, err := client.client.do(http.MethodPut, "api/v1/userDataStream", Datastream{ListenKey: listenKey}, false, true)
	return err
}

// DataStreamClose closes the datastream key
func (client *BinanceClient) DataStreamClose(listenKey string) error {
	_, err := client.client.do(http.MethodDelete, "api/v1/userDataStream", Datastream{ListenKey: listenKey}, false, true)
	return err
}

// DepthWS opens websocket with depth updates for the given symbol
func (client *BinanceClient) DepthWS(symbol string) (*DepthWS, error) {
	addr := strings.ToLower(symbol) + "@depth"
	fmt.Println(client.client.ws + "/" + addr)
	conn, _, err := client.dialer.Dial(client.client.ws+"/"+addr, nil)
	if err != nil {
		return nil, err
	}
	return &DepthWS{wsWrapper{conn: conn}}, nil
}

// DepthLevelWS opens websocket with depth updates for the given symbol
func (client *BinanceClient) DepthLevelWS(symbol, level string) (*DepthLevelWS, error) {
	addr := strings.ToLower(symbol) + "/" + "@depth" + level
	conn, _, err := client.dialer.Dial(client.client.ws+addr, nil)
	if err != nil {
		return nil, err
	}
	return &DepthLevelWS{wsWrapper{conn: conn}}, nil
}

// AllMarketTickerWS opens websocket with with single depth summary for all tickers
func (client *BinanceClient) AllMarketTickerWS() (*AllMarketTickerWS, error) {
	addr := "!ticker@arr"
	conn, _, err := client.dialer.Dial(client.client.ws+addr, nil)
	if err != nil {
		return nil, err
	}
	return &AllMarketTickerWS{wsWrapper{conn: conn}}, nil
}

// IndivTickerWS opens websocket with with single depth summary for all tickers
func (client *BinanceClient) IndivTickerWS(symbol string) (*IndivTickerWS, error) {
	addr := strings.ToLower(symbol) + "/" + "@ticker"
	conn, _, err := client.dialer.Dial(client.client.ws+addr, nil)
	if err != nil {
		return nil, err
	}
	return &IndivTickerWS{wsWrapper{conn: conn}}, nil
}

// KlinesWS opens websocket with klines updates for the given symbol with the given interval
func (client *BinanceClient) KlinesWS(symbol string, interval KlineInterval) (*KlinesWS, error) {
	addr := fmt.Sprintf("%s@kline_%s", strings.ToLower(symbol), interval)
	conn, _, err := client.dialer.Dial(client.client.ws+"/"+addr, nil)
	if err != nil {
		return nil, err
	}
	return &KlinesWS{wsWrapper{conn: conn}}, nil
}

// TradesWS opens websocket with trades updates for the given symbol
func (client *BinanceClient) TradesWS(symbol string) (*TradesWS, error) {
	addr := strings.ToLower(symbol) + "/" + "@aggTrade"
	conn, _, err := client.dialer.Dial(client.client.ws+addr, nil)
	if err != nil {
		return nil, err
	}
	return &TradesWS{wsWrapper{conn: conn}}, nil
}

// AccountInfoWS opens websocket with account info updates
func (client *BinanceClient) AccountInfoWS(listenKey string) (*AccountInfoWS, error) {
	conn, _, err := client.dialer.Dial(client.client.ws+"/"+listenKey, nil)
	if err != nil {
		return nil, err
	}
	return &AccountInfoWS{wsWrapper{conn: conn}}, nil
}

//多路复用market
func (client *BinanceClient) NewMultiWS(f func([]byte)) error {
	client.multiWs = NewMarketWsClient(&Config{
		WsPoint: client.client.ws,
	}, f)
	client.multiWs.Start()
	client.multiWs.Loop()
	return nil
}

func (client *BinanceClient) SubWsSymbol(list []string) {
	client.multiWs.Subscript(list)
}

func (client *BinanceClient) UnSubWsSymbol(list []string) {
	client.multiWs.UnSubscript(list)
}

func (client *BinanceClient) StartMarketWs(f func([]byte)) {
	client.NewMultiWS(f)
}
