package binance

import (
	"context"
	"spot/libs/log"
	"time"
)

type PrivateWs struct {
}

func (client *BinanceClient) StatPrivateWs(f func(b []byte)) {
	client.privateCallFunc = f
	go func() {
		client.NewPrivateWs()
	}()
}

func (client *BinanceClient) NewPrivateWs() {
	log.Infof("binance begin private ws")
	var err error
	defer func() {
		if err != nil {
			client.NewPrivateWs()
		}
	}()
	ctx, cancel := context.WithCancel(context.Background())
	key, err := client.DataStream()
	if err != nil {
		log.Errorf("binance get private data stream fail,%v", err)
		return
	}

	tick := time.Tick(20 * time.Minute)
	defer client.DataStreamClose(key)
	conn, err := client.AccountInfoWS(key)
	if err != nil {
		log.Errorf("biance get account info for ws fail,%v", err)
		return
	}

	go func() {
		for {
			select {
			case <-ctx.Done():
				return
			case <-tick:
				client.DataStreamKeepAlive(key)
			}
		}
	}()

	defer conn.Close()
	for {
		data, err := conn.Read()
		if err != nil {
			log.Errorf("biance private ws read fail,%v", err)
			cancel()
			break
		}
		client.privateCallFunc(data)
	}
}
