package binance

type UpdateType string

const (
	UpdateTypeDepth  UpdateType = "depthUpdate"
	UpdateTypeTicker UpdateType = "24hrTicker"
	UpdateTypeKline  UpdateType = "kline"
	UpdateTypeTrades UpdateType = "aggTrade"
	UpdateTypeTrade  UpdateType = "trade"
	UpdateTypeIndex  UpdateType = "compositeIndex"

	UpdateTypeOutboundAccountInfo UpdateType = "outboundAccountInfo"
	UpdateTypeExecutionReport     UpdateType = "executionReport"
)

// IndivTickerUpdate represents incoming ticker websocket feed
type IndivTickerUpdate struct {
	EventType     UpdateType `json:"e"` // EventType represents the update type
	Time          uint64     `json:"E"` // Time represents the event time
	Symbol        string     `json:"s"` // Symbol represents the symbol related to the update
	Price         string     `json:"p"` // Price is the order price
	PricePercent  string     `json:"P"` // Price percent change
	WeightedPrice string     `json:"w"` // Weighted average price
	FirstTrade    string     `json:"x"` // First trade(F)-1 price (first trade before the 24hr rolling window)
	LastPrice     string     `json:"c"` // Last price
	LastQty       string     `json:"Q"` // Last quantity
	BestBidPrice  string     `json:"b"` // Best bid price
	BestBidQty    string     `json:"B"` // Best bid quantity
	BestAskPrice  string     `json:"a"` // Best ask price
	BestAskQty    string     `json:"A"` // Best ask quantity
	OpenPrice     string     `json:"o"` // Open price
	HighPrice     string     `json:"h"` // High price
	LowPrice      string     `json:"l"` // Low price
	VolumeBase    string     `json:"v"` // Total traded base asset volume
	VolumeQuote   string     `json:"q"` // Total traded quote asset volume
	StatisticOT   uint64     `json:"O"` // Statistics open time
	StatisticsCT  uint64     `json:"C"` // Statistics close time
	FirstTradeID  int        `json:"F"` // First trade ID
	LastTradeID   int        `json:"L"` // Last trade ID
	TotalTrades   int        `json:"n"` // Total number of trades
}

// AllMarketTickerUpdate represents incoming ticker websocket feed for all tickers
type AllMarketTickerUpdate []IndivTickerUpdate

// DepthUpdate represents the incoming messages for depth websocket updates
type DepthUpdate struct {
	EventType UpdateType  `json:"e"` // EventType represents the update type
	Time      uint64      `json:"E"` // Time represents the event time
	Symbol    string      `json:"s"` // Symbol represents the symbol related to the update
	UpdateID  int         `json:"u"` // UpdateID to sync up with updateid in /api/v1/depth
	Bids      []DepthElem `json:"b"` // Bids is a list of bids for symbol
	Asks      []DepthElem `json:"a"` // Asks is a list of asks for symbol
}

// DepthLevelUpdate represents the incoming messages for depth level websocket updates
type DepthLevelUpdate struct {
	LastUpdateID uint64      `json:"lastUpdateId"` // EventType represents the update type
	Bids         []DepthElem `json:"bids"`         // Bids is a list of bids for symbol
	Asks         []DepthElem `json:"asks"`         // Asks is a list of asks for symbol
}

// KlinesUpdate represents the incoming messages for klines websocket updates
type KlinesUpdate struct {
	EventType UpdateType `json:"e"` // EventType represents the update type
	Time      uint64     `json:"E"` // Time represents the event time
	Symbol    string     `json:"s"` // Symbol represents the symbol related to the update
	Kline     struct {
		StartTime    uint64        `json:"t"` // StartTime is the start time of this bar
		EndTime      uint64        `json:"T"` // EndTime is the end time of this bar
		Symbol       string        `json:"s"` // Symbol represents the symbol related to this kline
		Interval     KlineInterval `json:"i"` // Interval is the kline interval
		FirstTradeID int           `json:"f"` // FirstTradeID is the first trade ID
		LastTradeID  int           `json:"L"` // LastTradeID is the first trade ID

		OpenPrice            string `json:"o"` // OpenPrice represents the open price for this bar
		ClosePrice           string `json:"c"` // ClosePrice represents the close price for this bar
		High                 string `json:"h"` // High represents the highest price for this bar
		Low                  string `json:"l"` // Low represents the lowest price for this bar
		Volume               string `json:"v"` // Volume is the trades volume for this bar
		Trades               int    `json:"n"` // Trades is the number of conducted trades
		Final                bool   `json:"x"` // Final indicates whether this bar is final or yet may receive updates
		VolumeQuote          string `json:"q"` // VolumeQuote indicates the quote volume for the symbol
		VolumeActiveBuy      string `json:"V"` // VolumeActiveBuy represents the volume of active buy
		VolumeQuoteActiveBuy string `json:"Q"` // VolumeQuoteActiveBuy represents the quote volume of active buy
	} `json:"k"` // Kline is the kline update
}

// TradesUpdate represents the incoming messages for aggregated trades websocket updates
type TradesUpdate struct {
	EventType             UpdateType `json:"e"` // EventType represents the update type
	Time                  uint64     `json:"E"` // Time represents the event time
	Symbol                string     `json:"s"` // Symbol represents the symbol related to the update
	TradeID               int        `json:"a"` // TradeID is the aggregated trade ID
	Price                 string     `json:"p"` // Price is the trade price
	Quantity              string     `json:"q"` // Quantity is the trade quantity
	FirstBreakDownTradeID int        `json:"f"` // FirstBreakDownTradeID is the first breakdown trade ID
	LastBreakDownTradeID  int        `json:"l"` // LastBreakDownTradeID is the last breakdown trade ID
	TradeTime             uint64     `json:"T"` // Time is the trade time
	Maker                 bool       `json:"m"` // Maker indicates whether buyer is a maker
}

// IndexUpdate
//{
//"e":"compositeIndex",     // 事件类型
//"E":1602310596000,        // 事件事件
//"s":"DEFIUSDT",           // 交易对
//"p":"554.41604065",       // 价格
//"c":[                 // 成分信息
//{
//"b":"BAL",          //基础资产
//"w":"1.********",   // 权重(数量)
//"W":"0.********"    // 权重(比例)
//},
//{
//"b":"BAND",
//"w":"3.********",
//"W":"0.********"
//}
//]
//}
type IndexUpdate struct {
	EventType UpdateType `json:"e"` // EventType represents the update type
	Time      uint64     `json:"E"` // Time represents the event time
	Symbol    string     `json:"s"` // Symbol represents the symbol related to the update
	Price     string     `json:"p"` // Price is the trade price
}

// AccountUpdate represents the incoming messages for account info websocket updates
type AccountUpdate struct {
	EventType        UpdateType `json:"e"` // EventType represents the update type
	Time             uint64     `json:"E"` // Time represents the event time
	MakerCommission  int        `json:"m"` // MakerCommission is the maker commission for the account
	TakerCommission  int        `json:"t"` // TakerCommission is the taker commission for the account
	BuyerCommission  int        `json:"b"` // BuyerCommission is the buyer commission for the account
	SellerCommission int        `json:"s"` // SellerCommission is the seller commission for the account
	CanTrade         bool       `json:"T"`
	CanWithdraw      bool       `json:"W"`
	CanDeposit       bool       `json:"D"`
	Balances         []*struct {
		Asset  string `json:"a"`
		Free   string `json:"f"`
		Locked string `json:"l"`
	} `json:"B"`
}

// OrderUpdate represents the incoming messages for account orders websocket updates
//{
//"e": "executionReport",        // 事件类型
//"E": *************,            // 事件时间
//"s": "ETHBTC",                 // 交易对
//"c": "mUvoqJxFIILMdfAW5iGSOW", // clientOrderId
//"S": "BUY",                    // 订单方向
//"o": "LIMIT",                  // 订单类型
//"f": "GTC",                    // 有效方式
//"q": "1.********",             // 订单原始数量
//"p": "0.********",             // 订单原始价格
//"P": "0.********",             // 止盈止损单触发价格
//"F": "0.********",             // 冰山订单数量
//"g": -1,                       // OCO订单 OrderListId
//"C": "",                       // 原始订单自定义ID(原始订单，指撤单操作的对象。撤单本身被视为另一个订单)
//"x": "NEW",                    // 本次事件的具体执行类型
//"X": "NEW",                    // 订单的当前状态
//"r": "NONE",                   // 订单被拒绝的原因
//"i": 4293153,                  // orderId
//"l": "0.********",             // 订单末次成交数量
//"z": "0.********",             // 订单累计已成交数量
//"L": "0.********",             // 订单末次成交价格
//"n": "0",                      // 手续费数量
//"N": null,                     // 手续费资产类别
//"T": 1499405658657,            // 成交时间
//"t": -1,                       // 成交ID
//"I": 8641984,                  // 请忽略
//"w": true,                     // 订单是否在订单簿上？
//"m": false,                    // 该成交是作为挂单成交吗？
//"M": false,                    // 请忽略
//"O": 1499405658657,            // 订单创建时间
//"Z": "0.********",             // 订单累计已成交金额
//"Y": "0.********",              // 订单末次成交金额
//"Q": "0.********"              // Quote Order Qty
//}
type OrderUpdate struct {
	EventType        UpdateType   `json:"e"` // EventType represents the update type
	Time             uint64       `json:"E"` // Time represents the event time
	Symbol           string       `json:"s"` // Symbol represents the symbol related to the update
	NewClientOrderID string       `json:"c"` // NewClientOrderID is the new client order ID
	Side             OrderSide    `json:"S"` // Side is the order side
	OrderType        OrderType    `json:"o"` // OrderType represents the order type
	TimeInForce      TimeInForce  `json:"f"` // TimeInForce represents the order TIF type
	OrigQty          string       `json:"q"` // OrigQty represents the order original quantity
	Price            string       `json:"p"` // Price is the order price
	ExecutionType    OrderStatus  `json:"x"` // ExecutionType represents the execution type for the order
	Status           OrderStatus  `json:"X"` // Status represents the order status for the order
	Error            OrderFailure `json:"r"` // Error represents an order rejection reason
	OrderID          int          `json:"i"` // OrderID represents the order ID
	OrderOther       int          `json:"I"` //ignore
	OrderTime        uint64       `json:"O"` // OrderTime represents the order time
	FilledQty        string       `json:"l"` // FilledQty represents the quantity of the last filled trade
	FilledPrice      string       `json:"L"` // FilledPrice is the price of last filled trade
	TotalFilledQty   string       `json:"z"` // TotalFilledQty is the accumulated quantity of filled trades on this order
	TotalFilledMoney string       `json:"Z"` // TotalFilledMoney
	Commission       string       `json:"n"` // Commission is the commission for the trade
	CommissionAsset  string       `json:"N"` // CommissionAsset is the asset on which commission is taken
	TradeTime        uint64       `json:"T"` // TradeTime is the trade time
	TradeID          int          `json:"t"` // TradeID represents the trade ID
	Maker            bool         `json:"m"` // Maker represents whether buyer is maker or not
}
