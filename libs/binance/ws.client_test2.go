/*
@Time : 2019-12-17 19:06
<AUTHOR> mocha
@File : client_test
*/
package binance

import (
	"fmt"
	"io/ioutil"
	"testing"

	"github.com/gorilla/websocket"
	"spot/libs/convert"
	"spot/libs/json"
)

func TestWsClient(te *testing.T) {
	w, rsp, err := websocket.DefaultDialer.Dial("wss://fstream.binance.com/ws/stream", nil)
	if err != nil {
		fmt.Println(err)
	}
	b, err := ioutil.ReadAll(rsp.Body)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(convert.Bytes2Str(b))

	go func() {
		for {
			w.Set<PERSON>ing<PERSON>andler(func(appData string) error {
				//te.Log(appData)
				w.WriteMessage(websocket.PongMessage, nil)
				return nil
			})
			t, b, err := w.ReadMessage()
			//d, _ := zip.UnzipByte(b)
			te.Log(t, convert.Bytes2Str(b), err)
		}
	}()

	go func() {
		str := `{"method": "SUBSCRIBE","params":["btcusdt@compositeIndex"],"id": 2}`
		//str := `{"method": "SUBSCRIBE","params":["btcusdt@aggTrade"],"id": 1}`
		w.WriteMessage(websocket.TextMessage, convert.Str2Bytes(str))

		//time.Sleep(1 * time.Minute)
		//t = model.SubSymbolArg{ContractCode: "eth/usdt"}
		//goto RE
	}()
	select {}

}

func TestBClient(t *testing.T) {
	c := NewMarketWsClient(nil, func(data []byte) {
		t.Logf("d:%v", convert.Bytes2Str(data))

		p := new(ResultPayload)
		err := json.Unmarshal(data, p)
		if err != nil {
			t.Logf("unmarsha fail,%v", err)
			return
		}
		if p.Event == "aggTrade" {
			ticker := new(ResultTicker)
			err = json.Unmarshal(data, ticker)
			if err != nil {
				return
			}
			ticker.DealTime = ticker.DealTime / 1000
			t.Logf("ticker:%+v", ticker)

		}
		t.Logf(convert.Bytes2Str(data))
	})
	c.Subscript([]string{"btcusdt@aggTrade", "ethusdt@aggTrade"})
	c.Start()
	select {}
}
