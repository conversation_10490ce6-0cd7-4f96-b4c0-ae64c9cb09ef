package binance

import (
	"encoding/json"

	"github.com/gorilla/websocket"
)

type wsWrapper struct {
	conn *websocket.Conn
}

func (w *wsWrapper) Close() error {
	return w.conn.Close()
}

// DepthWS is a wrapper for depth websocket
type De<PERSON>h<PERSON> struct {
	wsWrapper
}

// Read reads a depth update message from the depth websocket
func (d *DepthWS) Read() (*DepthUpdate, error) {
	_, data, err := d.conn.ReadMessage()
	if err != nil {
		return nil, err
	}
	update := &DepthUpdate{}
	return update, json.Unmarshal(data, update)
}

// DepthLevelWS is a wrapper for depth level websocket
type DepthLevelWS struct {
	wsWrapper
}

// Read reads a depth update message from the depth level websocket
func (d *DepthLevelWS) Read() (*DepthLevelUpdate, error) {
	_, data, err := d.conn.ReadMessage()
	if err != nil {
		return nil, err
	}
	update := &DepthLevelUpdate{}
	return update, json.Unmarshal(data, update)
}

// AllMarketTickerWS is a wrapper for all market tickers websocket
type AllMarketTickerWS struct {
	wsWrapper
}

// Read reads a depth update message from the depth level websocket
func (d *AllMarketTickerWS) Read() (*AllMarketTickerUpdate, error) {
	_, data, err := d.conn.ReadMessage()
	if err != nil {
		return nil, err
	}
	update := &AllMarketTickerUpdate{}
	return update, json.Unmarshal(data, update)
}

// IndivTickerWS is a wrapper for an individual ticker websocket
type IndivTickerWS struct {
	wsWrapper
}

// Read reads a depth update message from the depth level websocket
func (d *IndivTickerWS) Read() (*IndivTickerUpdate, error) {
	_, data, err := d.conn.ReadMessage()
	if err != nil {
		return nil, err
	}
	update := &IndivTickerUpdate{}
	return update, json.Unmarshal(data, update)
}

// KlinesWS is a wrapper for klines websocket
type KlinesWS struct {
	wsWrapper
}

// Read reads a klines update message from the klines websocket
func (d *KlinesWS) Read() (*KlinesUpdate, error) {
	_, data, err := d.conn.ReadMessage()
	if err != nil {
		return nil, err
	}
	update := &KlinesUpdate{}
	return update, json.Unmarshal(data, update)
}

// TradesWS is a wrapper for trades websocket
type TradesWS struct {
	wsWrapper
}

// Read reads a trades update message from the trades websocket
func (d *TradesWS) Read() (*TradesUpdate, error) {
	_, data, err := d.conn.ReadMessage()
	if err != nil {
		return nil, err
	}
	update := &TradesUpdate{}
	return update, json.Unmarshal(data, update)
}

// AccountInfoWS is a wrapper for account info websocket
type AccountInfoWS struct {
	wsWrapper
}

// Read reads a account info update message from the account info websocket
// Remark: The websocket is used to update two different structs, which both are flat, hence every call to this function
// will return either one of the types initialized and the other one will be set to nil
func (d *AccountInfoWS) Read() ([]byte, error) {
	_, data, err := d.conn.ReadMessage()
	if err != nil {
		return nil, err
	}

	return data, nil
}
