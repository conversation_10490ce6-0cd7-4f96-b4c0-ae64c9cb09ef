/*
@Time : 2/7/20 3:31 下午
<AUTHOR> mocha
@File : main
*/
package binance

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/shopspring/decimal"
	"spot/libs/log"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

const (
	//_wsMarket = "wss://stream.binance.com/ws/stream"
	_sub   = "SUBSCRIBE"
	_unSub = "UNSUBSCRIBE"
)

//entries

type MarketWsClient struct {
	dialer        *websocket.Dialer
	wsPoint       string
	topics        []string
	con           *websocket.Conn
	lastError     error
	lock, errLock sync.Mutex
	status        bool
	msgHandler    func([]byte)
	closeHandler  func(int, string)
	lastRv        time.Time
}

type Config struct {
	WsPoint string
	dialer  *websocket.Dialer
}

func NewMarketWsClient(config *Config, f func([]byte)) *MarketWsClient {
	c := &MarketWsClient{
		dialer:  nil,
		wsPoint: config.WsPoint,
		topics:  nil,
	}
	if config != nil {
		if config.WsPoint != "" {
			c.wsPoint = config.WsPoint
		}
		if config.dialer != nil {
			c.dialer = config.dialer
		} else {
			c.dialer = websocket.DefaultDialer
		}
	}
	c.msgHandler = f
	return c
}

func (c *MarketWsClient) CloseHook(f func(int, string)) {
	c.closeHandler = f
}

func (c *MarketWsClient) Start() {
Retry:
	//fmt.Println("start")
	conn, rsp, err := c.dialer.Dial(c.wsPoint, nil)
	if err != nil {
		fmt.Errorf("dial to binance fail,err:%v,http rsp:%+v", err, rsp)
		goto Retry
	}
	c.setLastError(nil)
	c.con = conn
	c.subscript()
	c.status = true
	log.Infof("success get websocket client conn for binance")
	c.receive()
}

func (c *MarketWsClient) Subscript(topics []string) {
	log.Infof("biance sub topics:%v", topics)
	c.topics = topics
	c.subscript()

}

func (c *MarketWsClient) subscript() {
	if len(c.topics) == 0 {
		fmt.Println("binance 订阅topic数量为0")
		return
	}
	d := BasicData{Method: _sub, Params: c.topics}
	b, err := json.Marshal(d)
	if err != nil {
		fmt.Errorf("marshal fail,%v", err)
		return
	}
	//fmt.Printf("binance sub:%v\n", convert.Bytes2Str(b))
	if c.con == nil {
		fmt.Printf("binance 连接为空")
		return
	}
	c.lock.Lock()
	defer c.lock.Unlock()
	e := c.con.WriteMessage(websocket.TextMessage, b)
	fmt.Printf("write sub msg:%v", string(b))
	if e != nil {
		fmt.Printf("write sub msg fail,%v\n", e)
		c.setLastError(e)
	}
}

func (c *MarketWsClient) unSubscript() {
	d := BasicData{Method: _unSub, Params: c.topics}
	b, err := json.Marshal(d)
	if err != nil {
		fmt.Errorf("marshal fail,%v", err)
		return
	}
	c.lock.Lock()
	defer c.lock.Unlock()
	e := c.con.WriteMessage(websocket.TextMessage, b)
	if e != nil {
		c.setLastError(e)
	}
}

func (c *MarketWsClient) UnSubscript(topics []string) {
	d := BasicData{Method: _unSub, Params: c.topics}
	b, err := json.Marshal(d)
	if err != nil {
		fmt.Errorf("marshal fail,%v", err)
		return
	}
	c.lock.Lock()
	defer c.lock.Unlock()
	e := c.con.WriteMessage(websocket.TextMessage, b)
	if e != nil {
		c.setLastError(e)
	}
}

func (c *MarketWsClient) setLastError(err error) {
	c.errLock.Lock()
	defer c.errLock.Unlock()
	c.lastError = err
}

func (c *MarketWsClient) setConn(conn *websocket.Conn) {
	c.errLock.Lock()
	defer c.errLock.Unlock()
	c.con = conn
}

func (c *MarketWsClient) receive() {
	go func() {
		for {
			if c.reMsg() {
				return
			}
		}
	}()

}

func (c *MarketWsClient) reMsg() bool {
	if !c.status {
		return true
	}
	conn := c.con
	if conn == nil {
		c.setLastError(errors.New("ws conn is null"))
		return true
	}
	conn.SetPingHandler(func(appData string) error {
		c.con.WriteMessage(websocket.PongMessage, nil)
		return nil
	})
	_, b, err := conn.ReadMessage()
	if err != nil {
		fmt.Errorf("bian readmessage fail,%v", err)
		c.setLastError(err)
		if e, ok := err.(*websocket.CloseError); ok {
			if c.closeHandler != nil {
				c.closeHandler(e.Code, e.Text)
			}
		}
		return true
	}
	//fmt.Printf("d:%v,err:%v\n", string(b), err)
	if c.msgHandler != nil {
		c.msgHandler(b)
	}
	return false
}

func (c *MarketWsClient) Loop() {
	go func() {
		for {
			if !c.status {
				continue
			}
			c.checkError()
			time.Sleep(time.Second)
		}
	}()
}

func (c *MarketWsClient) checkError() {
	if c.con == nil || c.lastError != nil {
		c.status = false
		c.Start()
	}
}

//{
//"method": "SUBSCRIBE",
//"params":
//[
//"btcusdt@aggTrade",
//"btcusdt@depth"
//],
//"id": 1
//}
type BasicData struct {
	Method string   `json:"method"`
	Params []string `json:"params"`
	Id     int64    `json:"id"`
}

type ResultPayload struct {
	Event  string `json:"e"`
	Time   int64  `json:"E"`
	Symbol string `json:"s"`
}

//{
//"e": "trade",     // 事件类型
//"E": 123456789,   // 事件时间
//"s": "BNBBTC",    // 交易对
//"t": 12345,       // 交易ID
//"p": "0.001",     // 成交价格
//"q": "100",       // 成交数量
//"b": 88,          // 买方的订单ID
//"a": 50,          // 卖方的订单ID
//"T": 123456785,   // 成交时间
//"m": true,        // 买方是否是做市方。如true，则此次成交是一个主动卖出单，否则是一个主动买入单。
//"M": true         // 请忽略该字段
//}
type ResultTicker struct {
	ResultPayload
	AId      int64  `json:"a"` //归集Id
	Price    string `json:"p"` //成交价格
	Amount   string `json:"q"` //成交数量
	DealTime int64  `json:"T"` //成交时间
	MType    bool   `json:"m"`
	M        bool   `json:"M"`
}

//{
//"e": "24hrTicker",  // 事件类型
//"E": 123456789,     // 事件时间
//"s": "BNBUSDT",      // 交易对
//"p": "0.0015",      // 24小时价格变化
//"P": "250.00",      // 24小时价格变化(百分比)
//"w": "0.0018",      // 平均价格
//"c": "0.0025",      // 最新成交价格
//"Q": "10",          // 最新成交价格上的成交量
//"o": "0.0010",      // 24小时内第一比成交的价格
//"h": "0.0025",      // 24小时内最高成交价
//"l": "0.0010",      // 24小时内最低成交加
//"v": "10000",       // 24小时内成交量
//"q": "18",          // 24小时内成交额
//"O": 0,             // 统计开始时间
//"C": 86400000,      // 统计关闭时间
//"F": 0,             // 24小时内第一笔成交交易ID
//"L": 18150,         // 24小时内最后一笔成交交易ID
//"n": 18151          // 24小时内成交数
//}
type Ticker24 struct {
	ResultPayload
	Change24h decimal.Decimal `json:"p"`
	Amount    decimal.Decimal `json:"v"`
	Money     decimal.Decimal `json:"q"`
}
