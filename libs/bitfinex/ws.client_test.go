/*
@Time : 2019-12-17 19:06
<AUTHOR> mocha
@File : client_test
*/
package bitfinex

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"spot/libs/convert"
	"spot/libs/log"
	"strings"
	"testing"
	"time"

	"github.com/gorilla/websocket"
)

func TestClient(te *testing.T) {
	w, rsp, err := websocket.DefaultDialer.Dial("wss://api-pub.bitfinex.com/ws/2", nil)
	if err != nil {
		fmt.Println(err)
	}
	b, err := ioutil.ReadAll(rsp.Body)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(convert.Bytes2Str(b))
	//str := `{
	//event: "subscribe",
	//channel: "Trades",
	//symbol: "tBTCUSD",
	//}`

	str := `{
   "event":"ping",
   "cid": 1234
}
`

	w.WriteMessage(websocket.TextMessage, convert.Str2Bytes(str))
	s := `{"event":"subscribe","channel":"Trades","symbol":"tBTCUSD"}`
	w.WriteMessage(websocket.TextMessage, convert.Str2Bytes(s))

	go func() {
		for {
			t, b, err := w.ReadMessage()
			//d, _ := zip.UnzipByte(b)
			//b, _ = compress.GzipUnCompress(b)
			te.Log(t, convert.Bytes2Str(b), err)
			//ping := new(Ping)
			//_ = json.Unmarshal(b, ping)
			//if ping.Ping != 0 {
			//	te.Logf("ping:%+v", *ping)
			//	pong, _ := json.Marshal(&Pong{Pong: ping.Ping})
			//	//pong, _ := json.Marshal(&Pong{Pong: compress.GetUinxMillisecond()})
			//	//pong:=[]byte(`{"pong": 1492420473027}`)
			//	w.WriteMessage(websocket.TextMessage, pong)
			//	te.Logf("pong:%+v", string(pong))
			//} else {
			//}

		}
	}()

	//TestClient: ws.client_test.go:52: 1 {"event":"subscribed","channel":"trades","chanId":868934,"symbol":"tBTCUSD","pair":"BTCUSD"} <nil>
	//订阅完毕取chanId
	//	[868934,[[568430668(id),1610106721075(time),0.0149943,40925.54385297(价格)],[568430667,1610106721075,0.0000057,40901]]
	select {}

}

func TestBClient(t *testing.T) {
	log.InitLogger("ws.log", "info", false)
	s := time.Tick(time.Minute * 1)
	t1 := time.Tick(20 * time.Second)
	channelMap := make(map[int64]string)
	c := NewMarketWsClient(&Config{WsPoint: "wss://api-pub.bitfinex.com/ws/2"}, func(data []byte) {
		t.Logf("d:%v", string(data))
		t.Logf(string(data))
		if bytes.Contains(data, []byte("subscribed")) {
			sub := new(Sub)
			e := json.Unmarshal(data, sub)
			if e != nil {
				log.Errorf("json unmarshal fail,%v", e)
				return
			}
			if sub.ChannelId > 0 {
				key := sub.ChannelId
				code := strings.ReplaceAll(sub.Pair, "USD", "USDT")
				channelMap[key] = code
			}
			return
		}
		var list []interface{}
		e := json.Unmarshal(data, &list)
		if e != nil {
			return
		}
		id := list[0].(float64)
		cId := int64(id)
		code, ok := channelMap[cId]
		if !ok {
			return
		}
		typeS, ok := list[1].(string)
		if !ok || (typeS != "tu" && typeS != "te") {
			log.Infof("不支持解")
			return
		}

		d := list[2].([]interface{})
		if len(d) < 3 {
			return
		}
		time := d[1].(float64)
		price := d[3].(float64)

		log.Infof("code:%v,time:%v,price:%v", code, time, price)
	})
	c.Subscript([]string{"Trades"}, []string{"tBTCUSD"})
	c.Start()
	go func() {
		for {
			select {
			case <-s:
				channelMap = make(map[int64]string)
				c.Restart()
			case <-t1:
				c.Subscript([]string{"Trades"}, []string{"tBTCUSD"})

			}
		}
	}()
	select {}
}
