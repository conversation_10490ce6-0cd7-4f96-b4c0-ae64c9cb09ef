/*
@Time : 2019-12-17 19:06
<AUTHOR> mocha
@File : client_test
*/
package bitmex

import (
	"fmt"
	"io/ioutil"
	"spot/libs/log"
	"testing"
	"time"

	"spot/libs/convert"

	"github.com/gorilla/websocket"
)

func TestClient(te *testing.T) {
	//Retry:
	w, rsp, err := websocket.DefaultDialer.Dial("wss://www.bitmex.com/realtime", nil)
	if err != nil {
		fmt.Println(err)
	}
	b, err := ioutil.ReadAll(rsp.Body)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(convert.Bytes2Str(b))
	t := time.Tick(5 * time.Second)
	str := `{"op": "subscribe", "args": ["trade"]}`

	w.WriteMessage(websocket.TextMessage, convert.Str2Bytes(str))
	go func() {
		for {
			select {
			case <-t:
				w.WriteMessage(websocket.PingMessage, nil)
				//str1 := `{"op": "unsubscribe", "args": ["funding"]}`
				//w.WriteMessage(websocket.TextMessage, convert.Str2Bytes(str1))

			}
		}
	}()

	go func() {
		for {
			w.SetPingHandler(func(appData string) error {
				//te.Log(appData)
				w.WriteMessage(websocket.PongMessage, nil)
				return nil
			})
			t, b, err := w.ReadMessage()
			//d, _ := zip.UnzipByte(b)
			te.Log(t, convert.Bytes2Str(b), err)
		}
	}()

	go func() {
		str := `{"op": "subscribe", "args": ["funding"]}`
		w.WriteMessage(websocket.TextMessage, convert.Str2Bytes(str))
		//t:=time.Tick(30*time.Second)
		//for range t {
		//	w.WriteMessage(websocket.TextMessage, convert.Str2Bytes(str))
		//
		//}

		//time.Sleep(1 * time.Minute)
		//t = model.SubSymbolArg{ContractCode: "eth/usdt"}
		//goto RE
	}()
	//tim := time.After(10 * time.Second)
	//for range tim {
	//
	//	fmt.Printf("will close ws\n")
	//	goto Retry
	//}
	select {}

}

func TestBClient(t *testing.T) {
	log.InitLogger("ws", "info", false)

	c := NewMarketWsClient(&Config{WsPoint: "wss://www.bitmex.com/realtime"}, func(data []byte) {
		t.Logf("d:%v", string(data))

		//p := new(ResultPayload)
		//err := json.Unmarshal(data, p)
		//if err != nil {
		//	t.Logf("unmarsha fail,%v", err)
		//	return
		//}
		//if p.Event == "aggTrade" {
		//	ticker := new(ResultTicker)
		//	err = json.Unmarshal(data, ticker)
		//	if err != nil {
		//		return
		//	}
		//	ticker.DealTime = ticker.DealTime / 1000
		//	t.Logf("ticker:%+v", ticker)
		//
		//}
		t.Logf(string(data))
	})
	c.Subscript([]string{"funding", "trade"})
	c.Start()

	select {}
}
