/*
@Time : 2/7/20 3:31 下午
<AUTHOR> mocha
@File : main
*/
package bittrex

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/gorilla/websocket"
)

const (
	url = "ws://127.0.0.1:8080/ws"
)

func main() {
	conn, rsp, err := websocket.DefaultDialer.Dial(url, nil)
	if err != nil {
		log.Fatalf("dial to bitmex fail,err:%v,http rsp:%+v", err, rsp)
	}

	var msg Message
	ctx, cancel := context.WithCancel(context.Background())
	msgChan := ReadMessage(ctx, conn)
	tick := time.NewTicker(time.Second * 13)
	for {
		select {
		case msg = <-msgChan:
			//tick.Reset(time.Second)
			if msg.Err != nil {
				cancel()
				fmt.Printf("接收消息失败 msgType；%v，msg:%s, err:%v", msg.Type, msg.Msg, msg.Err)
				return
			}
			fmt.Printf("收到消息 msgType；%v，msg:%s\n", msg.Type, msg.Msg)
		case <-tick.C:
			//fmt.Printf("未收到数据\n")
			cancel()
			fmt.Printf("结束 :%v\n", conn.Close())
			break
		}
	}
	//time.Sleep(time.Second * 5)
}
