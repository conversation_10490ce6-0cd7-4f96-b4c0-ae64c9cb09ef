/*
@Time : 2019-12-17 19:06
<AUTHOR> mocha
@File : client_test
*/
package bittrex

import (
	"fmt"
	"io/ioutil"
	"spot/libs/compress"
	"spot/libs/convert"
	"spot/libs/json"
	"spot/libs/log"
	"testing"
	"time"

	"github.com/gorilla/websocket"
)

func TestClient(te *testing.T) {
	w, rsp, err := websocket.DefaultDialer.Dial("https://socket-v3.bittrex.com/signalr", nil)
	//w, rsp, err := websocket.DefaultDialer.Dial("wss://api.hbdm.com/linear-swap-ws", nil)
	if err != nil {
		fmt.Println(err)
	}
	b, err := ioutil.ReadAll(rsp.Body)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(convert.Bytes2Str(b))
	str := `{
		"sub": "market.btcusdt.detail",
			"id": "id1"
	}`

	w.WriteMessage(websocket.TextMessage, convert.Str2Bytes(str))

	go func() {
		for {
			t, b, err := w.ReadMessage()
			//d, _ := zip.UnzipByte(b)
			b, _ = compress.GzipUnCompress(b)
			te.Log(t, convert.Bytes2Str(b), err)
			ping := new(Ping)
			_ = json.Unmarshal(b, ping)
			if ping.Ping != 0 {
				te.Logf("ping:%+v", *ping)
				pong, _ := json.Marshal(&Pong{Pong: ping.Ping})
				//pong, _ := json.Marshal(&Pong{Pong: compress.GetUinxMillisecond()})
				//pong:=[]byte(`{"pong": 1492420473027}`)
				w.WriteMessage(websocket.TextMessage, pong)
				te.Logf("pong:%+v", string(pong))
			} else {
			}

		}
	}()

	select {}

}

func TestBClient(t *testing.T) {
	log.InitLogger("ws.log", "info", false)
	s := time.Tick(time.Minute * 1)
	c := NewMarketWsClient(&Config{WsPoint: "wss://api.huobi.pro/ws"}, func(data []byte) {
		t.Logf("d:%v", string(data))
		t.Logf(string(data))
	})
	c.Subscript([]string{"market.btcusdt.depth.step0"})
	c.Start()
	go func() {
		for range s {
			t.Logf("开始重启")
			c.Restart()
		}
	}()
	select {}
}
