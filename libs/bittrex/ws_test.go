package bittrex

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/thebotguys/signalr"
	"testing"
	"time"
)

func TestApi(t *testing.T) {
	s := GetTickers(map[string]struct{}{"BTCUSDT": {}})
	t.Logf("v:%+v", s)
}

func TestBittrex(t *testing.T) {
	client := signalr.NewWebsocketClient()
	client.OnClientMethod = func(hub, method string, arguments []json.RawMessage) {
		fmt.Println("Message Received: ")
		fmt.Println("HUB: ", hub)
		fmt.Println("METHOD: ", method)
		fmt.Println("ARGUMENTS: ", arguments)
	}
	client.OnMessageError = func(err error) {

		fmt.Println("ERROR OCCURRED: ", err)
	}
	client.Connect("https", "socket-v3.bittrex.com/signalr", []string{"c3"})
	client.CallHub("c3", "Subscribe", []string{"tickers"})
	//client.CallHub("c3", "GET", "params", 1, 1.4, "every type is accepted")
	//client.Close()
	select {}
}

func TestBittrexSubscribeOrderBook(t *testing.T) {
	bt := New()
	ch := make(chan ExchangeState, 16)
	errCh := make(chan error)
	go func() {
		var haveInit bool
		var msgNum int
		for st := range ch {
			haveInit = haveInit || st.Initial
			msgNum++
			if msgNum >= 3 {
				break
			}
		}
		if haveInit {
			errCh <- nil
		} else {
			errCh <- errors.New("no initial message")
		}
	}()
	go func() {
		errCh <- bt.SubscribeExchangeUpdate("USDT-BTC", ch, nil)
	}()
	select {
	case <-time.After(time.Second * 6):
		t.Error("timeout")
	case err := <-errCh:
		if err != nil {
			t.Error(err)
		}
	}
}
