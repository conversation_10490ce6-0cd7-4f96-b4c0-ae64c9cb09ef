package cache

import (
	"encoding/json"
	"github.com/go-redis/redis"
	"go.uber.org/zap"
	"spot/libs/convert"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
	"spot/libs/utils"
)

func getBasePriceKlineKey(priceType, contractCode, timeQuantum string) string {
	return utils.StrBuilderBySep(":", define.CacheKeyBasePriceKline, contractCode, priceType, timeQuantum)
}

//获取kline长度
func GetBasePriceKlineWithType(priceType, contractCode, timeQuantum string) int64 {
	key := getBasePriceKlineKey(priceType, contractCode, timeQuantum)
	b, err := DefaultRedis().LLen(key).Result()
	if err != nil {
		log.Errorf("GetBasePriceKlineWithType redis fail,%v", err)
		return 0
	}
	return b
}

//获取指定kline
func GetBasePriceKLineNodeByIndex(priceType, contractCode, timeQuantum string, index int64) (node *proto.KLine) {
	key := getBasePriceKlineKey(priceType, contractCode, timeQuantum)
	b, err := DefaultRedis().LIndex(key, index).Bytes()
	if err != nil {
		log.Errorf("GetBasePriceKLineNodeByIndex redis fail,%v", err)
		return
	}
	k := new(proto.KLine)
	err = json.Unmarshal(b, k)
	if err != nil {
		log.Errorf("GetBasePriceKLineNodeByIndex json unmarshal fail,%v", err)
		return
	}
	node = k
	return
}

//修改指定的kline节点
func UpdateBasePriceKlineNodeByIndex(priceType, timeQuantum string, index int64, kline *proto.KLine) error {
	key := getBasePriceKlineKey(priceType, kline.Symbol, timeQuantum)
	b, err := json.Marshal(kline)
	if err != nil {
		log.Error("UpdateBasePriceKlineNodeByIndex json marshal error", zap.String("key", key), zap.Error(err))
		return err
	}
	err = DefaultRedis().LSet(key, index, b).Err()
	if err != nil {
		log.Errorf("UpdateBasePriceKlineNodeByIndex lset err:%v,d:%+v", err, *kline)
		return err
	}
	return err
}

func GetBasePriceKLineNode(priceType, contractCode, timeQuantum string, start int64, end int64) ([]proto.KLine, error) {
	key := getBasePriceKlineKey(priceType, contractCode, timeQuantum)
	encode, err := DefaultRedis().LRange(key, start, end).Result()
	if err != nil && err != redis.Nil {
		log.Error("GetBasePriceKLineNode redis error",
			zap.String("key", key),
			zap.Int64("start", start),
			zap.Int64("end", end),
			zap.Error(err))
		return nil, err
	}

	var node proto.KLine
	list := make([]proto.KLine, 0)

	for i := range encode {
		err = json.Unmarshal(convert.Str2Bytes(encode[i]), &node)
		if err != nil {
			log.Error("GetBasePriceKLineNode json unmarshal error",
				zap.String("key", key),
				zap.Int64("start", start),
				zap.Int64("end", end),
				zap.Error(err))
			return nil, err
		}
		list = append(list, node)
	}

	return list, nil
}
func PushBasePriceKLineNode(priceType, timeQuantum string, node *proto.KLine) error {
	key := getBasePriceKlineKey(priceType, node.Symbol, timeQuantum)
	b, err := json.Marshal(node)
	if err != nil {
		log.Error("PushBasePriceKLineNode json marshal error", zap.String("key", key), zap.Error(err))
		return err
	}

	err = DefaultRedis().RPush(key, b).Err()
	if err != nil {
		log.Error("PushBasePriceKLineNode redis error", zap.String("key", key), zap.Error(err))
		return err
	}
	err = DefaultRedis().LTrim(key, -10000, -1).Err()
	if err != nil {
		log.Error("PushBasePriceKLineNode trim redis error", zap.String("key", key), zap.Error(err))
		return err
	}
	return nil
}

// 正序取K线
func GetBasePriceKLineNodeASC(priceType, contractCode string, timeQuantum string, start int64, end int64) ([]proto.KLine, error) {
	key := getBasePriceKlineKey(priceType, contractCode, timeQuantum)
	encode, err := DefaultRedis().LRange(key, start, end).Result()
	if err != nil && err != redis.Nil {
		log.Error("GetBasePriceKLineNodeASC redis error",
			zap.String("key", key),
			zap.Int64("start", start),
			zap.Int64("end", end),
			zap.Error(err))
		return nil, err
	}

	list := make([]proto.KLine, len(encode))
	for i := range encode {
		err = json.Unmarshal(convert.Str2Bytes(encode[i]), &list[i])
		if err != nil {
			log.Error("GetBasePriceKLineNodeASC json unmarshal error",
				zap.String("key", key),
				zap.Int64("start", start),
				zap.Int64("end", end),
				zap.Error(err))
			return nil, err
		}
	}
	return list, nil
}
