/*
@Time : 3/12/20 2:21 下午
<AUTHOR> mocha
@File : change
*/
package cache

import (
	"spot/libs/convert"
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/proto"
)

//设置涨跌幅
func SetContractAppliesChange(applies *proto.Applies) {
	b, err := json.Marshal(applies)
	if err != nil {
		log.Errorf("json marshal fail,%v", err)
		return
	}
	DefaultRedis().HSet(define.CacheKeyApplies, applies.ContractCode, b)
}

//获取涨跌幅
func GetContractApplies(contractCode string) (index *proto.Applies) {
	index = new(proto.Applies)
	b, err := DefaultRedis().HGet(define.CacheKeyApplies, contractCode).Bytes()
	if err != nil {
		log.Errorf("GetContractApplies get fail,%v", err)
		return
	}
	err = json.Unmarshal(b, index)
	if err != nil {
		log.Errorf("GetContractApplies json unmarshal fail,%v", err)
		return
	}
	return
}

// 获取全部涨跌幅
func GetAllContractApplies() (mp map[string]proto.Applies) {
	mp = make(map[string]proto.Applies)
	b, err := DefaultRedis().HGetAll(define.CacheKeyApplies).Result()
	if err != nil {
		log.Errorf("GetAllContractApplies get fail,%v", err)
		return
	}
	var index proto.Applies
	for key, val := range b {
		//log.Debugf("s:%v",string(val))
		err = json.Unmarshal(convert.Str2Bytes(val), &index)
		if err != nil {
			log.Errorf("GetAllContractApplies json unmarshal fail,%v", err)
			return
		}
		mp[key] = index
	}
	return
}
