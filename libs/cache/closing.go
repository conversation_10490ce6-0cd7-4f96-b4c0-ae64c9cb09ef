package cache

import (
	"go.uber.org/zap"
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/mq"
	"spot/libs/nums"
	"spot/libs/utils"
	"time"
)

func ListClosingMsg() (result []mq.MessagePack) {
	list, err := DefaultRedis().HVals(define.CacheMsgClosing).Result()
	if err != nil {
		return
	}
	for _, s := range list {
		m := new(mq.MessagePack)
		err := json.Unmarshal([]byte(s), m)
		if err != nil {
			log.Error("ListClosingMsg json unMarshal fail", zap.Error(err))
			return
		}
		result = append(result, *m)
	}
	return
}

func SetClosingMsg(msg mq.MessagePack) {
	b, err := json.Marshal(msg)
	if err != nil {
		log.Error("SetClosingMsg fail", zap.Error(err))
		return
	}
	err = DefaultRedis().HSet(define.CacheMsgClosing, nums.Int64String(msg.MsgId), b).Err()

}

func DelClosingMsg(msgId int64) (err error) {
	e := DefaultRedis().HDel(define.CacheMsgClosing, nums.Int64String(msgId)).Err()
	if e != nil {
		log.Errorf("DelContractDepthConfig fail,%v", err)
		return
	}
	return
}

func SetUserContractAc(userId int64, code, side string, duration time.Duration) {
	key := utils.StrBuilderBySep(":", define.CacheKeyUserAc, code, side, nums.Int64String(userId))
	e := DefaultRedis().Set(key, userId, duration).Err()
	if e != nil {
		log.Error("SetUserContractAc fail", zap.Error(e))
		return
	}
}

func IsUserInAc(userId int64, code, side string) bool {
	key := utils.StrBuilderBySep(":", define.CacheKeyUserAc, code, side, nums.Int64String(userId))
	return ExistKey(key)
}
