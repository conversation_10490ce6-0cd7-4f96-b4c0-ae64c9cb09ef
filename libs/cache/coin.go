package cache

import (
	"spot/libs/singleflight"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/go-redis/redis"
	"go.uber.org/zap"
	"spot/libs/convert"
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/proto"
)

var coinSetLock sync.Mutex

func SaveCoinList(list []proto.Coin, force bool) {
	var counter int
flag:
	if !coinSetLock.TryLock() {
		if !force || counter > 10 {
			return
		}

		counter++
		time.Sleep(time.Second)
		goto flag
	}
	defer coinSetLock.Unlock()

	idKeys := make(map[string]define.Placeholder, len(list))
	nameKeys := make(map[string]define.Placeholder, len(list))

	for i := range list {
		nameKeys[list[i].CurrencyName] = define.PlaceholderEntity
		idKeys[strconv.Itoa(list[i].CurrencyId)] = define.PlaceholderEntity
	}

	var delIDs []string

	currIDKeys := DefaultRedisWithDB(define.CacheDBNumber2).HKeys(define.CacheKeyCoinListByID).Val()
	for i := range currIDKeys {
		if _, ok := idKeys[currIDKeys[i]]; !ok {
			delIDs = append(delIDs, currIDKeys[i])
		}
	}

	var delNames []string
	currNameKeys := DefaultRedisWithDB(define.CacheDBNumber2).HKeys(define.CacheKeyCoinListByName).Val()
	for i := range currNameKeys {
		if _, ok := nameKeys[currNameKeys[i]]; !ok {
			delNames = append(delNames, currNameKeys[i])
		}
	}

	pp := DefaultRedisWithDB(define.CacheDBNumber2).TxPipeline()
	for i := range list {
		data, err := json.Marshal(&list[i])
		if err != nil {
			log.Error("SaveCoinList json error", zap.Error(err))
			return
		}

		pp.HSet(define.CacheKeyCoinListByID, strconv.Itoa(list[i].CurrencyId), data)
		pp.HSet(define.CacheKeyCoinListByName, strings.ToUpper(list[i].CurrencyName), data)
	}
	if len(delIDs) > 0 {
		pp.HDel(define.CacheKeyCoinListByID, delIDs...)
	}
	if len(delNames) > 0 {
		pp.HDel(define.CacheKeyCoinListByName, delNames...)
	}
	_, err := pp.Exec()
	if err != nil {
		log.Error("SaveCoinList redis EXEC error", zap.Error(err))
		return
	}
}

func GetAllCoinList() ([]proto.Coin, error) {
	encode, err := DefaultRedisWithDB(define.CacheDBNumber2).HVals(define.CacheKeyCoinListByID).Result()
	if err != nil {
		log.Error("GetAllCoinList redis error", zap.Error(err))
		return nil, err
	}

	var coin proto.Coin
	list := make([]proto.Coin, 0, len(encode))
	for i := range encode {
		//log.Infof("print:%v",string(encode[i]))
		err = json.Unmarshal(convert.Str2Bytes(encode[i]), &coin)
		if err != nil {
			log.Error("GetAllCoinList json unmarshal error", zap.Error(err))
			return nil, err
		}
		list = append(list, coin)
	}

	return list, nil
}

func GetAllCoinMapByID() (map[int]proto.Coin, error) {
	encode, err := DefaultRedisWithDB(define.CacheDBNumber2).HGetAll(define.CacheKeyCoinListByID).Result()
	if err != nil {
		log.Error("GetAllCoinMapByID redis error", zap.Error(err))
		return nil, err
	}

	var id int
	var coin proto.Coin
	mp := make(map[int]proto.Coin, len(encode))
	for key, val := range encode {
		err = json.Unmarshal(convert.Str2Bytes(val), &coin)
		if err != nil {
			log.Error("GetAllCoinMapByID json unmarshal error", zap.String("field", key), zap.Error(err))
			return nil, err
		}
		id, _ = strconv.Atoi(key)
		mp[id] = coin
	}
	return mp, nil
}

func GetAllCoinMapByName() (map[string]proto.Coin, error) {
	encode, err := DefaultRedisWithDB(define.CacheDBNumber2).HGetAll(define.CacheKeyCoinListByName).Result()
	if err != nil {
		log.Error("GetAllCoinMapByName redis error", zap.Error(err))
		return nil, err
	}

	var coin proto.Coin
	mp := make(map[string]proto.Coin, len(encode))
	for key, val := range encode {
		err = json.Unmarshal(convert.Str2Bytes(val), &coin)
		if err != nil {
			log.Error("GetAllCoinMapByName json unmarshal error", zap.String("field", key), zap.Error(err))
			return nil, err
		}
		mp[key] = coin
	}
	return mp, nil
}

func GetCoinByID(coinID int) (*proto.Coin, error) {
	coin := new(proto.Coin)
	encode, err := DefaultRedisWithDB(define.CacheDBNumber2).HGet(define.CacheKeyCoinListByID, strconv.Itoa(coinID)).Bytes()
	if err != nil {
		if err != redis.Nil {
			log.Error("GetCoinByID redis error", zap.Error(err))
		}
		return coin, err
	}

	err = json.Unmarshal(encode, &coin)
	if err != nil {
		log.Error("GetCoinByID json unmarshal error", zap.Int("coinID", coinID), zap.Error(err))
		return coin, err
	}
	return coin, nil
}

func GetCoinByName(coinName string) (*proto.Coin, error) {
	coin := new(proto.Coin)
	encode, err := DefaultRedisWithDB(define.CacheDBNumber2).HGet(define.CacheKeyCoinListByName, strings.ToUpper(coinName)).Bytes()
	if err != nil {
		if err != redis.Nil {
			log.Error("GetCoinByName redis error", zap.Error(err))
		}
		return coin, err
	}

	err = json.Unmarshal(encode, &coin)
	if err != nil {
		log.Error("GetCoinByName json unmarshal error", zap.String("coinName", coinName), zap.Error(err))
		return coin, err
	}
	return coin, nil
}

var marginCoinSetLock sync.Mutex

func SaveMarginCoinList(list []proto.MarginCurrency, force bool) {
	var counter int
flag:
	if !marginCoinSetLock.TryLock() {
		if !force || counter > 10 {
			return
		}

		counter++
		time.Sleep(time.Second)
		goto flag
	}
	defer marginCoinSetLock.Unlock()

	idKeys := make(map[string]define.Placeholder, len(list))
	nameKeys := make(map[string]define.Placeholder, len(list))

	for i := range list {
		nameKeys[list[i].CurrencyName] = define.PlaceholderEntity
		idKeys[strconv.Itoa(list[i].CurrencyId)] = define.PlaceholderEntity
	}

	var delIDs []string

	currIDKeys := DefaultRedisWithDB(define.CacheDBNumber2).HKeys(define.CacheKeyMarginCoinListByID).Val()
	for i := range currIDKeys {
		if _, ok := idKeys[currIDKeys[i]]; !ok {
			delIDs = append(delIDs, currIDKeys[i])
		}
	}

	var delNames []string
	currNameKeys := DefaultRedisWithDB(define.CacheDBNumber2).HKeys(define.CacheKeyMarginCoinListByName).Val()
	for i := range currNameKeys {
		if _, ok := nameKeys[currNameKeys[i]]; !ok {
			delNames = append(delNames, currNameKeys[i])
		}
	}

	pp := DefaultRedisWithDB(define.CacheDBNumber2).TxPipeline()
	for i := range list {
		data, err := json.Marshal(&list[i])
		if err != nil {
			log.Error("SaveMarginCoinList json error", zap.Error(err))
			return
		}

		pp.HSet(define.CacheKeyMarginCoinListByID, strconv.Itoa(list[i].CurrencyId), data)
		pp.HSet(define.CacheKeyMarginCoinListByName, strings.ToUpper(list[i].CurrencyName), data)
	}
	if len(delIDs) > 0 {
		pp.HDel(define.CacheKeyMarginCoinListByID, delIDs...)
	}
	if len(delNames) > 0 {
		pp.HDel(define.CacheKeyMarginCoinListByName, delNames...)
	}
	_, err := pp.Exec()
	if err != nil {
		log.Error("SaveMarginCoinList redis EXEC error", zap.Error(err))
		return
	}
}

func GetAllMarginCoinList() ([]proto.MarginCurrency, error) {
	encode, err := DefaultRedisWithDB(define.CacheDBNumber2).HVals(define.CacheKeyMarginCoinListByID).Result()
	if err != nil {
		log.Error("GetAllMarginCoinList redis error", zap.Error(err))
		return nil, err
	}

	var coin proto.MarginCurrency
	list := make([]proto.MarginCurrency, 0, len(encode))
	for i := range encode {
		//log.Infof("print:%v",string(encode[i]))
		err = json.Unmarshal(convert.Str2Bytes(encode[i]), &coin)
		if err != nil {
			log.Error("GetAllMarginCoinList json unmarshal error", zap.Error(err))
			return nil, err
		}
		list = append(list, coin)
	}

	return list, nil
}

func GetAllMarginCoinMapByID() (map[int]proto.MarginCurrency, error) {
	encode, err := DefaultRedisWithDB(define.CacheDBNumber2).HGetAll(define.CacheKeyMarginCoinListByID).Result()
	if err != nil {
		log.Error("GetAllMarginCoinMapByID redis error", zap.Error(err))
		return nil, err
	}

	var id int
	var coin proto.MarginCurrency
	mp := make(map[int]proto.MarginCurrency, len(encode))
	for key, val := range encode {
		err = json.Unmarshal(convert.Str2Bytes(val), &coin)
		if err != nil {
			log.Error("GetAllMarginCoinMapByID json unmarshal error", zap.String("field", key), zap.Error(err))
			return nil, err
		}
		id, _ = strconv.Atoi(key)
		mp[id] = coin
	}
	return mp, nil
}

func GetAllMarginCoinMapByName() (map[string]proto.MarginCurrency, error) {
	encode, err := DefaultRedisWithDB(define.CacheDBNumber2).HGetAll(define.CacheKeyMarginCoinListByName).Result()
	if err != nil {
		log.Error("GetAllMarginCoinMapByName redis error", zap.Error(err))
		return nil, err
	}

	var coin proto.MarginCurrency
	mp := make(map[string]proto.MarginCurrency, len(encode))
	for key, val := range encode {
		err = json.Unmarshal(convert.Str2Bytes(val), &coin)
		if err != nil {
			log.Error("GetAllMarginCoinMapByName json unmarshal error", zap.String("field", key), zap.Error(err))
			return nil, err
		}
		mp[key] = coin
	}
	return mp, nil
}

func GetMarginCoinByID(coinID int) (*proto.MarginCurrency, error) {
	coin := new(proto.MarginCurrency)
	encode, err := DefaultRedisWithDB(define.CacheDBNumber2).HGet(define.CacheKeyMarginCoinListByID, strconv.Itoa(coinID)).Bytes()
	if err != nil {
		if err != redis.Nil {
			log.Error("GetMarginCoinByID redis error", zap.Error(err))
		}
		return coin, err
	}

	err = json.Unmarshal(encode, &coin)
	if err != nil {
		log.Error("GetMarginCoinByID json unmarshal error", zap.Int("coinID", coinID), zap.Error(err))
		return coin, err
	}
	return coin, nil
}

func GetMarginCoinByName(coinName string) (*proto.MarginCurrency, error) {
	coin := new(proto.MarginCurrency)
	encode, err := DefaultRedisWithDB(define.CacheDBNumber2).HGet(define.CacheKeyMarginCoinListByName, strings.ToUpper(coinName)).Bytes()
	if err != nil {
		if err != redis.Nil {
			log.Error("GetMarginCoinByName redis error", zap.Error(err))
		}
		return coin, err
	}

	err = json.Unmarshal(encode, &coin)
	if err != nil {
		log.Error("GetMarginCoinByName json unmarshal error", zap.String("coinName", coinName), zap.Error(err))
		return coin, err
	}
	return coin, nil
}

func GetCoinWalletConfByNameAndProtocol(coinName, protocol string) (proto.WalletCoinConf, error) {
	var val proto.WalletCoinConf
	result, err := DefaultRedisWithDB(define.CacheDBNumber2).HGet(define.CacheKeyWalletCoinDictV2, coinName).Bytes()
	if err != nil {
		return val, err
	}
	dict := make(map[string]proto.WalletCoinConf)
	err = json.Unmarshal(result, &dict)
	if err != nil {
		return val, err
	}
	if val, ok := dict[protocol]; ok {
		return val, nil
	}
	return val, redis.Nil
}

// map[coinID]map[proto]coinConf{}
func GetAllWalletConfDictFromCache() (dict map[string]map[string]proto.WalletCoinConf, err error) {
	result, err := DefaultRedisWithDB(define.CacheDBNumber2).HGetAll(define.CacheKeyWalletCoinDictV2).Result()
	if err != nil {
		log.Errorf("GetAllWalletConfDictFromCache redis HGETALL error, err:%v", err)
		return
	}
	if len(result) == 0 {
		return nil, redis.Nil
	}

	dict = make(map[string]map[string]proto.WalletCoinConf, len(result))
	for key, val := range result {
		var conf map[string]proto.WalletCoinConf
		err = json.Unmarshal([]byte(val), &conf)
		if err != nil {
			log.Errorf("GetAllWalletConfDictFromCache json error, err:%v", err)
			return
		}
		dict[key] = conf
	}
	return
}

func SaveCoinWalletDict(dict map[string]map[string]proto.WalletCoinConf) {
	if len(dict) == 0 {
		return
	}

	var err error
	var data []byte
	pp := DefaultRedisWithDB(define.CacheDBNumber2).TxPipeline()
	for key, val := range dict {
		data, err = json.Marshal(&val)
		if err != nil {
			log.Errorf("SaveCoinWalletDict json error, err:%v", err)
			return
		}
		pp.HSet(define.CacheKeyWalletCoinDictV2, key, data)
	}
	_, err = pp.Exec()
	if err != nil {
		log.Errorf("SaveCoinWalletDict redis EXEC error, err:%v", err)
		return
	}
}

func GetWalletConfFromCache(walletName string) (walletConf proto.WalletCoinConf, err error) {
	result, err := DefaultRedisWithDB(define.CacheDBNumber2).HGet(define.CacheKeyWalletCoinConfig, walletName).Bytes()
	if err != nil {
		return
	}
	err = json.Unmarshal(result, &walletConf)
	return
}

// map[walletName]coinConf{}
func GetAllWalletConfFromCache() (dict map[string]proto.WalletCoinConf, err error) {
	result, err := DefaultRedisWithDB(define.CacheDBNumber2).HVals(define.CacheKeyWalletCoinConfig).Result()
	if err != nil {
		log.Errorf("GetAllWalletConfFromCache redis HVALS error, err:%v", err)
		return
	}
	if len(result) == 0 {
		return nil, redis.Nil
	}

	var conf proto.WalletCoinConf
	dict = make(map[string]proto.WalletCoinConf, len(result))
	for i := range result {
		err = json.Unmarshal([]byte(result[i]), &conf)
		if err != nil {
			log.Errorf("GetAllWalletConfFromCache json error, err:%v", err)
			return
		}
		dict[conf.WalletName] = conf
	}
	return
}

func SaveAllWalletConf(dict map[string]map[string]proto.WalletCoinConf) {
	if len(dict) == 0 {
		return
	}

	var err error
	var data []byte
	pp := DefaultRedisWithDB(define.CacheDBNumber2).TxPipeline()
	for _, v := range dict {
		for _, vv := range v {
			data, err = json.Marshal(&vv)
			if err != nil {
				log.Errorf("SaveAllWalletConf json error, err:%v", err)
				return
			}
			pp.HSet(define.CacheKeyWalletCoinConfig, vv.WalletName, data)
		}
	}
	_, err = pp.Exec()
	if err != nil {
		log.Errorf("SaveAllWalletConf redis EXEC error, err:%v", err)
		return
	}
}

func GetAllCoinLegalRate() (map[string]proto.CoinLegalPrice, error) {
	// 同一服务的同一时间内执行,合并查询
	return singleflight.MergeDo[map[string]proto.CoinLegalPrice](define.CacheKeyCoinLegalRate, func() (any, error) {
		bs, err := DefaultRedis().HGetAll(define.CacheKeyCoinLegalRate).Result()
		if err != nil {
			return nil, err
		}

		var p proto.CoinLegalPrice
		m := make(map[string]proto.CoinLegalPrice, len(bs))
		for k, v := range bs {
			err = json.Unmarshal([]byte(v), &p)
			if err != nil {
				return nil, err
			}
			m[k] = p
		}
		return m, err
	})
}

func SaveCoinLegalRate(source string, v *proto.CoinLegalPrice) {
	b, err := json.Marshal(&v)
	if err != nil {
		log.Error("SaveCoinLegalRate marshal fail",
			zap.String("source", source),
			zap.Any("v", v),
			zap.Error(err))
		return
	}
	err = DefaultRedis().HSet(define.CacheKeyCoinLegalRate, strings.ToUpper(source), b).Err()
	if err != nil {
		log.Error("SaveLegalRate hmset fail",
			zap.String("source", source),
			zap.Any("v", v),
			zap.Error(err))
		return
	}
}
