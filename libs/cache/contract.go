package cache

import (
	"github.com/go-redis/redis"
	"sort"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
	"spot/libs/convert"
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/nums"
	"spot/libs/proto"
)

var contractSetLock sync.Mutex

func SaveContractList(list []proto.Contract, force bool) {
	var counter int
flag:
	if !contractSetLock.TryLock() {
		if !force || counter > 10 {
			return
		}

		counter++
		time.Sleep(time.Second)
		goto flag
	}
	defer contractSetLock.Unlock()

	codeKeys := make(map[string]define.Placeholder, len(list))
	for i := range list {
		codeKeys[list[i].ContractCode] = define.PlaceholderEntity
	}

	var delCodes []string
	currCodeKeys := DefaultRedis().HKeys(define.CacheKeyContractList).Val()
	for i := range currCodeKeys {
		if _, ok := codeKeys[currCodeKeys[i]]; !ok {
			delCodes = append(delCodes, currCodeKeys[i])
		}
	}

	pp := DefaultRedis().TxPipeline()
	for i := range list {
		data, err := json.Marshal(&list[i])
		if err != nil {
			log.Error("SaveContractList json error", zap.Error(err))
			return
		}

		pp.HSet(define.CacheKeyContractList, strings.ToUpper(list[i].ContractCode), data)
	}
	if len(delCodes) > 0 {
		pp.HDel(define.CacheKeyContractList, delCodes...)
	}
	_, err := pp.Exec()
	if err != nil {
		log.Error("SaveContractList redis EXEC error", zap.Error(err))
		return
	}
}

func GetAllContractList() ([]proto.Contract, error) {
	encode, err := DefaultRedis().HVals(define.CacheKeyContractList).Result()
	if err != nil {
		log.Error("GetAllContractList redis error", zap.Error(err))
		return nil, err
	}

	var contract proto.Contract
	list := make([]proto.Contract, 0, len(encode))
	for i := range encode {
		//log.Infof("print:%v",string(encode[i]))
		err = json.Unmarshal(convert.Str2Bytes(encode[i]), &contract)
		if err != nil {
			log.Error("GetAllContractList json unmarshal error", zap.Error(err))
			return nil, err
		}
		list = append(list, contract)
	}

	sort.SliceStable(list, func(i, j int) bool {
		return list[i].OrderBy > list[j].OrderBy
	})

	return list, nil
}

func GetAllContractMap() (map[string]proto.Contract, error) {
	encode, err := DefaultRedis().HGetAll(define.CacheKeyContractList).Result()
	if err != nil {
		log.Error("GetAllContractMap redis error", zap.Error(err))
		return nil, err
	}

	var contract proto.Contract
	mp := make(map[string]proto.Contract, len(encode))
	for key, val := range encode {
		err = json.Unmarshal(convert.Str2Bytes(val), &contract)
		if err != nil {
			log.Error("GetAllContractMap json unmarshal error", zap.String("field", key), zap.Error(err))
			return nil, err
		}
		mp[key] = contract
	}
	return mp, nil
}

func GetContractInfo(contractCode string) (*proto.Contract, error) {
	encode, err := DefaultRedis().HGet(define.CacheKeyContractList, contractCode).Bytes()
	if err != nil {
		log.Error("GetContractInfo redis error", zap.Error(err), zap.String("code", contractCode))
		return nil, err
	}

	contract := new(proto.Contract)
	err = json.Unmarshal(encode, contract)
	if err != nil {
		log.Error("GetContractInfo json unmarshal error", zap.String("field", contractCode), zap.Error(err))
		return nil, err
	}
	return contract, nil
}

//删除合约配置
func DelContractDepthConfig() (err error) {
	e := DefaultRedis().Del(define.CacheKeyContractDepthConfig).Err()
	if e != nil {
		log.Errorf("DelContractDepthConfig fail,%v", err)
		return
	}
	return
}

//设置合约深度配置
func SetContractDepthConfig(list []proto.ContractDepth) (err error) {
	b, err := json.Marshal(list)
	if err != nil {
		log.Errorf("SetContractDepth fail,%v", err)
		return
	}
	log.Info("设置深度配置到缓存", zap.ByteString("string", b))
	err = DefaultRedis().Set(define.CacheKeyContractDepthConfig, b, 10*time.Second).Err()
	//err = DefaultRedis().Set(define.CacheKeyContractDepthConfig, b, time.Minute).Err()
	return
}

//获取合约深度配置
func GetContractDepthConfig() (list []proto.ContractDepth, err error) {
	b, err := DefaultRedis().Get(define.CacheKeyContractDepthConfig).Bytes()
	if err != nil {
		log.Errorf("GetContractDepthConfig -》Get \"bc:contract:depth:config\"  fail,%v", err)
		return
	}
	err = json.Unmarshal(b, &list)
	if err != nil {
		log.Errorf("GetContractDepthConfig fail,%v", err)
		return
	}
	return
}

//删除合约配置
func DelContractDepthSource() (err error) {
	e := DefaultRedis().Del(define.CacheKeyContractDepthSource).Err()
	if e != nil {
		log.Errorf("DelContractDepthSource fail,%v", err)
		return
	}
	return
}

//设置合约深度配置
func SetContractDepthSource(list []proto.ContractDepthSource) (err error) {
	b, err := json.Marshal(list)
	if err != nil {
		log.Errorf("SetContractDepthSource fail,%v", err)
		return
	}
	err = DefaultRedis().Set(define.CacheKeyContractDepthSource, b, time.Minute).Err()
	return
}

//获取合约深度配置
func GetContractDepthSource() (list []proto.ContractDepthSource, err error) {
	b, err := DefaultRedis().Get(define.CacheKeyContractDepthSource).Bytes()
	if err != nil {
		log.Errorf("GetContractDepthSource -》Get \"bc:contract:depth:source\"  fail,%v", err)
		return
	}
	err = json.Unmarshal(b, &list)
	if err != nil {
		log.Errorf("GetContractDepthSource fail,%v", err)
		return
	}
	return
}

func SetDepthPriceSwitchException(code string) (err error) {
	err = DefaultRedis().HSet(define.DepthPriceSourceException, code, convert.Int64String(time.Now().Unix())).Err()
	return
}

func GetDepthPriceSwitchException(code string) (s int64) {
	s, err := DefaultRedis().HGet(define.DepthPriceSourceException, code).Int64()
	if err != nil {
		log.Errorf("GetDepthPriceSwitchException fail,%v,code：%v", err, code)
	}
	return
}

func DelDepthPriceSwitchException(code string) (seconds int64) {
	err := DefaultRedis().HDel(define.DepthPriceSourceException, code).Err()
	if err != nil {
		log.Errorf("DelDepthPriceSwitchException DefaultRedis().HDel(define.DepthPriceSourceException, code).Err():%v", err)
		return
	}
	return
}

func SetDepthSwitchExceptionCache(code string, originSwitch int) (err error) {
	key := define.DepthPriceSourceSwitchException + code + nums.Int2String(originSwitch)
	err = DefaultRedis().Set(key, time.Now().Unix(), 2*time.Minute).Err()
	return
}

func GetDepthSwitchExceptionCache(code string, originSwitch int) (s int64) {
	key := define.DepthPriceSourceSwitchException + code + nums.Int2String(originSwitch)
	s, err := DefaultRedis().Get(key).Int64()
	if err != nil {
		log.Errorf("GetDepthSwitchExceptionCache fail,%v,code：%v", err, code)
	}
	return
}

func DelDepthSwitchExceptionCache(code string, originSwitch int) (seconds int64) {
	key := define.DepthPriceSourceSwitchException + code + nums.Int2String(originSwitch)
	err := DefaultRedis().Del(key).Err()
	if err != nil {
		log.Errorf("DelDepthSwitchExceptionCache.Err():%v", err)
		return
	}
	return
}

func SetDepthAvgPriceSpotNumsCheck(code string) (s int64) {
	err := DefaultRedis().Set(define.DepthPriceAvgWarn+code, code, 10*time.Second).Err()
	if err != nil {
		log.Errorf("SetDepthAvgPriceSpotNumsCheck fail,%v,code：%v", err, code)
	}
	return
}

func GetDepthAvgPriceSpotNumsCheck(code string) bool {
	s, err := DefaultRedis().Exists(define.DepthPriceAvgWarn + code).Result()
	if err != nil {
		log.Errorf("GetDepthPriceSwitchException fail,%v,code：%v", err, code)
	}
	if s == 1 {
		return true
	}
	return false
}

func DelDepthAvgPriceSpotNumsCheck(code string) (seconds int64) {
	err := DefaultRedis().Del(define.DepthPriceAvgWarn + code).Err()
	if err != nil {
		log.Errorf("DelDepthAvgPriceSpotNumsCheck .Err():%v", err)
		return
	}
	return
}

func SaveCoinMarketConf(dict map[string]proto.CoinMarketConf) {
	var delCodes []string
	currCodeKeys := DefaultRedis().HKeys(define.CacheKeyCoinMarketConfList).Val()
	for i := range currCodeKeys {
		if _, ok := dict[currCodeKeys[i]]; !ok {
			delCodes = append(delCodes, currCodeKeys[i])
		}
	}

	pp := DefaultRedis().TxPipeline()
	defer func() { _ = pp.Close() }()
	for key, val := range dict {
		data, err := json.Marshal(&val)
		if err != nil {
			log.Error("SaveCoinMarketConf json error", zap.Error(err))
			return
		}

		pp.HSet(define.CacheKeyCoinMarketConfList, strings.ToUpper(key), data)
	}
	if len(delCodes) > 0 {
		pp.HDel(define.CacheKeyCoinMarketConfList, delCodes...)
	}
	_, err := pp.Exec()
	if err != nil {
		log.Error("SaveCoinMarketConf redis EXEC error", zap.Error(err))
		return
	}
}

func GetCoinMarketConf() (map[string]proto.CoinMarketConf, error) {
	res, err := DefaultRedis().HGetAll(define.CacheKeyCoinMarketConfList).Result()
	if err != nil {
		if err == redis.Nil {
			return make(map[string]proto.CoinMarketConf), nil
		}
		return nil, err
	}

	var mc proto.CoinMarketConf
	dict := make(map[string]proto.CoinMarketConf)
	for key, val := range res {
		err = json.Unmarshal([]byte(val), &mc)
		if err != nil {
			return nil, err
		}
		dict[key] = mc
	}
	return dict, nil
}
