package cache

import (
	"github.com/go-redis/redis"
	"go.uber.org/zap"
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/mq"
	"spot/libs/nums"
	"spot/libs/utils"
	"time"
)

func DelOrderSeqId(orderId int64) {
	DelOrderWaitFinish(orderId)
	DelOrderDealingMsg(orderId)
	key := utils.StrBuilderBySep(":", define.CacheOrderMsgSeqKey, nums.Int64String(orderId))
	pip := DefaultRedis().Pipeline()

	pip.Del(key)
	key2 := utils.StrBuilderBySep(":", define.CacheCurOrderSeqID, nums.Int64String(orderId))
	pip.Del(key2)
	_, err := pip.Exec()
	if err != nil {
		log.Error("DelOrderSeqId fail", zap.Error(err))
	}

}

//生成订单队列序号
func IncrementOrderSeqId(orderId int64) (seqId int64) {
	var err error
	key := utils.StrBuilderBySep(":", define.CacheOrderMsgSeqKey, nums.Int64String(orderId))
	seqId, err = DefaultRedis().Incr(key).Result()
	//pip := DefaultRedis().Pipeline()
	//seqId, err = pip.Incr(key).Result()
	if err != nil {
		log.Error("IncrementOrderSeqId", zap.Error(err))
		return
	}
	//pip.Expire(key, 5*time.Minute)
	//cmd, err := pip.Exec()
	//if err != nil {
	//	log.Error("IncrementOrderSeqId  pip.Exec() fail", zap.Error(err))
	//	return
	//}
	//for _, cmder := range cmd {
	//	log.Info("cmd",zap.Error(cmder.Err()))
	//}
	//log.Info("seqId",zap.Int64("d",seqId))
	return
}

func SetOrderCurDealSeqId(orderId, seqId int64) {
	key := utils.StrBuilderBySep(":", define.CacheCurOrderSeqID, nums.Int64String(orderId))
	_, err := DefaultRedis().Set(key, nums.Int64String(seqId), 0*time.Minute).Result()
	if err != nil {
		log.Error("SetOrderCurDealSeqId  pip.Exec() fail", zap.Error(err))
		return
	}
	return
}

func GetOrderCurDealSeqId(orderId int64) (seqId int64) {
	key := utils.StrBuilderBySep(":", define.CacheCurOrderSeqID, nums.Int64String(orderId))
	seqId, err := DefaultRedis().Get(key).Int64()
	if err != nil && err != redis.Nil {
		log.Error("GetOrderCurDealSeqId  pip.Exec() fail", zap.Error(err))
		return
	}
	return
}

func ExistOrderDealingMsg(id int64) bool {
	key := utils.StrBuilderBySep(":", define.CacheCurOrderDealing, nums.Int64String(id))
	result, err := DefaultRedis().Exists(key).Result()
	if err != nil {
		log.Error("ExistOrderDealingMsg redis error", zap.String("key", key), zap.Error(err))
	}
	return result == 1
}

func ListOrderDealingMsg(id int64) (result []mq.MessagePack) {
	key := utils.StrBuilderBySep(":", define.CacheCurOrderDealing, nums.Int64String(id))
	list, err := DefaultRedis().HVals(key).Result()
	if err != nil {
		return
	}
	for _, s := range list {
		m := new(mq.MessagePack)
		err := json.Unmarshal([]byte(s), m)
		if err != nil {
			log.Error("ListOrderDealingMsg json unMarshal fail", zap.Error(err))
			return
		}
		result = append(result, *m)
	}
	return
}

func GetOrderDealingMsgBySeqId(orderId, seqId int64) (result *mq.MessagePack) {
	key := utils.StrBuilderBySep(":", define.CacheCurOrderDealing, nums.Int64String(orderId))
	rsp, err := DefaultRedis().HGet(key, nums.Int64String(seqId)).Bytes()
	if err != nil {
		if err != redis.Nil {
			log.Error("GetOrderDealingMsgBySeqId hget fail", zap.Error(err))
		}
		return
	}
	m := new(mq.MessagePack)
	err = json.Unmarshal(rsp, m)
	if err != nil {
		log.Error("GetOrderDealingMsgBySeqId json unMarshal fail", zap.Error(err))
		return
	}
	result = m
	return
}

func SetOrderDealingMsg(msg mq.MessagePack) {
	key := utils.StrBuilderBySep(":", define.CacheCurOrderDealing, nums.Int64String(msg.Extra.ID))
	b, err := json.Marshal(msg)
	if err != nil {
		log.Error("SetClosingMsg fail", zap.Error(err))
		return
	}
	err = DefaultRedis().HSet(key, nums.Int64String(msg.Extra.SeqId), b).Err()

}

func DelOrderDealingMsgSeq(orderId, seqId int64) (err error) {
	key := utils.StrBuilderBySep(":", define.CacheCurOrderDealing, nums.Int64String(orderId))
	e := DefaultRedis().HDel(key, nums.Int64String(seqId)).Err()
	if e != nil {
		log.Errorf("DelOrderDealingMsgSeq fail,%v", err)
		return
	}
	return
}

func DelOrderDealingMsg(orderId int64) (err error) {
	key := utils.StrBuilderBySep(":", define.CacheCurOrderDealing, nums.Int64String(orderId))
	e := DefaultRedis().Del(key).Err()
	if e != nil {
		log.Errorf("DelOrderDealingMsg fail,%v", err)
		return
	}
	return
}

func ListOrderWaitFinish() (result []mq.MessagePack) {
	key := define.CacheOrderWaitFinish
	list, err := DefaultRedis().HVals(key).Result()
	if err != nil {
		return
	}
	for _, s := range list {
		m := new(mq.MessagePack)
		err := json.Unmarshal([]byte(s), m)
		if err != nil {
			log.Error("ListOrderWaitFinish json unMarshal fail", zap.Error(err))
			return
		}
		result = append(result, *m)
	}
	return
}

func SetOrderWaitFinish(msg mq.MessagePack) {
	key := define.CacheOrderWaitFinish
	b, err := json.Marshal(msg)
	if err != nil {
		log.Error("SetOrderWaitFinish fail", zap.Error(err))
		return
	}
	err = DefaultRedis().HSet(key, nums.Int64String(msg.Extra.ID), b).Err()
}

func DelOrderWaitFinish(orderId int64) (err error) {
	key := define.CacheOrderWaitFinish
	e := DefaultRedis().HDel(key, nums.Int64String(orderId)).Err()
	if e != nil {
		log.Errorf("DelOrderWaitFinish fail,%v", err)
		return
	}
	return
}
