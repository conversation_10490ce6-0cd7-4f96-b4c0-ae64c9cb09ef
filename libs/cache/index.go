/*
@Time : 3/6/20 2:48 下午
<AUTHOR> mocha
@File : index
*/
package cache

import (
	"go.uber.org/zap"
	"spot/libs/convert"
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/proto"
)

//设置合约成交价
func SetContractPriceIndex(history *proto.IndexHistory) {
	b, err := json.Marshal(*history)
	if err != nil {
		log.Errorf("json marshal fail,%v", err)
		return
	}
	DefaultRedis().HSet(define.CacheKeyContractIndex, history.ContractCode, b)
}

//获取合约价格指数
func GetContractPriceIndexIgnoreErr(contractCode string) (index *proto.IndexHistory) {
	index = GetContractPriceIndex(contractCode)
	if index == nil {
		index = new(proto.IndexHistory)
	}
	return
}

//获取合约成交价
func GetContractPriceIndex(contractCode string) (in *proto.IndexHistory) {
	index := new(proto.IndexHistory)
	b, err := DefaultRedis().HGet(define.CacheKeyContractIndex, contractCode).Bytes()
	if err != nil {
		log.Infof("GetContractPriceIndex get fail,%v,contractCode:%v,", err, contractCode)
		return
	}
	err = json.Unmarshal(b, index)
	if err != nil {
		log.Errorf("json unmarshal fail,%v", err)
		return
	}
	in = index
	return
}

// 获取全部合约最新成交价格
func GetAllPriceIndex() map[string]proto.IndexHistory {
	mp := make(map[string]proto.IndexHistory)
	result, err := DefaultRedis().HGetAll(define.CacheKeyContractIndex).Result()
	if err != nil {
		log.Error("GetAllPriceIndex redis error", zap.Error(err))
		return mp
	}

	var index proto.IndexHistory
	for key, val := range result {
		err = json.Unmarshal(convert.Str2Bytes(val), &index)
		if err != nil {
			log.Error("GetAllPriceIndex json unmarshal error", zap.Error(err))
			continue
		}
		mp[key] = index
	}
	return mp
}

//设置合约现货指数价格
func SetContractSpotIndexPrice(history *proto.ComplexPrice) {
	b, err := json.Marshal(*history)
	if err != nil {
		log.Errorf("SetContractSpotIndexPrice json marshal fail,contract:%v,%v", history.ContractCode, err)
		return
	}
	DefaultRedis().HSet(define.CacheKeyContractSpotIndexPrice, history.ContractCode, b)
}

//获取合约现货指数价格
func GetContractSpotIndexPrice(contractCode string) (index *proto.ComplexPrice) {
	index = new(proto.ComplexPrice)
	b, err := DefaultRedis().HGet(define.CacheKeyContractSpotIndexPrice, contractCode).Bytes()
	if err != nil {
		log.Warnf("GetContractSpotIndexPrice get fail,contract:%v,%v", contractCode, err)
		return
	}
	err = json.Unmarshal(b, index)
	if err != nil {
		log.Errorf("json unmarshal fail,%v", err)
		return
	}
	return
}

//获取所有合约现货指数价格
func GetAllContractSpotIndexPrice() (m map[string]proto.ComplexPrice) {
	m = make(map[string]proto.ComplexPrice)
	list, err := DefaultRedis().HVals(define.CacheKeyContractSpotIndexPrice).Result()
	if err != nil {
		log.Warnf("GetAllContractSpotIndexPrice get fail%v", err)
		return
	}
	for _, v := range list {
		index := new(proto.ComplexPrice)
		err = json.Unmarshal([]byte(v), index)
		if err != nil {
			log.Errorf("json unmarshal fail,%v", err)
			continue
		}
		m[index.ContractCode] = *index
	}
	return
}

//获取所有合约现货指数价格
func GetAllContractSpotIndexPriceWithExtraRedis() (m map[string]proto.ComplexPrice) {
	m = make(map[string]proto.ComplexPrice)
	list, err := DefaultRedisWithDB(define.CacheDBNumber2).HVals(define.CacheKeyContractSpotIndexPrice).Result()
	if err != nil {
		log.Warnf("GetAllContractSpotIndexPrice get fail%v", err)
		return
	}
	for _, v := range list {
		index := new(proto.ComplexPrice)
		err = json.Unmarshal([]byte(v), index)
		if err != nil {
			log.Errorf("json unmarshal fail,%v", err)
			continue
		}
		m[index.ContractCode] = *index
	}
	return
}

//设置合约铺单基准价格（采用三大所辅助使用现货指数）
func SetContractDepthBasePrice(history *proto.ComplexPrice) {
	b, err := json.Marshal(*history)
	if err != nil {
		log.Errorf("json marshal fail,contract:%v,%v", history.ContractCode, err)
		return
	}
	DefaultRedis().HSet(define.CacheKeyContractDepthBasePrice, history.ContractCode, b)
}

//获取合约铺单基准价格（采用三大所辅助使用现货指数)
func GetContractDepthBasePrice(contractCode string) (index *proto.ComplexPrice) {
	index = new(proto.ComplexPrice)
	b, err := DefaultRedis().HGet(define.CacheKeyContractDepthBasePrice, contractCode).Bytes()
	if err != nil {
		log.Warnf("GetContractComplexTradePrice get fail,contract:%v,%v", contractCode, err)
		return
	}
	err = json.Unmarshal(b, index)
	if err != nil {
		log.Errorf("json unmarshal fail,%v", err)
		return
	}
	return
}

// GetAllContractDepthBasePrice 获取所有合约的铺单基准价
func GetAllContractDepthBasePrice() (cmap map[string]proto.ComplexPrice) {
	cmap = make(map[string]proto.ComplexPrice)
	m, err := DefaultRedis().HGetAll(define.CacheKeyContractDepthBasePrice).Result()
	if err != nil {
		log.Warn("GetAllContractDepthBasePrice fail", zap.Error(err))
		return
	}
	for code, s := range m {
		index := new(proto.ComplexPrice)
		err = json.Unmarshal([]byte(s), index)
		if err != nil {
			log.Errorf("json unmarshal fail,%v", err)
			return
		}
		cmap[code] = *index
	}
	return
}

//设置合约上次基准价格
func SetContractCurDepthBasePrice(history *proto.ComplexPrice) {
	b, err := json.Marshal(*history)
	if err != nil {
		log.Errorf("json marshal fail,contract:%v,%v", history.ContractCode, err)
		return
	}
	DefaultRedis().HSet(define.CacheKeyContractCurDepthPrice, history.ContractCode, b)
}

//获取合约上次铺单基准价格
func GetContractCurDepthBasePrice(contractCode string) (index *proto.ComplexPrice) {
	index = new(proto.ComplexPrice)
	b, err := DefaultRedis().HGet(define.CacheKeyContractCurDepthPrice, contractCode).Bytes()
	if err != nil {
		log.Warnf("GetContractComplexTradePrice get fail,contract:%v,%v", contractCode, err)
		return
	}
	err = json.Unmarshal(b, index)
	if err != nil {
		log.Errorf("json unmarshal fail,%v", err)
		return
	}
	return
}

//获取撮合成交信息
// GetContractTradeMerge 获取合约成交综合信息
func GetContractTradeMerge(code string) (d *proto.DealInfo, err error) {
	b, err := DefaultRedis().HGet(define.CacheKeyTradePrice, code).Bytes()
	if err != nil {
		log.Errorf("GetContractTradeMerge fail,%v", err)
		return
	}
	d = new(proto.DealInfo)
	err = json.Unmarshal(b, d)
	if err != nil {
		return
	}
	return
}

// SetContractTradeMerge 设置合约价格和原始数据入缓存
func SetContractTradeMerge(d *proto.DealInfo) {
	if d == nil {
		return
	}
	in, _ := GetContractTradeMerge(d.Code)
	if in != nil {
		d.LastDealPrice = in.LastDealPrice
		if !in.CreateTime.IsZero() && d.CreateTime.Before(in.CreateTime) {
			log.Info("设置合约综合成交数据，数据早于最新数据，本次不做处理", zap.Any("数据", d), zap.Any("缓存最新", in))
			return
		}
	}
	b, err := json.Marshal(d)
	if err != nil {
		log.Errorf("json marshal fail,%v", err)
		return
	}
	err = DefaultRedis().HSet(define.CacheKeyTradePrice, d.Code, b).Err()
	if err != nil {
		log.Errorf("SetContractTradeMerge hset fail,%v", err)
		return
	}
}

// GetAllTradeMerge 获取全部合约最新成交价格
func GetAllTradeMerge() map[string]proto.DealInfo {
	mp := make(map[string]proto.DealInfo)
	result, err := DefaultRedis().HGetAll(define.CacheKeyTradePrice).Result()
	if err != nil {
		log.Error("GetAllTradeMerge redis error", zap.Error(err))
		return mp
	}

	var index proto.DealInfo
	for key, val := range result {
		err = json.Unmarshal(convert.Str2Bytes(val), &index)
		if err != nil {
			log.Error("GetAllTradeMerge json unmarshal error", zap.Error(err))
			continue
		}
		mp[key] = index
	}
	return mp
}

//设置铺单简单数据
func SetContractDepthInfo(history *proto.DepthSimpleInfo) {
	b, err := json.Marshal(*history)
	if err != nil {
		log.Errorf("SetContractDepthInfo json marshal fail,contract:%v,%v", history.ContractCode, err)
		return
	}
	DefaultRedis().HSet(define.CacheKeyContractDepthSimpleInfo, history.ContractCode, b)
}

//获取铺单简单数据
func GetContractDepthInfo(contractCode string) (index *proto.DepthSimpleInfo) {
	index = new(proto.DepthSimpleInfo)
	b, err := DefaultRedis().HGet(define.CacheKeyContractDepthSimpleInfo, contractCode).Bytes()
	if err != nil {
		log.Warnf("GetContractDepthInfo get fail,contract:%v,%v", contractCode, err)
		return
	}
	err = json.Unmarshal(b, index)
	if err != nil {
		log.Errorf("json unmarshal fail,%v", err)
		return
	}
	return
}

//获取所有合约铺单简易数据
func GetAllContractDepthInfo() (m map[string]proto.DepthSimpleInfo) {
	m = make(map[string]proto.DepthSimpleInfo)
	list, err := DefaultRedis().HVals(define.CacheKeyContractDepthSimpleInfo).Result()
	if err != nil {
		log.Warnf("GetAllContractDepthInfo get fail%v", err)
		return
	}
	for _, v := range list {
		index := new(proto.DepthSimpleInfo)
		err = json.Unmarshal([]byte(v), index)
		if err != nil {
			log.Errorf("json unmarshal fail,%v", err)
			continue
		}
		m[index.ContractCode] = *index
	}
	return
}
