package cache

import (
	"github.com/go-redis/redis"
	"github.com/shopspring/decimal"
	"spot/libs/define"
	"spot/libs/json"
	"strconv"
)

func GetThirdLegalPriceRate(busID int, coin string) (map[string]decimal.Decimal, error) {
	res, err := DefaultRedis().HGet(define.CacheKeyThirdLegalAllRates+strconv.Itoa(busID), coin).Bytes()
	if err != nil && err != redis.Nil {
		return nil, err
	}

	rates := make(map[string]decimal.Decimal)
	if err == redis.Nil {
		return rates, nil
	}
	err = json.Unmarshal(res, &rates)
	return rates, err
}

func GetThirdLegalAllPriceRate(busID int) (map[string]map[string]decimal.Decimal, error) {
	res, err := DefaultRedis().HGetAll(define.CacheKeyThirdLegalAllRates + strconv.Itoa(busID)).Result()
	if err != nil && err != redis.Nil {
		return nil, err
	}

	rates := make(map[string]map[string]decimal.Decimal, len(res))
	for coin, val := range res {
		var rate map[string]decimal.Decimal
		err = json.Unmarshal([]byte(val), &rate)
		if err != nil {
			return nil, err
		}
		rates[coin] = rate
	}
	return rates, nil
}
