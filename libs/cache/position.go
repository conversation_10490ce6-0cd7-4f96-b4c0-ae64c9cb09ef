package cache

import (
	"github.com/go-redis/redis"
	"github.com/shopspring/decimal"
	"spot/libs/convert"
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/nums"
	"spot/libs/proto"
	"spot/libs/utils"
	"time"
)

//增加合约大额成交笔数
func IncrContractSideLargeTradeCount(code string, side string, count int64) (result int64) {
	c := DefaultRedis()
	result = c.HIncrBy(define.CacheKeyContractSideTradeCount, code+":"+side, count).Val()
	return
}

//重置合约大额成交笔数
func ResetContractSideLargeTradeCount() {
	DefaultRedis().Del(define.CacheKeyContractSideTradeCount)
}

func IncrContractGivingCount(code string, count int64) (result int64, err error) {
	result, err = DefaultRedis().HIncrBy(define.CacheKeyContractGivingCount, code, count).Result()
	return
}

func DcrContractGivingCount(code string, count int64) (result int64, err error) {
	result, err = DefaultRedis().HIncrBy(define.CacheKeyContractGivingCount, code, -count).Result()
	return
}

func SetContractGivingCount(code string, count int64) (err error) {
	err = DefaultRedis().HSet(define.CacheKeyContractGivingCount, code, convert.Int64String(count)).Err()
	return
}

func GetContractGivingCount(code string) (result int64, err error) {
	s, err := DefaultRedis().HGet(define.CacheKeyContractGivingCount, code).Result()
	if err != nil {
		if err != redis.Nil {
			log.Errorf("GetContractGivingCount fail,%v", err)
		}
		return
	}
	result = convert.String2Int64(s)
	return
}

//设置合约上次对敲上限
func SetContractBucketGivingLimit(bg *proto.Contract24Giving) {
	if bg == nil {
		return
	}
	b, err := json.Marshal(bg)
	if err != nil {
		log.Errorf("SetContractBucketGivingLimit json marshal fail,%v", err)
		return
	}
	err = DefaultRedis().HSet(define.CacheKeyContractLastGiving, bg.Contract, b).Err()
	if err != nil {
		log.Errorf("SetContractBucketGivingLimit hSet fail,%v", err)
	}
	return
}

//获取合约上次对敲放量
func GetContractBucketGivingLimit(code string) (bg *proto.Contract24Giving) {
	b, err := DefaultRedis().HGet(define.CacheKeyContractLastGiving, code).Bytes()
	if err != nil {
		if err != redis.Nil {
			log.Errorf("SetContractBucketGivingLimit hSet fail,%v", err)
		}
		return
	}
	if b == nil {
		return
	}
	bg = new(proto.Contract24Giving)
	err = json.Unmarshal(b, bg)
	if err != nil {
		return
	}
	return
}

func GetContractHourSplitRandIndex(code string) (bg *proto.HourRandV) {
	s := time.Now().Format("2006-01-02")
	key := utils.StrBuilder(define.CacheKeyContractHourDayRandIndex, ":", code, ":", s)
	b, err := DefaultRedis().Get(key).Bytes()
	if err != nil {
		log.Errorf("GetContractHourSplitRandIndex fail,%v", err)
		return
	}
	if b == nil {
		return
	}
	bg = new(proto.HourRandV)
	err = json.Unmarshal(b, bg)
	if err != nil {
		log.Errorf("GetContractHourSplitRandIndex json unmarshal fail,%v", err)
		return
	}
	return
}

func SetContractHourSplitRandIndex(bg *proto.HourRandV) {
	if bg == nil {
		return
	}
	s := time.Now().Format("2006-01-02")
	key := utils.StrBuilder(define.CacheKeyContractHourDayRandIndex, ":", bg.Code, ":", s)
	b, err := json.Marshal(bg)
	if err != nil {
		log.Errorf("SetContractHourSplitRandIndex fail,%v", err)
		return
	}
	err = DefaultRedis().Set(key, b, 24*time.Hour).Err()
	if err != nil {
		log.Errorf("SetContractHourSplitRandIndex fail,%v", err)
		return
	}
}

func GetContractDepthBuy(code string) (result decimal.Decimal) {
	s, err := DefaultRedis().HGet(define.CacheKeyContractDepthBuyValid, code).Result()
	if err != nil {
		if err != redis.Nil {
			log.Errorf("GetContractDepthBuy fail,%v", err)
		}
		return
	}
	return nums.NewFromString(s)
}

func SetContractDepthBuy(code string, count decimal.Decimal) {
	e := DefaultRedis().HSet(define.CacheKeyContractDepthBuyValid, code, count.String()).Err()
	if e != nil {
		log.Errorf("SetContractDepthBuy fail,%v", e)

	}
}

func GetContractDepthSell(code string) (result decimal.Decimal) {
	s, err := DefaultRedis().HGet(define.CacheKeyContractDepthSellValid, code).Result()
	if err != nil {
		log.Errorf("GetContractDepthSell fail,%v", err)
		return
	}
	return nums.NewFromString(s)
}

func SetContractDepthSell(code string, count decimal.Decimal) {
	e := DefaultRedis().HSet(define.CacheKeyContractDepthSellValid, code, count.String()).Err()
	if e != nil {
		log.Errorf("SetContractDepthSell fail,%v", e)
		return
	}
}

func DelContractDepthSellPosition() (err error) {
	err = DefaultRedis().Del(define.CacheKeyContractDepthSellValid).Err()
	if err != nil {
		log.Errorf("DelContractDepthSellPosition fail,%v", err)
		return err
	}
	return
}
