package cache

import (
	"strings"

	"go.uber.org/zap"
	"spot/libs/convert"
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/proto"
)

func GetAllCoinsRate(extraRedisId int) (map[string]proto.CoinRate, error) {
	mp := make(map[string]proto.CoinRate)
	bs, err := DefaultRedisWithDB(extraRedisId).HGetAll(define.CacheKeyCoinRate).Result()
	if err != nil {
		log.Error("GetAllCoinsRate redis error", zap.Error(err))
		return mp, err
	}

	var rate proto.CoinRate
	for key, val := range bs {
		err = json.Unmarshal(convert.Str2Bytes(val), &rate)
		if err != nil {
			log.Error("GetAllCoinsRate json unmarshal error", zap.Error(err))
			continue
		}
		mp[key] = rate
	}
	return mp, nil
}

func GetCoinRate(coin string, extraRedisId int) (*proto.CoinRate, error) {
	rate := new(proto.CoinRate)
	b, err := DefaultRedisWithDB(extraRedisId).HGet(define.CacheKeyCoinRate, strings.ToUpper(coin)).Bytes()
	if err != nil {
		//log.Error("GetCoinRate redis error", zap.Error(err))
		return rate, err
	}

	err = json.Unmarshal(b, &rate)
	if err != nil {
		log.Error("GetCoinRate json unmarshal error", zap.Error(err))
		return rate, err
	}
	return rate, nil
}
