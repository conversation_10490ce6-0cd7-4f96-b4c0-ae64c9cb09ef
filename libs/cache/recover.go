package cache

import (
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/proto"
)

func SetMatchOrder(code string, history []proto.MOrder) (err error) {
	b, err := json.Marshal(history)
	if err != nil {
		log.Errorf("json marshal fail,%v", err)
		return
	}
	err = DefaultRedis().HSet(define.CacheKeyMatchOrder, code, b).Err()
	return
}

// GetMatchOrders 获取缓存中订单
func GetMatchOrders(code string) (list []proto.MOrder) {
	b, err := DefaultRedis().HGet(define.CacheKeyMatchOrder, code).Bytes()
	if err != nil {
		log.Errorf("GetContractPriceIndex get fail,%v", err)
		return
	}
	err = json.Unmarshal(b, &list)
	if err != nil {
		log.Errorf("json unmarshal fail,%v", err)
	}
	return
}

// 删除合约缓存订单
func RemoveMatchContractOrders(code string) {
	err := DefaultRedis().HDel(define.CacheKeyMatchOrder, code).Err()
	if err != nil {
		log.Errorf("GetContractPriceIndex get fail,%v", err)
		return
	}
	return
}

func SetRCOrder(code string, history []proto.UserAction) (err error) {
	b, err := json.Marshal(history)
	if err != nil {
		log.Errorf("json marshal fail,%v", err)
		return
	}
	err = DefaultRedis().HSet(define.CacheKeyRCOrder, code, b).Err()
	return
}

// GetMatchOrders 获取缓存中订单
func GetRCOrders(code string) (list []proto.UserAction) {
	b, err := DefaultRedis().HGet(define.CacheKeyRCOrder, code).Bytes()
	if err != nil {
		log.Errorf("GetContractPriceIndex get fail,%v", err)
		return
	}
	err = json.Unmarshal(b, &list)
	if err != nil {
		log.Errorf("json unmarshal fail,%v", err)
	}
	return
}

// 删除合约缓存订单
func RemoveRCContractOrders(code string) {
	err := DefaultRedis().HDel(define.CacheKeyRCOrder, code).Err()
	if err != nil {
		log.Errorf("GetContractPriceIndex get fail,%v", err)
		return
	}
	return
}
