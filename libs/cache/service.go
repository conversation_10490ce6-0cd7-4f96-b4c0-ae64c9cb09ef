package cache

import (
	"spot/libs/define"
	"spot/libs/utils"
)

func IsSymbolCanInit(srvName, symbol string) bool {
	key := utils.StrBuilderBySep(":", define.CacheServerMatchSupport, srvName, symbol)
	return !Exist<PERSON>ey(key)
}

func IsEqualSrvIdentity(srvName, symbol, srvIdentity string) bool {
	key := utils.StrBuilderBySep(":", define.CacheServerMatchSupport, srvName, symbol)
	if !ExistKey(key) {
		return true
	}

	result, err := DefaultRedis().Get(key).Result()
	if err != nil {
		return false
	}
	if result == srvIdentity {
		return true
	}
	return false
}

func InitServerSymbolInit(srvName, symbol, srvIdentity string) bool {
	key := utils.StrBuilderBySep(":", define.CacheServerMatchSupport, srvName, symbol)

	return SetRedisLockTimeWithValue(0, srvIdentity, key)
}

func DelServerSymbol(srvName, symbol string) {
	key := utils.StrBuilderBySep(":", define.CacheServerMatchSupport, srvName, symbol)
	SetRedisUnLockStr(key)
}
