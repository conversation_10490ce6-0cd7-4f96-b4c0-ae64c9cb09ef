package cache

import (
	"github.com/go-redis/redis"
	"go.uber.org/zap"
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/proto"
	"strconv"
	"time"
)

func SetSysValue(list []proto.SystemValue) (err error) {
	b, err := json.Marshal(list)
	if err != nil {
		return
	}
	err = DefaultRedis().Set(define.CacheKeySysConfig, b, 30*time.Second).Err()
	return
}

func ListSysValue() (list []proto.SystemValue) {
	//b, err := DefaultRedis().Get(define.CacheKeySysConfig).Bytes()
	b, err := DefaultRedisWithDB(define.CacheDBNumber2).Get(define.CacheKeySysConfig).Bytes()
	if err != nil {
		log.Infof("ListSysValue fail,%v", err)
		return
	}
	err = json.Unmarshal(b, &list)
	if err != nil {
		log.Errorf("json unmarshal fail,%v", err)
		return
	}
	return
}

func GetSystemTradeConf(platformID int) *proto.SystemTradeConfig {
	var tc proto.SystemTradeConfig
	b, err := DefaultRedis().HGet(define.CacheKeySysTradeConf, strconv.Itoa(platformID)).Bytes()
	if err != nil {
		if err != redis.Nil {
			log.Error("GetSystemTradeConf cache error", zap.Int("platform", platformID), zap.Error(err))
		}
		return &tc
	}
	err = json.Unmarshal(b, &tc)
	if err != nil {
		log.Error("GetSystemTradeConf unmarshal failed", zap.Int("platform", platformID), zap.Error(err))
	}
	return &tc
}

func GetUserQuotaConfig(platformID int) (*proto.UserQuotaConfig, error) {
	var cfg proto.UserQuotaConfig
	res, err := DefaultRedis().HGet(define.CacheKeySysUserQuotaConf, strconv.Itoa(platformID)).Bytes()
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(res, &cfg)
	return &cfg, err
}
