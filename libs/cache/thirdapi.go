package cache

import (
	"github.com/go-redis/redis"
	"go.uber.org/zap"
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/nums"
	"spot/libs/proto"
	"time"
)

func GetThirdMarketHedgeSource() (source define.MarketSource) {
	id, err := DefaultRedis().Get(define.CacheKeySpotMarketSource).Int()
	if err != nil {
		if err == redis.Nil {
			return define.MarketSourceBinance
		}
		log.Error("SetExchangeHedgeSource set err", zap.Error(err))
		return
	}
	source = define.MarketSource(id)
	return
}

func ListThirdMarketHedgeConfig() (list []proto.ThirdMarketConfig, err error) {
	data, err := DefaultRedis().Get(define.CacheKeySpotMarketSourceConfig).Bytes()
	if err != nil {
		if err == redis.Nil {
			return
		}
		log.Error("ListExchangeHedgeConfig set err", zap.Error(err))
		return
	}
	err = json.Unmarshal(data, &list)
	if err != nil {
		log.Error("ListExchangeHedgeConfig Unmarshal fail", zap.Error(err))
		return
	}
	return
}

func SetThirdMarketConfig(list []proto.ThirdMarketConfig) {
	b, err := json.Marshal(list)
	if err != nil {
		log.Error("SetThirdMarketConfig fail", zap.Error(err))
		return
	}
	err = DefaultRedis().Set(define.CacheKeySpotMarketSourceConfig, b, 1*time.Minute).Err()
	if err != nil {
		log.Error("SetThirdMarketConfig error", zap.Error(err))
		return
	}
	return
}

func SetEntrustOrderForThirdId(order *proto.ThirdOrderPlaceArg) (err error) {
	key := define.CacheKeyOrderCache + ":" + nums.Int2String(int(order.MarketSource))
	b, _ := json.Marshal(order)
	err = DefaultRedis().HSet(key, nums.Int64String(order.ThirdOrderId), b).Err()
	if err != nil {
		log.Error("SetEntrustOrderForThirdId error", zap.Error(err))
		return
	}
	return
}

func GetEntrustOrderForThirdId(marketSource define.MarketSource, thirdId int64) (order *proto.ThirdOrderPlaceArg, err error) {
	key := define.CacheKeyOrderCache + ":" + nums.Int2String(int(marketSource))
	b, err := DefaultRedis().HGet(key, nums.Int64String(thirdId)).Bytes()
	if err != nil {
		log.Info("GetEntrustOrderForThirdId error", zap.Error(err), zap.Any("marketSource", marketSource), zap.Int64("thirdId", thirdId))
		if err == redis.Nil {
			err = nil
		}
		return
	}
	o := new(proto.ThirdOrderPlaceArg)
	err = json.Unmarshal(b, o)
	return
}

func IsThirdEntrustOrderExist(marketSource define.MarketSource, thirdId int64) bool {
	key := define.CacheKeyOrderCache + ":" + nums.Int2String(int(marketSource))
	result, err := DefaultRedis().HExists(key, nums.Int64String(thirdId)).Result()
	if err != nil {
		log.Error("ExistKey redis error", zap.String("key", key), zap.Error(err))
	}
	return result
}

func RemoveEntrustOrderForThirdId(marketSource define.MarketSource, thirdId int64) (err error) {
	key := define.CacheKeyOrderCache + ":" + nums.Int2String(int(marketSource))
	err = DefaultRedis().HDel(key, nums.Int64String(thirdId)).Err()
	if err != nil {
		log.Error("RemoveEntrustOrderForThirdId error", zap.Error(err))
		return
	}
	return
}

func SetThirdOrderTrade(thirdId, tradeId int64) (err error) {
	key := define.CacheKeyOrderTradeCache + ":" + nums.Int64String(thirdId)
	err = DefaultRedis().HSet(key, nums.Int64String(tradeId), 1).Err()
	if err != nil {
		log.Error("SetEntrustOrderForThirdId error", zap.Error(err))
		return
	}

	err = DefaultRedis().Expire(key, 15*time.Minute).Err()
	if err != nil {
		log.Error("SetThirdOrderTrade expire error", zap.Error(err))
		return
	}
	return
}

func IsThirdOrderTradeIdExist(thirdId, tradeId int64) bool {
	key := define.CacheKeyOrderTradeCache + ":" + nums.Int64String(thirdId)

	result, err := DefaultRedis().HExists(key, nums.Int64String(tradeId)).Result()
	if err != nil {
		log.Error("IsOrderTradeExist redis error", zap.String("key", key), zap.Error(err))
	}
	return result
}

func RemoveThirdOrderTrade(thirdId int64) (err error) {
	key := define.CacheKeyOrderTradeCache + ":" + nums.Int64String(thirdId)
	err = DefaultRedis().Del(key).Err()
	if err != nil {
		log.Error("RemoveEntrustOrderForThirdId error", zap.Error(err))
		return
	}
	return
}
