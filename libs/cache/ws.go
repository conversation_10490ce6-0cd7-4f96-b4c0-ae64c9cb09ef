package cache

import (
	"go.uber.org/zap"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/nums"
	"spot/libs/utils"
	"time"
)

func SetUserOnline(userId int64, times time.Duration) (err error) {
	key := utils.StrBuilderBySep(":", define.CacheKeyUserWsOnLine, nums.Int64String(userId))
	err = DefaultRedis().Set(key, time.Now().Unix(), times).Err()
	if err != nil {
		log.Error("SetUserOnline fail", zap.Error(err))
	}
	return
}

func IsUserWsOnLine(userId int64) bool {
	key := utils.StrBuilderBySep(":", define.CacheKeyUserWsOnLine, nums.Int64String(userId))
	s, err := DefaultRedis().Exists(key).Result()
	if err != nil {
		log.Error("IsUserWsOnLine fail", zap.Error(err))
		return true
	}
	return s == 1
}
