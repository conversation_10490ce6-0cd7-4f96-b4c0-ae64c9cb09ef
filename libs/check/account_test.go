package check

import (
	"spot/libs/log"
	"testing"
)

// 生成请求用密码
func TestEncryptPwd(t *testing.T) {
	t.Log(EncryptPwd("12345678"))
}

// 将请求用密码转换成库密码
func TestEncryptPassWord(t *testing.T) {
	t.Log(EncryptPassWord("OLoQ8ZJVeM02OAmI2qdL+cTz6753JOABw2ivHnvYPU0="))
}

func TestEmailAddrV2(t *testing.T) {
	log.InitLogger("demo", "info", false)
	t.Log(EmailAddrV2("<EMAIL>"))
}

func TestAreaNumber(t *testing.T) {
	t.Log(AreaNumber("64809885"))
}
