/*
@Time : 2019-12-17 19:06
<AUTHOR> mocha
@File : client_test
*/
package coinbase

import (
	"bytes"
	"fmt"
	"io/ioutil"
	"spot/libs/convert"
	"spot/libs/json"
	"spot/libs/log"
	"strings"
	"testing"
	"time"

	"github.com/gorilla/websocket"
)

//{"type":"heartbeat","last_trade_id":0,"product_id":"ETH-USD","sequence":12541910773,"time":"2021-01-08T09:37:27.491468Z"} <nil>
//TestClient: ws.client_test.go:53: 1 {"type":"ticker","sequence":12541910902,"product_id":"ETH-USD","price":"1201.45","open_24h":"1206.76","volume_24h":"797832.03690986","low_24h":"1063.18","high_24h":"1291.94","volume_30d":"10077707.94286806","best_bid":"1201.44","best_ask":"1201.48","side":"sell","time":"2021-01-08T09:37:27.923221Z","trade_id":74413684,"last_size":"0.38474618"} <nil>
func TestClient(te *testing.T) {
	w, rsp, err := websocket.DefaultDialer.Dial("wss://ws-feed.pro.coinbase.com", nil)
	if err != nil {
		fmt.Println(err)
	}
	b, err := ioutil.ReadAll(rsp.Body)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(convert.Bytes2Str(b))
	str := `{
    "type": "subscribe",
    "product_ids": [
        "ETH-USD",
        "ETH-EUR"
    ],
    "channels": [
        "heartbeat",
        {
            "name": "ticker",
            "product_ids": [
                "ETH-BTC",
                "ETH-USD"
            ]
        }
    ]
}`

	w.WriteMessage(websocket.TextMessage, convert.Str2Bytes(str))

	go func() {
		for {
			t, b, err := w.ReadMessage()
			//d, _ := zip.UnzipByte(b)
			te.Log(t, convert.Bytes2Str(b), err)

		}
	}()

	select {}

}

func TestBClient(t *testing.T) {
	log.InitLogger("ws.log", "info", false)
	s := time.Tick(time.Minute * 1)
	c := NewMarketWsClient(&Config{WsPoint: "wss://ws-feed.pro.coinbase.com"}, func(data []byte) {
		t.Logf("d:%v", string(data))
		t.Logf(string(data))
		if bytes.Contains(data, []byte("heartbeat")) {
			return
		}
		var ticker Ticker
		err := json.Unmarshal(data, &ticker)
		if err != nil {
			return
		}
		code := strings.ReplaceAll(ticker.Code, "-USD", "USDT")
		if ticker.Side == "sell" {

		}
		ticker.Code = code
		t.Logf("ticker:%+v", ticker)

	})
	c.Subscript([]string{"BTC-USD"})
	c.Start()
	go func() {
		for range s {
			t.Logf("开始重启")
			c.Restart()
		}
	}()
	select {}
}
