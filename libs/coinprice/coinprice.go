package coinprice

import (
	"errors"
	"fmt"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"io/ioutil"
	"net"
	"net/http"
	"net/url"
	"spot/libs/commonsrv"
	"spot/libs/conf"
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/proto"
	"time"
)

const (
	_coinGeckoBaseRate    = "/simple/price"
	_coinGeckoCoinList    = "/coins/list"
	_coinGeckoCoinsMarket = "/coins/markets"
)

var client = &http.Client{
	Timeout: 10 * time.Second,
	Transport: &http.Transport{
		Proxy: http.ProxyFromEnvironment,
		DialContext: (&net.Dialer{
			Timeout:   30 * time.Second,
			KeepAlive: 30 * time.Second,
		}).DialContext,
		ForceAttemptHTTP2:     true,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 10 * time.Second,
		IdleConnTimeout:       10 * time.Second,
		ResponseHeaderTimeout: 10 * time.Second,
	},
}

type _price struct {
	CNY float64         `json:"cny"`
	USD float64         `json:"usd"`
	KRW decimal.Decimal `json:"krw"` // 韩元价格
	VND decimal.Decimal `json:"vnd"` // 越南盾价格
	IDR decimal.Decimal `json:"idr"` // 印尼盾价格
	RUB decimal.Decimal `json:"rub"` // 卢布价格
	EUR decimal.Decimal `json:"eur"` // 欧元价格
}

func httpGet(uri string, param map[string]string) ([]byte, int, error) {
	req, err := http.NewRequest("GET", uri, nil)
	if err != nil {
		log.Error("httpGet http error", zap.Error(err))
		return nil, http.StatusOK, err
	}

	q := url.Values{}
	for key, val := range param {
		q.Add(key, val)
	}

	req.Header.Set("Accepts", "application/json")
	req.URL.RawQuery = q.Encode()

	resp, err := client.Do(req)
	if err != nil {
		log.Error("httpGet http Do failed", zap.Error(err))
		return nil, http.StatusOK, err
	}
	if resp.StatusCode != http.StatusOK {
		log.Error("httpGet http status abnormal", zap.Int("resp.StatusCode", resp.StatusCode))
		return nil, resp.StatusCode, nil
	}

	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Error("httpGet http read body failed", zap.Error(err))
		return nil, http.StatusOK, err
	}
	return respBody, http.StatusOK, nil
}

func GetRateByCoinGecko(runID int64, coinId string) (*proto.CoinLegalPrice, error) {
	body, code, err := httpGet(conf.CoinGeckoOpenAPI()+_coinGeckoBaseRate, map[string]string{"ids": coinId, "vs_currencies": "cny,usd,krw"})
	if code != http.StatusOK {
		commonsrv.SaveExternalErrorRecord(nil, runID, 0, define.ExternalSysTypeGecko, code, http.StatusText(code), conf.CoinGeckoOpenAPI()+_coinGeckoBaseRate)
		log.Error("GetRateByCoinGecko http status abnormal", zap.Int64("runID", runID), zap.Int("resp.StatusCode", code))
		return nil, fmt.Errorf("http 返回状态码[%d]", code)
	}
	if err != nil {
		log.Error("GetRateByCoinGecko http error", zap.Int64("runID", runID), zap.Error(err))
		return nil, err
	}

	rate := make(map[string]*proto.CoinLegalPrice)
	err = json.Unmarshal(body, &rate)
	if err != nil {
		log.Error("GetRateByCoinGecko json unmarshal error", zap.Int64("runID", runID), zap.Error(err))
		return nil, err
	}
	if _, ok := rate[coinId]; !ok {
		log.Error("GetRateByCoinGecko empty error", zap.Int64("runID", runID), zap.Error(errors.New("数据为空")))
		return nil, errors.New("数据为空")
	}
	return rate[coinId], nil
}
