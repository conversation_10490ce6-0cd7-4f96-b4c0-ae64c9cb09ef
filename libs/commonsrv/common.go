package commonsrv

import (
	"github.com/shopspring/decimal"
	"math/rand"
	"spot/libs/conf"
	"spot/libs/nums"
	"spot/libs/utils"
	"strings"
	"time"

	"github.com/go-redis/redis"
	"github.com/jmoiron/sqlx"
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
)

func GetCoinList() ([]proto.Coin, error) {
	return cache.GetAllCoinList()
}

func GetCoinDetailByID(coinID int) (*proto.Coin, error) {
	return cache.GetCoinByID(coinID)
}

func GetCoinDetailByIDTx(tx *sqlx.Tx, coinID int) (*proto.Coin, error) {
	return cache.GetCoinByID(coinID)
}

func GetCoinDetailByName(tx *sqlx.Tx, coinName string) (*proto.Coin, error) {
	return cache.GetCoinByName(coinName)
}

// 获取随机滑点
func GetRandSlippage(min, max, step decimal.Decimal) (r decimal.Decimal) {
	gap := max.Sub(min)
	if gap.LessThanOrEqual(decimal.Zero) {
		return max
	}
	if step.LessThanOrEqual(decimal.Zero) {
		log.Error("GetRandSlippage step fail", zap.Any("min", min.String()), zap.String("max", max.String()), zap.String("step", step.String()))
		return max
	}

	source := gap.Div(step).IntPart()
	rand.Seed(time.Now().UnixNano())
	randInt := rand.Int63n(source)
	return min.Add(nums.NewFromInt64(randInt).Mul(step))
}

func GetDefaultPlatFormId() int {
	if conf.IsProduct() {
		return define.PlatFormDefault
	}
	return define.PlatForm
}

func CheckAuthCode(reqID int64, account, code string, mode uint8, clean bool) error {
	account = strings.ToLower(account)
	key := utils.StrBuilder(define.CacheKeyAuthCode, account)

	// 获取redis中验证码信息
	codeData, err := cache.GetAuthCode(key)
	if err != nil {
		if err == redis.Nil {
			return define.ErrMsgUserNotExist
		}
		log.Error("CheckAuthCode GetAuthCode error",
			zap.Int64("reqID", reqID),
			zap.String("account", account),
			zap.String("code", code),
			zap.Uint8("mode", mode),
			zap.Error(err))
		return define.ErrMsgBusy
	}
	if codeData.Code != code || code == "" || codeData.Mode != mode {
		return define.ErrMsgUserNotExist
	}

	if clean {
		cache.DelKey(key)
	}
	return nil
}
