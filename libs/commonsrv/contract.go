/*
@Time : 3/12/20 3:06 下午
<AUTHOR> mocha
@File : contract
*/
package commonsrv

import (
	"database/sql"
	"sort"

	"github.com/jmoiron/sqlx"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
)

func GetApiSymbolList(symbolList []proto.Contract) (list []proto.ApiSymbolList) {
	// 获取全部涨跌幅
	changes := cache.GetAllContractApplies()

	var symbol proto.ApiSymbolList
	list = make([]proto.ApiSymbolList, 0)
	for _, s := range symbolList {
		if !s.Delisted && s.IsShow {
			symbol.Symbol = s.ContractCode
			symbol.Icon = s.ContractIcon
			symbol.FeeMaker = s.FeeMaker
			symbol.FeeTaker = s.FeeTaker
			symbol.Digit = s.Digit
			symbol.VolumeDigit = s.VolumeDigit
			symbol.IOCLimit = s.IOCLimit
			symbol.IOCBuyLimit = s.IOCBuyLimit
			symbol.IOCSellLimit = s.IOCSellLimit
			trade := cache.GetContractPriceIndex(s.ContractCode)
			if trade == nil {
				trade = new(proto.IndexHistory)
			}
			symbol.Price = trade.TradePrice.Round(s.Digit)
			symbol.PriceCNY = trade.TradePriceCn.Round(s.Digit)
			symbol.Delisted = s.Delisted
			symbol.CoinName = s.BaseCoinName
			symbol.MarketName = s.CoinName
			symbol.IsMaintenance = s.IsMaintenance
			symbol.LockFloatFactor = s.LockFloatFactor
			symbol.RedundancyFactor = s.RedundancyFactor
			chg, ok := changes[s.ContractCode]
			if ok {
				symbol.ChangeRatio = chg.ChangeDaily
				symbol.Change = chg.Change
				symbol.TradeVolume = chg.TradeV24h
				symbol.TradeValue = chg.TradeM24h
				symbol.HighPrice = chg.HighPrice
				symbol.LowPrice = chg.LowPrice
			} else {
				symbol.Change = decimal.Zero
			}
			list = append(list, symbol)
		}
	}
	return list
}

func GetApiSymbolDetail(detail *proto.Contract, applies *proto.Applies) (reply *proto.ApiSymbolDetail) {
	reply = &proto.ApiSymbolDetail{
		Symbol:           detail.ContractCode,
		Icon:             detail.ContractIcon,
		Digit:            detail.Digit,
		VolumeDigit:      detail.VolumeDigit,
		Step:             detail.PriceStep,
		CoinName:         detail.BaseCoinName,
		MarketName:       detail.CoinName,
		MinOrderVolume:   detail.MinOrderVolume,
		MaxOrderVolume:   detail.MaxOrderVolume,
		MinOrderMoney:    detail.MinOrderMoney,
		MaxOrderMoney:    detail.MaxOrderMoney,
		FeeMaker:         detail.FeeMaker,
		FeeTaker:         detail.FeeTaker,
		Delisted:         detail.Delisted,
		IOCLimit:         detail.IOCLimit,
		IOCBuyLimit:      detail.IOCBuyLimit,
		IOCSellLimit:     detail.IOCSellLimit,
		IsMaintenance:    detail.IsMaintenance,
		LockFloatFactor:  detail.LockFloatFactor,
		RedundancyFactor: detail.RedundancyFactor,
	}

	// 指数价格
	index := cache.GetContractPriceIndex(detail.ContractCode)
	if index == nil {
		index = new(proto.IndexHistory)
	}
	reply.Price = index.TradePrice
	reply.PriceCNY = index.TradePriceCn
	reply.BuyPrice = index.BuyFirst.Round(detail.Digit)
	reply.SellPrice = index.SellFirst.Round(detail.Digit)

	// 获取涨跌幅
	reply.ChangeDaily = applies.ChangeDaily
	reply.Change24h = applies.Change24h
	reply.Change8h = applies.Change8h
	reply.Change4h = applies.Change4h
	reply.Change2h = applies.Change2h
	reply.Change1h = applies.Change1h
	reply.Change30m = applies.Change30m
	reply.Change10m = applies.Change10m
	reply.HighPrice = applies.HighPrice
	reply.LowPrice = applies.LowPrice
	reply.ChangeVolume = applies.Change
	reply.Trade24h = applies.TradeV24h
	reply.TradeValue24h = applies.TradeM24h

	return
}

func GetContractList(reqID int64, tx *sqlx.Tx) ([]proto.Contract, error) {
	list, err := cache.GetAllContractList()
	if err != nil || len(list) == 0 {
		list, err = database.GetSupportContractsTx(tx)
		if err != nil {
			log.Error("GetContractList GetSupportContract error", zap.Int64("reqID", reqID), zap.Error(err))
			return nil, define.ErrMsgBusy
		}
		log.Info("LoadContractList", zap.Any("list", list))
		cache.SaveContractList(list, false)
	}
	sort.SliceStable(list, func(i, j int) bool {
		return list[i].OrderBy > list[j].OrderBy
	})
	return list, nil
}

func GetContractMap(reqID int64, tx *sqlx.Tx) (map[string]proto.Contract, error) {
	contracts, err := cache.GetAllContractMap()
	if err != nil {
		list, err := database.GetSupportContractsTx(tx)
		if err != nil {
			log.Error("GetContractMap GetSupportContractsTx error", zap.Int64("reqID", reqID), zap.Error(err))
			return nil, define.ErrMsgBusy
		}
		cache.SaveContractList(list, false)
		for i := range list {
			contracts[list[i].ContractCode] = list[i]
		}
	}
	return contracts, nil
}

func GetContractMapWithNoSetCache(reqID int64, tx *sqlx.Tx) (map[string]proto.Contract, error) {
	contracts, err := cache.GetAllContractMap()
	if err != nil {
		list, err := database.GetSupportContractsTx(tx)
		if err != nil {
			log.Error("GetContractMap GetSupportContractsTx error", zap.Int64("reqID", reqID), zap.Error(err))
			return nil, define.ErrMsgBusy
		}
		//cache.SaveContractList(list, false)
		for i := range list {
			contracts[list[i].ContractCode] = list[i]
		}
	}
	return contracts, nil
}

func GetContractDetail(reqID int64, contractCode string, tx *sqlx.Tx) (*proto.Contract, error) {
	detail, err := cache.GetContractInfo(contractCode)
	if err != nil {
		detail, err = database.GetContractByCodeTx(tx, contractCode)
		if err != nil {
			if err == sql.ErrNoRows {
				return nil, define.ErrMsgSpotNotExist
			}
			log.Error("GetContractDetail GetContractByCode error", zap.Int64("reqID", reqID), zap.Error(err))
			return nil, define.ErrMsgBusy
		}
		log.Info("从数据库中获取合约信息", zap.Any("信息", detail))
	}
	return detail, nil
}
