package commonsrv

import (
	"github.com/shopspring/decimal"
	"math"
	"math/rand"
	"spot/libs/cache"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/nums"
	"spot/libs/proto"
	"time"
)

//func mergeDepth(level int32, priceLimit int32, list []proto.Depth) (l []proto.Depth) {
//	//log.Infof("合并深度,level:%v,limit:%v",level,priceLimit)
//	if level == 0 || len(list) == 0 {
//		return
//	}
//	digit := int32(priceLimit - level)
//	if digit < 0 {
//		log.Warnf("保留小数为赋值")
//		return l
//	}
//	for _, depth := range list {
//		price := nums.NewFromString(depth.Price).Truncate(digit)
//		d := depth
//		d.Price = price.StringFixed(digit)
//		if len(l) == 0 {
//			l = append(l, d)
//			continue
//		}
//		//check current to last
//		lastIndex := len(l) - 1
//		last := l[lastIndex]
//		if d.Price == last.Price {
//			last.Amount += d.Amount
//			last.TotalAmount += d.Amount
//			l[lastIndex] = last
//		} else {
//			d.TotalAmount = last.TotalAmount + d.Amount
//			l = append(l, d)
//		}
//	}
//	return
//}

//func CreateLevelDepth(orginal *proto.DepthContainer, level int32, isCache bool) (depthContainer *proto.DepthContainer) {
//	//log.Debugf("准备创建:%v,limit:%v",level,orginal.PriceLimit)
//	digit := int32(orginal.PriceLimit - level)
//	if digit < 0 {
//		return
//	}
//	if orginal.PriceLimit-level < 0 {
//		return
//	}
//	depthContainer = &proto.DepthContainer{ContractCode: orginal.ContractCode, Digit: orginal.PriceLimit - level, PriceLimit: orginal.PriceLimit, Level: level}
//	depthContainer.Buy = mergeDepth(level, orginal.PriceLimit, orginal.Buy)
//	depthContainer.Sell = mergeDepth(level, orginal.PriceLimit, orginal.Sell)
//	if isCache {
//		cache.SetContractDepth(depthContainer)
//	}
//	//log.Debugf("合约深度:%v,level:%v,data:%+v", orginal.ContractCode, level, depthContainer)
//	return
//}

//func giveTradePriceForV1(price decimal.Decimal, tradeVolume decimal.Decimal, rangPrice, rangVolume float64, precision int32, dSize int) (b, s []Depth) {
//	rangePrice := nums.NewFromFloat(rangPrice)
//	rangeVolume := nums.NewFromFloat(rangVolume)
//	buyFirst, sellFirst := price.Truncate(precision), price.Truncate(precision)
//	buyEnd, sellEnd := price.Mul(nums.NewFromInt(1).Sub(rangePrice)), price.Mul(nums.NewFromInt(1).Add(rangePrice))
//	var buys, sells []decimal.Decimal
//	buys = append(buys, buyFirst)
//	sells = append(sells, sellFirst)
//	for i := 0; i < dSize; i++ {
//		buys = append(buys, nums.RangDecimal(buyEnd, buyFirst, precision))
//		sells = append(sells, nums.RangDecimal(sellFirst, sellEnd, precision))
//	}
//	sort.Slice(buys, func(i, j int) bool {
//		return buys[i].GreaterThan(buys[j])
//	})
//	sort.Slice(sells, func(i, j int) bool {
//		return sells[i].LessThan(sells[j])
//	})
//	log.Infof("buys:%+v", buys)
//	log.Infof("sells:%+v", sells)
//	var buyD, sellD []Depth
//	for i, buy := range buys {
//		if i != 0 && buy.Equal(buyD[len(buyD)-1].Price) {
//			continue
//		}
//		buyD = append(buyD, Depth{Price: buy, Volume: GetRandomDecimal(tradeVolume.Mul(nums.NewFromInt(1).Add(rangeVolume)))})
//	}
//
//	for i, sell := range sells {
//		if i != 0 && sell.Equal(sellD[len(sellD)-1].Price) {
//			continue
//		}
//		sellD = append(sellD, Depth{Price: sell, Volume: GetRandomDecimal(tradeVolume.Mul(nums.NewFromInt(1).Add(rangeVolume)))})
//	}
//
//	min := getMinSize(buyD, sellD, _depthShowHeight)
//	log.Infof("size:%v", min)
//	return buyD[:min], sellD[:min]
//
//}

func GenerateDepthByTradePrice(price decimal.Decimal, tradeVolume decimal.Decimal, adjustPoint int, rangVolume decimal.Decimal, precision int32, dSize int) (b, s []proto.MarketDepth) {
	adjustV := rangVolume
	point := int64(adjustPoint)
	buy := generateBuyDepth(price, tradeVolume, adjustV, point, precision, dSize)
	sell := generateSellDepth(price, tradeVolume, adjustV, point, precision, dSize)
	min := getMinSize(buy, sell, define.DepthShowHeight)
	return buy[:min], sell[:min]
}

func generateSellDepth(price, volume, adjustVolume decimal.Decimal, adjustPoint int64, precision int32, size int) []proto.MarketDepth {
	var list []proto.MarketDepth
	var curPrice = price
	var curAdjust decimal.Decimal
	step := nums.NewFromInt(1).Div(nums.NewFromFloat(math.Pow10(int(precision))))
	for i := size; i >= 0; i-- {
		if curAdjust.IsZero() {
			curAdjust = nums.NewFromInt64(adjustPoint)
		}
		curAdjust = DecimalRandom(curAdjust, int(precision))
		d := proto.MarketDepth{
			Price:  curPrice.Add(step).Add(curAdjust),
			Volume: GetRandomDecimal(volume.Mul(nums.NewFromInt(1).Add(adjustVolume))),
		}
		curPrice = d.Price
		if price.IsPositive() {
			list = append(list, d)
		}
	}
	return list
}

func generateBuyDepth(price, volume, adjustVolume decimal.Decimal, adjustPoint int64, precision int32, size int) []proto.MarketDepth {
	//fmt.Printf("price:%v,volume:%v，adjstV；%v,point:%v，digit；%v,size:%v\n",price.String(),volume.String(),adjustVolume.String(),adjustPoint,precision,size)
	var list []proto.MarketDepth
	var curPrice = price
	var curAdjust decimal.Decimal
	step := nums.NewFromInt(1).Div(nums.NewFromFloat(math.Pow10(int(precision))))
	for i := size; i >= 0; i-- {
		if curAdjust.Equal(decimal.Zero) {
			curAdjust = nums.NewFromInt64(adjustPoint)
		}
		curAdjust = DecimalRandom(curAdjust, int(precision))
		d := proto.MarketDepth{
			Price:  curPrice.Sub(step).Sub(curAdjust),
			Volume: GetRandomDecimal(volume.Mul(nums.NewFromInt(1).Add(adjustVolume))),
		}
		curPrice = d.Price
		if price.IsPositive() {
			list = append(list, d)
		}
	}
	return list
}

func getMinSize(d []proto.MarketDepth, d2 []proto.MarketDepth, h int) int {
	s1, s2 := len(d), len(d2)
	height := s2
	if s1 == s2 {
		height = s1
	}
	if s1 < s2 {
		height = s1
	}
	if height < h {
		return height
	}
	return h
}

func GetRandomDecimal(v decimal.Decimal) (d decimal.Decimal) {
	//rand.Seed(time.Now().Unix())
	rand.Seed(time.Now().UnixNano())
	if v.IntPart() == 0 {
		log.Infof("GetRandomDecimal 整数部分%v为0,%v", v.IntPart(), v.String())
		return v
	}
	i := rand.Int63n(v.IntPart())
	if i == 0 {
		d = v
		return
	}
	return nums.NewFromInt64(i)

}

var r = rand.New(rand.NewSource(time.Now().UnixNano()))

func RandFloat(n float64) decimal.Decimal {
	v := nums.NewFromFloat(n)
	digit := v.Exponent()
	if digit < 0 {
		digit = -digit
	}
	if digit == 0 {
		return nums.NewFromInt64(r.Int63n(v.IntPart()))
	}
	if digit > 0 {
		f := nums.NewFromFloat(math.Pow10(int(digit)))
		n := v.Mul(f).IntPart()
		if n == 0 {
			return v
		}
		rn := r.Int63n(n)
		return nums.NewFromInt64(rn).Div(f)
	}
	return v
}

//
//func generateBuy(b []proto.MarketDepth, c *proto.Contract) (list []proto.Depth) {
//	cTotal := decimal.Zero
//	for _, depth := range b {
//		cTotal = cTotal.Add(depth.Volume)
//		d := proto.Depth{
//			Price:       depth.Price.StringFixed(c.Digit),
//			Amount:      depth.Volume.IntPart(),
//			TotalAmount: cTotal.IntPart(),
//		}
//		list = append(list, d)
//	}
//	return list
//}

//point-调整点数，digit-精度
func DecimalRandom(point decimal.Decimal, digit int) decimal.Decimal {
	if digit < 0 {
		return decimal.Zero
	}
	if point.Equal(decimal.Zero) {
		return decimal.Zero
	}
	rand.Seed(time.Now().UnixNano())
	if point.LessThanOrEqual(decimal.Zero) {
		return decimal.Zero
	}
	if point.IntPart() == 0 {
		return decimal.Zero
	}
	//fmt.Printf("random；%v,int:%v",point.IntPart(),rand.Int63n(point.IntPart()))
	return nums.NewFromInt64(rand.Int63n(point.IntPart())).Div(nums.NewFromFloat(math.Pow10(digit)))
}

func LoadContractDepthConfig() (list []proto.ContractDepth, err error) {
	list, err = database.ListContractDepthConfig()
	if err != nil {
		log.Errorf("database.ListContractDepthConfig fail,%v", err)
		return
	}
	err = cache.SetContractDepthConfig(list)
	if err != nil {
		log.Errorf("cache.SetContractDepthConfig fail,%v", err)
		return
	}
	return
}

//获取指定合约深度配置
func GetContractDepthConfig(code string) (c *proto.ContractDepth) {
	list, err := cache.GetContractDepthConfig()
	if err != nil {
		log.Errorf("cache.GetContractDepthConfig() fail,%v", err)
		list, err = LoadContractDepthConfig()
		if err != nil {
			log.Errorf("LoadContractDepthConfig fail,%v", err)
			return
		}
	}
	for _, v := range list {
		if v.ContractCode == code {
			s := v
			c = &s
			return
		}
	}
	return
}
