package commonsrv

import (
	"errors"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/nums"
	"spot/libs/proto"
	"time"
)

//获取成交价格
func GenerateContractPrice(cg *proto.Contract, order *proto.EntrustOrder, isDev bool) (mp *proto.MatchPrice, err error) {
	mode := order.Mode
	side := order.Side
	var (
		tp                  decimal.Decimal
		buyFirst, sellFirst decimal.Decimal
		isForce             bool
	)
	if cg == nil || cg.ContractCode == "" {
		return nil, errors.New("无效的合约信息")
	}
	if mode == 0 {
		mode = define.EntrustModeBest5Level
	}
	if mode != define.EntrustModeDefault && mode != define.EntrustModeBest3Level && mode != define.EntrustModeBest5Level {
		return nil, errors.New("无效的下单模式")
	}
	if side != define.OrderBuy && side != define.OrderSell {
		return nil, errors.New("无效的下单方向")
	}
	code := cg.ContractCode
	depth := cache.GetContractOriginDepth(code)
	if depth == nil {
		err = errors.New("没有可成交的深度")
		return
	}
	log.Infof("获取合约;%v，可交易深度：%+v", code, *depth)
	if !isDev {
		if time.Now().Unix()-depth.TS >= 90 {
			err = errors.New("行情陈旧")
			log.Errorf("获取合约%v可交易深度大于等于90s没有更新，暂不可成交", code)
			return
		}
	}

	if len(depth.Buy) > 0 {
		buyFirst = depth.Buy[0].Price
	}
	if len(depth.Sell) > 0 {
		sellFirst = depth.Sell[0].Price
	}

	var data []proto.Depth
	if side == define.OrderBuy {
		data = depth.Sell
	} else {
		data = depth.Buy
	}
	if data == nil || len(data) == 0 {
		err = errors.New("not enough levels to trade")
		log.Errorf("code；%v合约深度数据有误,%+v", code, *depth)
		return
	}

	if len(data) == 0 {
		err = errors.New("not enough levels to trade")
		log.Errorf("code；%v合约对手深度数据无效,%+v", code, *depth)
		return
	}
	log.Debugf("对手盘深度：%v", data)
	mp = &proto.MatchPrice{
		BuyFirst:  buyFirst,
		SellFirst: sellFirst,
	}

	level := 1
	switch mode {
	case define.EntrustModeDefault:
		level = 1
	case define.EntrustModeBest3Level:
		level = 3
	case define.EntrustModeBest5Level:
		level = 5
	default:
		level = 5
	}

	if isForce {
		level = 6
	}

	var tPrice, tVolume decimal.Decimal
	entrustMoney := order.Money
	for i, node := range data {
		if node.Price.Equal(decimal.Zero) {
			node.Price = node.Price
		}
		log.Infof("depth node:%+v", node)
		var thisDeal decimal.Decimal
		depthVolume := node.Amount
		depthMoney := depthVolume.Mul(node.Price)
		if entrustMoney.LessThanOrEqual(depthMoney) {
			thisDeal = entrustMoney.Div(node.Price).Truncate(cg.VolumeDigit)
		} else {
			thisDeal = depthVolume
		}

		tPrice = tPrice.Add(node.Price.Mul(thisDeal))
		tVolume = tVolume.Add(thisDeal)

		entrustMoney = entrustMoney.Sub(thisDeal.Mul(node.Price))
		if entrustMoney.Equal(decimal.Zero) || i >= level-1 {
			break
		}
	}
	if tVolume.GreaterThan(decimal.Zero) {
		tp = tPrice.Div(tVolume).Truncate(cg.Digit)
		if side == define.OrderBuy {
			tp = nums.Ceiling(tp, cg.PriceStep).Truncate(cg.Digit)
		} else {
			tp = nums.Floor(tp, cg.PriceStep).Truncate(cg.Digit)
		}
	}
	if tVolume.Mul(tp).GreaterThan(order.Money) {
		log.Error("计算的成交量有误，大于总花费", zap.Any("价格", tp.String()), zap.String("数量", tVolume.String()), zap.Any("cost", tVolume.Mul(tp).String()), zap.Any("money", order.Money.String()))
		return
	}
	log.Infof("成交价：%+v,成交总金额;%v,成交量：%v", tp.String(), tPrice.String(), tVolume.String())
	var status define.OrderState
	if tVolume.Equal(decimal.Zero) {
		status = define.OrderStatusNotDealCancel
	} else {
		if order.Volume.GreaterThan(decimal.Zero) && tVolume.Equal(order.Volume) {
			status = define.OrderStatusFull
		} else if order.Money.GreaterThan(decimal.Zero) && tVolume.Mul(tp).Div(order.Money).GreaterThanOrEqual(nums.NewFromFloat(0.95)) {
			status = define.OrderStatusFull
		} else {
			status = define.OrderStatusPartCancel
		}
	}

	mp = &proto.MatchPrice{
		TradePrice:  tp,
		TradeVolume: tVolume,
		BuyFirst:    buyFirst,
		SellFirst:   sellFirst,
		Status:      status,
	}
	return
}

// GenerateContractPriceForEntrustLimit 限价单逻辑
func GenerateContractPriceForEntrustLimit(cg *proto.Contract, side string, amount decimal.Decimal, isDev bool, price decimal.Decimal) (mp *proto.MatchPrice, err error) {
	var (
		tp                  decimal.Decimal
		buyFirst, sellFirst decimal.Decimal
		isForce             bool
	)
	if cg == nil || cg.ContractCode == "" {
		return nil, errors.New("无效的合约信息")
	}

	if side != define.OrderBuy && side != define.OrderSell {
		return nil, errors.New("无效的下单方向")
	}
	volume := amount //委托量
	code := cg.ContractCode
	depth := cache.GetContractOriginDepth(code)
	if depth == nil {
		err = errors.New("没有可成交的深度")
		return
	}
	log.Infof("获取合约;%v，可交易深度：%+v", code, *depth)
	//if !isDev && orderType != define.OrderTypeForceCloseOut {
	//	if time.Now().Unix()-depth.TS >= 90 {
	//		err = errors.New("行情陈旧")
	//		log.Errorf("获取合约%v可交易深度大于等于90s没有更新，暂不可成交", code)
	//		return
	//	}
	//}

	if len(depth.Buy) > 0 {
		buyFirst = depth.Buy[0].Price
	}
	if len(depth.Sell) > 0 {
		sellFirst = depth.Sell[0].Price
	}

	var data []proto.Depth
	if side == define.OrderBuy {
		data = depth.Sell
	} else {
		data = depth.Buy
	}
	if data == nil || len(data) == 0 {
		err = errors.New("not enough levels to trade")
		log.Errorf("code；%v合约深度数据有误,%+v", code, *depth)
		return
	}

	if len(data) == 0 {
		err = errors.New("not enough levels to trade")
		log.Errorf("code；%v合约对手深度数据无效,%+v", code, *depth)
		return
	}
	log.Debugf("对手盘深度：%v", data)
	mp = &proto.MatchPrice{
		BuyFirst:  buyFirst,
		SellFirst: sellFirst,
	}

	level := 6

	var tPrice, tVolume decimal.Decimal
	entrustAmount := volume //委托剩余量
	for i, node := range data {
		if side == define.OrderBuy && node.Price.GreaterThan(price) {
			break
		}
		if side == define.OrderSell && node.Price.LessThan(price) {
			break
		}
		log.Infof("depth node:%+v", node)
		var thisDeal decimal.Decimal
		depthVolume := node.Amount
		if entrustAmount.LessThanOrEqual(depthVolume) {
			thisDeal = entrustAmount
		} else {
			thisDeal = depthVolume
		}

		tPrice = tPrice.Add(node.Price.Mul(thisDeal))
		tVolume = tVolume.Add(thisDeal)

		entrustAmount = entrustAmount.Sub(thisDeal)
		if entrustAmount.Equal(decimal.Zero) || i >= level-1 {
			break
		}
	}
	if tVolume.GreaterThan(decimal.Zero) {
		tp = tPrice.Div(tVolume).Truncate(cg.Digit)
		if side == define.OrderBuy {
			tp = nums.Ceiling(tp, cg.PriceStep).Truncate(cg.Digit)
		} else {
			tp = nums.Floor(tp, cg.PriceStep).Truncate(cg.Digit)
		}
	}
	log.Infof("成交价：%+v,成交总金额;%v,成交量：%v", tp.String(), tPrice.String(), tVolume.String())
	if isForce {
		tVolume = volume
	}
	var status define.OrderState
	if tVolume.Equal(decimal.Zero) {
		status = define.OrderStatusNotDealCancel
	} else {
		if tVolume.Equal(amount) {
			status = define.OrderStatusFull
		} else {
			status = define.OrderStatusPartCancel
		}
	}

	mp = &proto.MatchPrice{
		TradePrice:  tp,
		TradeVolume: tVolume,
		BuyFirst:    buyFirst,
		SellFirst:   sellFirst,
		Status:      status,
	}
	return
}

//OrderTypeLimit         = 2 // 止盈单
//OrderTypeStop          = 4 // 止损单
//OrderTypeForceCloseOut = 5 // 强平单
//OrderTypePlanCloseOut  = 6 // 条件平仓
//OrderTypeFollowClose   = 7 // 带单平仓(跟单者被动平仓）
//OrderTypeFollowLimit   = 8 // 带单止盈
//OrderTypeFollowStop    = 9 // 带单止损
//判断是否强平或止盈止损单
func CheckOrderType(accountType define.AccountType, orderType int, isOpen bool) bool {
	if !isOpen && accountType == define.AccountTypeByFollow {
		return true
	}
	return orderType == define.OrderTypeLimit || orderType == define.OrderTypeStop || orderType == define.OrderTypeFollowLimit || orderType == define.OrderTypeFollowStop || orderType == define.OrderTypeForceCloseOut
}
