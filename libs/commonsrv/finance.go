/*
@Time : 2019-08-21 19:59
<AUTHOR> mocha
@File : msghandler
*/
package commonsrv

import (
	"database/sql"
	"time"

	"github.com/jmoiron/sqlx"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
)

//处理平台交易账户财务
//amount传正值，财务资产增加，反之减少
func dealFinanceTradeAccountRecord(r proto.FsWalletHistory) {
	var (
		tx     *sqlx.Tx
		err    error
		wallet proto.FsWallet
	)
	tx, err = database.Begin()
	if err != nil {
		log.Errorf("dealCapitalRecord get db tx fail,err:%v", err)
		return
	}
	defer database.CommitTx(tx, &err, 0, nil)
	//查询交易钱包资产
	wallet, err = database.GetFsCapitalWithLockByCoinName(tx, r.CoinName, r.<PERSON>)
	if err != nil {
		if err == sql.ErrNoRows {
			m := proto.FsWallet{CoinName: r.Co<PERSON>, Type: define.WalletTypeTrade, PlatformID: r.PlatformID}
			err = database.InsertFsWalletWithTx(tx, m)
			if err != nil {
				log.Errorf("checkCapitalAccount InsertFsCapital fail:%v", err)
				return
			}
			err = nil
		} else {
			log.Errorf(" database.GetFsCapitalWithLock fail,err:%v,history:%+v", err, r)
			return
		}
	}
	r.AmountAfter = wallet.Amount + r.Amount
	err = database.IncrFsCapital(tx, r.CoinName, r.Amount, r.PlatformID)
	if err != nil {
		log.Errorf("IncreFsWallet fail,err:%v", err)
		return
	}
	err = database.InsertFsCapitalHistory(tx, r)
	if err != nil {
		log.Errorf("InsertFsCapitalHistory fail,err:%v", err)
		return
	}
}

//处理交易手续费
func dealFSTradeFee(platformId int, userId int64, fee float64, coinName string, sourceId int64, isMaker bool) {
	feeHistory := proto.FsWalletHistory{UserId: userId, CoinName: coinName, Amount: fee, OpType: define.FsTradeFee, CreateAt: time.Now(), SourceId: sourceId, Remark: define.FsCategoryMap[define.FsTradeFee], PlatformID: platformId, IsMaker: isMaker}
	dealFinanceTradeAccountRecord(feeHistory)
}
