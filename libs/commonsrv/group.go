package commonsrv

import (
	"github.com/jmoiron/sqlx"
	"github.com/shopspring/decimal"
	"spot/libs/database"
	"spot/libs/log"
)

//获取用户指定合约taker maker费率
func GetUserFeeByContractCode(tx *sqlx.Tx, userId int64, contractCode string) (takerFee, makerFee decimal.Decimal) {
	takerFee, makerFee = GetContractFee(tx, contractCode)
	_, err := database.GetUserInfoByID(userId, tx)
	if err != nil {
		log.Errorf("获取用户信息失败,userId:%v,err:%v", userId, err)
		return
	}
	return
}

func GetContractFee(tx *sqlx.Tx, contractCode string) (takerFee, makerFee decimal.Decimal) {
	c, err := GetContractDetail(0, contractCode, tx)
	if err != nil {
		log.Errorf("获取合约信息失败,code:%v,err:%v", contractCode, err)
		return
	}
	takerFee = c.<PERSON>e<PERSON><PERSON>
	return
}
