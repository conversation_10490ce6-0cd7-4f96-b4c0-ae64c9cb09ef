package commonsrv

import (
	"spot/libs/define"
	"spot/libs/proto"
)

//入库并发送所有支持语言的消息
func NotifyMessageWithAllLang(arg *proto.MessageArg) {
	langs := define.GetMsgSupportLang()
	for _, lang := range langs {
		arg.Lang = lang
		NotifyMessageWithLang(arg)
	}
}
func NotifyMessageWithLang(arg *proto.MessageArg) (err error) {
	//title, _ := define.GetMsgLanguageWithTemplate(arg.Lang, arg.TemplateTitleId, arg.TemplateData)
	//content, err := define.GetMsgLanguageWithTemplate(arg.Lang, arg.TemplateContentId, arg.TemplateData)
	//if err != nil {
	//	log.Errorf("CreateMessage content template fail,%v", err)
	//	return
	//}
	//message := &proto.NotifyMessage{
	//	ID:             arg.MsgId,
	//	SenderID:       arg.From,
	//	ReceiverID:     arg.To,
	//	SenderNickname: arg.NickName,
	//	Title:          title,
	//	Content:        content,
	//	Category:       arg.Category,
	//	LanguageType:   arg.Lang,
	//	CreateTime:     time.Now(),
	//}
	//err = database.InsertNoticeMessage(nil, message)
	//if err != nil {
	//	log.Errorf("database.InsertNoticeMessage fail,%v", err)
	//	return
	//}
	//if arg.NoticeFunc != nil {
	//	m := &proto.Message{}
	//	m.NotifyMessage = *message
	//	arg.NoticeFunc(m)
	//}
	//log.Infof("发送用户消息：%+v", *message)
	return
}
