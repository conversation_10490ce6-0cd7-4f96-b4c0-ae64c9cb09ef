package commonsrv

var isPushAccount = false

//
//func NotifyAccountChange(userId int64, currencyName string, fn func(*proto.AccountChange)) {
//	tx, err := database.Begin()
//	if err != nil {
//		log.Errorf("datbase get tx fail,%v", err)
//		return
//	}
//	defer database.CommitTx(tx, &err, 0, func(i int64, e error) {
//	})
//
//	//获取账户余额信息
//	account, err := database.GetUserAccountByCurrencyNameWithLock(tx, userId, currencyName)
//	if err != nil {
//		log.Error("database.GetUserAccountByCurrencyNameWithLock fail", zap.Int64("userId", userId), zap.String("currencyName", currencyName), zap.Error(err))
//		return
//	}
//
//	cache.UpdateContractAccount(account)
//
//	if !isPushAccount {
//		return
//	}
//
//	_, _, _, floatProfit, err := GetCoinMarginDetail(tx, userId, 0, currencyName)
//	if err != nil {
//		log.Errorf("commonsrv.GetCoinFollowMargin fail,%v", err)
//		return
//	}
//
//	ac := &proto.AccountChange{
//		UserId:       userId,
//		AccountType:  0,
//		CoinName: currencyName,
//		Balance:      account.Balance,
//		Available:    account.Available,
//		LockAmount:   account.LockAmount,
//		Rights:       account.Balance.Add(floatProfit).Truncate(define.ReplyFloatPrecision),
//	}
//	if ac.Rights.LessThan(decimal.Zero) {
//		ac.Rights = decimal.Zero
//	}
//	if fn != nil {
//		fn(ac)
//	}
//}
//
//func NotifyAccountChangeWithAccountAndTx(userId int64, currencyName string, account *proto.Account, tx *sqlx.Tx, fn func(*proto.AccountChange)) (err error) {
//	if tx == nil {
//		tx, err = database.Begin()
//		if err != nil {
//			log.Errorf("datbase get tx fail,%v", err)
//			return
//		}
//		defer database.CommitTx(tx, &err, 0, func(i int64, e error) {
//		})
//
//	}
//	//获取账户余额信息
//	if account == nil {
//		account, err = database.GetUserAccountByCurrencyNameWithLock(tx, userId, currencyName)
//		if err != nil {
//			log.Error("database.GetUserAccountByCurrencyNameWithLock fail", zap.Int64("userId", userId), zap.String("currencyName", currencyName), zap.Error(err))
//			return
//		}
//		cache.UpdateContractAccount(account)
//	}
//
//	if !isPushAccount {
//		return
//	}
//
//	_, _, _, floatProfit, err := GetCoinMarginDetail(tx, userId, 0, currencyName)
//	if err != nil {
//		log.Errorf("commonsrv.GetCoinFollowMargin fail,%v", err)
//		return
//	}
//
//	ac := &proto.AccountChange{
//		UserId:       userId,
//		AccountType:  0,
//		CoinName: currencyName,
//		Balance:      account.Balance,
//		Available:    account.Available,
//		LockAmount:   account.LockAmount,
//		Rights:       account.Balance.Add(floatProfit).Truncate(define.ReplyFloatPrecision),
//	}
//	if ac.Rights.LessThan(decimal.Zero) {
//		ac.Rights = decimal.Zero
//	}
//	if fn != nil {
//		fn(ac)
//	}
//	return
//}
//
//func NotifyFollowAccountChange(userId int64, currencyName string, fn func(*proto.AccountChange)) {
//	if !isPushAccount {
//		return
//	}
//
//	tx, err := database.Begin()
//	if err != nil {
//		log.Errorf("datbase get tx fail,%v", err)
//		return
//	}
//	defer database.CommitTx(tx, &err, 0, func(i int64, e error) {
//	})
//
//	//获取账户余额信息
//	account, err := database.GetFollowAccountByCurrencyNameWithLocked(tx, userId, currencyName)
//	if err != nil {
//		log.Error("database.GetFollowAccountByUserIdWithLocked fail", zap.Int64("userId", userId), zap.String("currencyName", currencyName), zap.Error(err))
//		return
//	}
//
//	//获取保证金及浮动盈亏
//	mkMap := cache.GetAllContractComplexPrice()
//	total, float, err := GetCoinFollowMargin(tx, userId, currencyName, mkMap)
//	if err != nil {
//		log.Errorf("commonsrv.GetCoinFollowMargin fail,%v", err)
//		return
//	}
//	balance := account.Balance.Add(total).Sub(float).Add(account.LockAmount).Truncate(define.MarginPrecision)
//	accountRights := account.Balance.Add(total).Truncate(define.MarginPrecision)
//	if balance.LessThan(decimal.Zero) {
//		balance = decimal.Zero
//	}
//	if accountRights.LessThan(decimal.Zero) {
//		accountRights = decimal.Zero
//	}
//	ac := &proto.AccountChange{
//		UserId:       userId,
//		AccountType:  define.AccountTypeByFollow,
//		CoinName: currencyName,
//		Balance:      balance,
//		LockAmount:   account.LockAmount,
//		Rights:       accountRights,
//	}
//	if fn != nil {
//		fn(ac)
//	}
//}
