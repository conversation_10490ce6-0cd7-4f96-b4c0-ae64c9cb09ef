package commonsrv

import (
	"github.com/shopspring/decimal"
	"spot/libs/cache"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
	"strings"
	"time"
)

func GenerateIndexHistory(order *proto.MatchOrder) (index *proto.IndexHistory) {
	//查询合约详情
	c, err := GetContractDetail(order.Id, order.Symbol, nil)
	if err != nil {
		log.Errorf("GenerateIndexHistory GetContractDetail fail,%v", err)
		return nil
	}

	//推送成交消息
	usd, _ := cache.GetCoinRate(define.LegalCoinNameUSD, define.CacheDBNumber2)
	var priceCn decimal.Decimal
	if strings.ToUpper(c.CoinName) == "USDT" {
		priceCn = order.TradePrice.Mul(usd.PriceCNY).Round(define.CNYPrecision)
	} else {
		//折合成保证金币种量，换算成美元，折合人民币
		u, _ := cache.GetCoinRate(c.CoinName, define.CacheDBNumber2)
		priceCn = order.TradePrice.Mul(u.PriceUSDT).Mul(usd.PriceCNY).Round(define.CNYPrecision)
	}

	index = &proto.IndexHistory{
		IndexId:      database.NextID(),
		ContractCode: c.ContractCode,
		TradePrice:   order.TradePrice,
		TradePriceCn: priceCn,
		DealAmount:   order.TradeVolume,
		Side:         order.Side,
		DealTime:     order.TradeTime.Unix(),
		CreateBy:     order.TradeTime,
		IsMain:       true,
	}
	order.TradePriceCn = priceCn
	return
}

func GenerateIndexHistoryWithMarkTrade(order *proto.MarketTrade) (index *proto.IndexHistory) {
	//查询合约详情
	c, err := GetContractDetail(0, order.Symbol, nil)
	if err != nil {
		log.Errorf("GenerateIndexHistory GetContractDetail fail,%v", err)
		return nil
	}

	//推送成交消息
	tradePrice := order.Price
	usd, _ := cache.GetCoinRate(define.LegalCoinNameUSD, define.CacheDBNumber2)
	var priceCn decimal.Decimal
	if strings.ToUpper(c.CoinName) == "USDT" {
		priceCn = tradePrice.Mul(usd.PriceCNY).Round(define.CNYPrecision)
	} else {
		//折合成保证金币种量，换算成美元，折合人民币
		u, _ := cache.GetCoinRate(c.CoinName, define.CacheDBNumber2)
		priceCn = tradePrice.Mul(u.PriceUSDT).Mul(usd.PriceCNY).Round(define.CNYPrecision)
	}
	si := cache.GetContractDepthInfo(order.Symbol)
	index = &proto.IndexHistory{
		IndexId:      database.NextID(),
		ContractCode: c.ContractCode,
		TradePrice:   tradePrice,
		TradePriceCn: priceCn,
		DealAmount:   order.Volume,
		Side:         order.Side,
		DealTime:     time.Now().Unix(),
		CreateBy:     time.Now(),
		IsMain:       false,
	}
	if si != nil {
		index.BuyFirst = si.BuyFirst
		index.SellFirst = si.SellFirst
	}
	cache.SetContractPriceIndex(index)
	return
}

func IsMaintenance(code string) bool {
	c, err := GetContractDetail(0, code, nil)
	if err != nil {
		log.Errorf("OrderOpenPosition GetContractDetail fail,%v", err)
		return false
	}
	return c.IsMaintenance.Bool()
}
