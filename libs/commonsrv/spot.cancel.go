package commonsrv

import (
	"github.com/jmoiron/sqlx"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/messagequeue"
	"spot/libs/nums"
	"spot/libs/proto"
	"time"
)

const _fullPercent = "99"

func DealContractAccountOrderCanceled(data *proto.MCancel) (err error) {
	log.Info("开始处理账户撤销", zap.Any("订单", data.OrderId), zap.Any("标记", data))
	var order *proto.EntrustOrder
	tx, err := database.Begin()
	if err != nil {
		log.Error("dealMatchOverForUser get tx fail", zap.Error(err))
		return
	}
	defer database.CommitTx(tx, &err, 0, func(i int64, e error) {
		if e == nil {
			cache.DelOrderSeqId(order.ID)
			if order == nil {
				return
			}
			//处理账户资金变化
			if order != nil {
				messagequeue.NotifyMqEntrustOrderStatusUpdate(order)
			}
		}
	})

	order, err = database.GetUserEntrustOrderWithLock(tx, data.OrderId)
	if err != nil {
		log.Error("dealMatchOverForUser database.GetUserEntrustOrderWithLock fail", zap.Error(err))
		return
	}
	if order == nil {
		log.Info("没有查询到订单", zap.Any("data", order))
		cache.DelOrderSeqId(order.ID)
		return
	}

	info, err := GetContractDetail(0, order.ContractCode, tx)
	if err != nil {
		log.Error("commonsrv.GetContractDetail fail", zap.Error(err))
		return
	}

	var outName string
	if order.Side == define.OrderBuy {
		outName = info.CoinName
	} else {
		outName = info.BaseCoinName
	}

	//获取用户账户信息
	account, err := database.GetUserAccountByCurrencyNameWithLock(tx, order.UserID, outName)
	if err != nil {
		log.Error("database.GetUserAccountByCurrencyNameWithLock fail", zap.Int64("userId", order.UserID), zap.String("currencyName", order.CoinName), zap.Error(err))
		return
	}
	err = dealOrderCancelingForContractAccount(tx, account, order, data.CancelMark, data.TradeMark)
	if err != nil {
		log.Error("dealFullOrderCanceled dealOrderCancelingForContractAccount", zap.Error(err))
		return
	}

	return
}

func dealOrderCancelingForContractAccount(tx *sqlx.Tx, account *proto.Account, order *proto.EntrustOrder, mark, tradeMark int) (err error) {
	order.UpdateTime = time.Now()
	//委托订单变化
	if order.State == define.OrderStatusDefault || order.TradeVolume.Equal(decimal.Zero) {
		order.State = define.OrderStatusNotDealCancel
		if mark == define.CancelOrderDrop {
			order.State = define.OrderStatusDrop
		}
	} else {
		if !order.IsFinished() && order.TradeVolume.GreaterThan(decimal.Zero) {
			if order.TradeVolume.Equal(order.Volume) {
				order.State = define.OrderStatusFull
			} else {
				order.State = define.OrderStatusPartCancel
				if order.EntrustType == define.EntrustTypeMarket && order.Side == define.OrderBuy {
					//如果委托的金额消耗到99%，标记为全部成交
					if order.TradeMoney.Div(order.Money).GreaterThanOrEqual(nums.NewFromString(_fullPercent)) {
						order.State = define.OrderStatusFull
					}
					if tradeMark == define.TradeMarkWithTradeFinish {
						order.State = define.OrderStatusFull
					}
				}
			}
		}

	}

	releaseAmount := order.AssetLock
	order.AssetLock = decimal.Zero
	if order.IsFinished() {
		//计算委托资产到账户，释放剩余到可用
		err = database.UpdateUserAccountByAccountId(tx, account.AccountId, 0, nums.Float(releaseAmount), -nums.Float(releaseAmount), 0)
		if err != nil {
			log.Errorf("db.UpdateUserAccountByAccountId fail,%v", err)
			return
		}
		order.AssetLock = decimal.Zero
		cache.RemoveEntrustOrderForThirdId(order.MarketSource, order.ThirdOrderId)
	}

	//修改委托订单状态
	err = database.UpdateEntrustOrderForReleaseLock(tx, order)
	if err != nil {
		log.Error("database.UpdateEntrustOrderForTrade", zap.Error(err))
		return
	}

	//插入历史记录
	if !(define.IsDeleteDropOrder && order.State == define.OrderStatusDrop) {
		err = database.InsertEntrustOrderHistory(tx, order)
		if err != nil {
			log.Error("database.InsertEntrustOrderHistory fail ", zap.Error(err))
			return
		}
	}
	err = database.DeleteEntrustOrder(tx, order.ID)
	if err != nil {
		log.Error("database.DeleteEntrustOrder fail", zap.Error(err))
		return
	}

	return err
}
