package commonsrv

import (
	"database/sql"
	"errors"
	"fmt"
	"github.com/jmoiron/sqlx"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"math"
	"spot/libs/cache"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/messagequeue"
	"spot/libs/nums"
	"spot/libs/proto"
	"spot/libs/xrpcclient/user_rpc"
	"time"
)

func DealUserTrade(mr *proto.MRecord) {
	log.Info("开始处理成交数据", zap.Any("mr", mr))
	var (
		tx            *sqlx.Tx
		err           error
		info          *proto.Contract
		order         *proto.EntrustOrder
		isOrderCancel bool
	)
	if mr.TradeId == 0 && mr.MatchID > 0 {
		mr.TradeId = mr.MatchID
	}
	tradeVolume := mr.DealVolume
	updateTime := time.Now()
	tx, err = database.Begin()
	if err != nil {
		log.Error("dealUserTradeForFullOpen get tx fail", zap.Error(err))
		return
	}
	defer database.CommitTx(tx, &err, 0, func(i int64, e error) {
		//通知修改币种所有强评价
		if e == nil {
			if order == nil {
				log.Error("order is nil", zap.Any("order", order))
				return
			}

			if order.ThirdOrderId > 0 {
				cache.SetThirdOrderTrade(order.ThirdOrderId, mr.TradeId)
			}

			if isOrderCancel {
				return
			}

			//通知用户委托订单变化
			messagequeue.NotifyMqEntrustOrderStatusUpdate(order)

			if order.IsFinished() {
				cache.DelOrderSeqId(order.ID)
				cache.RemoveEntrustOrderForThirdId(order.MarketSource, order.ThirdOrderId)
			}

		}
	})

	//锁定委托订单
	order, err = database.GetUserEntrustOrderWithLock(tx, mr.OrderId)
	if err != nil {
		fmt.Println(err)
		log.Error("dealUserTradeForFullOpen database.GetUserEntrustOrderWithLock fail，没有查询到订单，订单可能已完成", zap.Error(err))
		cache.DelOrderSeqId(mr.OrderId)
		return
	}
	log.Info("order", zap.Any("order", order))
	if order == nil {
		err = errors.New("订单为空")
		cache.DelOrderSeqId(mr.OrderId)
		return
	}
	if order.IsFinished() {
		log.Info("订单已完成,不在处理本次成交", zap.Any("订单", order), zap.Any("mr", mr))
		err = errors.New("订单已完成")
		return
	}

	info, err = GetContractDetail(0, order.ContractCode, tx)
	if err != nil {
		log.Error("commonsrv.GetContractDetail fail", zap.Error(err))
		return
	}

	var inCoinName, outName string
	var inAmount, outAmount decimal.Decimal

	//计算本次开仓成本及手续费
	isMaker, feeRate := getUserFeeRate(tx, order.UserID, order.ID, order.ContractCode, info, mr, false)

	thisTradePrice := mr.DealPrice
	thisTradeAsset := tradeVolume.Mul(thisTradePrice)

	if order.Side == define.OrderBuy {
		inCoinName = info.BaseCoinName
		outName = info.CoinName
		outAmount = thisTradeAsset
		inAmount = tradeVolume
	} else {
		outName = info.BaseCoinName
		inCoinName = info.CoinName

		inAmount = thisTradeAsset
		outAmount = tradeVolume
	}

	//获取用户账户信息
	inAccount, err := database.GetUserAccountByCurrencyNameWithLock(tx, order.UserID, inCoinName)
	if err != nil {
		if err == sql.ErrNoRows {
			log.Info("当前用户币种数据没有查询到，准备插入新的数据", zap.Any("data", order))
			inAccount = &proto.Account{
				Available:    decimal.Decimal{},
				LockAmount:   decimal.Decimal{},
				Balance:      decimal.Decimal{},
				CurrencyName: inCoinName,
				TotalProfit:  decimal.Decimal{},
				UserId:       order.UserID,
				PlatformID:   order.PlatformID,
			}
			var id int64
			id, err = database.InsertUserAccount(tx, inAccount)
			if err != nil {
				log.Error("database.InsertUserAccount fail", zap.Error(err))
				return
			}
			if id == 0 {
				err = errors.New("database.InsertUserAccount get id fail")
				return
			}
			inAccount.AccountId = int(id)
		} else {
			log.Error("database.GetUserAccountByCurrencyNameWithLock fail", zap.Int64("userId", order.UserID), zap.String("currencyName", inCoinName), zap.Error(err))
			return
		}
	}

	outAccount, err := database.GetUserAccountByCurrencyNameWithLock(tx, order.UserID, outName)
	if err != nil {
		log.Error("database.GetUserAccountByCurrencyNameWithLock fail", zap.Int64("userId", order.UserID), zap.String("currencyName", outName), zap.Error(err))
		return
	}

	//判断可用是否充足
	if outAmount.GreaterThan(order.AssetLock) || outAmount.LessThanOrEqual(decimal.Zero) {
		log.Error("委托订单冻结不足", zap.Any("mr", mr), zap.Any("订单", order), zap.Any("花费", outAmount.String()))
		//直接处理订单最终状态，将订单撤销
		log.Info("委托订单冻结不足，将撤销本次委托", zap.Any("order", order), zap.Any("开仓花费", outAmount.String()), zap.Any("资产锁定", order.AssetLock.String()))

		//标记订单状态为风险控制取消
		err = database.UpdateEntrustOrderMarkForSystemCanceling(tx, order.ID, define.CancelMarkRiskData)
		if err != nil {
			log.Error("database.UpdateEntrustOrderMarkForCanceling fail", zap.Error(err))
			return
		}
		err = dealOrderCancelingForContractAccount(tx, outAccount, order, define.CancelMarkRiskData, define.TradeMarkWithOrderDefault)
		if err != nil {
			log.Error("dealOrderCancelingForContractAccount fail", zap.Error(err))
			return
		}
		isOrderCancel = true
		return
	}

	tv := order.TradeVolume.Add(tradeVolume)

	//委托订单变化
	if order.State == define.OrderStatusTemp || order.State == define.OrderStatusDefault || order.State == define.OrderStatusPart {
		order.State = define.OrderStatusPart
	}

	//计算本次实际到账
	fee := inAmount.Mul(feeRate).Truncate(define.FullPrecision)
	realIn := inAmount.Sub(fee).Truncate(define.FullPrecision)

	order.TradePrice = order.TradePrice.Mul(order.TradeVolume).Add(mr.DealPrice.Mul(tradeVolume)).Div(tv).Truncate(info.Digit)
	order.TradeVolume = tv
	order.TradeFee = order.TradeFee.Add(fee)
	order.TradeMoney = order.TradeMoney.Add(thisTradeAsset)
	order.AssetLock = order.AssetLock.Sub(outAmount)
	if !mr.IsProtocalTrade {
		order.LastMatchPrice = mr.DealPrice
	}

	//订单状态修改
	if (order.Volume.GreaterThan(decimal.Zero) && tv.Equal(order.Volume)) || mr.TradeFinish {
		order.State = define.OrderStatusFull
	} else {
		if order.EntrustType == define.EntrustTypeMarket && order.Side == define.OrderBuy {
			//如果委托的金额消耗到99%，标记为全部成交
			if order.TradeMoney.Div(order.Money).GreaterThanOrEqual(nums.NewFromString(_fullPercent)) {
				order.State = define.OrderStatusFull
			}
		}
	}

	//从委托订单中扣除花费
	order.UpdateTime = updateTime
	err = database.UpdateEntrustOrderForTrade(tx, order)
	if err != nil {
		log.Error("database.UpdateEntrustOrderForTrade", zap.Error(err))
		return
	}

	//从账户扣除
	err = database.UpdateUserAccountByAccountId(tx, outAccount.AccountId, -nums.Float(outAmount), 0, -nums.Float(outAmount), 0)
	if err != nil {
		log.Errorf("db.UpdateUserAccountByAccountId fail,%v", err)
		return
	}
	outAccount.Balance = outAccount.Balance.Sub(outAmount)
	outAccount.LockAmount = outAccount.LockAmount.Sub(outAmount)

	//实际到账
	err = database.UpdateUserAccountByAccountId(tx, inAccount.AccountId, nums.Float(realIn), nums.Float(realIn), 0, nums.Float(fee.Neg()))
	if err != nil {
		log.Errorf("db.UpdateUserAccountByAccountId fail,%v", err)
		return
	}
	inAccount.Balance = inAccount.Balance.Add(realIn)
	inAccount.Available = inAccount.Available.Add(realIn)
	inAccount.TotalProfit = inAccount.TotalProfit.Add(fee.Neg())

	if order.IsFinished() {
		//计算委托资产到账户，释放剩余到可用
		outAccount.Available = outAccount.Available.Add(order.AssetLock)
		err = database.UpdateUserAccountByAccountId(tx, outAccount.AccountId, 0, nums.Float(order.AssetLock), -nums.Float(order.AssetLock), 0)
		if err != nil {
			log.Errorf("db.UpdateUserAccountByAccountId fail,%v", err)
			return
		}
		order.AssetLock = decimal.Zero
	}

	order.LastMatchPrice = mr.DealPrice
	order.LastThirdTradeId = mr.TradeId
	err = database.UpdateEntrustOrderForTrade(tx, order)
	if err != nil {
		log.Error("database.UpdateEntrustOrderForTrade", zap.Error(err))
		return
	}

	if order.IsFinished() {
		err = database.InsertEntrustOrderHistory(tx, order)
		if err != nil {
			log.Error("database.InsertEntrustOrderHistory fail ", zap.Error(err))
			return
		}

		err = database.DeleteEntrustOrder(tx, order.ID)
		if err != nil {
			log.Error("database.DeleteEntrustOrder fail", zap.Error(err))
			return
		}
	}

	tradeId := database.NextID()

	//资产转出
	ah := &proto.AccountHistory{
		Id:           database.NextID(),
		OrderId:      order.ID,
		UserId:       order.UserID,
		Amount:       -nums.Float(outAmount),
		RawAmount:    decimal.Decimal{},
		Available:    outAccount.Available,
		Balance:      outAccount.Balance,
		CreatedTime:  updateTime,
		CurrencyName: outAccount.CurrencyName,
		Imei:         order.Imei,
		IpAddress:    order.IpAddress,
		OrderClient:  order.OrderClient,
		Type:         define.AccountBillTypeTradeOut,
		PlatformID:   order.PlatformID,
		Momo:         "",
		ExtraID:      0,
		TradeId:      tradeId,
	}
	err = database.InsertUserAccountHistory(tx, ah)
	if err != nil {
		log.Errorf("database.InsertUserAccountHistory fail,%v", err)
		return
	}

	//资产交易转入
	aih := &proto.AccountHistory{
		Id:           database.NextID(),
		OrderId:      order.ID,
		UserId:       order.UserID,
		Amount:       nums.Float(realIn),
		RawAmount:    decimal.Decimal{},
		Available:    inAccount.Available,
		Balance:      inAccount.Balance,
		CreatedTime:  updateTime,
		CurrencyName: inAccount.CurrencyName,
		Imei:         order.Imei,
		IpAddress:    order.IpAddress,
		OrderClient:  order.OrderClient,
		Type:         define.AccountBillTypeTradeIn,
		PlatformID:   order.PlatformID,
		Momo:         "",
		ExtraID:      0,
		TradeId:      tradeId,
	}
	err = database.InsertUserAccountHistory(tx, aih)
	if err != nil {
		log.Errorf("db.InsertUserAccountHistory fail,%v", err)
		return
	}

	//资产交易手续费
	aifh := &proto.AccountHistory{
		Id:           database.NextID(),
		OrderId:      order.ID,
		UserId:       order.UserID,
		Amount:       nums.Float(fee.Neg()),
		RawAmount:    decimal.Decimal{},
		Available:    inAccount.Available,
		Balance:      inAccount.Balance,
		CreatedTime:  updateTime,
		CurrencyName: inAccount.CurrencyName,
		Imei:         order.Imei,
		IpAddress:    order.IpAddress,
		OrderClient:  order.OrderClient,
		Type:         define.AccountBillTypeOpenFee,
		PlatformID:   order.PlatformID,
		Momo:         "",
		ExtraID:      0,
		TradeId:      tradeId,
	}
	if isMaker {
		aifh.Type = define.AccountBillTypeOpenFeeMaker
	}
	err = database.InsertUserAccountHistory(tx, aifh)
	if err != nil {
		log.Errorf("db.InsertUserAccountHistory fail,%v", err)
		return
	}

	//增加成交记录
	trade := &proto.Trade{
		TradeId:           tradeId,
		UserId:            order.UserID,
		ContractCode:      order.ContractCode,
		Imei:              order.Imei,
		IpAddress:         order.IpAddress,
		OrderClient:       order.OrderClient,
		OrderId:           order.ExtraID,
		OrderType:         order.OrderType,
		CurrentPrice:      decimal.Decimal{},
		Side:              order.Side,
		Commission:        fee,
		Price:             mr.DealPrice,
		Volume:            tradeVolume,
		TradeTime:         updateTime,
		TradeAmount:       thisTradeAsset,
		ShareResource:     nil,
		PlatformID:        order.PlatformID,
		ExtraID:           0,
		EntrustOrderId:    order.ID,
		EntrustType:       order.EntrustType,
		EntrustMode:       order.Mode,
		EntrustVolume:     decimal.Decimal{},
		EntrustStatus:     order.State,
		MatchType:         order.TradeMatchType(mr.IsProtocalTrade),
		MatchId:           mr.MatchID,
		IsMaker:           isMaker,
		CurrencyName:      info.CoinName,
		PriceCurrencyName: info.BaseCoinName,
		OutBalance:        outAccount.Balance,
		OutAvailable:      outAccount.Available,
		InBalance:         inAccount.Balance,
		InAvailable:       inAccount.Available,
		ThirdTradeId:      mr.TradeId,
	}
	if mr.TradeId == 0 {
		mr.ThirdOrderId = trade.TradeId
	}
	err = database.InsertTrade(tx, trade)
	if err != nil {
		log.Errorf("database.InsertTrade err:%v", err)
		return
	}

	//手续费入平台财务
	go dealFSTradeFee(order.PlatformID, order.UserID, math.Abs(nums.Float(fee)), inAccount.CurrencyName, trade.TradeId, mr.IsMaker)

}

func getUserFeeRate(tx *sqlx.Tx, userId, orderId int64, code string, info *proto.Contract, mr *proto.MRecord, isTrader bool) (isMaker bool, feeRate decimal.Decimal) {
	//var ratePoint decimal.Decimal
	//isMaker = mr.IsMaker
	//ratePoint, e := getUserFeePoint(tx, userId, code)
	//if e != nil {
	//	log.Error("getUserFeePoint fail", zap.Error(e))
	//}

	feeMaker, feeTaker := info.FeeMaker, info.FeeTaker
	isMaker = mr.IsMaker
	if isMaker {
		feeRate = feeMaker
		if feeRate.LessThanOrEqual(decimal.Zero) {
			feeRate = feeTaker
		}
	} else {
		feeRate = feeTaker
	}

	//if feeRate.LessThanOrEqual(decimal.Zero) {
	//	feeRate = feeMaker
	//}

	//if !isTrader {
	//	feeRate = feeRate.Add(ratePoint)
	//}

	log.Info("获取用户费率加点", zap.Int64("userId", userId), zap.String("code", code), zap.String("合约费率", info.FeeTaker.String()), zap.String("费率", feeRate.String()))
	return
}

func getUserFeePoint(tx *sqlx.Tx, userId int64, code string) (rate decimal.Decimal, err error) {
	//return
	label, err := user_rpc.GetInnerUserLabelByCode(database.NextID(), userId, code, false)
	if err != nil {
		log.Error("user.GetInnerUserLabelByCode fail", zap.Error(err), zap.Any("userId", userId), zap.String("code", code))
		return
	}

	if label == nil {
		log.Error("user.GetInnerUserLabelByCode fail,label is nil", zap.Error(err), zap.Any("userId", userId), zap.String("code", code))
		return
	}
	//user, err = database.GetUserInfoByID(userId, tx)
	//if err != nil {
	//	log.Errorf("database.GetUserInfoByID userid；%v，fail,%v", userId, err)
	//	return
	//}
	rate = label.Fee
	return
}
