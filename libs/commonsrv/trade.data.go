package commonsrv

import (
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"math/rand"
	"spot/libs/cache"
	"spot/libs/log"
	"spot/libs/nums"
	"spot/libs/proto"
	"sync"
	"time"
)

//处理第三方现货成交价
func DealThirdSpotTrade(trade proto.MarketTrade) {
	log.Infof("收到%v现货最新成交记录,data:%+v", trade.Source, trade)
	if trade.Price.LessThanOrEqual(decimal.Zero) {
		log.Errorf("收到%v现货最新成交记录无效,data:%+v", trade.Source, trade)
		return
	}
	var isUpdate bool
	//获取缓存中最新数据
	mk := cache.GeContractSpotPriceBySource(trade.Symbol, trade.Source)
	if mk == nil {
		isUpdate = true
	} else {
		if !mk.Price.Equal(trade.Price) {
			isUpdate = true
		} else {
			log.Warn("现货来源价格与缓存中数据价格一直，不更新本次价格", zap.Any("来源价格", trade), zap.Any("缓存最新价格", mk))
		}
	}
	if isUpdate {
		err := cache.SetContractSpotPrice(&trade)
		if err != nil {
			log.Errorf("DealThirdSpotTrade cache.SetContractSpotPrice fail,%v", err)
			return
		}
	}
}

//处理第三方深度数据
func DealDepth(holder proto.DepthHolder, c *proto.Contract) (h *proto.DepthHolder) {
	if holder.Source == "" {
		return
	}
	h = covertDepthForContract(holder, c)
	if h == nil {
		return
	}
	min := len(h.Asks)
	if len(h.Bids) < min {
		min = len(h.Bids)
	}
	cHeight := GetDepthCommonHeight()
	if min > cHeight {
		h.Asks = h.Asks[:cHeight]
		h.Bids = h.Bids[:cHeight]
	}
	cache.SetMarketDepths(h)
	return
}

func covertDepthForContract(holder proto.DepthHolder, c *proto.Contract) (dh *proto.DepthHolder) {
	var err error
	if c == nil {
		c, err = GetContractDetail(0, holder.Symbol, nil)
		if err != nil {
			log.Errorf("commonsrv.GetContractDetail fail,%v", err)
			return
		}
	}

	digit := c.Digit
	var bidDepth, askDepth []proto.DepthData
	var wg sync.WaitGroup
	wg.Add(2)
	go func() {
		defer wg.Done()
		for _, v := range holder.Bids {
			price := nums.Floor(v.Price, c.PriceStep).Truncate(digit)
			v.Volume = v.Volume.Round(c.VolumeDigit).Add(nums.NewFromInt64(rand.Int63n(4)).Add(nums.NewFromInt(1)).Shift(-5))
			if len(bidDepth) == 0 {
				bidDepth = append(bidDepth, proto.DepthData{Price: price, Volume: v.Volume})
				continue
			}
			lastIndex := len(bidDepth) - 1
			last := bidDepth[lastIndex]
			if price.Equal(last.Price) {
				last.Volume = last.Volume.Add(v.Volume)
				bidDepth[lastIndex] = last
			} else {
				bidDepth = append(bidDepth, proto.DepthData{Price: price, Volume: v.Volume})
			}
		}
	}()

	go func() {
		defer wg.Done()
		for _, v := range holder.Asks {
			price := nums.Ceiling(v.Price, c.PriceStep).Truncate(digit)
			v.Volume = v.Volume.Round(c.VolumeDigit).Add(nums.NewFromInt64(rand.Int63n(4)).Add(nums.NewFromInt(1)).Shift(-5))
			if len(askDepth) == 0 {
				askDepth = append(askDepth, proto.DepthData{Price: price, Volume: v.Volume})
				continue
			}
			lastIndex := len(askDepth) - 1
			last := askDepth[lastIndex]
			if price.Equal(last.Price) {
				last.Volume = last.Volume.Add(v.Volume)
				askDepth[lastIndex] = last
			} else {
				askDepth = append(askDepth, proto.DepthData{Price: price, Volume: v.Volume})
			}
		}
	}()
	wg.Wait()

	//log.Info("处理后数据", zap.Any("buy", bidDepth), zap.Any("sell", askDepth))
	var minSize = len(bidDepth)
	if minSize > len(askDepth) {
		minSize = len(askDepth)
	}
	if minSize == 0 {
		return nil
	}

	return &proto.DepthHolder{
		Symbol: holder.Symbol,
		Source: holder.Source,
		Asks:   askDepth[:minSize],
		Bids:   bidDepth[:minSize],
		Ts:     time.Now(),
	}
}

//最终深度高度
func GetDepthCommonHeight() int {
	return 80
}

//抓取深度高度
func GetFetchDepthCommonHeight() int {
	return 80
}
