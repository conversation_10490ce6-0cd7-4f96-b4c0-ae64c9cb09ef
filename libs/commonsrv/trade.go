package commonsrv

//处理并推送深度数据
//func DealDepthData(p *proto.Contract, h *proto.IndexHistory, price decimal.Decimal, b []proto.MarketDepth, s []proto.MarketDepth, notify func(*proto.DepthContainer)) {
//	var recentOrder = &proto.ApiRecentOrder{
//		TradeID:      h.IndexId,
//		ContractCode: h.ContractCode,
//		Price:        price.StringFixed(p.Digit),
//		Volume:       h.DealAmount,
//		Side:         "",
//		OrderTime:    h.CreateBy.Unix(),
//	}
//	cache.PushNewDealToRedis(recentOrder)
//
//	//推送深度数据
//	depth := &proto.DepthContainer{
//		TS:           time.Now().Unix(),
//		Level:        0,
//		Digit:        p.Digit,
//		PriceLimit:   p.Digit,
//		ContractCode: p.ContractCode,
//		Buy:          generateBuy(b, p),
//		Sell:         generateBuy(s, p),
//	}
//	cache.SetContractOriginDepth(depth)
//	notify(depth)
//	//notifyMqDepthChange(depth)
//
//	//计算不同级别深度数据
//	CalDepth(depth, true, notify)
//
//}
//
//func CalDepth(depthC *proto.DepthContainer, isCache bool, notify func(*proto.DepthContainer)) {
//	for level := range define.DepthSupportLevel {
//		if level == 0 {
//			continue
//		}
//		dp := CreateLevelDepth(depthC, int32(level), isCache)
//		if dp != nil {
//			notify(dp)
//			//notifyMqDepthChange(dp)
//		}
//		//go func(l int) {
//		//}(level)
//	}
//}
