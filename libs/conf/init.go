package conf

import (
	"flag"
	"fmt"
	"io/ioutil"
	"time"

	"gopkg.in/yaml.v3"
	"spot/libs/define"
	"spot/libs/generic"
)

const (
	ProdEnv = "prod"
	TestEnv = "test"
	DevEnv  = "dev"
)

var c *define.Config

func Identifier() string                            { return c.Identifier }
func RunEnv() string                                { return c.RunEnv }
func DebugEnv() bool                                { return c.Debug }
func IsSlave() bool                                 { return c.IsSlave }
func IsMaster() bool                                { return !c.IsSlave }
func CloneNums() int                                { return generic.If[int](c.CloneNums == 0, 1, c.CloneNums) }
func Symbols() []string                             { return c.Symbols }
func NotDepthSymbols() []string                     { return c.NotDepthSymbols }
func MQQueueID() int                                { return c.MQQueueID }
func CloneID() int                                  { return c.CloneID }
func WorkerID() int64                               { return c.WorkerID }
func ZipHttp() bool                                 { return c.ZipHttp }
func H5CanLogin() bool                              { return c.H5CanLogin }
func LocalName() string                             { return c.LocalName }
func Discovery() string                             { return c.Discovery }
func RPCAddr() string                               { return c.RPCAddr }
func RPCxAddr() string                              { return c.RPCxAddr }
func PprofAddr() string                             { return c.PprofAddr }
func ApiRPCAddr() string                            { return c.ApiRPCAddr }
func MarketRPCAddr() string                         { return c.MarketRPCAddr }
func CoreRPCAddr() string                           { return c.CoreRPCAddr }
func FollowRPCAddr() string                         { return c.FollowRPCAddr }
func ListenAddr() string                            { return c.ListenAddr }
func ListenAddrForCWS() string                      { return c.ListenAddrForCWS }
func ReadTimeout() int64                            { return c.ReadTimeout }
func WriteTimeout() int64                           { return c.WriteTimeout }
func DefaultDB() string                             { return c.DefaultDB }
func CrmDbDSN() string                              { return c.CrmDbDSN }
func DefaultRedis() string                          { return c.DefaultRedis }
func DefaultRedisPwd() string                       { return c.DefaultRedisPwd }
func RedisPool() int                                { return c.RedisPool }
func LogFile() string                               { return c.LogFile }
func LogLevel() string                              { return c.LogLevel }
func LogOnlyFile() bool                             { return c.LogOnlyFile }
func MailPrefix() string                            { return c.MailPrefix }
func CautionReceiver() []string                     { return c.CautionReceiver }
func HedgeReceiver() []string                       { return c.HedgeReceiver }
func MarketReceiver() []string                      { return c.MarketReceiver }
func MQ() string                                    { return c.MQ }
func MaxTradeCounter() int64                        { return c.MaxTradeCounter }
func MQQueueName() string                           { return c.MQQueueName }
func WalletURL() string                             { return c.WalletURL }
func WalletToken() string                           { return c.WalletToken }
func CwsNetwork() string                            { return c.CwsNetwork }
func DepthHeight() int                              { return c.DepthHeight }
func HedgeDuration() int                            { return c.HedgeDuration }
func IsSimulate() bool                              { return c.IsSimulate }
func ExtraRedis() string                            { return c.ExtraRedis }
func ExtraRedisPwd() string                         { return c.ExtraRedisPwd }
func ExtraRedisDB() int                             { return c.ExtraRedisDB }
func InitBalance() float64                          { return c.InitBalance }
func IsProduct() bool                               { return RunEnv() == ProdEnv }
func IsTest() bool                                  { return RunEnv() == TestEnv }
func IsDev() bool                                   { return RunEnv() == DevEnv }
func SinglePlace() int                              { return c.SinglePlace }
func MarketAliveDuration() int                      { return c.MarketAliveDuration }
func ShareLanguage() bool                           { return c.ShareLanguage }
func C2CListenAddr() string                         { return c.C2CListenAddr }
func C2CPlatformID() string                         { return c.C2CPlatformID }
func C2CSecretKey() string                          { return c.C2CSecretKey }
func WithdrawCautionReceiver() []string             { return c.WithdrawCautionReceiver }
func ExchangeCautionReceiver() []string             { return c.ExchangeCautionReceiver }
func KlineRecover() bool                            { return c.KlineRecover }
func KlineWeekNature() bool                         { return c.KlineWeekNature }
func StatisticsEmailReceiver() []string             { return c.StatisticsEmailReceiver }
func ErrCollectEmailReceiver() []string             { return c.ErrCollectEmailReceiver }
func WithdrawTimeoutEmailReceiver() []string        { return c.WithdrawTimeoutEmailReceiver }
func UploadImageMaxSize() int64                     { return c.UploadImageMaxSize }
func UploadImageFilePath() string                   { return c.UploadImageFilePath }
func ImageRequestUrlBase() string                   { return c.ImageRequestUrlBase }
func IsNotGenerateTick() bool                       { return c.IsNotGenerateTick }
func NewsConfig() define.NewsConfig                 { return c.NewsConfig }
func IPWhiteListForCWS() []string                   { return c.IPWhiteListForCWS }
func MsgServerURL() string                          { return c.MsgServerURL }
func MsgServerAppID() string                        { return c.MsgServerAppID }
func MsgServerSecretKey() string                    { return c.MsgServerSecretKey }
func DefaultRedisTLS() bool                         { return c.DefaultRedisTLS }
func ExtraRedisTLS() bool                           { return c.ExtraRedisTLS }
func HedgeOkexConcurrency() int                     { return c.HedgeOkexConcurrency }
func HedgeOkexDelay() int                           { return c.HedgeOkexDelay }
func HedgeOkexEndpoint() string                     { return c.HedgeOkexEndpoint }
func HedgeOkexSimulated() bool                      { return c.HedgeOkexSimulated }
func HedgeRESTTimeout() time.Duration               { return time.Duration(c.HedgeRESTTimeout) * time.Second }
func SenseApiKey() string                           { return c.SenseApiKey }
func SenseApiSecret() string                        { return c.SenseApiSecret }
func SenseApiUrl() string                           { return c.SenseApiUrl }
func IPDBFilePath() string                          { return c.IPDBFilePath }
func FXHOpenAPI() string                            { return c.FXHOpenAPI }
func CoinGeckoOpenAPI() string                      { return c.CoinGeckoOpenAPI }
func GetClosingWorkerNums() int                     { return c.ClosingWorkerNums }
func ClosingQueueNums() int                         { return c.ClosingQueueNums }
func GetRcDealNums() int                            { return c.RcDealNums }
func DefaultRedisConf() define.RedisConfig          { return c.DefaultRedisConf }
func ExtraRedisConf() define.RedisConfig            { return c.ExtraRedisConf }
func MigrationApiUrl() string                       { return c.MigrationApiUrl }
func MigrationRestUrl() string                      { return c.MigrationRestUrl }
func MigrationId() string                           { return c.MigrationId }
func MigrationKey() string                          { return c.MigrationKey }
func MultipleDbConf() map[string]define.DBConf      { return c.MultipleDbConf }
func AppKeyConfig() map[string]define.AppKey        { return c.AppKeyConfig }
func MarketConfig() define.MarketConfig             { return c.MarketConfig }
func GinTrustedProxies() []string                   { return c.GinTrustedProxies }
func DocConf() define.SwaggerConf                   { return c.DocConf }
func GetConfig() *define.Config                     { return c }
func TickerDuration() int64                         { return c.TickerDuration }
func SupportAccountType() define.SupportAccountType { return c.SupportAccountType }

func Init() {
	if c == nil {
		filepath := flag.String("c", getConfigFile(), "config file path")
		flag.Parse()
		c = newConfig()
		parseConfig(*filepath)
		if len(c.SensitiveConf) > 0 {
			// 获取私密的配置文件
			parseConfig(c.SensitiveConf)
		}
	}

	define.RedisCommonDb = c.DefaultRedisConf.DefaultDB
	define.RedisExtraDb = c.ExtraRedisConf.DefaultDB
	return
}

func SetConfig(config *define.Config) {
	if config == nil {
		c = newConfig()
		return
	}
	c = config
}

func parseConfig(filepath string) {
	if len(filepath) > 0 {
		yamlFile, err := ioutil.ReadFile(filepath)
		if err != nil {
			fmt.Printf("Init config read file failed, filepath:%s, err:%v\n", filepath, err)
			return
		}

		err = yaml.Unmarshal(yamlFile, c)
		if err != nil {
			fmt.Printf("Init parse config failed, filepath:%s, err:%v\n", filepath, err)
			panic(err)
		}
	}
}

func getConfigFile() string {
	return "/Users/<USER>/hj_job/web3/spot-server/pkg/core/cfg/config_local.yaml"
	//if _, file, _, ok := runtime.Caller(1); ok {
	//	return path.Dir(file) + "/config.yaml"
	//}
	//return "./config.yaml"
}

func newConfig() *define.Config {
	return &define.Config{
		Debug:           true,
		ZipHttp:         false,
		H5CanLogin:      false,
		LocalName:       "Test Server",
		RPCAddr:         "0.0.0.0:9902",
		ApiRPCAddr:      "127.0.0.1:9902",
		MarketRPCAddr:   "127.0.0.1:9903",
		ListenAddr:      "0.0.0.0:9901",
		ReadTimeout:     30,
		WriteTimeout:    30,
		DefaultDB:       "root:123456@tcp(127.0.0.1:3306)/basecoin?charset=utf8&parseTime=true&loc=Local",
		DefaultRedis:    "127.0.0.1:6379",
		DefaultRedisPwd: "",
		DefaultRedisDB:  15,
		RedisPool:       20,
		LogFile:         "basecoin/core",
		LogLevel:        "debug",
		SensitiveConf:   "",
		MailPrefix:      "【Base】",
		CautionReceiver: []string{"<EMAIL>", "<EMAIL>"},
		//MQ:              "amqp://quickly:quickly123@127.0.0.1:5672",
		MaxTradeCounter: 50,
		MQQueueName:     "",
		WalletURL:       "https://test.walletservice.com/api/v2",
		WalletToken:     "3760780f34c3dd2718f12fdf7b4f766d8cb079aa4f28870f2cc9a995215c6397",
		CwsNetwork:      "test",
	}
}
