package plist

//
//import (
//	"spot/libs/log"
//	"sync"
//)
//
//type SymbolMap struct {
//	l sync.RWMutex
//	m map[string]*PriceContainer
//}
//
//func NewSymbolMap() *SymbolMap {
//	return &SymbolMap{
//		m: make(map[string]*PriceContainer),
//	}
//}
//
////正序，买盘从大到小，买盘从小到大，反之相反 isDesc 以买盘顺序
//func (s *SymbolMap) Put(symbol string) *PriceContainer {
//	s.l.Lock()
//	defer s.l.Unlock()
//	list, ok := s.m[symbol]
//	if ok {
//		return list
//	} else {
//		log.Infof("初始化交易对链表:%+v", symbol)
//	}
//	list = NewPriceList(symbol)
//	s.m[symbol] = list
//	return list
//}
//
//func (s *SymbolMap) Get(symbol string) (*PriceContainer, bool) {
//	s.l.RLock()
//	defer s.l.RUnlock()
//	list, ok := s.m[symbol]
//	return list, ok
//}
//
//func (s *SymbolMap) Each(f func(string, *PriceContainer)) {
//	s.l.RLock()
//	defer s.l.RUnlock()
//	for k, v := range s.m {
//		f(k, v)
//	}
//}
