///*
//@Time : 3/15/20 10:54 上午
//<AUTHOR> mocha
//@File : forecontainer
//*/
package plist

//
//import (
//	"spot/libs/container"
//	"spot/libs/define"
//	"spot/libs/log"
//	"spot/libs/proto"
//	"fmt"
//	"github.com/emirpasic/gods/lists/doublylinkedlist"
//	"github.com/emirpasic/gods/maps/treemap"
//	"sync"
//)
//
//type PriceContainer struct {
//	l            sync.RWMutex
//	More         *treemap.Map //从大到小
//	Less         *treemap.Map //从小到大
//	contractCode string
//}
//
////大于等于指数价格
//func NewPriceList(symbol string) *PriceContainer {
//	return &PriceContainer{
//		contractCode: symbol,
//		More:         treemap.NewWith(container.FloatDescCompartor), //从大到小
//		Less:         treemap.NewWith(container.FloatAscCompartor),  //从小到大
//	}
//}
//
//func (p *PriceContainer) Stat() string {
//	return fmt.Sprintf("buy,size:%v,sell:size:%v,", p.More.Size(), p.Less.Size())
//}
//
//func (p *PriceContainer) Add(o *proto.PlanCloseOrder) {
//	p.l.Lock()
//	defer p.l.Unlock()
//	switch o.Condition {
//	case define.PlanCloseMoreThanEqual:
//		v, ok := p.More.Get(o.ClosePrice)
//		if !ok {
//			p.More.Put(o.ClosePrice, doublylinkedlist.New(o.PlanCloseOrderId))
//			return
//		}
//		vs, ok := v.(*doublylinkedlist.List)
//		if !ok {
//			log.Error("not is doublylinkedlist.List")
//			return
//		}
//		if vs.Contains(o.PlanCloseOrderId) {
//			log.Infof("盘中存在id:%v", o.PlanCloseOrderId)
//			return
//		}
//		vs.Add(o.PlanCloseOrderId)
//	case define.PlanCloseLessThanEqual:
//		v, ok := p.Less.Get(o.ClosePrice)
//		if !ok {
//			p.Less.Put(o.ClosePrice, doublylinkedlist.New(o.PlanCloseOrderId))
//			return
//		}
//		vs, ok := v.(*doublylinkedlist.List)
//		if !ok {
//			log.Error("not is doublylinkedlist.List")
//			return
//		}
//		if vs.Contains(o.PlanCloseOrderId) {
//			return
//		}
//		vs.Add(o.PlanCloseOrderId)
//	}
//}
//
//func (p *PriceContainer) RemoveId(id int64) {
//	p.l.Lock()
//	defer p.l.Unlock()
//	p.More.Each(func(_ interface{}, value interface{}) {
//		vs, ok := value.(*doublylinkedlist.List)
//		if !ok {
//			log.Error("not is doublylinkedlist.List")
//			return
//		}
//		vs.Remove(vs.IndexOf(id))
//	})
//	p.Less.Each(func(_ interface{}, value interface{}) {
//		vs, ok := value.(*doublylinkedlist.List)
//		if !ok {
//			log.Error("not is doublylinkedlist.List")
//			return
//		}
//		vs.Remove(vs.IndexOf(id))
//	})
//}
//
//func (p *PriceContainer) UpdateId(o *proto.PlanCloseOrder) {
//	p.RemoveId(o.PlanCloseOrderId)
//	p.Add(o)
//}
//
//func (p *PriceContainer) RemovePrice(side int, price string) {
//	p.l.Lock()
//	defer p.l.Unlock()
//	switch side {
//	case define.PlanCloseMoreThanEqual:
//		p.More.Remove(price)
//	case define.PlanCloseLessThanEqual:
//		p.Less.Remove(price)
//	}
//}
//
//func (p *PriceContainer) Clear(side int, price string) {
//	p.l.Lock()
//	defer p.l.Unlock()
//	switch side {
//	case define.PlanCloseMoreThanEqual:
//		p.More.Clear()
//	case define.PlanCloseLessThanEqual:
//		p.Less.Clear()
//	}
//}
//
//func (p *PriceContainer) Name() string {
//	return p.contractCode
//}
