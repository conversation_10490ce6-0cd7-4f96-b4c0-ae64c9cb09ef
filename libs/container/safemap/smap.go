/*
@Time : 2/17/20 11:04 上午
<AUTHOR> mocha
@File : container
*/
package safemap

import "sync"

type Map struct {
	m  map[interface{}]interface{}
	mu sync.RWMutex
}

func New() *Map {
	return &Map{m: make(map[interface{}]interface{})}
}

func (m *Map) Set(k, v interface{}) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.m[k] = v
}

func (m *Map) Remove(k interface{}) {
	m.mu.Lock()
	defer m.mu.Unlock()
	delete(m.m, k)
}

func (m *Map) IfEmptyAndRun(k interface{}, f func(interface{})) {
	m.mu.Lock()
	defer m.mu.Unlock()
	if len(m.m) == 0 {
		f(k)
	}
}

func (m *Map) Clear() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.m = make(map[interface{}]interface{})
}

func (m *Map) Size() int {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return len(m.m)
}

func (m *Map) Get(k interface{}) (interface{}, bool) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	v, ok := m.m[k]
	return v, ok
}

func (m *Map) IsContains(k interface{}) bool {
	m.mu.RLock()
	defer m.mu.RUnlock()
	_, s := m.Get(k)
	return s
}
func (m *Map) IsExist(k interface{}) bool {
	m.mu.RLock()
	defer m.mu.RUnlock()
	_, ok := m.m[k]
	return ok
}

func (m *Map) All() map[interface{}]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.m
}

func (m *Map) Each(f func(interface{}, interface{})) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	for k, v := range m.m {
		f(k, v)
	}
}
