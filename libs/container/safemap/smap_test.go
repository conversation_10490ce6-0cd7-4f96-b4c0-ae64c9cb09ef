/*
@Time : 2/17/20 11:12 上午
<AUTHOR> mocha
@File : smap_test
*/
package safemap

import (
	"fmt"
	"testing"
)

func TestSmap(t *testing.T) {
	s := New()
	s.Set(1, 1)
	s.Set(2, 2)
	s.Set(3, 3)
	//s.Each(func(k interface{}, v interface{}) {
	//	a, ok := k.(int)
	//	if !ok {
	//		fmt.Println("不是Int")
	//		return
	//	}
	//	if a == 1 {
	//		return
	//	}
	//	if a > 2 {
	//		return
	//	} else {
	//		fmt.Println(a)
	//	}
	//})
	s.IfEmptyAndRun("abc", func(i interface{}) {
		fmt.Println(i)
	})
}
