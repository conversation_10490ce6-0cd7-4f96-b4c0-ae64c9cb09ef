package safetreemap

import (
	"github.com/emirpasic/gods/maps/treemap"
	"github.com/emirpasic/gods/utils"
	"sync"
)

type SafeTreeMap struct {
	name  string
	mutex sync.RWMutex
	tm    *treemap.Map
}

func NewSafeTreeMap(name string, comparator utils.Comparator) *SafeTreeMap {
	return &SafeTreeMap{
		name: name,
		tm:   treemap.NewWith(comparator),
	}
}

func (s *SafeTreeMap) GetName() string {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.name
}

func (s *SafeTreeMap) Clear() {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.tm.Clear()
}

func (s *SafeTreeMap) Put(k, v interface{}) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.tm.Put(k, v)
}

func (s *SafeTreeMap) Remove(k interface{}) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.tm.Remove(k)
}

func (s *SafeTreeMap) Iterator() treemap.Iterator {
	s.mutex.RLock()
	defer s.mutex.Unlock()
	return s.tm.Iterator()
}

//Traverse until meet the condition
func (s *SafeTreeMap) Util(f func(k, v interface{}) bool) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	first := s.tm.Iterator()
	for first.Next() {
		if f(first.Key(), first.Value()) {
			return
		}
	}
}

//Traverse treemap
func (s *SafeTreeMap) Each(f func(k, v interface{}) bool) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	first := s.tm.Iterator()
	for first.Next() {
		f(first.Key(), first.Value())
	}
}
