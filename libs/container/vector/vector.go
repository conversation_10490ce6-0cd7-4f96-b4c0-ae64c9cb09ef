/*
@Time : 2/17/20 3:05 下午
<AUTHOR> mocha
@File : arraylist
*/
package vector

import (
	"reflect"
	"sync"
)

type Vector struct {
	mu sync.RWMutex
	l  []interface{}
}

func New() *Vector {
	return &Vector{}
}

func (v *Vector) Add(i ...interface{}) {
	v.mu.Lock()
	defer v.mu.Unlock()
	v.l = append(v.l, i...)
}

func (v *Vector) Del(p interface{}) {
	v.mu.Lock()
	defer v.mu.Unlock()
	index := -1
	for i, value := range v.l {
		if reflect.DeepEqual(p, value) {
			index = i
			break
		}
	}
	if index < 0 {
		return
	}
	v.l = append(v.l[:index], v.l[index+1:]...)
}

func (v *Vector) Each(f func(int, interface{})) {
	v.mu.RLock()
	defer v.mu.RUnlock()
	for i, v := range v.l {
		f(i, v)
	}
}
