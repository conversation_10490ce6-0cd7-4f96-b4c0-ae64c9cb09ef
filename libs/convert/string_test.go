package convert

import (
	"bytes"
	"testing"
)

func TestBytes2Str(t *testing.T) {
	t.Log(Bytes2Str([]byte("this is long long long long long long test text")))
}

func TestStr2Bytes(t *testing.T) {
	expect := []byte("this is long long long long long long test text")
	t.Log(bytes.Equal(Str2Bytes("this is long long long long long long test text"), expect))
}

/*
BenchmarkBytes2Str/Bytes2Str-16         	1000000000	        0.260 ns/op
BenchmarkBytes2Str/type_cast-16         	44631346	        25.9 ns/op
*/
func BenchmarkBytes2Str(b *testing.B) {
	data := []byte("this is long long long long long long test text")
	b.Run("Bytes2Str", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_ = Bytes2Str(data)
		}
	})
	b.Run("type cast", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_ = string(data)
		}
	})
}

/*
BenchmarkStr2Bytes/Str2Bytes-16         	1000000000	        0.283 ns/op
BenchmarkStr2Bytes/type_cast-16         	34132358	        32.2 ns/op
*/
func BenchmarkStr2Bytes(b *testing.B) {
	data := "this is long long long long long long test text"
	b.Run("Str2Bytes", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_ = Str2Bytes(data)
		}
	})
	b.Run("type cast", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_ = []byte(data)
		}
	})
}

func TestHideEmail(t *testing.T) {
	t.Log(HideEmail("<EMAIL>"))
	t.Log(HideEmail("<EMAIL>"))
	t.Log(HideEmail("<EMAIL>"))
	t.Log(HideEmail("<EMAIL>"))
	t.Log(HideEmail("<EMAIL>"))
	t.Log(HideEmail("<EMAIL>"))
	t.Log(HideEmail("<EMAIL>"))
	t.Log(HideEmail("<EMAIL>"))
	t.Log(HideEmail("<EMAIL>"))
}

func TestHidePhone(t *testing.T) {
	t.Log(HidePhone("1"))
	t.Log(HidePhone("12"))
	t.Log(HidePhone("123"))
	t.Log(HidePhone("1234"))
	t.Log(HidePhone("12345"))
	t.Log(HidePhone("123456"))
	t.Log(HidePhone("1234567"))
	t.Log(HidePhone("12345678"))
	t.Log(HidePhone("123456789"))
	t.Log(HidePhone("1234567890"))
	t.Log(HidePhone("12345678901"))
	t.Log(HidePhone("123456789012"))
	t.Log(HidePhone("1234567890123"))
}

func TestHideNickname(t *testing.T) {
	t.Log(HideNickname(""))
	t.Log(HideNickname("1"))
	t.Log(HideNickname("a"))
	t.Log(HideNickname("你"))
	t.Log(HideNickname("12"))
	t.Log(HideNickname("ab"))
	t.Log(HideNickname("你好"))
	t.Log(HideNickname("123"))
	t.Log(HideNickname("abc"))
	t.Log(HideNickname("你好啊"))
}

func TestConvertChain2Array(t *testing.T) {
	t.Log(ConvertChain2Array(".R00001.GWhU2x.Gxh2UW.hW2UGx.hU2WxG"))
}

func TestStringSplitTrim(t *testing.T) {
	t.Log(StringSplitTrim("..A.B.Cc.", "."))
}
