package convert

import (
	"errors"
	"fmt"
	"strconv"
	"time"
)

const (
	timeFormatDay = "2006-01-02" // 日期格式化
)

var (
	NotSupportTimeNumber = errors.New("not support convert to time")
)

// 数字时间转换成时间 0830转换为当天8点30分
func Number2Time(num string) (time.Time, error) {
	tm := time.Unix(0, 0)
	if len(num) != 4 {
		return tm, NotSupportTimeNumber
	}
	hour, minute, err := parseTimeNumber(num)
	if err != nil {
		return tm, NotSupportTimeNumber
	}
	tm = Hour2Time(hour).Add(time.Minute * time.Duration(minute))
	return tm, nil
}

func parseTimeNumber(num string) (int, int, error) {
	var err error
	var hour, minute int
	hour, err = strconv.Atoi(num[:2])
	if err != nil {
		return hour, minute, err
	}
	minute, err = strconv.Atoi(num[2:])
	return hour, minute, err
}

// 字符串转时间戳
func TimeStr2Unix(timeStr, layout string) (int64, error) {
	t, e := time.ParseInLocation(layout, timeStr, time.Local)
	if e != nil {
		fmt.Println("TimeStr2Unix error:", e.Error())
		return 0, e
	}
	return t.Unix(), nil
}

func Hour2Time(hour int) time.Time {
	nowDay, err := time.ParseInLocation(timeFormatDay, time.Now().Format(timeFormatDay), time.Local)
	if err != nil {
		fmt.Println("Hour2Time error:", err.Error())
		return time.Now()
	}
	return nowDay.Add(time.Hour * time.Duration(hour))
}
