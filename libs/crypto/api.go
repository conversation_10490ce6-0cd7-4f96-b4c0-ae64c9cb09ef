package crypto

import (
	"strconv"
	"time"
)

func GenAPIKey(userID string) (string, string) {
	key := userID + "@" + strconv.FormatInt(time.Now().UnixNano(), 10)
	assetKey := RandomUpperLower(Md5(key))
	secretKey := RandomUpperLower(Sha256(assetKey))
	return assetKey, secretKey
}

const _apiShowSize = 5
const _apiHideStr = "******"

func HideAPIKey(src string) string {
	if len(src) < _apiShowSize {
		return _apiHideStr
	}
	return src[:_apiShowSize] + _apiHideStr
}
