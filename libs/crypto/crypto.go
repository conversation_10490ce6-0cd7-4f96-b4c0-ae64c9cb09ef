package crypto

import (
	"crypto/md5"
	"crypto/sha256"
	"encoding/hex"
	"math/rand"
	"strings"
	"time"

	"spot/libs/convert"
)

func init() {
	rand.Seed(time.Now().Unix())
}

func Sha256(s string) string {
	h := sha256.New()
	h.Write(convert.Str2Bytes(s))
	return hex.EncodeToString(h.Sum(nil))
}

func Md5(s string) string {
	h := md5.New()
	h.Write(convert.Str2Bytes(s))
	return hex.EncodeToString(h.Sum(nil))
}

func RandomUpperLower(str string) string {
	idx := rand.Perm(len(str))
	var dest strings.Builder
	for i := range str {
		if idx[i]&1 == 1 {
			dest.WriteString(strings.ToUpper(string(str[i])))
		} else {
			dest.WriteString(strings.ToLower(string(str[i])))
		}
	}
	return dest.String()
}
