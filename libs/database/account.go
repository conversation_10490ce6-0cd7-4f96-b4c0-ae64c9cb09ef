package database

import (
	"github.com/jmoiron/sqlx"
	"github.com/shopspring/decimal"
)

// UpdateUserAccountByAccountId 修改交易账户余额
func UpdateUserAccountByAccountId(tx *sqlx.Tx, acountId int, increBalance, increAvailable, lockAmount, haveProfit float64) (err error) {
	str := "update tb_user_account set balance=balance+?,available=available+?,lock_amount=lock_amount+?,total_profit=total_profit+? where account_id=?"
	_, err = tx.Exec(str, increBalance, increAvailable, lockAmount, haveProfit, acountId)
	return
}

// UpdateUserAccountForProfit 更新交易账户余额和总资产
func UpdateUserAccountForProfit(tx *sqlx.Tx, accountId int, newBalance, newAvailable, diff, totalProfit, blowingFee decimal.Decimal) (err error) {
	//log.Infof("合约平仓：blowingFee；%v",blowingFee.String())
	str := "update tb_user_account set balance=?,available=?,diff=?,total_profit=?,blowing_fee=? where account_id=?"
	_, err = tx.Exec(str, newBalance, newAvailable, diff, totalProfit, blowingFee, accountId)
	return
}
