package database

import (
	"database/sql"
	"github.com/jmoiron/sqlx"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"spot/libs/log"
	"spot/libs/nums"
	"spot/libs/proto"
	"spot/libs/utils"
)

const _userAccountbase = "SELECT account_id, user_id, currency_name, balance, available, total_profit, platform_id, lock_amount FROM tb_user_account "

func InsertUserAccount(tx *sqlx.Tx, account *proto.Account) (accountId int64, err error) {
	str := "INSERT INTO tb_user_account (user_id, currency_name, balance, available, total_profit, platform_id, lock_amount) VALUES (?,?,?,?,?,?,?)"
	var result sql.Result
	if tx != nil {
		result, err = tx.Exec(str, account.UserId, account.CurrencyName, account.Balance, account.Available, account.TotalProfit, account.PlatformID, account.LockAmount)
	} else {
		result, err = DefaultDB().Exec(str, account.UserId, account.CurrencyName, account.Balance, account.Available, account.TotalProfit, account.PlatformID, account.LockAmount)
	}
	if err != nil {
		return
	}
	accountId, err = result.LastInsertId()
	return
}
func GetUserAccountByCurrencyNameWithLock(tx *sqlx.Tx, userId int64, currencyName string) (account *proto.Account, err error) {
	ac := new(proto.Account)
	str := _userAccountbase + " where user_id=? and currency_name=? for update"
	if tx != nil {
		err = tx.Get(ac, str, userId, currencyName)
	} else {
		err = DefaultDB().Get(ac, str, userId, currencyName)
	}
	if err != nil {
		return nil, err
	}
	account = ac
	return
}

func AccountExist(uid int64, coinID int) bool {
	var count int8
	err := DefaultDB().Get(&count, "SELECT COUNT(1) FROM tb_user_account WHERE user_id = ? AND currency_id = ?", uid, coinID)
	if err != nil {
		log.Error("AccountExist db error", zap.Int64("uid", uid), zap.Int("coinID", coinID), zap.Error(err))
		return false
	}
	return count != 0
}

const _getAssetDetail = "SELECT coin_id, coin_name, balance, position_margin,platform_id FROM tb_user_wallet WHERE user_id=? AND coin_id=?"

func GetAssetDetail(userID int64, coinID int) (*proto.ApiAsset, error) {
	asset := new(proto.ApiAsset)
	err := DefaultDB().Get(&asset, _getAssetDetail, userID, coinID)
	if err != nil {
		log.Error("GetAssetDetail db error", zap.Int64("userID", userID), zap.Int("coinID", coinID), zap.Error(err))
	}
	return asset, err
}

//获取交易账户资产
func GetTradeAccount(tx *sqlx.Tx, userId int64, coinName string) (account *proto.Account, err error) {
	ac := new(proto.Account)
	str := utils.StrBuilder(_userAccountbase, "where user_id=? and currency_name=?")
	if tx != nil {
		err = tx.Get(ac, str, userId, coinName)
	} else {
		err = DefaultDB().Get(ac, str, userId, coinName)
	}
	if err != nil {
		return nil, err
	}
	account = ac
	return
}

//获取交易账户资产
func ListTradeAccount(tx *sqlx.Tx, coinName string, page, size int) (list []proto.Account, err error) {
	str := utils.StrBuilder(_userAccountbase, "where currency_name=? limit ?,?")
	if tx != nil {
		err = tx.Select(&list, str, coinName, page*size, size)
	} else {
		err = DefaultDB().Select(&list, str, coinName, page*size, size)
	}
	if err != nil {
		return nil, err
	}
	return
}

//获取交易账户资产
func ListTradeAccountForPage(tx *sqlx.Tx, page, size int) (list []proto.Account, err error) {
	str := utils.StrBuilder(_userAccountbase, " limit ?,?")
	if tx != nil {
		err = tx.Select(&list, str, page*size, size)
	} else {
		err = DefaultDB().Select(&list, str, page*size, size)
	}
	if err != nil {
		return nil, err
	}
	return
}

//插入交易账户历史记录
func InsertUserAccountHistory(tx *sqlx.Tx, account *proto.AccountHistory) (err error) {
	if account.RawAmount.Equal(decimal.Zero) {
		account.RawAmount = nums.NewFromFloat(account.Amount)
	}
	str := "INSERT INTO tb_user_account_history (id, order_id, platform_id, user_id, currency_name, balance, available, type, amount, created_time, ip_address, imei, order_client, momo, raw_amount, extra_id, trade_id) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)"
	if tx != nil {
		_, err = tx.Exec(str, account.Id, account.OrderId, account.PlatformID, account.UserId, account.CurrencyName, account.Balance, account.Available, account.Type, account.Amount, account.CreatedTime, account.IpAddress, account.Imei, account.OrderClient, account.Momo, account.RawAmount, account.ExtraID, account.TradeId)
		return
	}
	_, err = DefaultDB().Exec(str, account.Id, account.OrderId, account.PlatformID, account.UserId, account.CurrencyName, account.Balance, account.Available, account.Type, account.Amount, account.CreatedTime, account.IpAddress, account.Imei, account.OrderClient, account.Momo, account.RawAmount, account.ExtraID, account.TradeId)
	return
}
