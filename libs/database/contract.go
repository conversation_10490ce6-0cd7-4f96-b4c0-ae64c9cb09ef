package database

import (
	"context"

	"github.com/jmoiron/sqlx"
	"go.uber.org/zap"
	"spot/libs/log"
	"spot/libs/proto"
	"spot/libs/utils"
)

const symbolsBase = "SELECT contract_id, contract_code, contract_icon, price_step, digit, volume_digit, min_order_volume, max_order_volume, trade_enable, coin_name, base_coin_name, fee_taker, fee_maker, order_by, delisted, recommend, is_show, label_max_order_volume, label_fee, label_slippage, max_side_amount, limit_buy_price, limit_sell_price, lock_float_factor, redundancy_factor, price_protect_factor, ioc_limit, ioc_buy_limit, ioc_sell_limit, max_ioc_factor, is_maintenance, min_order_money, max_order_money, market_source,is_third_market,bucketing_source FROM tb_contract "

//获取所有在线交易对
func GetOnlineSymbols() ([]proto.Contract, error) {
	return GetOnlineContractsTx(nil)
}

func GetOnlineContractsTx(tx *sqlx.Tx) (reply []proto.Contract, err error) {
	if tx != nil {
		err = tx.Select(&reply, utils.StrBuilder(symbolsBase, " where delisted=? order by order_by desc"), 0)
	} else {
		err = DefaultDB().Select(&reply, symbolsBase+" where delisted=? order by order_by desc", 0)
	}
	if err != nil {
		return nil, err
	}
	return
}

//获取所有支持交易对
func GetSupportContract() ([]proto.Contract, error) {
	return GetSupportContractsTx(nil)
}

func GetSupportContractsTx(tx *sqlx.Tx) (reply []proto.Contract, err error) {
	str := utils.StrBuilder(symbolsBase, " order by order_by desc")
	if tx != nil {
		err = tx.Select(&reply, str)
	} else {
		err = DefaultDB().Select(&reply, str)
	}
	if err != nil {
		log.Error("GetSuppportSymbols db error", zap.Error(err))
		return nil, err
	}
	return
}

//获取指定id交易对信息
func GetContractById(symbolID int) (*proto.Contract, error) {
	return GetContractByIdTx(nil, symbolID)
}

func GetContractByIdTx(tx *sqlx.Tx, symbolId int) (reply *proto.Contract, err error) {
	reply = new(proto.Contract)
	str := utils.StrBuilder(symbolsBase, " where contract_id=?")
	if tx != nil {
		err = tx.Get(reply, str, symbolId)
	} else {
		err = DefaultDB().Get(reply, str, symbolId)
	}
	if err != nil {
		log.Error("GetContractById db error", zap.Error(err))
	}
	return
}

//获取指定code交易对信息
func GetContractByCode(contractCode string) (*proto.Contract, error) {
	return GetContractByCodeTx(nil, contractCode)
}

func GetContractByCodeTx(tx *sqlx.Tx, contractCode string) (reply *proto.Contract, err error) {
	reply = new(proto.Contract)
	str := utils.StrBuilder(symbolsBase, " where contract_code=?")
	if tx != nil {
		err = tx.Get(reply, str, contractCode)
	} else {
		err = DefaultDB().Get(reply, str, contractCode)
	}
	if err != nil {
		log.Errorf("GetContractById db error,code:%v,err:%v", contractCode, err)
		return nil, err
	}
	return
}

func ListContractDepthConfig() (list []proto.ContractDepth, err error) {
	str := "SELECT contract_id, contract_code, bucket_base, bucket_pin_factor, bucket_price_update, bucket_threshold, bucket_amount, bucket_max_amount, pre_bucket_base, max_bucket_base, base_factor, bucket_base_duration, min_24h_amount, max_24h_amount, index_invalid_value, reason_range, departure_factor, index_diff_switch, index_ref_value, index_exception_adjust, exception_check_factor, spot_exception_time, spot_exception_threshold, trade_diff_warn, depth_switch, is_depth_switch, force_hop, large_trade_factor, buy_factor, sell_factor, buy_price_factor, sell_price_factor, discount_premium_rate, level_merge_threshold, extra_merge_hop, buy_level_max_limit, sell_level_max_limit, flow_compensation_start, flow_compensation_end, flow_compensation_amount, strategy_factor, depth_source, depth_exception_threshold, depth_price_diff_threshold, buy_protect_factor, sell_protect_factor, protect_amount, is_depth_backup_on, depth_exception_time, min_depth_volume, max_depth_volume, initial_depth_levels, data_valid_seconds, market_trade_max_level, rc_deal_max_volume FROM tb_contract_depth "
	err = DefaultDB().Select(&list, str)
	return
}

func UpdateContractDepthForResetBucketBaseDuration(code string) (err error) {
	str := "update tb_contract_depth set bucket_base_duration=0 where contract_code=?"
	_, err = DefaultDB().Exec(str, code)
	return
}

func UpdateDepthPriceSwitch(code string, s int) (err error) {
	str := "update tb_contract_depth set depth_switch=? where contract_code=?"
	_, err = DefaultDB().Exec(str, s, code)
	return
}

var _getCoinMarketConf = "SELECT coin_name, min_amount, delisted, order_by FROM tb_coin_area"

func GetCoinMarketConf(ctx context.Context) (map[string]proto.CoinMarketConf, error) {
	rows, err := DefaultDB().QueryxContext(ctx, _getCoinMarketConf)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var mc proto.CoinMarketConf
	dict := make(map[string]proto.CoinMarketConf)
	for rows.Next() {
		err = rows.StructScan(&mc)
		if err != nil {
			return nil, err
		}
		dict[mc.CoinName] = mc
	}
	return dict, nil
}
