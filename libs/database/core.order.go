package database

import (
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/jmoiron/sqlx"
	"go.uber.org/zap"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
	"spot/pkg/core/model"
)

//插入条件单
func InsertPlanOrder(tx *sqlx.Tx, o *proto.ConditionOrder) (err error) {
	str := "INSERT INTO tb_plan_order (plan_order_id, user_id, contract_code, coin_name, market_name, platform_id, side, amount, money, `status`, create_time, order_time, cancel_time, ip_address, imei, order_client, entrust_type, entrust_price, trigger_price, entrust_strategy, `condition`, `mode`) VALUES (:plan_order_id,:user_id,:contract_code, :coin_name, :market_name,:platform_id,:side,:amount,:money,:status,:create_time,:order_time,:cancel_time,:ip_address, :imei, :order_client, :entrust_type, :entrust_price, :trigger_price, :entrust_strategy, :condition, :mode)"
	if tx != nil {
		_, err = tx.NamedExec(str, o)
		return
	}
	_, err = DefaultDB().NamedExec(str, o)
	return
}

//修改计划单触发
func UpdatePlanOrderTrigger(tx *sqlx.Tx, orderId int64, triggleSuccess bool) (err error) {
	var status int
	if triggleSuccess {
		status = 2
	} else {
		status = 3
	}
	str := "update tb_plan_order set status=?,order_time=? where plan_order_id=?"
	if tx != nil {
		_, err = tx.Exec(str, status, time.Now(), orderId)
		return
	}
	_, err = DefaultDB().Exec(str, status, time.Now(), orderId)
	return
}

const _cancelConditionOrderBasic = "SELECT plan_order_id, user_id, contract_code, platform_id, side, amount, money, `status`, create_time, IFNULL(order_time, create_time) 'order_time', IFNULL(cancel_time, create_time) 'cancel_time', ip_address, imei, order_client, entrust_type, entrust_price, trigger_price, entrust_strategy, `condition`, `mode` FROM tb_plan_order "

func CancelConditionOrder(userID, orderID int64, contractCode string) (list []proto.ConditionOrder, err error) {
	var buf strings.Builder
	buf.WriteString(_cancelConditionOrderBasic)
	buf.WriteString(" WHERE ")
	if orderID != 0 {
		buf.WriteString(fmt.Sprintf(" plan_order_id=%d AND", orderID))
	}
	if userID != 0 {
		buf.WriteString(fmt.Sprintf(" user_id=%d AND", userID))
	}
	if len(contractCode) > 0 {
		buf.WriteString(fmt.Sprintf(" contract_code='%s' AND", contractCode))
	}
	buf.WriteString(fmt.Sprintf(" `status`=%d FOR UPDATE", define.OrderConditionNotTrigger))

	str := buf.String()
	log.Info("CancelConditionOrder", zap.String("sql", str))

	tx, err := Begin()
	if err != nil {
		log.Error("CancelConditionOrder open transaction failed",
			zap.Int64("userID", userID),
			zap.Int64("orderID", orderID),
			zap.Error(err))
		err = define.ErrMsgBusy
		return
	}
	defer CommitTx(tx, &err, orderID, nil)

	list, err = GetConditionOrderAndLock(tx, str)
	if err != nil {
		log.Error("CancelConditionOrder GetConditionOrderAndLock failed",
			zap.Int64("userID", userID),
			zap.Int64("orderID", orderID),
			zap.Error(err))
		err = define.ErrMsgBusy
		return
	}

	for i := range list {
		list[i].CancelTime = time.Now()
		list[i].Status = define.OrderCondCancel

		err = UpdateConditionOrderState(tx, list[i].PlanOrderId, define.OrderConditionNotTrigger, define.OrderCondCancel, list[i].CancelTime)
		if err != nil {
			log.Error("CancelConditionOrder UpdateConditionOrderState failed",
				zap.Int64("userID", userID),
				zap.Int64("orderID", orderID),
				zap.Error(err))
			err = define.ErrMsgBusy
			return
		}
	}

	return
}

// 获取条件单列表
func GetConditionOrderAndLock(tx *sqlx.Tx, sqlStr string) ([]proto.ConditionOrder, error) {
	var list []proto.ConditionOrder
	err := tx.Select(&list, sqlStr)
	return list, err
}

const _conditionOrderForMeetBasic = "SELECT plan_order_id, user_id, contract_code, platform_id, side, amount, money, status, create_time, order_time, cancel_time, ip_address, imei, order_client, entrust_type, entrust_price, trigger_price, entrust_strategy, `condition`, `mode` FROM tb_plan_order WHERE 1=1  "

//func GetConditionOrderForMeet(contractCode string, price float64) (list []proto.ConditionOrder, err error) {
//	var buf strings.Builder
//	buf.WriteString(_conditionOrderForMeetBasic)
//	buf.WriteString(fmt.Sprintf(" AND contract_code='%s'", contractCode))
//	buf.WriteString(fmt.Sprintf(" AND `status`=%d", define.OrderConditionNotTrigger))
//	buf.WriteString(fmt.Sprintf(" AND IF(`condition`=%d, trigger_price<=%f, trigger_price>=%f) FOR UPDATE", define.OrderConditionGreaterOrEqual, price, price))
//
//	str := buf.String()
//	//log.Info("GetConditionOrderForMeet", zap.String("sql", str))
//
//	tx, err := Begin()
//	if err != nil {
//		log.Error("GetConditionOrderForMeet open transaction failed",
//			zap.String("contract", contractCode),
//			zap.Float64("price", price),
//			zap.Error(err))
//		return
//	}
//	defer CommitTx(tx, &err, 0, nil)
//
//	list, err = GetConditionOrderAndLock(tx, str)
//	if err != nil {
//		log.Error("GetConditionOrderForMeet GetConditionOrderAndLock failed",
//			zap.String("contract", contractCode),
//			zap.Float64("price", price),
//			zap.Error(err))
//		return
//	}
//
//	now := time.Now()
//	userMP := make(map[int64]map[string]*model.PlanContactLeverCount)
//
//	for i := range list {
//		if _, ok := userMP[list[i].UserId]; !ok {
//			userMP[list[i].UserId] = make(map[string]*model.PlanContactLeverCount)
//			userMP[list[i].UserId][list[i].ContractCode] = new(model.PlanContactLeverCount)
//		}
//		userMP[list[i].UserId][list[i].ContractCode].Lever = list[i].Lever
//		userMP[list[i].UserId][list[i].ContractCode].Count = userMP[list[i].UserId][list[i].ContractCode].Count + 1
//
//		err = UpdateConditionOrderState(tx, list[i].PlanOrderId, define.OrderConditionNotTrigger, define.OrderCondRunning, now)
//		if err != nil {
//			log.Error("GetConditionOrderForMeet UpdateConditionOrderState failed",
//				zap.String("contract", contractCode),
//				zap.Float64("price", price),
//				zap.Error(err))
//			return
//		}
//		list[i].OrderTime = now
//	}
//
//	return
//}

func GetConditionOrderForMeetV2(contractCode string, ids string) (list []proto.ConditionOrder, err error) {
	var buf strings.Builder
	buf.WriteString(_conditionOrderForMeetBasic)
	buf.WriteString(fmt.Sprintf(" AND plan_order_id IN(%s)", ids))
	buf.WriteString(fmt.Sprintf(" AND `status`=%d", define.OrderConditionNotTrigger))

	str := buf.String()
	//log.Info("GetConditionOrderForMeet", zap.String("sql", str))

	tx, err := Begin()
	if err != nil {
		log.Error("GetConditionOrderForMeet open transaction failed",
			zap.String("contract", contractCode),
			zap.Error(err))
		return
	}
	defer CommitTx(tx, &err, 0, nil)

	list, err = GetConditionOrderAndLock(tx, str)
	if err != nil {
		log.Error("GetConditionOrderForMeet GetConditionOrderAndLock failed",
			zap.String("contract", contractCode),
			zap.Error(err))
		return
	}

	return
}

// 获取未触发计划单总张数
func GetPlanOrderCount(tx *sqlx.Tx, userID int64) (int, error) {
	var count int
	err := tx.Get(&count, "SELECT COUNT(1) FROM tb_plan_order WHERE user_id=? AND status=?", userID, define.OrderConditionNotTrigger)
	return count, err
}

func GetAllPlanOrderID(code string, condition int) ([]model.PlanOrderInitData, error) {
	var list []model.PlanOrderInitData
	err := DefaultDB().Select(&list, "SELECT plan_order_id, trigger_price,user_id FROM tb_plan_order WHERE contract_code = ? AND `status` = ? AND `condition` = ?", code, define.OrderConditionNotTrigger, condition)
	return list, err
}

func GetConditionOrderByOrderId(tx *sqlx.Tx, ids int64) (order *proto.ConditionOrder, err error) {
	var buf strings.Builder
	buf.WriteString(_conditionOrderForMeetBasic)
	buf.WriteString(fmt.Sprintf(" AND plan_order_id =%v", ids))
	buf.WriteString(fmt.Sprintf(" AND `status`=%d", define.OrderConditionNotTrigger))

	o := new(proto.ConditionOrder)
	if tx != nil {
		err = tx.Get(o, buf.String())
	} else {
		err = DefaultDB().Get(o, buf.String())
	}
	if err != nil {
		if err != sql.ErrNoRows {
			log.Errorf("GetConditionOrderByOrderId fail,%v,id；%v", err, ids)
			return nil, err
		}
		return nil, nil
	}
	order = o
	return
}

const _updateConditionOrderState = "UPDATE tb_plan_order SET `status`=?, order_time=IF(?=? OR ?=?,?,NULL), cancel_time=IF(?=?,?,NULL) WHERE plan_order_id=? AND `status`=?"

// 更新条件单状态
func UpdateConditionOrderState(tx *sqlx.Tx, id int64, origin, newState int, tm time.Time) error {
	var err error
	if tx != nil {
		_, err = tx.Exec(_updateConditionOrderState, newState, newState, define.OrderCondHadTrigger, newState, define.OrderCondFailed, tm, newState, define.OrderCondCancel, tm, id, origin)
	} else {
		_, err = DefaultDB().Exec(_updateConditionOrderState, newState, newState, define.OrderCondHadTrigger, newState, define.OrderCondFailed, tm, newState, define.OrderCondCancel, tm, id, origin)
	}
	return err
}
