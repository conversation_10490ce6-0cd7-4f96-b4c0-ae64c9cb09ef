package database

import (
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/jmoiron/sqlx"
	"go.uber.org/zap"
	"spot/libs/conf"
	"spot/libs/define"
	"spot/libs/log"
)

// sqlx 文档 http://jmoiron.github.io/sqlx/

var dbConn = new(Connection)

type Connection struct {
	DB    *sqlx.DB
	TxDB  *sqlx.DB
	CrmDB *sqlx.DB
	IW    *IdWorker
}

func InitDefaultDB() {
	dbConn.DB = connectDB(conf.DefaultDB())
	dbConn.TxDB = connectDB(conf.DefaultDB())
	dbConn.CrmDB = connectDB(conf.CrmDbDSN())
	dbConn.IW, _ = NewIdWorker(conf.WorkerID())
}

func InitTest(address string, workId int64) {
	dbConn.DB = connectDB(address)
	dbConn.TxDB = connectDB(address)
	dbConn.IW, _ = NewIdWorker(workId)
}

func InitDefaultDataBase(dbName string) {
	db, err := sqlx.Open("mysql", dbName)
	if err != nil {
		log.Fatal("InitDefaultDB db connect failed", zap.Error(err))
	}
	db.SetMaxOpenConns(10)
	db.SetMaxIdleConns(3)
	db.SetConnMaxLifetime(time.Second * 15)
	go dbPing(db, dbName)
	dbConn.DB = db
	dbConn.IW, _ = NewIdWorker(2)
}

func connectDB(dsn string) *sqlx.DB {
	if len(dsn) == 0 {
		return nil
	}
	db, err := sqlx.Open("mysql", dsn)
	if err != nil {
		log.Fatal("connectDB db connect failed", zap.Error(err))
	}
	db.SetMaxOpenConns(50)
	db.SetMaxIdleConns(10)
	db.SetConnMaxLifetime(time.Second * 15)
	go dbPing(db, dsn)
	return db
}

func dbPing(db *sqlx.DB, address string) {
	output := true
	for range time.Tick(time.Second * 5) {
		err := db.Ping()
		if err != nil {
			zap.Error(err)
			log.Fatal("db ping failed", zap.String("address", address), zap.Error(err))
		} else {
			if output {
				log.Info("连接mysql成功", zap.String("address", address))
				output = false
			}
		}
	}
}

// 提交或回滚事务
func CommitTx(tx *sqlx.Tx, err *error, reqID int64, f func(int64, error)) {
	if *err != nil {
		txErr := tx.Rollback()
		if txErr != nil {
			log.Errorf("tx rollback error, reqID:%d, err:%s", reqID, txErr.Error())
		}
	} else {
		*err = tx.Commit()
		if *err != nil {
			*err = define.ErrMsgBusy
			log.Errorf("tx Commit error, reqID:%d, err:%v", reqID, *err)
		}
	}
	if f != nil {
		f(reqID, *err)
	}
}

func CrmDB() *sqlx.DB {
	return dbConn.CrmDB
}

func DefaultDB() *sqlx.DB {
	return dbConn.DB
}

func Begin() (*sqlx.Tx, error) {
	return dbConn.TxDB.Beginx()
}

func NextID() int64 {
Retry:
	id, err := dbConn.IW.NextId()
	if err != nil {
		goto Retry
	}
	return id
}
