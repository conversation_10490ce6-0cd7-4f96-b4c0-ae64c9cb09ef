package database

import (
	"github.com/jmoiron/sqlx"
	"spot/libs/define"
)

func UpdateOrderForThirdOrder(tx *sqlx.Tx, orderId, thirdOrderId int64) (err error) {
	str := "update tb_order set third_order_id=?,third_mark=1,state=? where id=?"
	if tx != nil {
		_, err = tx.Exec(str, thirdOrderId, define.OrderStatusDefault, orderId)
	} else {
		_, err = DefaultDB().Exec(str, thirdOrderId, define.OrderStatusDefault, orderId)
	}
	return
}

func UpdateThirdMark(tx *sqlx.Tx, orderId int64) (err error) {
	str := "update tb_order set third_mark=2 where id=?"
	if tx != nil {
		_, err = tx.Exec(str, orderId)
	} else {
		_, err = DefaultDB().Exec(str, orderId)
	}
	return
}
