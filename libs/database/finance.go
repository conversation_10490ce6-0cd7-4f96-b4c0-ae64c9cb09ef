package database

import (
	"github.com/jmoiron/sqlx"
	"math"
	"spot/libs/define"
	"spot/libs/proto"
)

//获取资产账户币种钱包
func GetFsAssetWallet(currency string, platformId int) (m proto.FsWallet, err error) {
	str := "select  coin_name, `type`, amount, amount_lock, mix_charge, mix_with, ifnull(address, '') address,platform_id from tb_finance_asset where coin_name=? and `type`=1 and platform_id=?"
	err = DefaultDB().Get(&m, str, currency, platformId)
	return
}

//获取法币平台账户钱包
func GetC2cWallet(currency string, platformId int) (m proto.FsWallet, err error) {
	str := "select  coin_name, `type`, amount, amount_lock, mix_charge, mix_with, ifnull(address, '') address,platform_id from tb_finance_asset where coin_name=? and `type`=3 and platform_id=?"
	err = DefaultDB().Get(&m, str, currency, platformId)
	return
}

//获取指定币种地址的账户信息
func GetFsWalletByCoinName(address, name string) (*proto.FsWallet, error) {
	m := new(proto.FsWallet)
	str := "select fs.coin_name,fs.`type`, fs.amount, fs.amount_lock, ifnull(fs.address, '') address,platform_id from tb_finance_asset fs left join tb_coin_wallet_config cs on cs.coin_name=fs.coin_name where cs.wallet_name=? and fs.address=?"
	err := DefaultDB().Get(m, str, name, address)
	return m, err
}

func GetFsWalletWithLock(tx *sqlx.Tx, coinName string, platformId int) (m proto.FsWallet, err error) {
	str := "select  coin_name,`type`, amount, amount_lock, mix_charge, mix_with, fee, ifnull(address, '') address,platform_id from tb_finance_asset where coin_name=? and `type`=1 and platform_id=? for update "
	err = tx.Get(&m, str, coinName, platformId)
	return
}

func GetFsWalletWithLockByWalletType(tx *sqlx.Tx, coinName string, walletType int, platformId int) (m proto.FsWallet, err error) {
	str := "select coin_name,`type`, amount, amount_lock, mix_charge, mix_with, fee, ifnull(address, '') address,platform_id from tb_finance_asset where coin_name=? and `type`=? and platform_id=? for update "
	err = tx.Get(&m, str, coinName, walletType, platformId)
	return
}

func InsertFsWalletWithTx(tx *sqlx.Tx, m proto.FsWallet) (err error) {
	str := "insert ignore into tb_finance_asset( coin_name,`type`, amount, amount_lock, address,platform_id) values (?,?,?,?,?,?)"
	_, err = tx.Exec(str, m.CoinName, m.Type, m.Amount, m.AmountLock, m.Address, m.PlatformID)
	return
}

func InsertFsWallet(m proto.FsWallet) (err error) {
	str := "insert ignore into tb_finance_asset( coin_name,`type`, amount, amount_lock, address,platform_id) values (?,?,?,?,?,?)"
	_, err = DefaultDB().Exec(str, m.CoinName, m.Type, m.Amount, m.AmountLock, m.Address, m.PlatformID)
	return
}

func UpdateFsAssetWalletAddress(platformID int, coinName string, aType int, address string) (err error) {
	str := "update tb_finance_asset set address=? where coin_name=? and `type`=? and platform_id=?"
	_, err = DefaultDB().Exec(str, address, coinName, aType, platformID)
	return
}

func IncrFsWallet(tx *sqlx.Tx, coinName string, amount float64, platformId int) (err error) {
	var capital float64
	if amount < 0 {
		capital = math.Abs(amount)
	}
	str := "update tb_finance_asset set amount=amount+?,asset_award=asset_award+? where coin_name=? and `type`=1 and platform_id=?"
	_, err = tx.Exec(str, amount, capital, coinName, platformId)
	return
}

func IncrFsC2cWallet(tx *sqlx.Tx, coinName string, amount float64, platformId int) (err error) {
	var capital float64
	if amount < 0 {
		capital = math.Abs(amount)
	}
	str := "update tb_finance_asset set amount=amount+?,asset_award=asset_award+? where coin_name=? and `type`=3 and platform_id=?"
	_, err = tx.Exec(str, amount, capital, coinName, platformId)
	return
}

func GetFsCapital(coinName string, platformId int) (m proto.FsWallet, err error) {
	str := "select coin_name, `type`, amount, amount_lock, address, mix_charge, mix_with from tb_finance_asset where coin_name=? and `type`=2 and platform_id=?"
	err = DefaultDB().Get(&m, str, coinName, platformId)
	return
}

func GetFsCapitalWithLock(tx *sqlx.Tx, coinName string, platformId int) (m proto.FsWallet, err error) {
	str := "select coin_name,`type`, amount, amount_lock from tb_finance_asset where coin_name=? and `type`=2 and platform_id=? for update "
	err = tx.Get(&m, str, coinName, platformId)
	return
}

func GetFsCapitalWithLockByCoinName(tx *sqlx.Tx, coinName string, platformId int) (m proto.FsWallet, err error) {
	str := "select  coin_name,`type`, amount, amount_lock from tb_finance_asset where coin_name=? and `type`=2 and platform_id=? for update "
	err = tx.Get(&m, str, coinName, platformId)
	return
}

func IncrFsCapital(tx *sqlx.Tx, coinName string, amount float64, platformId int) (err error) {
	var capital float64
	if amount < 0 {
		capital = math.Abs(amount)
	}
	str := "update tb_finance_asset set amount=amount+?,asset_award=asset_award+? where coin_name=? and `type`=2 and platform_id=?"
	_, err = tx.Exec(str, amount, capital, coinName, platformId)
	return
}

func IncrFsCapitalByCoinName(tx *sqlx.Tx, coinName string, amount float64, platformId int) (err error) {
	var capital float64
	if amount < 0 {
		capital = math.Abs(amount)
	}
	str := "update tb_finance_asset set amount=amount+?,asset_award=asset_award+? where coin_name=? and `type`=2 and platform_id=?"
	_, err = tx.Exec(str, amount, capital, coinName, platformId)
	return
}

func InsertFsWalletHistory(tx *sqlx.Tx, r proto.FsWalletHistory) (id int64, err error) {
	str := "insert into tb_finance_asset_history(user_id, coin_name, amount, amount_after, op_type, remark, op_user_name, source_id, create_at,platform_id) VALUES (?,?,?,?,?,?,?,?,now(),?)"
	res, err := tx.Exec(str, r.UserId, r.CoinName, r.Amount, r.AmountAfter, r.OpType, r.Remark, r.OpUserName, r.SourceId, r.PlatformID)
	if err == nil {
		return res.LastInsertId()
	}
	return
}

func UpdateFsWalletHistorySourceID(tx *sqlx.Tx, billID, sourceID int64) (err error) {
	if tx != nil {
		_, err = tx.Exec("UPDATE tb_finance_asset_history SET source_id=? WHERE id=?", sourceID, billID)
	} else {
		_, err = DefaultDB().Exec("UPDATE tb_finance_asset_history SET source_id=? WHERE id=?", sourceID, billID)
	}
	return
}

func InsertFsCapitalHistory(tx *sqlx.Tx, r proto.FsWalletHistory) (err error) {
	str := "insert into tb_finance_capital_history(user_id, coin_name, amount, amount_after, op_type, remark, op_user_name, source_id, create_at,platform_id,is_maker) VALUES (?,?,?,?,?,?,?,?,now(),?,?)"
	_, err = tx.Exec(str, r.UserId, r.CoinName, r.Amount, r.AmountAfter, r.OpType, r.Remark, r.OpUserName, r.SourceId, r.PlatformID, r.IsMaker)
	return
}

func ExistFsWalletHistory(tx *sqlx.Tx, sourceID int64, opType int) bool {
	str := "select count(1) from tb_finance_asset_history where source_id=? and op_type=?"
	var count int
	_ = tx.Get(&count, str, sourceID, opType)
	return count != 0
}

func ExistFsC2cWalletHistory(tx *sqlx.Tx, sourceID int64, opType int) bool {
	str := "select count(1) from tb_finance_c2c_history where source_id=? and op_type=?"
	var count int
	_ = tx.Get(&count, str, sourceID, opType)
	return count != 0
}

//获取兑币账户钱包
func GetFsExchangeWallet(currency string, platformId int) (m proto.FsWallet, err error) {
	str := "select coin_name, `type`, amount, amount_lock, mix_charge, mix_with, ifnull(address, '') address,platform_id from tb_finance_asset where coin_name=? and `type`=? and platform_id=?"
	err = DefaultDB().Get(&m, str, currency, define.WalletTypeExchange, platformId)
	return
}

func GetFsExchangeAccountWithLock(tx *sqlx.Tx, coinName int, platformId int) (m proto.FsWallet, err error) {
	str := "select coin_name,`type`, amount, amount_lock, mix_charge, mix_with, fee, ifnull(address, '') address,platform_id from tb_finance_asset where coin_name=? and `type`=? and platform_id=? for update "
	err = tx.Get(&m, str, coinName, define.WalletTypeExchange, platformId)
	return
}

func InsertFsExchangeHistory(tx *sqlx.Tx, r proto.FsWalletHistory) (id int64, err error) {
	str := "insert into tb_finance_exchange_history(user_id, coin_name, amount, amount_after, op_type, remark, op_user_name, source_id, create_at,platform_id) VALUES (?,?,?,?,?,?,?,?,now(),?)"
	res, err := tx.Exec(str, r.UserId, r.CoinName, r.Amount, r.AmountAfter, r.OpType, r.Remark, r.OpUserName, r.SourceId, r.PlatformID)
	if err == nil {
		return res.LastInsertId()
	}
	return
}

//获取充提账户钱包
func GetFsChargeWallet(currency string, platformId int) (m proto.FsWallet, err error) {
	str := "select  coin_name, `type`, amount, amount_lock, mix_charge, mix_with, ifnull(address, '') address,platform_id from tb_finance_asset where coin_name=? and `type`=? and platform_id=?"
	err = DefaultDB().Get(&m, str, currency, define.WalletTypeCharge, platformId)
	return
}

func GetFsChargeAccountWithLock(tx *sqlx.Tx, currency string, platformId int) (m proto.FsWallet, err error) {
	str := "select  coin_name,`type`, amount, amount_lock, mix_charge, mix_with, fee, ifnull(address, '') address,platform_id from tb_finance_asset where coin_name=? and `type`=? and platform_id=? for update "
	err = tx.Get(&m, str, currency, define.WalletTypeCharge, platformId)
	return
}
