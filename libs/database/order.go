/*
@Time : 3/9/20 1:44 下午
<AUTHOR> mocha
@File : order
*/
package database

import (
	"database/sql"
	"errors"
	"github.com/jmoiron/sqlx"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
	"time"
)

// 只取取近期交易的50条记录
func GetRecentOrders(contractCode string) ([]proto.ApiRecentOrder, error) {
	str := "select match_id as 'trade_id', contract_code, price, volume, trade_amount AS 'trade_value', side, unix_timestamp(trade_time) order_time from tb_trade where contract_code = ? AND identity = ? order by trade_time desc limit ?"

	list := make([]proto.ApiRecentOrder, 0, define.ApiRecentTradeMaxCount)
	err := DefaultDB().Select(&list, str, contractCode, define.Taker, define.ApiRecentTradeMaxCount)
	if err != nil {
		log.Error("GetRecentOrders db error", zap.String("contractCode", contractCode), zap.Error(err))
	}
	return list, err
}

// 获取持仓信息 账户类型/未结束订单杠杆倍数/已持仓数量
func GetUserOrderStateForPlace(tx *sqlx.Tx, userID int64, contractCode string) (*proto.UserOrderState, error) {
	state := new(proto.UserOrderState)
	// 获取未成交订单信息
	row := tx.QueryRow("SELECT IFNULL( SUM( IF ( side = 'B', volume - trade_volume, 0 )), 0 ) AS 'unsold_buy',IFNULL( SUM( IF ( side = 'S', volume - trade_volume, 0 )), 0 ) AS 'unsold_sell',IFNULL( account_type, 0) AS 'account_type',IFNULL( lever, 0) AS 'lever' FROM tb_order WHERE user_id = ? AND contract_code = ? AND state IN ( 0, 2 )", userID, contractCode)
	err := row.Scan(&state.UnsoldBuy, &state.UnsoldSell, &state.AccountType, &state.Lever)
	if err != nil {
		return state, err
	}

	// 获取持仓订单信息
	row = tx.QueryRow("SELECT IFNULL( SUM( IF ( side = 'B', volume, 0 )), 0 ) AS 'hold_buy',IFNULL( SUM( IF ( side = 'S', volume, 0 )), 0 ) AS 'hold_sell',IFNULL( IF ( ? != 0, ?, account_type ), 0 ) AS 'account_type',IFNULL( IF ( ? != 0, ?, lever ), 0 ) AS 'lever' FROM tb_position WHERE user_id = ? AND contract_code = ?", state.AccountType, state.AccountType, state.Lever, state.Lever, userID, contractCode)
	err = row.Scan(&state.HoldBuy, &state.HoldSell, &state.AccountType, &state.Lever)
	return state, err
}

func CountUserClientId(tx *sqlx.Tx, userId, clientOrderId int64) (count int, err error) {

	str := "SELECT count(1) FROM tb_order where user_id=? and client_order_id=?"
	log.Info(str, zap.Int64("id", userId), zap.Int64("oid", clientOrderId))
	if tx != nil {
		err = tx.Get(&count, str, userId, clientOrderId)
	} else {
		err = DefaultDB().Get(&count, str, userId, clientOrderId)
	}
	return
}

func InsertEntrustOrder(tx *sqlx.Tx, order *proto.EntrustOrder) (err error) {
	str := "INSERT INTO tb_order (id, platform_id, user_id, coin_name, base_coin_name, contract_code, entrust_type, entrust_strategy, mode, order_type, side, price, volume,money, asset_lock, trade_volume, trade_price, cost_fee, cost_asset, state, create_time, update_time, mark, extra_id, imei, ip_address, order_client, last_match_price, user_type, client_order_id, plan_trigger_price, expect_price, third_order_id, third_mark, market_source, last_third_trade_id) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)"
	data := []interface{}{order.ID, order.PlatformID, order.UserID, order.CoinName, order.BaseCoinName, order.ContractCode, order.EntrustType, order.EntrustStrategy, order.Mode, order.OrderType, order.Side, order.Price, order.Volume, order.Money, order.AssetLock, order.TradeVolume, order.TradePrice, order.TradeFee, order.TradeMoney, order.State, order.CreateTime, order.UpdateTime, order.Mark, order.ExtraID, order.Imei, order.IpAddress, order.OrderClient, order.LastMatchPrice, define.IdentifierUser, order.ClientOrderId, order.PlanTriggerPrice, order.ExpectPrice, order.ThirdOrderId, order.ThirdMark, order.MarketSource, order.LastThirdTradeId}
	if tx != nil {
		_, err = tx.Exec(str, data...)
	} else {
		_, err = DefaultDB().Exec(str, data...)
	}
	return
}

const _baseEntrustOrder = "SELECT id, platform_id, user_id, coin_name, base_coin_name, contract_code, entrust_type, entrust_strategy, mode, order_type, side, price, volume, money,asset_lock, trade_volume, trade_price, cost_fee, cost_asset, state, create_time, update_time, mark, extra_id, imei, ip_address, order_client, last_match_price, user_type, client_order_id, plan_trigger_price, expect_price, third_order_id, third_mark, market_source, last_third_trade_id FROM tb_order where 1=1 "

func GetUserEntrustOrderCountForPlanOrder(tx *sqlx.Tx, extra_id, userId int64) (count int64, err error) {
	str := "SELECT count(*) from tb_order where extra_id=? and user_id=?"
	if tx != nil {
		err = tx.Get(&count, str, extra_id, userId)
	} else {
		err = DefaultDB().Get(&count, str, extra_id, userId)
	}
	return
}

func GetUserEntrustOrders(tx *sqlx.Tx, userId int64) (list []proto.EntrustOrder, err error) {
	str := _baseEntrustOrder + " and  user_id=?"
	str += " order by update_time desc limit 100"
	data := []interface{}{userId}
	if tx != nil {
		err = tx.Select(&list, str, data...)
	} else {
		err = DefaultDB().Select(&list, str, data...)
	}
	return
}

//根据订单类似及状态查询委托订单列表
func ListEntrustUnFinishOrdersByCustom(tx *sqlx.Tx, userId int64, code string, orderType []int, state []int) (list []proto.EntrustOrder, err error) {
	var data []interface{}
	str := _baseEntrustOrder + " and user_id=? "
	data = append(data, userId)
	if code != "" {
		str += "and contract_code=?"
		data = append(data, code)
	}
	if len(orderType) == 1 {
		str += "and orderType=?"
		data = append(data, orderType[0])
	}
	if len(orderType) > 1 {
		var strPart string
		var dataPart []interface{}
		strPart, dataPart, err = sqlx.In(" and orderType in (?)", orderType)
		if err != nil {
			return
		}
		str += strPart
		data = append(data, dataPart...)
	}

	if len(state) == 1 {
		str += "and state=?"
		data = append(data, state[0])
	}
	if len(orderType) > 1 {
		var strPart string
		var dataPart []interface{}
		strPart, dataPart, err = sqlx.In(" and state in (?)", state)
		if err != nil {
			return
		}
		str += strPart
		data = append(data, dataPart...)
	}

	str += " order by id desc limit 100"

	if tx != nil {
		err = tx.Select(&list, str, data...)
	} else {
		err = DefaultDB().Select(&list, str, data...)
	}
	return
}

func ListEntrustUnFinishOrdersByIds(tx *sqlx.Tx, userId int64, ids []string, code string, isContainLimitStop bool) (list []proto.EntrustOrder, err error) {
	var data []interface{}
	str := _baseEntrustOrder + " and  (state=? or state=?) and user_id=? "
	data = append(data, define.OrderStatusDefault, define.OrderStatusPart, userId)
	if !isContainLimitStop {
		str += " and order_type=0 "
	} else {
		str += " and order_type in (0,2,4) "
	}
	if len(ids) == 1 {
		str += "and id=?"
		data = append(data, ids[0])
	}
	if len(ids) > 1 {
		var strPart string
		var dataPart []interface{}
		strPart, dataPart, err = sqlx.In(" and id in (?)", ids)
		if err != nil {
			return
		}
		str += strPart
		data = append(data, dataPart...)
	}
	if code != "" {
		str += "and contract_code=?"
		data = append(data, code)
	}
	if tx != nil {
		err = tx.Select(&list, str, data...)
	} else {
		err = DefaultDB().Select(&list, str, data...)
	}
	return
}

//查询指定第三方未完成订单
func ListEntrustUnFinishOrdersByMarketSource(tx *sqlx.Tx, hedgeSource define.MarketSource) (list []proto.EntrustOrder, err error) {
	str := _baseEntrustOrder + " and  (state=? or state=?)  and market_source=? and third_order_id>0  order by third_order_id asc"
	if tx != nil {
		err = tx.Select(&list, str, define.OrderStatusDefault, define.OrderStatusPart, hedgeSource)
	} else {
		err = DefaultDB().Select(&list, str, define.OrderStatusDefault, define.OrderStatusPart, hedgeSource)
	}
	return
}

//查询第三方未完成订单
func ListEntrustUnFinishOrdersForThirdOrder(tx *sqlx.Tx) (list []proto.EntrustOrder, err error) {
	str := _baseEntrustOrder + " and  (state=? or state=?)  and market_source>0 and third_order_id>0 order by market_source,third_order_id asc"
	if tx != nil {
		err = tx.Select(&list, str, define.OrderStatusDefault, define.OrderStatusPart)
	} else {
		err = DefaultDB().Select(&list, str, define.OrderStatusDefault, define.OrderStatusPart)
	}
	return
}

// ListEntrustUnFinishOrdersByUserIdAndContractSide 获取用户单方向合约未完成的订单
func ListEntrustUnFinishOrdersByUserIdAndContractSide(tx *sqlx.Tx, userId int64, contractCode, side string) (list []proto.EntrustOrder, err error) {
	str := _baseEntrustOrder + " and  (state=? or state=?) and user_id=?  and contract_code=? and side=?"
	if tx != nil {
		err = tx.Select(&list, str, define.OrderStatusDefault, define.OrderStatusPart, userId, contractCode, side)
	} else {
		err = DefaultDB().Select(&list, str, define.OrderStatusDefault, define.OrderStatusPart, userId, contractCode, side)
	}
	return
}

func GetUserUnfinishedEntrustVolume(tx *sqlx.Tx, userId int64, contractCode string) (count float64, err error) {
	str := "SELECT ifnull(sum(volume-trade_volume),0)  from tb_order where user_id=?  and contract_code=? and offset='O' and (state=? or state=?)"
	if tx != nil {
		err = tx.Get(&count, str, userId, contractCode, define.OrderStatusDefault, define.OrderStatusPart)
	} else {
		err = DefaultDB().Get(&count, str, userId, contractCode, define.OrderStatusDefault, define.OrderStatusPart)
	}
	return
}

//只限限价单
func GetUserUnfinishedEntrustCount(tx *sqlx.Tx, userId int64) (count int, err error) {
	str := "SELECT ifnull(count(1),0) from tb_order where user_id=? and state IN(?,?) and entrust_type=1"
	if tx != nil {
		err = tx.Get(&count, str, userId, define.OrderStatusDefault, define.OrderStatusPart)
	} else {
		err = DefaultDB().Get(&count, str, userId, define.OrderStatusDefault, define.OrderStatusPart)
	}
	return
}

//已锁定模式查询订单
func GetUserEntrustOrderWithLock(tx *sqlx.Tx, orderId int64) (order *proto.EntrustOrder, err error) {
	str := _baseEntrustOrder + " and  id=? for update"
	data := []interface{}{orderId}
	o := new(proto.EntrustOrder)
	if tx != nil {
		err = tx.Get(o, str, data...)
	} else {
		err = errors.New("tx is nil")
	}
	if err != nil {
		return
	}
	order = o
	return
}

func GetUserEntrustOrderByThirdOrderId(tx *sqlx.Tx, orderId int64) (order *proto.EntrustOrder, err error) {
	str := _baseEntrustOrder + " and  third_order_id=? "
	data := []interface{}{orderId}
	o := new(proto.EntrustOrder)
	if tx != nil {
		err = tx.Get(o, str, data...)
	} else {
		err = DefaultDB().Get(o, str, data...)
	}
	if err != nil {
		log.Error("GetUserEntrustOrderByThirdOrderId fail", zap.Error(err), zap.Int64("orderId", orderId))
		if err == sql.ErrNoRows {
			err = nil
			return
		}
		return
	}
	order = o
	return
}

func GetUserEntrustOrder(tx *sqlx.Tx, orderId int64) (order *proto.EntrustOrder, err error) {
	str := _baseEntrustOrder + " and  id=? "
	data := []interface{}{orderId}
	o := new(proto.EntrustOrder)
	if tx != nil {
		err = tx.Get(o, str, data...)
	} else {
		err = DefaultDB().Get(o, str, data...)
	}
	if err != nil {
		log.Error("GetUserEntrustOrder fail", zap.Error(err), zap.Int64("orderId", orderId))
		if err == sql.ErrNoRows {
			err = nil
			return
		}
		return
	}
	order = o
	return
}

//修改委托订单信息
func UpdateEntrustOrderForTrade(tx *sqlx.Tx, order *proto.EntrustOrder) (err error) {
	str := "update tb_order set asset_lock=?,trade_volume=?,trade_price=?,cost_fee=?,cost_asset=?,`state`=?,update_time=?,last_match_price=?,last_third_trade_id=? where id=?"
	data := []interface{}{order.AssetLock, order.TradeVolume, order.TradePrice, order.TradeFee, order.TradeMoney, order.State, order.UpdateTime, order.LastMatchPrice, order.LastThirdTradeId, order.ID}
	if order == nil {
		return
	}
	if order.ID == 0 {
		return errors.New("entrust order id invalid")
	}
	if tx != nil {
		_, err = tx.Exec(str, data...)
	} else {
		_, err = DefaultDB().Exec(str, data...)
	}
	return
}

//修改订单服务标识
func UpdateEntrustOrderWithSrvMark(tx *sqlx.Tx, id int64, mark int) (err error) {
	str := "update tb_order set srv_mark=? where id=?"
	if id == 0 {
		return errors.New("entrust order id invalid")
	}
	if tx != nil {
		_, err = tx.Exec(str, mark, id)
	} else {
		_, err = DefaultDB().Exec(str, mark, id)
	}
	return
}

//修改委托订单信息
func UpdateEntrustOrderForReleaseLock(tx *sqlx.Tx, order *proto.EntrustOrder) (err error) {
	str := "update tb_order set asset_lock=?,`state`=?,update_time=? where id=?"
	data := []interface{}{order.AssetLock, order.State, order.UpdateTime, order.ID}
	if order == nil {
		return
	}
	if order.ID == 0 {
		return errors.New("entrust order id invalid")
	}
	if tx != nil {
		_, err = tx.Exec(str, data...)
	} else {
		_, err = DefaultDB().Exec(str, data...)
	}
	return
}

func UpdateEntrustOrderForCanceling(tx *sqlx.Tx, orderId int64, mark int) (err error) {
	str := "update tb_order set mark=? where id=?"
	if tx != nil {
		_, err = tx.Exec(str, mark, orderId)
	} else {
		_, err = DefaultDB().Exec(str, mark, orderId)
	}
	return
}

func UpdateEntrustOrderMarkForCanceling(tx *sqlx.Tx, orderId int64, mark int) (err error) {
	str := "update tb_order set mark=? where id=? and mark!=1 and  (state=? or state=?) "
	if tx != nil {
		_, err = tx.Exec(str, mark, orderId, define.OrderStatusDefault, define.OrderStatusPart)
	} else {
		_, err = DefaultDB().Exec(str, mark, orderId, define.OrderStatusDefault, define.OrderStatusPart)
	}
	return
}

func UpdateEntrustOrderMarkForSystemCanceling(tx *sqlx.Tx, orderId int64, mark int) (err error) {
	str := "update tb_order set mark=? where id=? and mark=0 and  (state=? or state=?) "
	if tx != nil {
		_, err = tx.Exec(str, mark, orderId, define.OrderStatusDefault, define.OrderStatusPart)
	} else {
		_, err = DefaultDB().Exec(str, mark, orderId, define.OrderStatusDefault, define.OrderStatusPart)
	}
	return
}

func ListEntrustUnFinishOrdersByOriginalMarket(tx *sqlx.Tx, duration time.Duration) (list []proto.EntrustOrder, err error) {
	now := time.Now()
	now = now.Add(-duration)
	str := _baseEntrustOrder + " and entrust_type=0 and (state=? or state=?) and market_source=0 and create_time<?"
	if err != nil {
		return
	}
	if tx != nil {
		err = tx.Select(&list, str, define.OrderStatusDefault, define.OrderStatusPart, now)
	} else {
		err = DefaultDB().Select(&list, str, define.OrderStatusDefault, define.OrderStatusPart, now)
	}
	return
}

func UpdateEntrustOrderForAC(tx *sqlx.Tx, orderId int64, slippage decimal.Decimal) (err error) {
	//str := "update tb_order set ac_slippage=? where id=?"
	str := "INSERT INTO tb_order_extend (order_id, ac_slippage, create_time) VALUES (?,?,?)"
	if tx != nil {
		_, err = tx.Exec(str, orderId, slippage, time.Now())
	} else {
		_, err = DefaultDB().Exec(str, orderId, slippage, time.Now())
	}
	return
}

func DeleteEntrustOrder(tx *sqlx.Tx, orderId int64) (err error) {
	str := "DELETE FROM tb_order WHERE id = ?"
	data := []interface{}{orderId}
	if tx != nil {
		_, err = tx.Exec(str, data...)
	} else {
		_, err = DefaultDB().Exec(str, data...)
	}
	return
}

func InsertEntrustOrderHistory(tx *sqlx.Tx, order *proto.EntrustOrder) (err error) {
	str := "INSERT INTO tb_order_history (id, platform_id, user_id, coin_name, base_coin_name, contract_code, entrust_type, entrust_strategy, mode, order_type, side, price, volume,money, asset_lock, trade_volume, trade_price, cost_fee, cost_asset, state, create_time, update_time, mark, extra_id, imei, ip_address, order_client, last_match_price, user_type, client_order_id, plan_trigger_price, expect_price, third_order_id, third_mark, market_source, last_third_trade_id) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)"
	data := []interface{}{order.ID, order.PlatformID, order.UserID, order.CoinName, order.BaseCoinName, order.ContractCode, order.EntrustType, order.EntrustStrategy, order.Mode, order.OrderType, order.Side, order.Price, order.Volume, order.Money, order.AssetLock, order.TradeVolume, order.TradePrice, order.TradeFee, order.TradeMoney, order.State, order.CreateTime, order.UpdateTime, order.Mark, order.ExtraID, order.Imei, order.IpAddress, order.OrderClient, order.LastMatchPrice, define.IdentifierUser, order.ClientOrderId, order.PlanTriggerPrice, order.ExpectPrice, order.ThirdOrderId, order.ThirdMark, order.MarketSource, order.LastThirdTradeId}
	if tx != nil {
		_, err = tx.Exec(str, data...)
	} else {
		_, err = DefaultDB().Exec(str, data...)
	}
	return
}
