package database

import (
	"testing"

	"github.com/jmoiron/sqlx"
)

func TestGetUserOrderStateForPlace(t *testing.T) {
	db, err := sqlx.Open("mysql", "root:123456@tcp(127.0.0.1:3306)/qbpro?charset=utf8&parseTime=true&loc=Local")
	if err != nil {
		t.Error("InitDefaultDB db connect failed", err)
		return
	}

	tx, _ := db.Beginx()
	state, err := GetUserOrderStateForPlace(tx, 100, "BTCUSD")
	_ = tx.Commit()
	t.Logf("%+v", state)
	t.Log(err)
}
