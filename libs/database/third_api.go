package database

import (
	"github.com/jmoiron/sqlx"
	"spot/libs/proto"
)

const _thirdMarkConfig = "SELECT source_id, name, public_key, private_key, hint_phrase, api_status, create_time, update_time, is_test FROM tb_third_market_config "

func GetThirdMarketConfig(tx *sqlx.Tx) (list []proto.ThirdMarketConfig, err error) {
	if tx == nil {
		err = DefaultDB().Select(&list, _thirdMarkConfig)
	} else {
		err = tx.Select(&list, _thirdMarkConfig)
	}
	return
}
