package database

import (
	"github.com/jmoiron/sqlx"
	"spot/libs/proto"
)

func InsertTrade(tx *sqlx.Tx, t *proto.Trade) (err error) {
	str := "INSERT INTO tb_trade (id, platform_id, user_id, entrust_order_id, order_id, contract_code, side, price, volume, trade_amount, commission, order_type, trade_time, ip_address, imei, order_client, extra_id, match_type, match_id, is_maker, currency_name, price_currency_name, out_balance, out_available, in_balance, in_available,market_source,third_trade_id) VALUES (:id,:platform_id,:user_id,:entrust_order_id,:order_id,:contract_code,:side,:price,:volume,:trade_amount,:commission,:order_type,:trade_time,:ip_address,:imei,:order_client,:extra_id,:match_type,:match_id,:is_maker,:currency_name,:price_currency_name,:out_balance,:out_available,:in_balance,:in_available,:market_source,:third_trade_id)"
	_, err = tx.NamedExec(str, t)
	return
}
