package define

var (
	RedisCommonDb int // redis db
	RedisExtraDb  int // redis db
)

const (
	CacheDBNumber0  = iota // 数据库号0
	CacheDBNumber1         // 数据库号1
	CacheDBNumber2         // 数据库号2
	CacheDBNumber3         // 数据库号3
	CacheDBNumber4         // 数据库号4
	CacheDBNumber5         // 数据库号5
	CacheDBNumber6         // 数据库号6
	CacheDBNumber7         // 数据库号7
	CacheDBNumber8         // 数据库号8
	CacheDBNumber9         // 数据库号9
	CacheDBNumber10        // 数据库号10
	CacheDBNumber11        // 数据库号11
	CacheDBNumber12        // 数据库号12
	CacheDBNumber13        // 数据库号13
	CacheDBNumber14        // 数据库号14
	CacheDBNumber15        // 数据库号15
)

const (
	// pro
	CacheKeyProjectPrefix               = "bc:"                                                   // 项目key前缀
	CacheKeyVersion                     = CacheKeyProjectPrefix + "version:%d:%d"                 // 用于查询app升级信息
	CacheKeyCoinRate                    = CacheKeyProjectPrefix + "coins:rate"                    // 币种汇率
	CacheKeyCoinLegalRate               = CacheKeyProjectPrefix + "coins:legal:rate"              // 币种的法币汇率
	CacheKeyContractMarketPrice         = CacheKeyProjectPrefix + "price:market:price"            // 合约不同市场最新成交价
	CacheKeyContractMarketIndexPrice    = CacheKeyProjectPrefix + "price:market:index:price"      // 合约不同市场最新指数价格
	CacheKeyContractMarketMarkPrice     = CacheKeyProjectPrefix + "price:market:mark:price"       // 合约不同市场标记价格
	CacheKeyContractMarketDepth         = CacheKeyProjectPrefix + "price:market:depth"            // 合约不同市场深度
	CacheKeyContractDepthBasePrice      = CacheKeyProjectPrefix + "market:depth:base:price"       // 铺单基准(三大所合约价格生成）
	CacheKeyContractCurDepthPrice       = CacheKeyProjectPrefix + "market:cur:depth:base:price"   // 上次使用的的铺单基准
	CacheKeyContractDepthSimpleInfo     = CacheKeyProjectPrefix + "market:depth:simple:info"      // 合约铺单基础信息
	CacheKeyContractSpotPrice           = CacheKeyProjectPrefix + "price:spot:price"              // 合约不同市场最新成交价(现货）
	CacheKeyContractSpotIndexPrice      = CacheKeyProjectPrefix + "market:spot:index:price"       // 现货指数价格
	CacheKeyContractSpotIndexProtect    = CacheKeyProjectPrefix + "market:spot:index:protect"     // 现货指数价格保护缓存
	CacheKeyContractSpotIndexProtectDay = CacheKeyProjectPrefix + "market:spot:index:protect:day" // 现货指数价格保护缓存日次数
	CacheKeyTradePrice                  = CacheKeyProjectPrefix + "price:trade"                   // 最新成交价
	CacheKeyContractIndex               = CacheKeyProjectPrefix + "price:index"                   // 细心成交价
	CacheKeyDepthMidPrice               = CacheKeyProjectPrefix + "depth:mid"                     //合约深度中间价
	CacheKeyOriginDepth                 = CacheKeyProjectPrefix + "depth:0"                       // 合约原始深度
	CacheKeyDepth                       = CacheKeyProjectPrefix + "depth:"                        // 合约大于0深度
	CacheKeyTradeDepth                  = CacheKeyProjectPrefix + "depth:trade"                   // 可成交档位深度
	CacheKeyContractCurrentTrade        = CacheKeyProjectPrefix + "contract:trade:current"        // 合约最近成交（伪装成交）
	ContractSelfCurrentTrade            = CacheKeyProjectPrefix + "contract:trade:self:current"   // 合约最近成交（真实）
	ContractSelfCurrentBaitTrade        = CacheKeyProjectPrefix + "contract:bait:trade:current"   // 合约最近中鱼成交（真实）
	ContractSpotIndexDistance           = CacheKeyProjectPrefix + "contract:spot:index:distance"  // 合约现货指数全局
	CacheKeyContractList                = CacheKeyProjectPrefix + "contract:list"                 // 合约列表
	CacheKeyContractDepthConfig         = CacheKeyProjectPrefix + "contract:depth:config"         // 合约深度配置
	CacheKeyContractDepthSource         = CacheKeyProjectPrefix + "contract:depth:source"         // 合约铺单来源配置
	CacheKeyKline                       = CacheKeyProjectPrefix + "kline"                         // K线数据
	CacheKeyBasePriceKline              = CacheKeyProjectPrefix + "base:price:kline"              // K线基础价格
	CacheKeyRecentTrade                 = CacheKeyProjectPrefix + "order:recent:"                 // 近期交易
	CacheKeyApplies                     = "bc.applies"                                            // 涨跌幅
	CacheKeyCoinListByID                = CacheKeyProjectPrefix + "coin:list:id"                  // 币种列表 币种id为域
	CacheKeyCoinListByName              = CacheKeyProjectPrefix + "coin:list:name"                // 币种列表 币种名为域
	CacheKeyMarginCoinListByID          = CacheKeyProjectPrefix + "coin:margin:list:id"           // 保证金币种列表 币种id为域
	CacheKeyMarginCoinListByName        = CacheKeyProjectPrefix + "coin:margin:list:name"         // 保证金币种列表 币种名为域
	CacheKeyTradeCounter                = CacheKeyProjectPrefix + "trade:counter"                 // 用户下单计数
	CacheKeyMatchOrder                  = CacheKeyProjectPrefix + "match:order"                   // 撮合订单缓存(停止服务时用）
	CacheKeyRCOrder                     = CacheKeyProjectPrefix + "rc:order"                      // Rc订单缓存(停止服务时用）
	CacheKeyMatchMsgQueue               = CacheKeyProjectPrefix + "match:msg:queue"               // 撮合消息缓存
	CacheKeyCoinMarketConfList          = CacheKeyProjectPrefix + "coin:market:conf"              // 币种市场配置

	CacheKeyInviteCodeMaxId             = CacheKeyProjectPrefix + "invite:CodeMaxIDList"        // 当前生成的邀请码列表的最大id
	CacheKeyInviteCodeList              = CacheKeyProjectPrefix + "invite:CodeList"             // 未使用邀请码列表
	CacheKeyInviteCodeGenLock           = CacheKeyProjectPrefix + "invite:CodeGenLock"          // 邀请码生成锁
	CacheKeyAuthCode                    = CacheKeyProjectPrefix + "authCode:"                   // 短信验证码
	CacheKeyUserAction                  = CacheKeyProjectPrefix + "userAction"                  // 用户操作标记(找回密码,修改手机...)
	CacheKeyLoginInfo                   = CacheKeyProjectPrefix + "login:info:"                 // 用户登录信息
	CacheKeyUserIDGenBasic              = CacheKeyProjectPrefix + "uid:basic"                   // 用户id生成要素
	CacheKeySafeGuard                   = CacheKeyProjectPrefix + "safe:guard:"                 // 用户安全保护,用于修改密码后24小时内禁止用户提币
	CacheKeyBindAccountLock             = CacheKeyProjectPrefix + "lock:account"                // 绑定账号锁
	CacheKeyAssertRecordLock            = CacheKeyProjectPrefix + "assert:record:lock"          // 用于判断记录是否存在
	CacheKeyRechargeLock                = CacheKeyProjectPrefix + "recharge:lock"               // 用于给充币回调加锁
	CacheKeyWithdrawLock                = CacheKeyProjectPrefix + "withdraw:lock"               // 用于给用户提币操作加锁
	CacheKeyWithdrawAutoTask            = CacheKeyProjectPrefix + "withdraw:auto"               // 提币自动审核任务列表
	CacheKeyLockWithdrawOperate         = CacheKeyProjectPrefix + "withdraw:operate:"           // 提币审核操作锁
	CacheKeyLockLegalAuditOperate       = CacheKeyProjectPrefix + "legal:order:operate:"        // 法币订单审核操作锁
	CacheKeyLegalPlaceLock              = CacheKeyProjectPrefix + "legal:place:lock:"           // 法币下单锁
	CacheKeySimAssetIncreaseLock        = CacheKeyProjectPrefix + "sim:asset:increase:lock"     // 用于给用户领取模拟资产操作加锁
	CacheKeySimAssetDecreaseLock        = CacheKeyProjectPrefix + "sim:asset:decrease:lock"     // 用于给用户减少模拟资产操作加锁
	CacheKeyIndexContractDealLock       = CacheKeyProjectPrefix + "index:contract:deal:lock"    //合约指数价格处理锁
	CacheKeyPlanCloseLock               = CacheKeyProjectPrefix + "plan:close:"                 //计划条件单锁
	CacheKeyPlanOpenLock                = CacheKeyProjectPrefix + "plan:open:"                  //计划条件开仓单锁
	CacheKeyBannerList                  = CacheKeyProjectPrefix + "banner:list:%d"              // 轮播图列表缓存
	CacheKeyNoticeList                  = CacheKeyProjectPrefix + "notice:list:%d"              // 公告列表
	CacheKeyNoticeDetail                = CacheKeyProjectPrefix + "notice:detail:%d"            // 公告详情
	CacheKeyImportantNoticeList         = CacheKeyProjectPrefix + "notice:impartant:list:%d"    // 重要通知列表
	CacheKeyAboutCommunity              = CacheKeyProjectPrefix + "about:community"             // 关于社群详情
	CacheKeyNewVersion                  = CacheKeyProjectPrefix + "version:new:%d:%d:%d"        // app升级信息
	CacheKeyAppVersionStore             = CacheKeyProjectPrefix + "version:store:"              // app商店下载页链接
	CacheKeyFollowConfig                = CacheKeyProjectPrefix + "follow:config"               // 跟单全局配置
	CacheKeyHotfixPatch                 = CacheKeyProjectPrefix + "version:hotfix:"             // 热升级补丁
	CacheKeyDownloadStatistic           = CacheKeyProjectPrefix + "statistic:download:%s:%d"    // app下载统计
	CacheKeyOnlineUserStatistic         = CacheKeyProjectPrefix + "statistic:onlineUser:%s:%d"  // 在线用户统计
	CacheKeyOnlineUserStatisticWithUser = CacheKeyProjectPrefix + "statistic:user:online:%s:%d" // 在线用户统计(使用{uid}-{invite_parent} 方式)

	CacheKeySyncLegalStatLock    = CacheKeyProjectPrefix + "lock:sync:legal:stat:"    // 同步前一日法币统计锁
	CacheKeyMailStatDailyLock    = CacheKeyProjectPrefix + "lock:mail:stat:daily:"    // 日报邮件任务锁
	CacheKeyMailStatHalfLock     = CacheKeyProjectPrefix + "lock:mail:stat:half:"     // 半日报邮件任务锁
	CacheKeyMailErrorCollectLock = CacheKeyProjectPrefix + "lock:mail:error:collect:" // 日志收集邮件任务锁

	CacheKeySysConfig = CacheKeyProjectPrefix + "system:config" //系统配置项

	CacheKeyShareImages = CacheKeyProjectPrefix + "share:image" // 分享图片资源
	CacheKeyShareTexts  = CacheKeyProjectPrefix + "share:text"  // 分享文案资源

	CacheKeyServerConfigCommon  = CacheKeyProjectPrefix + "server:config:common"   // 服务器配置信息
	CacheKeyServerConfigHosts   = CacheKeyProjectPrefix + "server:config:hosts"    // 服务器域名列表
	CacheKeyServerConfigHostsV2 = CacheKeyProjectPrefix + "server:config:hosts:v2" // 服务器域名列表
	CacheKeyUserApiInfos        = "bc_openapi:user:api:list"                       //openapi缓存列表
	CacheKeyRobotMarketPosition = CacheKeyProjectPrefix + "robot:position:"        // 做市商持仓数缓存key

	CacheKeyContractFastClose = CacheKeyProjectPrefix + "close:all:contract" //合约一键平仓锁
)

const (
	CacheHegeBackAddAPILock = CacheKeyProjectPrefix + "back:api:add:update"
)

const (
	CacheLastNews = CacheKeyProjectPrefix + "news:last"
)

const (
	CacheUpdateNicknameLock   = CacheKeyProjectPrefix + "lock:nickname"
	CacheUpdateNicknameENLock = CacheKeyProjectPrefix + "lock:nicknameEN"
)

const (
	CacheLeaderListOrderByNormal          = CacheKeyProjectPrefix + "leader:list:normal:"
	CacheLeaderListOrderByProfit          = CacheKeyProjectPrefix + "leader:list:profit:amount:"
	CacheLeaderListOrderByProfitRate      = CacheKeyProjectPrefix + "leader:list:profit:rate:"
	CacheLeaderListOrderByWin             = CacheKeyProjectPrefix + "leader:list:win:"
	CacheLeaderListOrderByTrades          = CacheKeyProjectPrefix + "leader:list:trades:"
	CacheLeaderListOrderByTotalProfitRate = CacheKeyProjectPrefix + "leader:list:total:profit:rate:"
	CacheLeaderDetailData                 = CacheKeyProjectPrefix + "leader:detail"
	CacheLeaderFollowDetailData           = CacheKeyProjectPrefix + "leader:follow:detail"
	CacheFollowSetLock                    = CacheKeyProjectPrefix + "lock:follow:set"
	CacheFollowExitLock                   = CacheKeyProjectPrefix + "lock:follow:exit"
)

const (
	CacheFullForceKey   = CacheKeyProjectPrefix + "force:lock:"      //全仓强平锁
	CacheForceQueue     = CacheKeyProjectPrefix + "force:queue"      //全平队列
	CacheForceQueueLock = CacheKeyProjectPrefix + "force:queue:lock" //全平队列处理锁

	CacheFastCloseQueue = CacheKeyProjectPrefix + "fast:close:queue" //一键全平队列

	CacheForceKey     = CacheKeyProjectPrefix + "force:position:"      //普通强平锁仓
	CacheWareForceKey = CacheKeyProjectPrefix + "force:position:ware:" //普通强平锁仓

	CacheReturnMatch = "match:order:return"

	CacheRequest = CacheKeyProjectPrefix + "request"

	//跟单全仓强平锁
	CacheFollowAccountFullForceKey = CacheKeyProjectPrefix + "follow:full:force:lock:" //全仓强平锁
)

const (
	CacheCloseTask       = CacheKeyProjectPrefix + "close:task"
	CacheCloseTaskBackUp = CacheKeyProjectPrefix + "close:task:backup"
)

//触发重复锁
const (
	CacheTriggerCoreFullForceClose = CacheKeyProjectPrefix + "core:full:force:trigger"
	CacheTriggerCoreWareForceClose = CacheKeyProjectPrefix + "core:ware:force:trigger"
	CacheTriggerCorePlanClose      = CacheKeyProjectPrefix + "trigger:core:plan:close"
	CacheTriggerCoreStop           = CacheKeyProjectPrefix + "trigger:core:stop"
	CacheTriggerPlanOpen           = CacheKeyProjectPrefix + "trigger:plan:open"
	CacheTriggerFollowForceClose   = CacheKeyProjectPrefix + "follow:force:trigger"
	CacheTriggerFollowPlanClose    = CacheKeyProjectPrefix + "trigger:follow:plan:close"
)

const (
	CacheKeyContractSideTradeCount = CacheKeyProjectPrefix + "contract:side:largeTrade:count" //合约当方向大额成交数
	CacheKeyContractTradeAmount    = CacheKeyProjectPrefix + "contract:trade:amount"          //合约同时交易量
	CacheKeyContractWarningLock    = CacheKeyProjectPrefix + "contract:warning:lock"
	CacheKeyContractNetPosCount    = CacheKeyProjectPrefix + "contract:net:position:count"  //合约净持仓统计
	CacheKeyContractBuyPosCount    = CacheKeyProjectPrefix + "contract:buy:position:count"  //合约买持仓统计
	CacheKeyContractSellPosCount   = CacheKeyProjectPrefix + "contract:sell:position:count" //合约卖持仓统计
	CacheKeyContractDepthBuyValid  = CacheKeyProjectPrefix + "contract:depth:buy:count"     //合约深度有效期买统计
	CacheKeyContractDepthSellValid = CacheKeyProjectPrefix + "contract:depth:sell:count"    //合约深度有效期买统计
)

const (
	CacheContractBucketAmountFactor = CacheKeyProjectPrefix + "contract:bucket:factor"     // 合约对敲数量系数
	CacheContractValid24Volume      = CacheKeyProjectPrefix + "contract:market:valid"      // 合约2小时有效的24小时成交量
	CacheContractValidMinuteVolume  = CacheKeyProjectPrefix + "contract:market:valid:1m"   // 合约分钟成交量
	CacheContractMarket24           = CacheKeyProjectPrefix + "contract:market:day:volume" // 合约2小时有效的24小时成交量
	CacheContractHourRandFactor     = CacheKeyProjectPrefix + "contract:hour:factor"       //合约小时随机系数
)

const (
	CacheStatusKeyNotice  = CacheKeyProjectPrefix + "status:notice:" // 公告缓存状态,防止空数据时频繁访问数据库
	CacheStatusKeyBanner  = CacheKeyProjectPrefix + "status:banner:" // 轮播图缓存状态,防止空数据时频繁访问数据库
	CacheStatusKeyVersion = CacheKeyProjectPrefix + "status:version" // app版本信息缓存状态,防止空数据时频繁访问数据库
)

const (
	CacheKeyWalletCoinDictV2 = CacheKeyProjectPrefix + "wallet:coin:dict:v2" // 只存储币种名称对应的大钱包名称对应关系,1对多 hash {coinName:{protocol:walletName...}...}
	CacheKeyWalletCoinConfig = CacheKeyProjectPrefix + "wallet:coin:config"  // 存储大钱包名称对应的配置信息,1对1 hash {walletName: {info}...}
)

const (
	CacheKeyContractGivingCount      = CacheKeyProjectPrefix + "contract:bucket:giving:count" //合约对敲放量计数器
	CacheKeyContractLastGiving       = CacheKeyProjectPrefix + "contract:bucket:last:giving"  //合约上次对敲上限
	CacheKeyContractHourDayRandIndex = CacheKeyProjectPrefix + "contract:day:hourRandIndex"   //合约小时拆分系数缓存打散序列
)

const (
	CacheKeyMarket24History    = CacheKeyProjectPrefix + "contract:market:24h:history"
	CacheKeyLastBucketDuration = CacheKeyProjectPrefix + "contract:bucket:duration:current"
)

const (
	CacheKeyDepthSpotIndexBaseMinuteHistory = CacheKeyProjectPrefix + "spot:index:base:depth"   // 铺单现货基础指数历史
	CacheKeySpotIndexBaseMinuteHistory      = CacheKeyProjectPrefix + "spot:index:base:1m"      // 现货基础指数分钟历史
	CacheKeyContractDynamicBaseDiff         = CacheKeyProjectPrefix + "contract:move:base:diff" //合约移动基差值
)

const (
	CacheLockLegalWithdraw = CacheKeyProjectPrefix + "lock:legal:withdraw:" // 法币入金禁止提币锁
)

const (
	CacheKeySysTradeConf     = CacheKeyProjectPrefix + "system:traders:key" // 全局系统交易配置
	CacheKeySysUserQuotaConf = CacheKeyProjectPrefix + "system:quota:conf"  // 全局系统用户限额配置
)

const (
	CacheKeyWithdrawDailyUsedAmount = CacheKeyProjectPrefix + "withdraw:daily:used:%s:0102" // 保存用户当日提币已用额度
	CacheKeyWithdrawWhiteList       = CacheKeyProjectPrefix + "withdraw:white:list"         // 提币白名单用户,不进行提币次数限制
)

const (
	CacheKeySafeUserNewTotpSecret    = CacheKeyProjectPrefix + "safe:secret:totp:new:"
	CacheKeySafeTradeVerifyState     = CacheKeyProjectPrefix + "safe:trade:verify:state:"
	CacheKeySafeFundPasswordMiscount = CacheKeyProjectPrefix + "safe:password:fund:miscount:"
	CacheKeySafeMarkVerifyFund       = CacheKeyProjectPrefix + "safe:mark:verify:fund:"
	CacheKeySafeMarkVerifyCode       = CacheKeyProjectPrefix + "safe:mark:verify:code:"
)

const (
	CacheTaskKeyWithdrawVerifyList = CacheKeyProjectPrefix + "task:withdraw:list"
)

const (
	CacheLockUserAPIModify = CacheKeyProjectPrefix + "lock:user:api:modify:"
)

const (
	DepthPriceSourceException       = CacheKeyProjectPrefix + "depth:price:source:exc"
	DepthPriceSourceSwitchException = CacheKeyProjectPrefix + "depth:switch:exc:"
	DepthPriceAvgWarn               = CacheKeyProjectPrefix + "depth:price:avg:warn"
)

const (
	CacheKeyPriceCount     = CacheKeyProjectPrefix + "price:count"
	CacheKeyAlarmRecord    = CacheKeyProjectPrefix + "alarm"
	CacheKeyIndexException = CacheKeyProjectPrefix + "index:exception"
)

const CacheKeyWarnExpire = CacheKeyProjectPrefix + "warn:expire"
const CacheKeyAlarmDuplicate = CacheKeyProjectPrefix + "duplicate:alarm"

//服务缓存key
const (
	CacheServerMatchSupport = CacheKeyProjectPrefix + "server"
)

const (
	CacheKeySyncPositionDuplicate   = CacheKeyProjectPrefix + "full:position:sync"
	CacheKeyUserContractAccountSync = CacheKeyProjectPrefix + "user:contract:position:sync"
	CacheKeyUserFollowAccountSync   = CacheKeyProjectPrefix + "user:follow:position:sync"
)

const CacheMarketWarnReceive = CacheKeyProjectPrefix + "warn:receiver" //报警邮件缓存

const (
	CacheKeyUserPlaceLimit = CacheKeyProjectPrefix + "user:contract:netPos:limit"
)

const (
	CacheResponseKeyActivityMyWard = CacheKeyProjectPrefix + "cache:activity:myWard:"
)

const (
	CacheMsgClosing = CacheKeyProjectPrefix + "cache.match.msg:closing"
	CacheKeyUserAc  = CacheKeyProjectPrefix + "cache.user.contract:ac"
)

const (
	CacheOrderMsgSeqKey          = CacheKeyProjectPrefix + "order:deal:seq"             //订单消息生产序列
	CacheOrderMsgDealLock        = CacheKeyProjectPrefix + "order:closing:deal:lock"    //清算订单处理锁
	CacheCurOrderSeqID           = CacheKeyProjectPrefix + "order:closing:deal:cur:seq" //上次处理的订单订单消息序列
	CacheCurOrderDealing         = CacheKeyProjectPrefix + "order:closing:dealing"      //待处理订单消息
	CacheOrderWaitFinish         = CacheKeyProjectPrefix + "order:closing:waitFinish"   //待处理订单待完成消息
	CacheOrderWaitFinishDealLock = CacheKeyProjectPrefix + "order:closing:lock:waitFinish"
)

const (
	CacheForceFollowHandler = CacheKeyProjectPrefix + "force:handler"
)

const (
	CacheKeySpotExchangePreOrder   = CacheKeyProjectPrefix + "spot:preorder:"
	CacheKeySpotMarketSource       = CacheKeyProjectPrefix + "task:spot:hedge:source"
	CacheKeySpotMarketSourceConfig = CacheKeyProjectPrefix + "task:spot:hedge:config"

	CacheKeyOrderCache      = CacheKeyProjectPrefix + "third:entrust:Order"
	CacheKeyOrderTradeCache = CacheKeyProjectPrefix + "third:trade"
)

const (
	CacheKeyThirdLegalAllRates  = CacheKeyProjectPrefix + "third:legal:rates:"   // 三方买币汇率
	CacheKeyThirdLegalAllLimits = CacheKeyProjectPrefix + "third:legal:limits:"  // 三方买币限制
	CacheKeyThirdMerchants      = CacheKeyProjectPrefix + "third:merchants:list" //三方商家
)

const (
	CacheKeyUserWsOnLine = CacheKeyProjectPrefix + "ws:user"
)
