/*
@Time : 2019-12-26 16:20
<AUTHOR> mocha
@File : config.ws
*/
package define

// room名称配置
const (
	RoomMarket  = "market.%s" //默认一个交易对一个房间
	RoomDepth   = "market.depth.%s.%s"
	RoomTicker  = "market.ticker.%s"
	RoomApplies = "market.applies.%s"
	RoomSymbols = "symbols.market" //所有交易对行情及价格推送
)

// notify消息类型
const (
	NotifyBase         = "pot."
	NotifyDepth        = NotifyBase + "market.depth"
	NotifyTicker       = NotifyBase + "market.ticker"             //最新成交
	NotifySymbols      = NotifyBase + "symbols.market"            //所有合约价格及深度
	NotifyExchangeRate = NotifyBase + "exchange.rate"             // 所有支持币种的兑换价格汇率推送
	NotifyContractInfo = NotifyBase + "symbol.applies"            //合约明细
	NotifyMessage      = NotifyBase + "message"                   //消息
	NotifyOrderChange  = NotifyBase + "user.entrust.order.update" //消息
)

// 订阅及通知动作
const (
	WsActionSub    = "sub"
	WsActionUnSub  = "unSub"
	WsActionAuth   = "auth"
	WsActionNotify = "notify"
)

// 自动订阅房间
var AutoSubRooms = []string{RoomMarket}

const (
	RoomOpRegister   = 0
	RoomOpUnRegister = 1
)

const (
	ActionAdd    = "add"
	ActionUpdate = "update"
	ActionRemove = "remove"
)
