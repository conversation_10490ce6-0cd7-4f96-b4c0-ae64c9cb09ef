/*
@Time : 3/16/20 2:50 下午
<AUTHOR> mocha
@File : contract
*/
package define

const (
	ContractCoinUnit  = 0 //币本位
	ContractMoneyUnit = 1 //金本位
)

type DealerState int

func (ds DealerState) CanApply() bool {
	return ds == DealerStateUnknow || ds == DealerStateCancel || ds == DealerStateReject
}

func (ds DealerState) NewApply() bool {
	return ds == DealerStateUnknow
}

const (
	DealerStateUnknow    DealerState = -2 // 未申请
	DealerStateCancel    DealerState = -1 // 已注销
	DealerStatePause     DealerState = 0  // 已停止
	DealerStateNormal    DealerState = 1  // 正常
	DealerStateWaitAudit DealerState = 2  // 待审核
	DealerStateReject    DealerState = 3  // 驳回
)
