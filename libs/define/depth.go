/*
@Time : 3/7/20 9:33 上午
<AUTHOR> mocha
@File : depth
*/
package define

import "time"

const (
	DepthValidSeconds = 10 //深度有效计算秒数
)

const (
	DepthLockKey = "DepthLock" // 深度redis key 时间锁深度数据不会推送过于频繁
	Depth0       = "0"         // 深度redis key 0级别后缀
	Depth1       = "1"         // 深度redis key 1级别后缀
	Depth2       = "2"         // 深度redis key 2级别后缀
	Depth3       = "3"         // 深度redis key 3级别后缀
	Depth4       = "4"         // 深度redis key 4级别后缀
	// CacheKeyKline     = "pro:kline:"       // K线redis key 移动至cache.go
	Kline1M    = "1m"      // K线redis key 1分钟后缀
	Kline5M    = "5m"      // K线redis key 5分钟后缀
	Kline15M   = "15m"     // K线redis key 15分钟后缀
	Kline30M   = "30m"     // K线redis key 30分钟后缀
	Kline1H    = "1h"      // K线redis key 1小时后缀
	Kline2H    = "2h"      // K线redis key 2小时后缀
	Kline4H    = "4h"      // K线redis key 4小时后缀
	Kline6H    = "6h"      // K线redis key 6小时后缀
	Kline12H   = "12h"     // K线redis key 12小时后缀
	Kline1D    = "1d"      // K线redis key 1天后缀
	Kline1W    = "1w"      // K线redis key 1周后缀
	ChgKey     = "Chg"     // 涨跌幅数据redis key
	ChgNodeKey = "ChgNode" // 涨跌幅节点数据redis key
)

var klineDurationMap = map[string]time.Duration{
	Kline1M:  Kline1MValue,
	Kline5M:  Kline5MValue,
	Kline15M: Kline15MValue,
	Kline30M: Kline30MValue,
	Kline1H:  Kline1HValue,
	Kline2H:  Kline2HValue,
	Kline4H:  Kline4HValue,
	Kline6H:  Kline6HValue,
	Kline12H: Kline12HValue,
	Kline1D:  Kline1DValue,
	Kline1W:  Kline1WValue,
}

func GetKlineDuration() []string {
	return []string{Kline1M, Kline5M, Kline15M, Kline30M, Kline1H, Kline2H, Kline4H, Kline6H, Kline12H, Kline1D, Kline1W}
}

func DurationStr2Duration(str string) int64 {
	return int64(klineDurationMap[str])
}

func LastKlineStartTime(str string) int64 {
	dur := klineDurationMap[str]
	if dur == 0 {
		return 0
	}

	now := time.Now()
	switch str {
	case Kline1M, Kline5M, Kline15M, Kline30M:
		return now.Truncate(dur * time.Second).Unix()
	case Kline1H, Kline2H, Kline4H, Kline6H, Kline12H:
		return now.Truncate(time.Hour).Add(-time.Duration(now.Hour()%(int(dur)/60/60)) * time.Hour).Unix()
	case Kline1D:
		return now.Truncate(time.Hour).Add(-time.Duration(now.Hour()) * time.Hour).Unix()
	case Kline1W:
		return LastMonday(now).Unix()
	default:
		return 0
	}
}

// 获取上个周一0点时间
func LastMonday(t time.Time) time.Time {
	t = t.Truncate(time.Hour).Add(-time.Duration(t.Hour()) * time.Hour)
	week := t.Weekday()
	if week == time.Sunday {
		return t.AddDate(0, 0, -6)
	}
	return t.AddDate(0, 0, -int(week-1))
}

var DepthSupportLevel = map[int]bool{0: true, 1: true, 2: true, 3: true, 4: true}

const (
	DepthHeight       = 30
	DepthShowHeight   = 20
	DefalutDepthValue = 0
	Depth0Value       = 0                                // 深度分级0（不合并）
	Depth1Value       = 1                                // 深度分级1 合并1位
	Depth2Value       = 2                                // 深度分级2 合并2位
	Depth3Value       = 3                                // 深度分级3 合并3位
	Depth4Value       = 4                                // 深度分级4 合并4位
	Kline1MValue      = 1 * time.Minute / time.Second    // K线时间间隔1分钟
	Kline5MValue      = 5 * time.Minute / time.Second    // K线时间间隔5分钟
	Kline15MValue     = 15 * time.Minute / time.Second   // K线时间间隔15分钟
	Kline30MValue     = 30 * time.Minute / time.Second   // K线时间间隔30分钟
	Kline1HValue      = time.Hour / time.Second          // K线时间间隔1小时
	Kline2HValue      = 2 * time.Hour / time.Second      // K线时间间隔2小时
	Kline4HValue      = 4 * time.Hour / time.Second      // K线时间间隔4小时
	Kline6HValue      = 6 * time.Hour / time.Second      // K线时间间隔6小时
	Kline12HValue     = 12 * time.Hour / time.Second     // K线时间间隔12小时
	Kline1DValue      = 24 * time.Hour / time.Second     // K线时间间隔1天
	Kline1WValue      = 7 * 24 * time.Hour / time.Second // K线时间间隔1周
	AppliesChange10m  = "10m"
	AppliesChange30m  = "30m"
	AppliesChange1h   = "1h"
	AppliesChange2h   = "2h"
	AppliesChange4h   = "4h"
	AppliesChange8h   = "8h"
	AppliesChange24h  = "24h"
)

const ValidSeconds = 60
