package define

// UserWithdrawLimit 用户提币限制标记枚举 1-不限制 2-主动开启限制 3-被动开启限制(因为上级代理被限制,导致的关联限制) 4-主动解除限制(不再重复标记) 5-被动解除限制(不再重复标记)(因为上级代理被解除限制,导致的关联解除)
type UserWithdrawLimit uint8

func (t UserWithdrawLimit) Valid() bool {
	return t > UserWithdrawLimitAll && t < UserWithdrawLimitNonsupport
}

const (
	UserWithdrawLimitAll           UserWithdrawLimit = iota // 0-全部 仅用于筛选
	UserWithdrawLimitNone                                   // 1-未开启
	UserWithdrawLimitActiveOpen                             // 2-主动开启限制
	UserWithdrawLimitPassiveOpen                            // 3-被动开启限制(因为上级代理被限制,导致的关联限制)
	UserWithdrawLimitActiveCancel                           // 4-主动解除限制(不再重复标记)
	UserWithdrawLimitPassiveCancel                          // 5-被动解除限制(不再重复标记)(因为上级代理被解除限制,导致的关联解除)
	UserWithdrawLimitNonsupport                             // 6-不支持的类型开始
)

const (
	UserWithdrawLimitModeFirst = "首次提币"
	UserWithdrawLimitModeLabel = "小额限制"
)

// ManageOperationLogType
// 1 修改标签，2添加代理，3修改用户信息，4添加备注，5添加标签，6添加标签信息，7身份审核，8审核提币，
// 9修改标签信息，10发放返佣，11 删除用户标签，12 添加用户风控组，13 更新用户风控组，15 设置用户风控白名单，
// 14 删除用户风控白名单，16 修改用户风控组信息，17 设置用户提币审核限制，18 解除用户提币限制
type ManageOperationLogType uint

const (
	ManageOperationLogTypeModifyLabel ManageOperationLogType = iota + 1
	ManageOperationLogTypeOpenAgent
	ManageOperationLogTypeModifyUserInfo
	ManageOperationLogTypeAddUserRemark
	ManageOperationLogTypeAddLabel
	ManageOperationLogTypeAddLabelInfo
	ManageOperationLogTypeUserVerify
	ManageOperationLogTypeAuditWithdraw
	ManageOperationLogTypeModifyLabelInfo
	ManageOperationLogTypeReleaseRebate
	ManageOperationLogTypeDeleteUserLabel
	ManageOperationLogTypeAddUserRiskGroup
	ManageOperationLogTypeModifyUserRiskGroup
	ManageOperationLogTypeSetUserRiskGroupWhiteList
	ManageOperationLogTypeDeleteUserRiskGroupWhiteList
	ManageOperationLogTypeModifyUserRiskGroupInfo
	ManageOperationLogTypeSetUserWithdrawLimit
	ManageOperationLogTypeCancelUserWithdrawLimit
)

type UserLimitState uint64

// Contains 是否包含指定的限制状态
func (ls UserLimitState) Contains(sub UserLimitState) bool {
	return ls&sub == sub
}

const (
	UserLimitStateDeleteAccount UserLimitState = 1 << iota // 1-(1<<0) 用户是否已注销账户
)

var ShareResourceLevels = []int8{3, 2, 1, -1, -2, -3}

type SupportAccountType uint8

const (
	SupportAccountTypePhone SupportAccountType = 1 << iota // 1-手机号
	SupportAccountTypeEmail                                // 2-邮箱
)

// FollowLogType 1-修改带单设置 2-跟随交易员 3-调整跟随设置 4-停止跟随交易员 5-移除跟随者
type FollowLogType uint8

const (
	FollowLogTypeModifyTraderConfig FollowLogType = iota + 1 // 1-修改带单设置
	FollowLogTypeSetFollowTrader                             // 2-跟随交易员
	FollowLogTypeModifyFollowTrader                          // 3-调整跟随设置
	FollowLogTypeExitFollowTrader                            // 4-停止跟随交易员
	FollowLogTypeDelFollower                                 // 5-移除跟随者
)
