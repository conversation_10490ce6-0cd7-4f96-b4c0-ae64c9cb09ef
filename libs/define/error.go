package define

import (
	"fmt"
)

// 定义API错误码
const (
	ErrCodeNone = 0 // 正确

	// 公共错误
	ErrCodeBusy                          = 1001 // 系统繁忙
	ErrCodeServerNotAvailable            = 1003 // 服务暂时不可用
	ErrCodeParam                         = 1004 // 请求参数错误
	ErrCodeSignature                     = 1006 // 签名验证失败
	ErrCodeResubmit                      = 1014 // 重复提交
	ErrCodeShouldLogin                   = 1101 // 未登录
	ErrCodeUserNotExist                  = 1102 // 用户不存在
	ErrCodeTradeNotAllowed               = 1113 // 当前用户被禁止交易
	ErrCodeBalanceInsufficient           = 1204 // 资产不足
	ErrCodeTradeFrequent                 = 1307 // 委托请求频繁 每秒最多49单
	ErrCodePlanOrderLimit                = 1314 // 达到未触发计划单数量限制
	ErrCodeDepthInvalid                  = 1350 // 没有足够的深度成交
	ErrCodePriceOverLimit                = 1351 // 当前委托价格超过允许范围
	ErrCodePriceInvalid                  = 1352 // 当前委托价格无效
	ErrCodeInMaintenance                 = 1360 // 系统维护
	ErrCodeOrderIdUserd                  = 1364 // 订单号已存在
	ErrCodeUnfinishedEntrustOrderTooMany = 1365 // 当前未完成委托数过多
	ErrCodeOrderState                    = 1366 // 当前订单状态已更新,请刷新后重试

	// 现货相关错误码
	// 从3开始(主站的1开头,反向合约2开头,现货3开头)
	ErrCodeEntrustBelowMinOpenVolume = 3001 // 委托数量请大于最小下单数量【此交易对的最小下单数量】【基础货币单位】
	ErrCodeEntrustOverMaxOpenVolume  = 3002 // 委托数量请小于最大下单数量【此交易对的最小下单数量】【基础货币单位】
	ErrCodeEntrustBelowMinOpenValue  = 3003 // 委托金额请大于最小下单金额【此交易对的最小下单金额】【计价货币单位】
	ErrCodeEntrustOverMaxOpenValue   = 3004 // 委托金额请小于最大下单金额【此交易对的最小下单金额】【计价货币单位】
	ErrCodeSpotNotExist              = 3005 // 交易对不存在
	ErrCodeSpotNotTrade              = 3006 // 交易对暂不可交易
	ErrCodeSpotOverMaxSideEntrust    = 3007 // 超出单边最大挂单量
	ErrCodeSpotTradeLimit            = 3008 // 交易限制，请稍后再试。
	ErrCodeAccountFormat             = 1104 // 账号格式错误
	ErrCodeNonsupportPhoneNumber     = 1155 // 暂不支持手机号

)

const (
	NormalErrMsg = "系统繁忙,请稍后再试"
)

var (
	ErrMsgNone                          *ReplyError = nil
	ErrMsgBusy                                      = newReplyError(ErrCodeBusy)                          // 系统繁忙
	ErrMsgServerNotAvailable                        = newReplyError(ErrCodeServerNotAvailable)            // 服务暂时不可用
	ErrMsgParam                                     = newReplyError(ErrCodeParam)                         // 请求参数错误
	ErrMsgSignature                                 = newReplyError(ErrCodeSignature)                     // 签名验证失败
	ErrMsgResubmit                                  = newReplyError(ErrCodeResubmit)                      // 重复提交
	ErrMsgShouldLogin                               = newReplyError(ErrCodeShouldLogin)                   // 未登录
	ErrMsgUserNotExist                              = newReplyError(ErrCodeUserNotExist)                  // 用户不存在
	ErrMsgTradeNotAllowed                           = newReplyError(ErrCodeTradeNotAllowed)               // 当前用户被禁止交易
	ErrMsgBalanceInsufficient                       = newReplyError(ErrCodeBalanceInsufficient)           // 资产不足
	ErrMsgTradeFrequent                             = newReplyError(ErrCodeTradeFrequent)                 // 委托请求频繁 每秒最多49单
	ErrMsgPlanOrderLimit                            = newReplyError(ErrCodePlanOrderLimit)                // 达到未触发计划单数量限制
	ErrMsgDepthInvalid                              = newReplyError(ErrCodeDepthInvalid)                  // 没有足够的深度成交
	ErrMsgPriceOverLimit                            = newReplyError(ErrCodePriceOverLimit)                // 当前委托价格超过允许范围
	ErrMsgPriceInvalid                              = newReplyError(ErrCodePriceInvalid)                  // 当前委托价格无效
	ErrMsgInMaintenance                             = newReplyError(ErrCodeInMaintenance)                 // 系统维护
	ErrMsgOrderIdUserd                              = newReplyError(ErrCodeOrderIdUserd)                  // 订单号已存在
	ErrMsgUnfinishedEntrustOrderTooMany             = newReplyError(ErrCodeUnfinishedEntrustOrderTooMany) // 当前未完成委托数过多
	ErrMsgOrderState                                = newReplyError(ErrCodeOrderState)                    // 当前订单状态已更新,请刷新后重试
	ErrMsgEntrustBelowMinOpenVolume                 = newReplyError(ErrCodeEntrustBelowMinOpenVolume)     // 委托数量请大于最小下单数量【此交易对的最小下单数量】【基础货币单位】
	ErrMsgEntrustOverMaxOpenVolume                  = newReplyError(ErrCodeEntrustOverMaxOpenVolume)      // 委托数量请小于最大下单数量【此交易对的最小下单数量】【基础货币单位】
	ErrMsgEntrustBelowMinOpenValue                  = newReplyError(ErrCodeEntrustBelowMinOpenValue)      // 委托金额请大于最小下单金额【此交易对的最小下单金额】【计价货币单位】
	ErrMsgEntrustOverMaxOpenValue                   = newReplyError(ErrCodeEntrustOverMaxOpenValue)       // 委托金额请小于最大下单金额【此交易对的最小下单金额】【计价货币单位】
	ErrMsgSpotNotExist                              = newReplyError(ErrCodeSpotNotExist)                  // 交易对不存在
	ErrMsgSpotNotTrade                              = newReplyError(ErrCodeSpotNotTrade)                  // 交易对暂不可交易
	ErrMsgSpotOverMaxSideEntrust                    = newReplyError(ErrCodeSpotOverMaxSideEntrust)        // 超出单边最大挂单量
	ErrMsgSpotTradeLimit                            = newReplyError(ErrCodeSpotTradeLimit)                // 交易限制，请稍后再试。
	ErrMsgAccountFormat                             = newReplyError(ErrCodeAccountFormat)                 // 账号格式错误
	ErrMsgNonsupportPhoneNumber                     = newReplyError(ErrCodeNonsupportPhoneNumber)         // 暂不支持手机号

)

var ErrMap = map[int]*ReplyError{
	ErrCodeNonsupportPhoneNumber: ErrMsgNonsupportPhoneNumber,

	ErrCodeAccountFormat:                 ErrMsgAccountFormat,
	ErrCodeBusy:                          ErrMsgBusy,
	ErrCodeServerNotAvailable:            ErrMsgServerNotAvailable,
	ErrCodeParam:                         ErrMsgParam,
	ErrCodeSignature:                     ErrMsgSignature,
	ErrCodeResubmit:                      ErrMsgResubmit,
	ErrCodeShouldLogin:                   ErrMsgShouldLogin,
	ErrCodeUserNotExist:                  ErrMsgUserNotExist,
	ErrCodeTradeNotAllowed:               ErrMsgTradeNotAllowed,
	ErrCodeBalanceInsufficient:           ErrMsgBalanceInsufficient,
	ErrCodeTradeFrequent:                 ErrMsgTradeFrequent,
	ErrCodePlanOrderLimit:                ErrMsgPlanOrderLimit,
	ErrCodeDepthInvalid:                  ErrMsgDepthInvalid,
	ErrCodePriceOverLimit:                ErrMsgPriceOverLimit,
	ErrCodePriceInvalid:                  ErrMsgPriceInvalid,
	ErrCodeInMaintenance:                 ErrMsgInMaintenance,
	ErrCodeOrderIdUserd:                  ErrMsgOrderIdUserd,
	ErrCodeUnfinishedEntrustOrderTooMany: ErrMsgUnfinishedEntrustOrderTooMany,
	ErrCodeOrderState:                    ErrMsgOrderState,
	ErrCodeEntrustBelowMinOpenVolume:     ErrMsgEntrustBelowMinOpenVolume,
	ErrCodeEntrustOverMaxOpenVolume:      ErrMsgEntrustOverMaxOpenVolume,
	ErrCodeEntrustBelowMinOpenValue:      ErrMsgEntrustBelowMinOpenValue,
	ErrCodeEntrustOverMaxOpenValue:       ErrMsgEntrustOverMaxOpenValue,
	ErrCodeSpotNotExist:                  ErrMsgSpotNotExist,
	ErrCodeSpotNotTrade:                  ErrMsgSpotNotTrade,
	ErrCodeSpotOverMaxSideEntrust:        ErrMsgSpotOverMaxSideEntrust,
	ErrCodeSpotTradeLimit:                ErrMsgSpotTradeLimit,
}

var errMsgMap = map[ReqLang]map[int]string{
	ReqLangCN: {
		ErrCodeBusy:                          NormalErrMsg,
		ErrCodeServerNotAvailable:            "服务暂时不可用",
		ErrCodeParam:                         "请求参数错误",
		ErrCodeSignature:                     "签名验证失败",
		ErrCodeResubmit:                      "请勿重复提交",
		ErrCodeShouldLogin:                   "请先登录",
		ErrCodeUserNotExist:                  "用户不存在",
		ErrCodeTradeNotAllowed:               "当前用户被禁止交易",
		ErrCodeBalanceInsufficient:           "可用余额不足",
		ErrCodeTradeFrequent:                 "委托请求频繁",
		ErrCodePlanOrderLimit:                "您合约未触发的计划单已达到10条",
		ErrCodeDepthInvalid:                  "当前深度不足",
		ErrCodePriceOverLimit:                "当前委托价格超过允许范围",
		ErrCodePriceInvalid:                  "当前委托价格无效",
		ErrCodeInMaintenance:                 "该合约正在维护中，请稍后再试",
		ErrCodeOrderIdUserd:                  "订单号已存在",
		ErrCodeUnfinishedEntrustOrderTooMany: "当前未完成委托数过多",
		ErrCodeOrderState:                    "当前订单状态已更新,请刷新后重试",
		ErrCodeEntrustBelowMinOpenVolume:     "委托数量请大于最小下单数量%s%s",
		ErrCodeEntrustOverMaxOpenVolume:      "委托数量请小于最大下单数量%s%s",
		ErrCodeEntrustBelowMinOpenValue:      "委托金额请大于最小下单金额%s%s",
		ErrCodeEntrustOverMaxOpenValue:       "委托金额请小于最大下单金额%s%s",
		ErrCodeSpotNotExist:                  "交易对不存在",
		ErrCodeSpotNotTrade:                  "交易对暂不可交易",
		ErrCodeSpotOverMaxSideEntrust:        "超出单边最大挂单量",
		ErrCodeSpotTradeLimit:                "交易限制，请稍后再试。",
	},
	ReqLangEN: {
		ErrCodeBusy:                          "The system is busy",
		ErrCodeServerNotAvailable:            "Service Temporarily Unavailable",
		ErrCodeParam:                         "Request parameter error",
		ErrCodeSignature:                     "Sign verification failed",
		ErrCodeResubmit:                      "Please click only once",
		ErrCodeShouldLogin:                   "Please sign in first",
		ErrCodeUserNotExist:                  "User does not exist",
		ErrCodeTradeNotAllowed:               "The current user is prohibited transaction",
		ErrCodeBalanceInsufficient:           "Insufficient available balance",
		ErrCodeTradeFrequent:                 "Frequent delegation requests",
		ErrCodePlanOrderLimit:                "10 contract has not triggered",
		ErrCodeDepthInvalid:                  "Current depth is insufficient",
		ErrCodePriceOverLimit:                "The current commission price exceeds the allowable range",
		ErrCodePriceInvalid:                  "The current order price is invalid",
		ErrCodeInMaintenance:                 "The contract is under maintenance, please try again later",
		ErrCodeOrderIdUserd:                  "Order number already exists",
		ErrCodeUnfinishedEntrustOrderTooMany: "Too many outstanding orders",
		ErrCodeOrderState:                    "The current order status has been updated, please refresh and try again",
		ErrCodeEntrustBelowMinOpenVolume:     "The order quantity should be greater than the minimum order quantity of %s %s",
		ErrCodeEntrustOverMaxOpenVolume:      "The order quantity should be less than the maximum order quantity of %s %s",
		ErrCodeEntrustBelowMinOpenValue:      "The entrusted amount should be greater than the minimum order amount of %s %s",
		ErrCodeEntrustOverMaxOpenValue:       "The entrusted amount should be less than the maximum order amount of %s %s",
		ErrCodeSpotNotExist:                  "trading pair does not exist",
		ErrCodeSpotNotTrade:                  "The trading pair is temporarily unavailable",
		ErrCodeSpotOverMaxSideEntrust:        "Exceeds the maximum pending order amount on one side",
		ErrCodeSpotTradeLimit:                "Transaction limit, please try again later.",
	},
	ReqLangTC: {
		ErrCodeBusy:                          "系統繁忙,請稍後再試",
		ErrCodeServerNotAvailable:            "服務暫時不可用",
		ErrCodeParam:                         "請求參數錯誤",
		ErrCodeSignature:                     "簽名驗證失敗",
		ErrCodeResubmit:                      "請勿重複提交",
		ErrCodeShouldLogin:                   "請先登錄",
		ErrCodeUserNotExist:                  "用戶不存在",
		ErrCodeTradeNotAllowed:               "當前用戶被禁止交易",
		ErrCodeBalanceInsufficient:           "可用餘額不足",
		ErrCodeTradeFrequent:                 "委託請求頻繁",
		ErrCodePlanOrderLimit:                "您合約未觸發的計劃單已達到10條",
		ErrCodeDepthInvalid:                  "當前深度不足",
		ErrCodePriceOverLimit:                "當前委託價格超過允許範圍",
		ErrCodePriceInvalid:                  "當前委託價格無效",
		ErrCodeInMaintenance:                 "該合約正在維護中，請稍後再試",
		ErrCodeOrderIdUserd:                  "訂單號已存在",
		ErrCodeUnfinishedEntrustOrderTooMany: "當前未完成委託數過多",
		ErrCodeOrderState:                    "當前訂單狀態已更新，請重繪後重試",
		ErrCodeEntrustBelowMinOpenVolume:     "委託數量請大於最小下單數量%s%s",
		ErrCodeEntrustOverMaxOpenVolume:      "委託數量請小於最大下單數量%s%s",
		ErrCodeEntrustBelowMinOpenValue:      "委託金額請大於最小下單金額%s%s",
		ErrCodeEntrustOverMaxOpenValue:       "委託金額請小於最大下單金額%s%s",
		ErrCodeSpotNotExist:                  "交易對不存在",
		ErrCodeSpotNotTrade:                  "交易對暫不可交易",
		ErrCodeSpotOverMaxSideEntrust:        "超出單邊最大掛單量",
		ErrCodeSpotTradeLimit:                "交易限制，請稍後再試。",
	},
	ReqLangKR: {
		ErrCodeBusy:                          "시스템이 바쁘니 잠시 후에 다시 시도해 주십시오.",
		ErrCodeServerNotAvailable:            "서비스가 일시적으로 사용 불가능합니다",
		ErrCodeParam:                         "요청 인자 오류",
		ErrCodeSignature:                     "서명 검증 실패",
		ErrCodeResubmit:                      "중복 제출하지 마시오",
		ErrCodeShouldLogin:                   "먼저 로그인 하세요",
		ErrCodeUserNotExist:                  "사용자가 존재하지 않습니다",
		ErrCodeTradeNotAllowed:               "현재 사용자의 거래 금지",
		ErrCodeBalanceInsufficient:           "가용잔고부족",
		ErrCodeTradeFrequent:                 "청탁이 잦다",
		ErrCodePlanOrderLimit:                "계약에 의해 실행되지 않은 계획 주문이 10 개 있습니다.",
		ErrCodeDepthInvalid:                  "현재 깊이가 충분하지 않습니다.",
		ErrCodePriceOverLimit:                "현재 커미션 가격이 허용 범위를 초과합니다.",
		ErrCodePriceInvalid:                  "현재 주문 가격이 잘못되었습니다.",
		ErrCodeInMaintenance:                 "계약이 유지 보수 중입니다. 나중에 다시 시도하십시오.",
		ErrCodeOrderIdUserd:                  "주문 번호가 이미 있습니다.",
		ErrCodeUnfinishedEntrustOrderTooMany: "미결제 주문이 너무 많습니다.",
		ErrCodeOrderState:                    "현재 주문 상태가 업데이트되었으므로 새로 고치고 다시 시도하십시오",
		ErrCodeEntrustBelowMinOpenVolume:     "주문 수량은 최소 주문 수량인 %s %s보다 커야 합니다.",
		ErrCodeEntrustOverMaxOpenVolume:      "주문 수량은 최대 주문 수량인 %s %s 미만이어야 합니다.",
		ErrCodeEntrustBelowMinOpenValue:      "위탁 금액은 최소 주문 금액인 %s %s 이상이어야 합니다.",
		ErrCodeEntrustOverMaxOpenValue:       "위탁 금액은 최대 주문 금액인 %s %s 미만이어야 합니다.",
		ErrCodeSpotNotExist:                  "거래 쌍이 존재하지 않습니다",
		ErrCodeSpotNotTrade:                  "거래 쌍을 일시적으로 사용할 수 없습니다.",
		ErrCodeSpotOverMaxSideEntrust:        "한 쪽의 최대 보류 주문 금액을 초과합니다.",
		ErrCodeSpotTradeLimit:                "거래 한도가 있습니다. 나중에 다시 시도해 주세요.",
	},
	ReqLangVN: {
		ErrCodeBusy:                          "Hệ thống đang bận, vui lòng thử lại sau",
		ErrCodeServerNotAvailable:            "Dịch vụ tạm thời không có",
		ErrCodeParam:                         "tham số yêu cầu sai",
		ErrCodeSignature:                     "Xác minh chữ ký không thành công",
		ErrCodeResubmit:                      "Không gửi lại",
		ErrCodeShouldLogin:                   "vui lòng đăng nhập trước",
		ErrCodeUserNotExist:                  "người dùng không tồn tại",
		ErrCodeTradeNotAllowed:               "Người dùng hiện tại bị cấm giao dịch",
		ErrCodeBalanceInsufficient:           "Không đủ số dư khả dụng",
		ErrCodeTradeFrequent:                 "Yêu cầu ủy quyền là thường xuyên",
		ErrCodePlanOrderLimit:                "Hợp đồng của bạn chưa kích hoạt 10 đơn đặt hàng theo kế hoạch",
		ErrCodeDepthInvalid:                  "Không đủ độ sâu hiện tại",
		ErrCodePriceOverLimit:                "Giá đặt hàng hiện tại vượt quá phạm vi cho phép",
		ErrCodePriceInvalid:                  "Giá đặt hàng hiện tại không hợp lệ",
		ErrCodeInMaintenance:                 "Hợp đồng này đang được bảo trì, vui lòng thử lại sau",
		ErrCodeOrderIdUserd:                  "Số đơn hàng đã tồn tại",
		ErrCodeUnfinishedEntrustOrderTooMany: "Quá nhiều đơn đặt hàng chưa hoàn thành",
		ErrCodeOrderState:                    "Trạng thái đơn hàng hiện tại đã được cập nhật, vui lòng làm mới và thử lại",
		ErrCodeEntrustBelowMinOpenVolume:     "Số lượng đặt hàng phải lớn hơn số lượng đặt hàng tối thiểu là %s %s",
		ErrCodeEntrustOverMaxOpenVolume:      "Số lượng đặt hàng phải nhỏ hơn số lượng đặt hàng tối đa là %s %s",
		ErrCodeEntrustBelowMinOpenValue:      "Số tiền được ủy thác phải lớn hơn số tiền đặt hàng tối thiểu là %s %s",
		ErrCodeEntrustOverMaxOpenValue:       "Số tiền được ủy thác phải nhỏ hơn số tiền đặt hàng tối đa là %s %s",
		ErrCodeSpotNotExist:                  "cặp giao dịch không tồn tại",
		ErrCodeSpotNotTrade:                  "Cặp giao dịch tạm thời không khả dụng",
		ErrCodeSpotOverMaxSideEntrust:        "Vượt quá số lượng đơn đặt hàng đang chờ xử lý tối đa ở một bên",
		ErrCodeSpotTradeLimit:                "Hạn mức giao dịch, vui lòng thử lại sau.",
	},
	ReqLangID: {
		ErrCodeBusy:                          "Sistem sedang sibuk, silakan coba lagi nanti",
		ErrCodeServerNotAvailable:            "Layanan tidak tersedia untuk sementara",
		ErrCodeParam:                         "parameter permintaan yang salah",
		ErrCodeSignature:                     "Verifikasi tanda tangan gagal",
		ErrCodeResubmit:                      "Jangan kirim ulang",
		ErrCodeShouldLogin:                   "silahkan masuk terlebih dahulu",
		ErrCodeUserNotExist:                  "pengguna tidak ada",
		ErrCodeTradeNotAllowed:               "Pengguna saat ini dilarang berdagang",
		ErrCodeBalanceInsufficient:           "Saldo yang tersedia tidak mencukupi",
		ErrCodeTradeFrequent:                 "Permintaan delegasi sering terjadi",
		ErrCodePlanOrderLimit:                "Kontrak Anda belum memicu 10 pesanan yang direncanakan",
		ErrCodeDepthInvalid:                  "Kedalaman arus tidak mencukupi",
		ErrCodePriceOverLimit:                "Harga pesanan saat ini melebihi kisaran yang diizinkan",
		ErrCodePriceInvalid:                  "Harga pesanan saat ini tidak valid",
		ErrCodeInMaintenance:                 "Kontrak ini sedang dalam pemeliharaan, silakan coba lagi nanti",
		ErrCodeOrderIdUserd:                  "Nomor pesanan sudah ada",
		ErrCodeUnfinishedEntrustOrderTooMany: "Terlalu banyak pesanan yang belum selesai",
		ErrCodeOrderState:                    "Status pesanan saat ini telah diperbarui, harap segarkan dan coba lagi",
		ErrCodeEntrustBelowMinOpenVolume:     "Jumlah pesanan harus lebih besar dari jumlah pesanan minimum %s %s",
		ErrCodeEntrustOverMaxOpenVolume:      "Jumlah pesanan harus kurang dari jumlah pesanan maksimum %s %s",
		ErrCodeEntrustBelowMinOpenValue:      "Jumlah yang dipercayakan harus lebih besar dari jumlah pesanan minimum %s %s",
		ErrCodeEntrustOverMaxOpenValue:       "Jumlah yang dipercayakan harus kurang dari jumlah pesanan maksimum %s %s",
		ErrCodeSpotNotExist:                  "pasangan perdagangan tidak ada",
		ErrCodeSpotNotTrade:                  "Pasangan perdagangan untuk sementara tidak tersedia",
		ErrCodeSpotOverMaxSideEntrust:        "Melebihi jumlah pesanan tertunda maksimum di satu sisi",
		ErrCodeSpotTradeLimit:                "Batas transaksi, silakan coba lagi nanti.",
	},
	ReqLangRU: {
		ErrCodeBusy:                          "Система занята",
		ErrCodeServerNotAvailable:            "Сервис временно недоступен",
		ErrCodeParam:                         "неверный параметр запроса",
		ErrCodeSignature:                     "Ошибка проверки подписи",
		ErrCodeResubmit:                      "Не отправлять повторно",
		ErrCodeShouldLogin:                   "Пожалуйста, войдите сначала",
		ErrCodeUserNotExist:                  "Пользователь не существует",
		ErrCodeTradeNotAllowed:               "Текущему пользователю запрещено торговать",
		ErrCodeBalanceInsufficient:           "Недостаточно доступного баланса",
		ErrCodeTradeFrequent:                 "Частые запросы делегирования",
		ErrCodePlanOrderLimit:                "Ваш контракт не вызвал 10 запланированных заказов",
		ErrCodeDepthInvalid:                  "Недостаточная текущая глубина",
		ErrCodePriceOverLimit:                "Текущая цена ордера превышает допустимый диапазон",
		ErrCodePriceInvalid:                  "Текущая цена заказа недействительна",
		ErrCodeInMaintenance:                 "Этот контракт находится на техническом обслуживании, повторите попытку позже.",
		ErrCodeOrderIdUserd:                  "Номер заказа уже существует",
		ErrCodeUnfinishedEntrustOrderTooMany: "Слишком много невыполненных заказов",
		ErrCodeOrderState:                    "Текущий статус заказа обновлен, обновите и повторите попытку.",
		ErrCodeEntrustBelowMinOpenVolume:     "Количество заказа должно быть больше минимального количества заказа %s %s.",
		ErrCodeEntrustOverMaxOpenVolume:      "Сумма заказа должна быть меньше максимальной суммы заказа %s %s.",
		ErrCodeEntrustBelowMinOpenValue:      "Доверенная сумма должна быть больше минимальной суммы заказа %s %s.",
		ErrCodeEntrustOverMaxOpenValue:       "Сумма заказа должна быть меньше максимальной суммы заказа %s %s.",
		ErrCodeSpotNotExist:                  "торговая пара не существует",
		ErrCodeSpotNotTrade:                  "Торговая пара временно недоступна",
		ErrCodeSpotOverMaxSideEntrust:        "Превышает максимальную сумму отложенного ордера с одной стороны",
		ErrCodeSpotTradeLimit:                "Лимит транзакции, повторите попытку позже.",
	},
	ReqLangDE: {
		ErrCodeBusy:                          "Das System ist ausgelastet, bitte versuchen Sie es später erneut",
		ErrCodeServerNotAvailable:            "Der Dienst ist vorübergehend nicht verfügbar",
		ErrCodeParam:                         "falscher Anforderungsparameter",
		ErrCodeSignature:                     "Signaturprüfung gescheitert",
		ErrCodeResubmit:                      "Nicht erneut einreichen",
		ErrCodeShouldLogin:                   "Bitte loggen Sie sich zuerst ein",
		ErrCodeUserNotExist:                  "Benutzer existiert nicht",
		ErrCodeTradeNotAllowed:               "Der aktuelle Benutzer ist vom Handel ausgeschlossen",
		ErrCodeBalanceInsufficient:           "Unzureichendes verfügbares Guthaben",
		ErrCodeTradeFrequent:                 "Delegiertenanfragen sind häufig",
		ErrCodePlanOrderLimit:                "Ihr Vertrag hat keine 10 Auftragsvorschläge ausgelöst",
		ErrCodeDepthInvalid:                  "Unzureichende Stromtiefe",
		ErrCodePriceOverLimit:                "Der aktuelle Auftragspreis überschreitet den zulässigen Bereich",
		ErrCodePriceInvalid:                  "Der aktuelle Bestellpreis ist ungültig",
		ErrCodeInMaintenance:                 "Dieser Vertrag wird gewartet, bitte versuchen Sie es später erneut",
		ErrCodeOrderIdUserd:                  "Bestellnummer existiert bereits",
		ErrCodeUnfinishedEntrustOrderTooMany: "Zu viele unerledigte Bestellungen",
		ErrCodeOrderState:                    "Der aktuelle Bestellstatus wurde aktualisiert, bitte aktualisieren Sie und versuchen Sie es erneut",
		ErrCodeEntrustBelowMinOpenVolume:     "Die Bestellmenge sollte größer als die Mindestbestellmenge von %s %s sein",
		ErrCodeEntrustOverMaxOpenVolume:      "Die Bestellmenge sollte kleiner als die maximale Bestellmenge von %s %s sein",
		ErrCodeEntrustBelowMinOpenValue:      "Die Bestellsumme sollte größer als die Mindestbestellsumme von %s %s sein",
		ErrCodeEntrustOverMaxOpenValue:       "Die Bestellsumme sollte unter der maximalen Bestellsumme von %s %s liegen",
		ErrCodeSpotNotExist:                  "Handelspaar existiert nicht",
		ErrCodeSpotNotTrade:                  "Das Handelspaar ist vorübergehend nicht verfügbar",
		ErrCodeSpotOverMaxSideEntrust:        "Überschreitet den maximalen Pending-Order-Betrag auf einer Seite",
		ErrCodeSpotTradeLimit:                "Transaktionslimit, bitte versuchen Sie es später erneut.",
	},
}

// 注意 如果想使用这个方法请 添加对应的ErrMap
func NewReplyErrorByCode(Code int) *ReplyError {
	return ErrMap[Code]
}

// ReplyError 带错误码的错误
type ReplyError struct {
	Code     int
	Msg      string
	isFormat bool
	ErrData  []interface{}
}

func (e *ReplyError) Error() string {
	if e == nil {
		return ""
	}
	return e.LangErrMsg(ReqLangCN)
}

func (e *ReplyError) ErrMsg(lang ReqLang) string {
	if e == nil {
		return ""
	}
	errs, ok := errMsgMap[lang]
	if !ok {
		errs = errMsgMap[ReqLangCN]
	}
	msg, ok := errs[e.Code]
	if !ok {
		if len(e.Msg) == 0 {
			msg = NormalErrMsg
		} else {
			msg = e.Msg
		}
	}
	return msg
}

func (e *ReplyError) LangErrMsg(lang ReqLang) string {
	if e == nil {
		return ""
	}
	msg := e.ErrMsg(lang)
	if !e.isFormat {
		return msg
	}
	e.Msg = msg
	msg = fmt.Sprintf(e.Msg, e.ErrData...)
	return msg
}

// newReplyError 返回ReplyError
func newReplyError(code int) *ReplyError {
	return &ReplyError{Code: code}
}

// NewReplyErrorWithMsg 返回ReplyError
func NewReplyErrorWithMsg(code int, msg string) error {
	return &ReplyError{Code: code, Msg: msg}
}

// NewReplyErrorWithFormatData 错误携带数据
func NewReplyErrorWithFormatData(code int, data ...interface{}) error {
	return &ReplyError{Code: code, isFormat: true, ErrData: data}
}
