package define

import (
	"bytes"
	"fmt"
	"strings"
	"testing"

	"github.com/shopspring/decimal"
	"text/template"
)

func TestReplyErrorFullDefined(t *testing.T) {
	// 验证是否对错误都进行了翻译
	for code := range ErrMap {
		for lang, langMp := range errMsgMap {
			if _, ok := langMp[code]; !ok {
				t.Fatalf("验证未通过,错误缺少翻译,语言:%d,错误码:%d", lang, code)
			}
		}
	}
	t.Log("验证通过")
}

func TestNewReplyErrorWithFormatData(t *testing.T) {
	e := NewReplyErrorWithFormatData(ErrCodeEntrustBelowMinOpenVolume, "0", "BTC").(*ReplyError)
	t.Log(e.LangErrMsg(1))
	//t.Log(e.isFormat)
	//for _, datum := range e.ErrData {
	//	t.Log(datum)
	//
	//}
	//
	//s := "err %v,%d"
	//t.Log(strings.Count(s, "%"))
}

func TestPrintErrorMsg(t *testing.T) {
	list := make([]string, 1500)
	for key, val := range errMsgMap[0] {
		list[key] = val
	}
	t.Log(decimal.New(1, 5))

	for i := range list {
		if list[i] != "" {
			fmt.Println(list[i])
		}
	}
}

func TestTemplate(t *testing.T) {
	str := "你好,ass"
	//str:="你好,{{.name}},你{{.age}}"
	tm, err := template.New("test").Parse(str)
	var s bytes.Buffer
	err = tm.Execute(&s, map[string]string{"name": "liupeng", "age": "10"})
	if err != nil {
		t.Log("err", err)
		return
	}
	t.Logf("s:%v", s.String())

}

func TestF(t *testing.T) {
	t.Log(strings.Join([]string{"abc"}, ":"))
}
