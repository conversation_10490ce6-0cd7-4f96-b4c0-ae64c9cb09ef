/*
@Time : 2019-08-21 20:13
<AUTHOR> mocha
@File : finance
*/
package define

var FsCategoryMap = map[int]string{
	FsTradeFee:                        "交易手续费",
	FsFeeWithdraw:                     "提币手续费",
	FsAssetProfitAndLoss:              "盈亏收入",
	FsAssetFundingFee:                 "资金费用",
	FsAccountRecharge:                 "平台账户充币",
	FsAccountWithdraw:                 "平台账户提币",
	FsAccountSimSubsidy:               "模拟账户补领资产",
	FsAccountSimDeduct:                "模拟账户减少资产",
	FsAccountIncome:                   "转入",
	FsAccountIncomeTrade:              "转入到交易账户",
	FsAccountIncomeAsset:              "转入到资产账户",
	FsAccountOut:                      "转出",
	FsAccountOutAsset:                 "转出到资产账户",
	FsAccountOutTrade:                 "转出到交易账户",
	FsAccountFeeWithdraw:              "平台提币手续费",
	FsAccountInviteBrokerage:          "邀请佣金奖励",
	FsAccountAgentBrokerage:           "代理佣金奖励",
	FsAccountAirdrop:                  "空投",
	FsAccountC2CSettle:                "C2C结算",
	FsAccountC2COther:                 "C2C其他用途",
	FsAccountActivityReward:           "活动奖励",
	FsAccountAirdropOther:             "空投奖励",
	FsAccountAirdropActivity:          "手续费活动",
	FsAccountAirdropSystem:            "系统故障",
	FsAccountAirdropTeam:              "营销活动",
	FsAccountAirdropBack:              "返佣奖励",
	FsTradeBlowingFee:                 "爆仓清算费",
	FsTradeBlowingLoss:                "爆仓穿仓",
	FsUserLegalBuy:                    "用户法币买入垫资",
	FsUserLegalSell:                   "用户法币卖出垫资",
	FsTradeCloseOverLoss:              "平仓穿仓",
	FsAccountFrozenWallet:             "异常资产扣除(用户资产账户)",
	FsAccountFrozenTrade:              "异常资产扣除(用户交易账户)",
	FsAccountFrozenFollow:             "异常资产扣除(用户跟单账户)",
	FsAccountCoinExchangeFee:          "用户兑出手续费",
	FsAccountCoinExchangeOut:          "平台兑出",
	FsAccountCoinExchangeIn:           "平台兑入",
	FsAccountTransfer2Charge:          "转出至充提账户", // 转出到充提账户
	FsAccountTransferFromCharge:       "由充提账户转入", // 从充提账户转入
	FsAccountIncomeExchange:           "由兑币账户转入", // 从充提账户转入
	FsAccountTransferFromAsset:        "由资产账户转入", // 从充提账户转入
	FsAccountGiftIn:                   "赠金领取",    //
	FsAccountGiftExpireOut:            "赠金过期",    //
	FsAccountGiftReturn:               "收回赠金",    //
	FsAccountGiftFeeDeduction:         "赠金交易抵扣",  //
	FsAccountGiftFeeDeduction:         "赠金手续费",   // 赠金手续费
	FsAccountGiftCloseProfitDeduction: "赠金盈亏",    // 赠金盈亏
	FsAccountGiftBlowingDeduction:     "赠金爆仓清算费", // 赠金爆仓费用
}

var (
	FsTradeFee                        = 1   // 交易手续费
	FsFeeWithdraw                     = 4   // 提币手续费
	FsAssetProfitAndLoss              = 8   // 盈亏收入
	FsAssetFundingFee                 = 9   // 资金费用
	FsAccountRecharge                 = 100 // 平台账户充币
	FsAccountWithdraw                 = 200 // 平台账户提币
	FsAccountSimSubsidy               = 201 // 模拟盘领取资产
	FsAccountSimDeduct                = 202 // 模拟盘扣除资产
	FsAccountIncome                   = 300 // 转入
	FsAccountIncomeTrade              = 301 // 转入到交易账户
	FsAccountIncomeAsset              = 302 // 转入到资产账户
	FsAccountOut                      = 310 // 转出
	FsAccountOutTrade                 = 311 // 转出到交易账户
	FsAccountOutAsset                 = 312 // 转出到资产账户
	FsAccountFeeWithdraw              = 320 // 平台提币手续费
	FsAccountInviteBrokerage          = 330 // 邀请佣金奖励
	FsAccountAgentBrokerage           = 331 // 代理佣金奖励
	FsAccountAirdrop                  = 332 // 空投(弃用)
	FsAccountC2CSettle                = 333 // C2C结算
	FsAccountC2COther                 = 334 // C2C其他用途
	FsAccountActivityReward           = 341 // 活动奖励
	FsAccountAirdropOther             = 343 // 空投奖励
	FsAccountAirdropActivity          = 344 // 手续费活动
	FsAccountAirdropSystem            = 345 // 系统故障
	FsAccountAirdropTeam              = 346 // 营销活动
	FsAccountAirdropBack              = 347 // 返佣奖励
	FsTradeBlowingFee                 = 350 // 爆仓手续费
	FsTradeBlowingLoss                = 351 // 全仓穿仓
	FsUserLegalBuy                    = 352 // 用户法币买入
	FsUserLegalSell                   = 353 // 用户法币卖出
	FsTradeCloseOverLoss              = 354 // 平仓穿仓
	FsAccountFrozenWallet             = 360 // 异常资产扣除(用户资产账户)
	FsAccountFrozenTrade              = 361 // 异常资产扣除(用户交易账户)
	FsAccountFrozenFollow             = 362 // 异常资产扣除(用户跟单账户)
	FsAccountCoinExchangeFee          = 363 // 兑币手续费
	FsAccountCoinExchangeOut          = 364 // 平台兑出
	FsAccountCoinExchangeIn           = 365 // 平台兑入
	FsAccountTransfer2Charge          = 366 // 转出到充提账户
	FsAccountTransferFromCharge       = 367 // 从充提账户转入
	FsAccountIncomeExchange           = 368 // 从兑币账户转入
	FsAccountTransferFromAsset        = 369 // 由资产账户转入
	FsAccountGiftIn                   = 370 // 赠金领取
	FsAccountGiftExpireOut            = 380 // 赠金过期
	FsAccountGiftReturn               = 381 // 收回赠金
	FsAccountGiftFeeDeduction         = 382 // 赠金手续费
	FsAccountGiftCloseProfitDeduction = 383 // 赠金盈亏
	FsAccountGiftBlowingDeduction     = 384 // 赠金爆仓费用
)

// 1-交易手续费 4-提币手续费 8-盈亏收入

const (
	WalletTypeAsset    = 1 //资产账户
	WalletTypeTrade    = 2 //交易账户
	WalletTypeLegal    = 3 //法币
	WalletTypeExchange = 4 //兑币账户
	WalletTypeCharge   = 5 //充提账户
)
