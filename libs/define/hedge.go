package define

const (
	HedgeTypeSlave = 0
	HedgeTypeMain  = 1
)

const (
	HedgeApiDefault = 0
	HedgeApiSuccess = 1
)

const (
	HedgePosOverValue = 4200
	//HedgePosOverValue = 20
)

var TransContractType = map[int]string{
	0: "永续合约",
	1: "交割合约",
}

const (
	HedgeBuy  = "long"
	HedgeSell = "short"
)

const (
	HedgeOpenBuy   = 1
	HedgeOpenSell  = 2
	HedgeCloseBuy  = 3
	HedgeCloseSell = 4
)

var TransHedgeOrderType = map[int]string{
	HedgeOpenBuy:   "开多",
	HedgeOpenSell:  "开空",
	HedgeCloseBuy:  "平多",
	HedgeCloseSell: "平空",
}

//warning

//var WarningType=map[int]string

const (
	WaringTypeAsset      = "资产类"
	WaringTypeTrade      = "交易类"
	WaringTypeAPIStatus  = "api状态类"
	WaringTypeMarket     = "行情数据类"
	WaringTypeEfficiency = "效率类"
)

const NoUse = "NoUse"
const WSDataManul = "WSDataManul"

const (
	MarketSubTypeBuySellDiff = "1"
	MarketSubTypeVolatility  = "2"
)

const (
	MarketWarnDepthCopy                  = "1001"
	MarketWarnSpotIndexException         = "1002"
	MarketWarnMultiDataException         = "1003"
	MarketWarnSingleSideExtremeException = "1004"
	MarketWarnSingleSideException        = "1005"
	MarketWarnLeverFactorOverException   = "1006"
	MarketWarnContinueException          = "1007"
	MarketWarnPanPriceDiffException      = "1008"
	MarketWarnSingleContinueException    = "1009"

	MarketWarnSpotIndexProtectRemoveException   = "1010"
	MarketWarnSpotIndexProtectLongTimeException = "1011"
	MarketWarnSpotIndexUseBackupException       = "1012"
	MarketWarnSpotIndexStartProtectException    = "1013"
	MarketSingleRealException                   = "1014"
)

const (
	HedgingEfficiencyCautionLv1Content = "1 级警报，%s 平台对冲效率异常，已经持续 %d 分钟，可能需要调整做市流动性或者切换对冲平台"
	HedgingEfficiencyCautionLv2Content = "2 级警报，%s 平台对冲效率异常，已经持续 %d 分钟，可能需要切换对冲平台"
)

const (
	HedgingAssetCautionLv1Content = "当前 %s 对冲账户 %s，累计亏损已经达到初始总投入本金的 %s 比例（参数对冲账户累计亏损比例），存在保证金不足风险，请关注"
	HedgingAssetCautionLv2Content = "当前 %s 对冲账户 %s，累计亏损已经达到初始总投入本金的 %s 比例，保证金面临不足，对冲策略可能需要调整"
	HedgingAssetCautionLv3Content = "当前 %s 对冲账户 %s，累计亏损已经达到初始总投入本金的 %s 比例，保证金不足，对冲策略异常，请立即处理"
)

const (
	HedgingMarginCautionContent = "当前 %s 对冲账户 %s 因档位梯度存在面临限仓，将影响对冲执行，请处理"
)

const (
	HedgingApiCautionContent = "%s 合约 %s 对冲账户状态存在异常，请立即上线处理: %s"
)
