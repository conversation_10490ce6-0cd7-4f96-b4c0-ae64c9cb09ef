package define

type LegalOrderState int

const (
	//0-订单待审核 1 等待买家付款; 2 卖家确认收款／等待卖家发货; 3用户取消; 4 商家取消; 5 超时取消; 6交易完成(已放币）; 7：补充放款; 8-后台拒绝
	LegalOrderStateDefault        LegalOrderState = iota
	LegalOrderStateWaitPay                        // 1-等待买家付款
	LegalOrderStateWaitRelease                    // 2-等待放币(卖家确认收款／等待卖家发货)
	LegalOrderStateCustomerCancel                 // 3-客户取消
	LegalOrderStateBusinessCancel                 // 4-商家取消
	LegalOrderStateTimeoutCancel                  // 5-超时取消
	LegalOrderStateFinish                         // 6-交易完成
	LegalOrderStateReissue                        // 7-补充放款
	LegalOrderStateAuditCancel                    // 8-后台拒绝
)

type LegalOrderApiState int

const (
	LegalOrderApiStatePending LegalOrderApiState = iota + 1 // 1-进行中
	LegalOrderApiStateFinish                                // 2-已完成
	LegalOrderApiStateCancel                                // 3-已取消
)
