package define

import "time"

const (
	RepeatRequestDuration = 2 * time.Minute
)

const (
	IdentifierUser        = 0 //普通用户
	IdentifierBusiness    = 1 //做市商（普通用户一样）
	IdentifierMarketRobot = 2 //做市商机器人（无账户）
)

const (
	MatchOverDefault = "freeCancel"        //策略撤销，剩余的部分撤销
	MatchOverForce   = "freeProtocolClose" //强平，剩余协议平仓
	MatchTradeOver   = "tradeAll"
)

const (
	CancelOrderFinish        = 0 //订单完成
	CancelMarkUser           = 1 //用户撤销标记
	CancelOrderDrop          = 2 //下单失败废弃
	CancelMarkRiskController = 3 //风险控制转撤销
	CancelMarkRiskData       = 4 //系统成交风险转撤销（成交数据重复、资金不足）

)

const (
	TradeMarkWithOrderDefault = 0 //以订单状态为主
	TradeMarkWithTradeFinish  = 1 //标记为全部成交
)

const (
	ActionPlace  = "place"
	ActionCancel = "cancel"
)
