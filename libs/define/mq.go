/*
@Time : 3/5/20 8:20 上午
<AUTHOR> mocha
@File : mq
*/
package define

const (
	MQReliable = true
)

const (
	MQDefaultExchangeName           = "symbol.basecoin"
	MQDefaultMQBindKey              = "base.coin.key"
	MQDefaultMQBindKeyClosing       = "base.key.closing"
	MQConsumerPushQueueName         = "push"
	MQConsumerMarketQueueName       = "market"
	MQConsumerPriceQueueName        = "price"
	MQConsumerKlineQueueName        = "kline"
	MQConsumerCoreQueueName         = "core"
	MQConsumerOrderQueueName        = "order"
	MQConsumerPlanOrderQueueName    = "plan.order"
	MQConsumerTaskQueueName         = "task"
	MQConsumerClosingQueueName      = "closing"
	MQConsumerClosingStateQueueName = "ClosingState"
	MQConsumerMatchQueueName        = "match"
	MQConsumerBucketingQueueName    = "bucketing"
)

//market push
const (
	MQTopicRawDepth        = "mq.topic.raw.depth" //原始深度
	MQTopicDepth           = "mq.topic.depth"
	MQTopicPriceIndex      = "mq.topic.price.index"              //成交价格买一卖一触发
	MQTopicComplexPrice    = "mq.topic.market.complex.price"     //综合标记价格推送
	MQTopicSpotIndexPrice  = "mq.topic.market.spot.index.price"  //现货价格推送
	MQTopicDepthIndexPrice = "mq.topic.market.depth.index.price" //铺单基准价格推送
	MQTopicNewTrade        = "mq.topic.order.ticker"             //market生成，push消费（保留)
	MQTopicNewTrades       = "mq.topic.order.tickers"            //market生成，push消费（保留)
)

//core,follow push

const (
	MQTopicPlatFormTrade        = "mq.topic.self.trade"             //平台自己成交数据(market消费后推送给push)
	MQTopicPositionUpdate       = "mq.topic.core.position.update"   //用户持仓变化(数量）
	MQTopicFollowPositionUpdate = "mq.topic.follow.position.update" //用户跟单持仓变化(数量）

	MQTopicCorePlanCloseAdd    = "mq.topic.core.plan.close.add"    //计划平仓订单添加
	MQTopicCorePlanCloseUpdate = "mq.topic.core.plan.close.update" //计划平仓订单添加
	MQTopicCorePlanCloseDel    = "mq.topic.core.plan.close.del"    //计划平仓单移除

	MQTopicPlanOpenAdd = "mq.topic.plan.open.add" //计划开仓订单添加
	MQTopicPlanOpenDel = "mq.topic.plan.open.del" //计划开仓单移除

	MQTopicContractChange = "mq.topic.contract.change" //合约信息变动
)

//follow
const (
	MQTopicFollowPlanCloseAdd    = "mq.topic.follow.plan.close.add"    //计划平仓订单添加
	MQTopicFollowPlanCloseUpdate = "mq.topic.follow.plan.close.update" //计划平仓订单添加
	MQTopicFollowPlanCloseDel    = "mq.topic.follow.plan.close.del"    //计划平仓单移除
)

//kline push
const (
	MQTopicAccountRiskRate = "mq.topic.account.risk.rate"
	MQTopicMessage         = "mq.topic.message" //消息内容
	MQTopicContractApplies = "mq.topic.applies" //消费match最新成交消息,生成kline,计算涨跌幅并推送到push
)

//trigger
const (
	MQTopicUserPositionDynamic = "mq.topic.user.position.dynamic" //持仓动态推送数据
	MQTopicPlanOrderOpen       = "mq:topic.plan.order.trigger"    //开仓触发消息 （条件单开仓）
)

//match
const (
	MQTopicMatchCancel         = "mq.topic.match.order.cancel"         //撮合订单撤销
	MQTopicMatchTrade          = "mq.topic.match.trade"                //撮合成交
	MQTopicMatchTradeOver      = "mq.topic.match.over"                 //处理订单撮合完成
	MQTopicMatchBatchTrade     = "mq.topic.contract.batch.match.trade" //撮合成交(批量）
	MQTopicMatchBatchTradeOver = "mq.topic.contract.batch.match.over"  //处理订单撮合完成（批量）
)

//msgsrv
const (
	MQTopicUserOrderTrade = "mq.topic.order.trade" //订单成交消息
	MQTopicUserOrderOver  = "mq.topic.order.over"  //订单完成消息
)

//closing
const (
	MQTopicOrderStatusChange = "mq.topic.order.status.change" //委托订单状态变化
)

const (
	EventAdd = 1
	EventDel = 2
)

const (
	MQTopicLimitOrderAdd    = "mq.topic.limit.order.add"    //限价单下单
	MQTopicLimitOrderRemove = "mq.topic.limit.order.remove" //限价单撤销
)
