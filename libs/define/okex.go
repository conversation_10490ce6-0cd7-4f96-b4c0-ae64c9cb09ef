package define

//30001-30013 api无效
var OkexErrMap = map[string]string{
	"30014": "请求太频繁",
	"30026": "请求超限",
	"33013": "下单失败",
	"30035": "暂停交易",
}

const (
	WarningAssetLess            = "可用数量低于阈值:%vUSDT;"
	WarningAssetMarginNotEnough = "保证金率小于 %v%%;"
	WarningAssetHedgeDiff       = "对冲账户与平台持仓净值偏离度 %v；"

	WarningTradeDoublePosition   = " %v持仓方向出现双边持仓；"
	WarningTradePlaceFail        = " 下单失败：%s;"
	WarningTradePlaceAssetEnough = "下单失败：可用数量不足；"
	WarningTradePlaceOkFail      = "委托失败：委托编号:%v;"
	WarningTradePositionOver     = "%v持仓张数大于阈值：%v张；"

	WarningApiAccessOver = "策略并发数超限；"
	WarningApiSignFail   = "签名错误;"
	WarningApiBreak      = "连接断开;"
)
