/*
@Time : 3/5/20 7:33 下午
<AUTHOR> mocha
@File : order
*/
package define

import (
	"math/rand"
	"time"
)

const (
	SystemTradeLevel = 5 //系统配置的市价最大成交档位
)

const (
	OrderAll  = "A" // 全部,当前用于全仓杠杆标记
	OrderBuy  = "B"
	OrderSell = "S"
)

func RandSide() string {
	a := []string{OrderBuy, OrderSell}
	rand.Seed(time.Now().UnixNano())
	p := rand.Perm(2)
	return a[p[0]]
}

//用户账户类型
const (
	UserAccountTypeFullHouse = 1 //1：全仓
	UserAccountTypeWareHouse = 2 //2：逐仓
	UserAccountTypeFollow    = 3 //2：跟单
)

// AccountType 持仓账户类型
type AccountType int

const (
	AccountTypeNoHold           AccountType = iota // 没有持仓
	AccountTypeByFullHouse                         // 1：全仓
	AccountTypeByWareHouse                         // 2：逐仓
	AccountTypeByFollow                            // 3：跟单-带单
	AccountTypeByFollowUp                          // 4：跟单-跟单
	AccountTypeByFullHouseSplit                    // 5：全仓-分仓
	AccountTypeByWareHouseSplit                    // 6：逐仓-分仓
)

func (u AccountType) GetUserAccountType() int {
	if u == AccountTypeByFullHouseSplit || u == AccountTypeByFullHouse {
		return UserAccountTypeFullHouse
	}
	if u == AccountTypeByWareHouseSplit || u == AccountTypeByWareHouse {
		return UserAccountTypeWareHouse
	}
	return int(u)
}

const (
	// OrderTypeUserTrade 订单类型 0: 用户自主下单 1：计划单 2：止盈单 4：止损单 5：强平单 6-条件平仓 7：带单平仓 8-带单止盈 9-带单止损
	OrderTypeUserTrade     = 0 // 用户自主下单
	OrderTypePlan          = 1 // 计划单
	OrderTypeLimit         = 2 // 止盈单
	OrderTypeStop          = 4 // 止损单
	OrderTypeForceCloseOut = 5 // 强平单
	OrderTypePlanCloseOut  = 6 // 条件平仓
	OrderTypeFollowClose   = 7 // 带单平仓(跟单者被动平仓）
	OrderTypeFollowLimit   = 8 // 带单止盈
	OrderTypeFollowStop    = 9 // 带单止损
)

const (
	IsDeleteDropOrder = true
)

//订单状态
type OrderState int

const (
	OrderStatusTemp          OrderState = 0   //订单临时状态，未上报
	OrderStatusDefault       OrderState = 1   //未成交
	OrderStatusPart          OrderState = 100 //部分成交  （订单未结束）
	OrderStatusFull          OrderState = 200 //全部成交
	OrderStatusNotDealCancel OrderState = 201 //未成已撤
	OrderStatusPartCancel    OrderState = 202 //部分成交已撤
	OrderStatusDrop          OrderState = -1  //废单
)

func (es OrderState) IsFinished() bool {
	return es == OrderStatusFull || es == OrderStatusNotDealCancel || es == OrderStatusPartCancel || es == OrderStatusDrop
}

func (es OrderState) IsHasTrade() bool {
	return es == OrderStatusFull || es == OrderStatusPartCancel || es == OrderStatusPart
}

//条件单状态
//(1: 未触发 0：取消 2：已触发 3: 触发失败 4: 处理中 5:平仓撤销）
const (
	OrderConditionNotTrigger = 1
	OrderCondCancel          = 0
	OrderCondHadTrigger      = 2
	OrderCondFailed          = 3
	OrderCondRunning         = 4
	OrderCondCloseCancel     = 5
)

// 条件单条件类型
const (
	OrderConditionGreaterOrEqual = 1 // 大于等于时触发
	OrderConditionLessOrEqual    = 2 // 小于等于时触发
)

//taker maker
const (
	Maker = 2
	Taker = 1
)

//trade
const (
	TradeEnable  = 1
	TradeDisable = 2
)

const (
	RatioLevelProfit1 = 1
	RatioLevelProfit2 = 2
	RatioLevelProfit3 = 3
	RatioLevelLoss1   = -1
	RatioLevelLoss2   = -2
	RatioLevelLoss3   = -3
)

const (
	// 持仓
	RatioLevelHoldProfit1Max = 1
	RatioLevelHoldProfit2Max = 3
	RatioLevelHoldLoss1Min   = -1
	RatioLevelHoldLoss2Min   = -3

	// 成交
	RatioLevelSoldProfit1Max = 1000
	RatioLevelSoldProfit2Max = 10000
	RatioLevelSoldLoss1Min   = -1000
	RatioLevelSoldLoss2Min   = -10000
)

const (
	EntrustTypeMarket = 0 //委托类型-市价
	EntrustTypeLimit  = 1 //委托类型-限价
)

const (
	EntrustTradeMarket   = 0 //成交类型-市价
	EntrustTradeLimit    = 1 //成交类型-限价
	EntrustTradeProtocol = 2 //成交类型-协议成交
)

const (
	MatchTypeDefault   = 0 //撮合类型-默认
	MatchTypeForce     = 1 //撮合类型-强平
	MatchTypeLimitStop = 2 //撮合类型-止盈止损
)

const (
	EntrustModeDefault    = 1 //对手价
	EntrustModeBest3Level = 2 //最优3挡
	EntrustModeBest5Level = 3 //最优5挡
	EntrustModeSystem     = 4 //系统配置
)

//成交策略
const (
	MatchStrategyDefault       = 0   //默认(限价单不能成交则挂单，市价单未成交撤销）
	MatchStrategyFOK           = 1   //以限价单处理,如果不能全部成交，则撤销单子
	MatchStrategyIOC           = 2   //未成交部分撤销(市价单使用同样逻辑）
	MatchStrategyMaker         = 3   //只做maker
	MatchStrategyDelay         = 6   //以限价单处理，剩余先挂单，超时后撤销 （内部策略）
	MatchStrategyProtocolTrade = 500 //未成交部分协议成交（内部策略）
)

type UserTradePermission uint8

const (
	UserTradePermissionOffAll   UserTradePermission = iota // 0-禁止交易
	UserTradePermissionOnAll                               // 1-可以交易
	UserTradePermissionOffOpen                             // 2-禁止开仓
	UserTradePermissionOffClose                            // 3-禁止平仓
)

func (p UserTradePermission) NotOpen() bool {
	return p == UserTradePermissionOffAll || p == UserTradePermissionOffOpen
}
func (p UserTradePermission) NotClose() bool {
	return p == UserTradePermissionOffAll || p == UserTradePermissionOffClose
}
