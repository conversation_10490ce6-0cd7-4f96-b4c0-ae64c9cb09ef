package define

const (
	_ServerNamePrefix   = "spot."
	ServerNameMarket    = _ServerNamePrefix + "market"
	ServerNamePrice     = _ServerNamePrefix + "price"
	ServerNameCore      = _ServerNamePrefix + "core"
	ServerNameMatch     = _ServerNamePrefix + "match"
	ServerNameLimit     = _ServerNamePrefix + "limit"
	ServerNameOrder     = _ServerNamePrefix + "order"
	ServerNameBucketing = _ServerNamePrefix + "bucketing" //对敲铺单
	ServerNameTask      = _ServerNamePrefix + "task"
	ServerNameKline     = _ServerNamePrefix + "kline"
	ServerNameClosing   = _ServerNamePrefix + "closing"

	_BaseServerNamePrefix = "base."
	BaseServerNameUser    = _BaseServerNamePrefix + "user"
	BaseServerNameWallet  = _BaseServerNamePrefix + "wallet"
)

const (
	BasePath = "panda"
)

const (
	RequestIDKey    = "RequestID"
	RequestTokenKey = "Token"
	ResponseCodeKey = "ResponseCode"
)
