package define

import (
	"encoding/json"
	"strconv"
	"time"

	"spot/libs/generic"
)

type Config struct {
	Identifier                   string            `yaml:"identifier"`                      // 服务唯一标识
	RunEnv                       string            `yaml:"run_env"`                         //运行环境
	Debug                        bool              `yaml:"debug"`                           // 环境
	IsSlave                      bool              `yaml:"is_slave"`                        //是否为从服务
	CloneNums                    int               `yaml:"clone_nums"`                      //副本个数
	CloneID                      int               `yaml:"clone_id"`                        //副本id(0,1,2....)
	MQQueueID                    int               `yaml:"mq_queue_id"`                     //消息队列标识
	WorkerID                     int64             `yaml:"worker_id"`                       // id生成序号
	ZipHttp                      bool              `yaml:"zip_http"`                        // 是否启用zip压缩
	Symbols                      []string          `yaml:"symbols"`                         //支持的交易对
	NotDepthSymbols              []string          `yaml:"not_depth"`                       //不支持铺单交易对
	Discovery                    string            `yaml:"discovery"`                       //服务注册发现地址
	H5CanLogin                   bool              `yaml:"h5_can_login"`                    // 是否启用h5登录
	LocalName                    string            `yaml:"local_name"`                      // 服务名称
	RPCAddr                      string            `yaml:"rpc_addr"`                        // rpc监听地址
	RPCxAddr                     string            `yaml:"rpcx_addr"`                       // rpcx监听地址
	PprofAddr                    string            `yaml:"pprof_addr"`                      // 服务运行状态监听地址
	ApiRPCAddr                   string            `yaml:"api_rpc_addr"`                    // api服务地址
	MarketRPCAddr                string            `yaml:"market_rpc_addr"`                 // market服务地址
	CoreRPCAddr                  string            `yaml:"core_rpc_addr"`                   // core服务地址
	FollowRPCAddr                string            `yaml:"follow_rpc_addr"`                 // follow服务地址
	ListenAddr                   string            `yaml:"listen_addr"`                     // rpc监听地址
	ReadTimeout                  int64             `yaml:"read_timeout"`                    // 读超时时间
	WriteTimeout                 int64             `yaml:"write_timeout"`                   // 写超时时间
	DefaultDB                    string            `yaml:"default_db"`                      // 默认数据库连接
	CrmDbDSN                     string            `yaml:"crm_db_dsn"`                      // CRM数据库连接
	DefaultRedis                 string            `yaml:"default_redis"`                   // 默认redis连接
	DefaultRedisPwd              string            `yaml:"default_redis_pwd"`               // 默认redis连接密码
	DefaultRedisDB               int               `yaml:"default_redis_db"`                // 默认redis库编号
	DefaultRedisTLS              bool              `yaml:"default_redis_tls"`               // 是否启用tls
	RedisPool                    int               `yaml:"redis_pool"`                      // redis连接池数量
	LogFile                      string            `yaml:"log_file"`                        // 日志文件路径
	LogLevel                     string            `yaml:"log_level"`                       // 日志等级
	LogOnlyFile                  bool              `yaml:"log_only_file"`                   // 日志只打印在文件里
	SensitiveConf                string            `yaml:"sensitive_conf"`                  // 私密配置
	MailPrefix                   string            `yaml:"mail_prefix"`                     // 邮件前缀(报警邮件用)
	CautionReceiver              []string          `yaml:"caution_receiver"`                // 报警邮件接收列表
	HedgeReceiver                []string          `yaml:"hedge_receiver"`                  // 对冲通知邮件接收列表
	MarketReceiver               []string          `yaml:"market_receiver"`                 // 对冲通知邮件接收列表
	MQ                           string            `yaml:"mq"`                              // rabbit mq
	MaxTradeCounter              int64             `yaml:"max_trade_counter"`               // 最大每秒种下单量
	MQQueueName                  string            `yaml:"mq_queue_name"`                   // mq consumer queue name
	WalletURL                    string            `yaml:"wallet_url"`                      // 钱包地址
	WalletToken                  string            `yaml:"wallet_token"`                    // 钱包token
	CwsNetwork                   string            `yaml:"cws_network"`                     // 钱包网络类型
	DepthHeight                  int               `yaml:"depth_height"`                    //深度计算高度
	HedgeDuration                int               `yaml:"hedge_duration"`                  //对冲间隔 1s
	SinglePlace                  int               `yaml:"single_place"`                    //okex单笔下单量
	IsSimulate                   bool              `yaml:"is_simulate"`                     // 是否是模拟盘
	ExtraRedis                   string            `yaml:"extra_redis"`                     // 默认redis连接
	ExtraRedisPwd                string            `yaml:"extra_redis_pwd"`                 // 默认redis连接密码
	ExtraRedisDB                 int               `yaml:"extra_redis_db"`                  // 默认redis库编号
	ExtraRedisTLS                bool              `yaml:"extra_redis_tls"`                 // 是否启用tls
	InitBalance                  float64           `yaml:"init_balance"`                    // 初始交易账户资产
	MarketAliveDuration          int               `yaml:"market_alive_duration"`           //行情有效时间
	ShareLanguage                bool              `yaml:"share_language"`                  // 是否开启分享多语言
	C2CListenAddr                string            `yaml:"c2c_listen_addr"`                 // c2c平台地址
	C2CPlatformID                string            `yaml:"c2c_platform_id"`                 // c2c平台标识
	C2CSecretKey                 string            `yaml:"c2c_secret_key"`                  // c2c平台密钥
	ExchangeCautionReceiver      []string          `yaml:"exchange_caution_receiver"`       // 兑币预警邮件接收列表
	WithdrawCautionReceiver      []string          `yaml:"withdraw_caution_receiver"`       // 提币预警邮件接收列表
	StatisticsEmailReceiver      []string          `yaml:"statistics_email_receiver"`       // 统计邮件接收列表
	ErrCollectEmailReceiver      []string          `yaml:"err_collect_email_receiver"`      // 错误汇总邮件接收列表
	WithdrawTimeoutEmailReceiver []string          `yaml:"withdraw_timeout_email_receiver"` // 提币超时邮件接收列表
	KlineRecover                 bool              `yaml:"kline_recover"`                   //是否从数据库恢复数据
	KlineWeekNature              bool              `yaml:"kline_week_nature"`               //kline周线使用自然周
	UploadImageMaxSize           int64             `yaml:"upload_image_max_size"`           // 上传图片最大尺寸
	UploadImageFilePath          string            `yaml:"upload_image_file_path"`          // 上传图片存放位置
	ImageRequestUrlBase          string            `yaml:"image_request_url_base"`          // 图片访问前缀
	IsNotGenerateTick            bool              `yaml:"is_not_generate_tick"`            //不生成tick数据
	NewsConfig                   NewsConfig        `yaml:"news_config"`                     //广告配置
	AppKeyConfig                 map[string]AppKey `yaml:"app_config"`                      //行情配置
	ListenAddrForCWS             string            `yaml:"listen_addr_for_cws"`             // cws 回调http服务监听地址
	IPWhiteListForCWS            []string          `yaml:"ip_white_list_for_cws"`           // cws回调ip白名单
	MarketConfig                 MarketConfig      `yaml:"market_config"`                   //行情相关配置
	MsgServerURL                 string            `yaml:"msg_server_url"`                  // 新短信邮件服务地址
	MsgServerAppID               string            `yaml:"msg_server_app_id"`               // 新短信邮件服务appID
	MsgServerSecretKey           string            `yaml:"msg_server_secret_key"`           // 新短信邮件服务secret
	HedgeOkexConcurrency         int               `yaml:"hedge_okex_concurrency"`          // okex对冲并发数
	HedgeOkexDelay               int               `yaml:"hedge_okex_delay"`                // okex对冲任务执行后延迟毫秒数
	HedgeOkexEndpoint            string            `yaml:"hedge_okex_endpoint"`             // okex对冲域名
	HedgeOkexSimulated           bool              `yaml:"hedge_okex_simulated"`            // okex对冲是否是模拟盘
	HedgeRESTTimeout             int               `yaml:"hedge_rest_timeout"`              // 对冲http请求超时限制(秒)
	IPDBFilePath                 string            `yaml:"ip_db_file_path"`                 // ip数据库位置
	FXHOpenAPI                   string            `yaml:"fxh_open_api"`                    // 非小号访问地址
	CoinGeckoOpenAPI             string            `yaml:"coin_gecko_open_api"`             // 币虎接口

	SenseApiKey    string `yaml:"sense_api_key"`    // 商汤实名认证key
	SenseApiSecret string `yaml:"sense_api_secret"` // 商汤实名认证私钥
	SenseApiUrl    string `yaml:"sense_api_url"`    // 商汤实名认证接口地址

	ClosingQueueNums  int `yaml:"closing_queue_nums"`  //队列长度
	ClosingWorkerNums int `yaml:"closing_worker_nums"` //处理用户队列的线程数

	RcDealNums int `yaml:"rc_deal_nums"` //限价单每次处理订单数上限

	DefaultRedisConf RedisConfig `yaml:"default_redis_conf"` // 默认redis配置
	ExtraRedisConf   RedisConfig `yaml:"extra_redis_conf"`   // 额外redis配置

	MigrationApiUrl  string `yaml:"migration_api_url"`  // migration api
	MigrationRestUrl string `yaml:"migration_rest_url"` // migration rest url
	MigrationId      string `yaml:"migration_id"`       // migration 商户ID
	MigrationKey     string `yaml:"migration_key"`      // migration 加密key

	MultipleDbConf     map[string]DBConf  `yaml:"multiple_db_conf"`     // 多数据库连接配置
	GinTrustedProxies  []string           `yaml:"gin_trusted_proxies"`  // 信任的前置代理ip地址池(nginx ip...)
	DocConf            SwaggerConf        `yaml:"doc_conf"`             // 文档配置
	TickerDuration     int64              `yaml:"ticker_duration"`      //ticker处理时间间隔
	SupportAccountType SupportAccountType `yaml:"support_account_type"` // 支持的账户类型 1-手机号 2-邮箱 多项相加

}

type RedisConfig struct {
	Address   string         `yaml:"address"`    // 连接地址
	Password  string         `yaml:"password"`   // 连接密码
	UseTLS    bool           `yaml:"use_tls"`    // 是否启用tls
	DefaultDB int            `yaml:"default_db"` // 默认数据库号
	PoolSize  int            `yaml:"pool_size"`  // 连接池数量
	DBNums    map[int]string `yaml:"db_nums"`    // 使用的redis库号
}

type MarketConfig struct {
	PriceDuration     int64                        `yaml:"price_duration"`      //价格计算间隔，毫秒
	DepthValidSeconds float64                      `yaml:"depth_valid_seconds"` //有效深度秒数
	DepthDuration     int64                        `yaml:"depth_duration"`      //铺单间隔，毫秒
	BucketDuration    int64                        `yaml:"bucket_duration"`     //对敲间隔，毫秒
	Duration          map[string]DepthDuration     `yaml:"duration"`            //每个合约配置
	TradeFactor       int                          `yaml:"trade_factor"`        //成交量比例
	BucketConfig      BucketRandConfig             `yaml:"bucket_rand_config"`  //对敲相关随机组配置
	BaitLevelRand     map[string][]float64         `yaml:"bait_rand_config"`    //中鱼随机数配置
	SpotWeight        map[string]map[string]Weight `yaml:"spot_weight"`         //现货权重配置
	DepthLevelRand    map[string][]float64         `yaml:"depth_level_rand"`    //深度随机数配置
	ContractWeight    map[string]DepthConfig       `yaml:"contract_weight"`     //合约权重配置
}

type DepthDuration struct {
	Depth  int64 `yaml:"depth"`
	Bucket int64 `yaml:"bucket"`
}

type Weight struct {
	Weight    float64 `yaml:"weight"`
	IsConvert bool    `yaml:"convert"`
}

type DepthConfig struct {
	Switch bool               `yaml:"switch"`
	Source string             `yaml:"source"`
	Weight map[string]float64 `yaml:"weight"`
}

type BucketRandConfig struct {
	DefaultConfig  ContractRandConfig            `yaml:"default_config"`
	ContractConfig map[string]ContractRandConfig `yaml:"configs"`
}

type ContractRandConfig struct {
	Code       string    `json:"code" yaml:"code"`
	RandHourA  []float64 `json:"randHourA" yaml:"randHourA"`
	RandHourB  []float64 `json:"randHourB" yaml:"randHourB"`
	RandMinute []float64 `json:"randMinute" yaml:"randMinute"`
	RandLevel  []float64 `json:"randLevel" yaml:"randLevel"`
}

type AppKey struct {
	PublicKey   string  `yaml:"public_key"`   //公钥
	SecretKey   string  `yaml:"secret_key"`   //私钥
	Phrase      string  `yaml:"phrase"`       //短语
	RestURI     string  `yaml:"rest_uri"`     //api url
	GlobalWSURI string  `yaml:"ws_uri"`       //全局ws url
	AssetWSURI  string  `yaml:"asset_ws_uri"` //资产ws url
	IsSimulate  bool    `yaml:"isSimulate"`   //是否虚拟盘
	Weight      float64 `yaml:"weight"`       //权重
}

type NewsConfig struct {
	IsComReport      bool     `yaml:"isComReport"`
	IsMaintainReport bool     `yaml:"isMaintainReport"`
	IsShowContent    bool     `yaml:"isShowContent"`
	NoticeEmails     []string `yaml:"news_emails"`
}

type NoneArg struct {
}

type NoneReply struct {
}

type Arg struct {
	Data json.RawMessage `json:"data"` // 请求数据
	ReqArg
	TokenPayload
}

type ReqArg struct {
	ReqID      int64      `json:"req_id"`      // 请求id
	ReqIP      string     `json:"req_ip"`      // ip地址
	ReqOs      OsType     `json:"req_os"`      // 请求端类型
	ReqLang    ReqLang    `json:"req_lang"`    // 语言类型
	ReqPID     int        `json:"platform_id"` // 平台id
	ReqCID     AppChannel `json:"channel_id"`  // 渠道id 安卓{0-极速下载 1-本地下载} IOS{0-testflight 1-超级签 2-企业签}
	SystemOs   string     `json:"system_os"`   // 操作系统
	Device     string     `json:"device"`      // 设备名称
	DeviceID   string     `json:"device_id"`   // 设备识别号 app:设备号 web:浏览器信息
	Version    string     `json:"version"`     // app数字版本号
	ApiVersion string     `json:"api_version"` // api版本
	Token      string     `json:"token"`       // 用户token
}

func (a *ReqArg) AppVersion() AppVersion {
	v, _ := strconv.ParseInt(a.Version, 10, 64)
	vv := generic.Max[AppVersion](AppVersionZero, AppVersion(v))
	return generic.If[AppVersion](a.ReqOs == OsAndroid || a.ReqOs == OsIos, vv, AppVersionNone)
}

type Reply struct {
	Ret        int         `json:"ret"`
	Msg        string      `json:"msg"`
	Identifier string      `json:"identifier"`
	Data       interface{} `json:"data"`

	NotPrintIn  bool `json:"-"`
	NotPrintOut bool `json:"-"`
}

type Page struct {
	Page  int `json:"page"`
	Count int `json:"count"`
}

func (p *Page) Check() {
	if p.Page < 0 {
		p.Page = 0
	}
	if p.Count <= 0 || p.Count > MaxPageCount {
		p.Count = MaxPageCount
	}
}

type TokenPayload struct {
	UserID           int64           `json:"user_id"`            // 用户id
	UserName         string          `json:"user_name"`          // 用户名
	Phone            string          `json:"phone"`              // 手机号
	Email            string          `json:"email"`              // 邮箱
	SpareEmail       string          `json:"spare_email"`        // 备用邮箱
	TotpSecret       string          `json:"totp_secret"`        // totp验证器私钥
	InviteCode       string          `json:"invite_code"`        // 邀请码
	EnableLogin      bool            `json:"enable_login"`       // 是否可登录
	EnableWithdraw   bool            `json:"enable_withdraw"`    // 是否可提现
	EnableTrade      bool            `json:"enable_trade"`       // 是否可交易
	ForbidOpenClose  uint8           `json:"forbid_open_close"`  // 开平仓权限 0: 都不禁止 1: 禁止开仓 2: 禁止平仓
	LoginPasswd      string          `json:"login_passwd"`       // 登录密码
	FundPasswd       string          `json:"fund_passwd"`        // 资金密码
	InviteParent     string          `json:"invite_parent"`      // 邀请链
	RestFundTime     int64           `json:"rest_fund_time"`     // 最后一次更新资金密码时间
	RestLoginTime    int64           `json:"rest_login_time"`    // 最后一次更新登录密码时间
	Verify           UserVerifyState `json:"verify"`             // 用户认证状态
	RealName         string          `json:"real_name"`          // 用户真实姓名
	CardNo           string          `json:"card_no"`            // 证件号码
	StaticIdentity   bool            `json:"static_identity"`    // 是否固定身份信息
	LastVerifyID     int64           `json:"last_verify_id"`     // 最后一条认证申请id
	CacheID          string          `json:"cache_id"`           // 缓存键
	LastDevice       string          `json:"last_device"`        // 最后一次登录的设备id
	AreaCode         string          `json:"area_code"`          // 国家区号
	CountryCode      string          `json:"country_code"`       // 国家代码
	Nickname         string          `json:"nickname"`           // 中文昵称
	NicknameEn       string          `json:"nickname_en"`        // 英文昵称
	Introduce        string          `json:"introduce"`          // 中文介绍
	IntroduceEn      string          `json:"introduce_en"`       // 英文介绍
	Avatar           string          `json:"avatar"`             // 头像
	DealerState      DealerState     `json:"dealer_state"`       // 交易员状态
	FollowApproved   bool            `json:"follow_approved"`    // 是否已经接受了跟单协议
	TradeApproved    bool            `json:"trade_approved"`     // 是否已经接受了交易协议
	PlatformID       int             `json:"platform_id_inner"`  // 用户平台id
	LoginVerifyPhone bool            `json:"login_verify_phone"` // 登录两步验证手机号开关
	LoginVerifyEmail bool            `json:"login_verify_email"` // 登录两步验证邮箱开关
	LoginVerifyTotp  bool            `json:"login_verify_totp"`  // 登录两步验证验证器开关
	TradeVerifyFund  uint8           `json:"trade_verify_fund"`  // 交易资金验证码验证方式 0-永不验证 1-24小时内验证一次 2-永远验证
	LabelID          int             `json:"label_id"`           // 标签id
	ProfitStyle      int             `json:"profit_style"`       // 盈亏计算方式 0-使用标记价格 1-使用成交价格
	ShowAgentRatio   bool            `json:"show_agent_ratio"`   // 是否显示邀请返佣比例
	IsShowDealer     bool            `json:"is_show_dealer"`     // 是否可以成为交易员
	CreatedTime      time.Time       `json:"created_time"`       // 注册时间
	IsAgent          bool            `json:"is_agent"`           // 是否是代理
}

type SwaggerConf struct {
	Version     string   `yaml:"version"`     // 文档版本
	Host        string   `yaml:"host"`        // 文档地址
	BasePath    string   `yaml:"base_path"`   // api路由前缀
	Schemes     []string `yaml:"schemes"`     // 支持的协议
	Title       string   `yaml:"title"`       // 文档标题
	Description string   `yaml:"description"` // 文档说明
}

type DBConf struct {
	Read  SubConf `yaml:"read"`  // 只读连接
	Write SubConf `yaml:"write"` // 可写连接
}

type SubConf struct {
	DSN     string `yaml:"dsn"`      // 连接地址
	MaxOpen int    `yaml:"max_open"` // 最大连接数
	MaxIdle int    `yaml:"max_idle"` // 最大空闲连接
}
