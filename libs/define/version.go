package define

const (
	AppChannelTextFast         = "fast"          // 极速下载
	AppChannelTextFast2        = "fast_2"        // 极速下载2
	AppChannelTextLocal        = "local"         // 本地下载
	AppChannelTextSpare        = "spare"         // 备用下载
	AppChannelTextAppStore     = "app_store"     // ios AppStore
	AppChannelTextTestFlight   = "test_flight"   // ios TestFlight
	AppChannelTextGooglePlay   = "google_play"   // 安卓 GooglePlay
	AppChannelTextDownloadPage = "download_page" // 下载页面
)

type AppChannel uint

const (
	AndroidAppChannelFast       AppChannel = iota // 安卓快速下载
	AndroidAppChannelLocal                        // 安卓本地下载
	AndroidAppChannelSpare                        // 安卓备用下载
	AndroidAppChannelNonSupport                   // 不支持的下载方式开始
)

var AndroidAppChannel = map[AppChannel]string{
	AndroidAppChannelFast:  AppChannelTextFast,
	AndroidAppChannelLocal: AppChannelTextLocal,
	AndroidAppChannelSpare: AppChannelTextSpare,
}

const (
	IosAppChannelFast       AppChannel = iota // IOS快速下载
	IosAppChannelFast2                        // IOS快速下载2
	IosAppChannelSpare                        // IOS备用下载
	IosAppChannelNonSupport                   // 不支持的下载方式开始
)

var IosAppChannel = map[AppChannel]string{
	IosAppChannelFast:  AppChannelTextFast,
	IosAppChannelFast2: AppChannelTextFast2,
	IosAppChannelSpare: AppChannelTextSpare,
}

type AppVersion int64

func (v AppVersion) CanPositionSplit() bool {
	return v == AppVersionNone || v >= AppVersionCanPositionSplit
}

const (
	AppVersionNone             AppVersion = -1
	AppVersionZero             AppVersion = 0
	AppVersionCanPositionSplit AppVersion = 1263
)
