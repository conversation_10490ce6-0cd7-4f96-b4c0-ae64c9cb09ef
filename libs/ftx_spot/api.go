package ftx_spot

import (
	"encoding/json"
	"github.com/shopspring/decimal"
	"io/ioutil"
	"net/http"
	"spot/libs/log"
	"spot/libs/proto"
	"strings"
	"time"
)

const (
	apiUrl = "https://ftx.com/api/markets"
)

//{"result":[{"ask":33188.0,"baseCurrency":"BTC","bid":33167.0,"change1h":-0.0056957851190119315,"change24h":0.013753896937465615,"changeBod":0.029710347396852008,"enabled":true,"highLeverageFeeExempt":true,"last":33168.0,"minProvideSize":0.0001,"name":"BTC/USDT","postOnly":false,"price":33168.0,"priceIncrement":1.0,"quoteCurrency":"USDT","quoteVolume24h":30382643.4557,"restricted":false,"sizeIncrement":0.0001,"type":"spot","underlying":null,"volumeUsd24h":30412425.43429458},
func GetTickers(codes map[string]struct{}) (list []proto.MarketTrade) {
	r, err := http.Get(apiUrl)
	if err != nil {
		return
	}
	defer r.Body.Close()
	if r.StatusCode != http.StatusOK {
		log.Errorf("get hitbtc tickers fail,code:%v", r.StatusCode)
		return
	}

	b, err := ioutil.ReadAll(r.Body)
	if err != nil {
		return
	}
	//log.Infof("rsp:%v", string(b))
	var tickers = new(rsp)
	err = json.Unmarshal(b, tickers)
	if err != nil {
		return
	}
	size := len(tickers.Result)
	if size == 0 {
		return
	}
	log.Infof("ticker:%+v", tickers.Result)
	for _, trade := range tickers.Result {
		if trade.Type != "spot" {
			continue
		}
		code := strings.ReplaceAll(trade.Code, "/", "")
		if _, ok := codes[code]; !ok {
			continue
		}
		mk := proto.MarketTrade{
			Symbol:   strings.ToUpper(code),
			DealTime: time.Now().Unix(),
			Price:    trade.Price,
			Ts:       time.Now(),
		}
		list = append(list, mk)
	}

	return
}

//{"ask":33188.0,"baseCurrency":"BTC","bid":33167.0,"change1h":-0.0056957851190119315,"change24h":0.013753896937465615,"changeBod":0.029710347396852008,"enabled":true,"highLeverageFeeExempt":true,"last":33168.0,"minProvideSize":0.0001,"name":"BTC/USDT","postOnly":false,"price":33168.0,"priceIncrement":1.0,"quoteCurrency":"USDT","quoteVolume24h":30382643.4557,"restricted":false,"sizeIncrement":0.0001,"type":"spot","underlying":null,"volumeUsd24h":30412425.43429458},
type tick struct {
	Type  string          `json:"type"`
	Code  string          `json:"name"`
	Price decimal.Decimal `json:"last"`
}

type rsp struct {
	Result []tick `json:"result"`
}
