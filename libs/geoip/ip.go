package geoip

import (
	"net"
	"strings"

	"github.com/oschwald/geoip2-golang"
	"go.uber.org/zap"
	"spot/libs/log"
)

var g *geoip2.Reader

func InitGeoIP(dbFile string) {
	var err error
	g, err = geoip2.Open(dbFile)
	if err != nil {
		log.Fatal("InitGeoIP open file failed", zap.String("filepath", dbFile), zap.Error(err))
	}
}

func ParseCountry(ip string) (*geoip2.Country, error) {
	return g.Country(net.ParseIP(ip))
}

func ParseCity(ip string) (*geoip2.City, error) {
	return g.City(net.ParseIP(ip))
}

func ParseIP(ip string) (location string, err error) {
	city, err := ParseCity(ip)
	if err != nil {
		return
	}

	var buf strings.Builder
	buf.WriteString(city.Country.Names["zh-CN"])
	if len(city.Subdivisions) > 0 && len(city.Subdivisions[0].Names["zh-CN"]) > 0 {
		buf.WriteString("-")
		buf.WriteString(city.Subdivisions[0].Names["zh-CN"])
	}
	if len(city.City.Names) > 0 && len(city.City.Names["ja"]) > 0 {
		buf.WriteString("-")
		buf.WriteString(city.City.Names["ja"])

	}

	location = buf.String()
	return
}
