package hitbtc

import (
	"github.com/shopspring/decimal"
	"io/ioutil"
	"net/http"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/proto"
	"spot/libs/utils"
	"strings"
	"time"
)

//HitBTC
//https://hitbtc.com/
//https://api.hitbtc.com/api/2/public/ticker

//[{"symbol":"VEOBTC","ask":"0.000692","bid":"0.000656","last":"0.000713","low":"0.000713","high":"0.000721","open":"0.000721","volume":"0.142","volumeQuote":"0.000101262","timestamp":"2021-01-08T07:06:47.000Z"},]

const (
	url       = "https://api.hitbtc.com"
	apiTicker = "/api/2/public/ticker"
	_name     = "hitbtc"
)

func GetTickers(codes map[string]struct{}) (list []proto.MarketTrade) {
	u := utils.StrBuilder(url, apiTicker)
	r, err := http.Get(u)
	if err != nil {
		return
	}
	defer r.Body.Close()
	if r.StatusCode != http.StatusOK {
		log.Errorf("get hitbtc tickers fail,code:%v", r.StatusCode)
		return
	}
	b, err := ioutil.ReadAll(r.Body)
	if err != nil {
		return
	}
	//fmt.Println(string(b))
	var tickers []tick
	err = json.Unmarshal(b, &tickers)
	if err != nil {
		return
	}
	if len(tickers) == 0 {
		return
	}

	for _, v := range tickers {
		code := strings.Replace(v.Code, "USD", "USDT", -1)
		if _, ok := codes[code]; ok {
			mk := proto.MarketTrade{
				Symbol:   code,
				DealTime: time.Now().Unix(),
				Price:    v.Price,
				Volume:   v.Volume,
				Side:     "",
				Ts:       time.Now(),
				Source:   _name,
			}
			list = append(list, mk)
		}
	}
	return
}

type tick struct {
	Code   string          `json:"symbol"`
	Price  decimal.Decimal `json:"last"`
	Volume decimal.Decimal `json:"volume"`
}
