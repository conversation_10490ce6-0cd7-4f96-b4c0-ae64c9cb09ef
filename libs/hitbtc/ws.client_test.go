/*
@Time : 2019-12-17 19:06
<AUTHOR> mocha
@File : client_test
*/
package hitbtc

import (
	"fmt"
	"github.com/gorilla/websocket"
	"io/ioutil"
	"spot/libs/convert"
	"spot/libs/json"
	"spot/libs/log"
	"testing"
	"time"
)

func TestClient(te *testing.T) {
	w, rsp, err := websocket.DefaultDialer.Dial("wss://api.hitbtc.com/api/2/ws", nil)
	//w, rsp, err := websocket.DefaultDialer.Dial("wss://api.hbdm.com/linear-swap-ws", nil)
	if err != nil {
		fmt.Println(err)
	}
	b, err := ioutil.ReadAll(rsp.Body)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(convert.Bytes2Str(b))
	str := `{
  "method": "subscribeTrades",
  "params": {
    "symbol": "BTCUSD",
    "limit": 1
  },
  "id": 123
}`

	//TestClient: ws.client_test.go:44: 1 {"jsonrpc":"2.0","method":"updateTrades","params":{"data":[{"id":1069973553,"price":"34560.33","quantity":"0.00001","side":"buy","timestamp":"2021-01-11T03:45:27.138Z"}],"symbol":"BTCUSD"}} <nil>

	w.WriteMessage(websocket.TextMessage, convert.Str2Bytes(str))

	go func() {
		for {
			t, b, err := w.ReadMessage()
			te.Log(t, convert.Bytes2Str(b), err)

		}
	}()

	select {}

}

func TestBClient(t *testing.T) {
	log.InitLogger("ws.log", "info", false)
	s := time.Tick(time.Minute * 1)
	c := NewMarketWsClient(&Config{WsPoint: "wss://api.hitbtc.com/api/2/ws"}, func(data []byte) {
		t.Logf("d:%v", string(data))
		t.Logf(string(data))
		tr := new(TickerRsp)
		e := json.Unmarshal(data, tr)
		if e != nil {
			return
		}
		//log.Infof("tr:%+v",*tr)
		if tr.Method == "updateTrades" {
			log.Infof("ticker；%+v", *tr)
		}

	})
	c.Subscript([]string{"subscribeTrades"}, []string{"BTCUSD"})
	c.Start()
	go func() {
		for range s {
			t.Logf("开始重启")
			c.Restart()
		}
	}()
	select {}
}
