package huobi

import (
	"fmt"
	"go.uber.org/zap"
	"io/ioutil"
	"net/http"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/nums"
	"spot/libs/proto"
	"strings"
	"time"
)

// "tick": {
//      "id": 订单唯一id（品种唯一）,
//      "ts": 最新成交时间,
//      "data": [
//        {
//                  "id": 成交唯一id（品种唯一）,
//                  "price": 成交价钱,
//                  "amount": 成交量(张)，买卖双边成交量之和,
//                  "direction": 主动成交方向,
//                  "ts": 成交时间
//        }
//      ]
//    }
const (
	apiUrl = "https://api.hbdm.com/linear-swap-ex/market/trade?contract_code=%s"
	_name  = "huobi"
)

func GetTickers(codes map[string]struct{}) (list []proto.MarketTrade) {
	for s := range codes {
		code := strings.ToUpper(s)
		code = strings.ReplaceAll(code, "USDT", "-USDT")
		mk := getTicker(code)
		if mk != nil {
			list = append(list, *mk)
		}
	}
	return
}

func getTicker(symbol string) (mk *proto.MarketTrade) {
	u := fmt.Sprintf(apiUrl, symbol)
	r, err := http.Get(u)
	if err != nil {
		log.Error("getTicker get fail", zap.Error(err))
		return
	}
	defer r.Body.Close()

	if r.StatusCode != http.StatusOK {
		log.Errorf("get hitbtc tickers fail,code:%v", r.StatusCode)
		return
	}
	b, err := ioutil.ReadAll(r.Body)
	if err != nil {
		log.Error("huobi swap getTicker fail", zap.Error(err))
		return
	}
	log.Infof("huobi swap rsp:%v", string(b))
	var tickers = new(TickerC)
	err = json.Unmarshal(b, tickers)
	if err != nil {
		log.Errorf("json unmarshal err；%v", err)
		return
	}
	size := len(tickers.Tick.Data)
	log.Infof("huobi swap t:%v", tickers.Tick)
	if size == 0 {
		return
	}
	ts := tickers.Tick.Ts / 1000
	t := time.Unix(ts, 0)
	if time.Since(t).Seconds() > 60 {
		log.Info("获取huobi swap 数据时间超时", zap.Any("data", tickers.Tick), zap.Any("time", t), zap.Any("seconds", ts))
		return
	}
	trade := tickers.Tick.Data[0]
	mk = &proto.MarketTrade{
		Symbol:   strings.ReplaceAll(strings.ToUpper(symbol), "-USDT", "USDT"),
		DealTime: time.Now().Unix(),
		Price:    nums.NewFromString(trade.Price),
		Ts:       time.Now(),
		Source:   _name,
	}
	log.Infof("ticker:%+v", *tickers)
	return
}

type TickerC struct {
	Channel string `json:"ch"`
	Ts      int64  `json:"ts"`
	Tick    TickC  `json:"tick"`
}

type TickDataC struct {
	Amount string `json:"amount"`
	Price  string `json:"price"`
	Side   string `json:"direction"`
}

type TickC struct {
	ID   int64       `json:"id"`
	Ts   int64       `json:"ts"`
	Data []TickDataC `json:"data"`
}

const index_url = "https://api.hbdm.com/linear-swap-api/v1/swap_index"

func GetIndex(codes map[string]struct{}) (list []proto.MarketTrade) {
	return getIndex(codes)
}

func getIndex(codes map[string]struct{}) (list []proto.MarketTrade) {
	u := fmt.Sprintf(index_url)
	r, err := http.Get(u)
	if err != nil {
		log.Error("getTicker get fail", zap.Error(err))
		return
	}
	defer r.Body.Close()

	if r.StatusCode != http.StatusOK {
		log.Errorf("get hitbtc tickers fail,code:%v", r.StatusCode)
		return
	}
	b, err := ioutil.ReadAll(r.Body)
	if err != nil {
		log.Error("huobi swap getTicker fail", zap.Error(err))
		return
	}
	log.Infof("huobi swap rsp:%v", string(b))
	var tickers = new(IndexTs)
	err = json.Unmarshal(b, tickers)
	if err != nil {
		log.Errorf("json unmarshal err；%v", err)
		return
	}
	for _, item := range tickers.Data {
		code := strings.ReplaceAll(item.ContractCode, "-", "")
		if _, ok := codes[code]; ok {
			mk := proto.MarketTrade{
				Symbol:   code,
				DealTime: time.Now().Unix(),
				Price:    nums.NewFromFloat(item.IndexPrice),
				Ts:       time.Now(),
				Source:   "huobi_index",
			}
			list = append(list, mk)

		}
	}
	return
}

type IndexTs struct {
	Status string `json:"status"`
	Data   []struct {
		IndexPrice     float64 `json:"index_price"`
		IndexTs        int64   `json:"index_ts"`
		ContractCode   string  `json:"contract_code"`
		TradePartition string  `json:"trade_partition"`
	} `json:"data"`
}
