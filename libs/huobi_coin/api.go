package huobi_coin

import (
	"io/ioutil"
	"net/http"
	"spot/libs/config"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/nums"
	"spot/libs/proto"
	"strings"
	"time"
)

//https://api.huobi.pro/market/trade?symbol=ethusdt
//{"ch":"market.ethusdt.trade.detail","status":"ok","ts":1610445253325,"tick":{"id":115555089613,"ts":1610445253175,"data":[{"id":115555089613188377074436985,"ts":1610445253175,"trade-id":102039675755,"amount":0.4,"price":1120.38,"direction":"sell"}]}}
const (
	apiUrl = "https://api.btcgateway.pro/swap-ex/market/trade"
)

func GetTickers(codes map[string]struct{}) (list []proto.MarketTrade) {
	r, err := http.Get(apiUrl)
	if err != nil {
		return
	}
	defer r.Body.Close()

	if r.StatusCode != http.StatusOK {
		log.Errorf("get huobi rc  tickers fail,code:%v", r.StatusCode)
		return
	}
	b, err := ioutil.ReadAll(r.Body)
	if err != nil {
		return
	}
	//log.Infof("rsp:%v", string(b))
	var tickers = new(trade)
	err = json.Unmarshal(b, tickers)
	if err != nil {
		return
	}
	size := len(tickers.Tick.Data)
	if size == 0 {
		return
	}

	for _, trade := range tickers.Tick.Data {
		code := strings.ToUpper(trade.ContractCode)
		code = strings.ReplaceAll(code, "-", "")
		if _, ok := codes[code]; ok {
			mk := proto.MarketTrade{
				Symbol:   code,
				DealTime: time.Now().Unix(),
				Price:    nums.NewFromString(trade.Price),
				Ts:       time.Unix(trade.Ts/1000, 0),
				Source:   config.MarketNameRcHuobi,
			}
			list = append(list, mk)
		}
	}

	//log.Infof("ticker:%+v", *tickers)
	return
}

type trade struct {
	Ch     string `json:"ch"`
	Status string `json:"status"`
	Tick   struct {
		Data []struct {
			Amount       string `json:"amount"`
			Direction    string `json:"direction"`
			Id           int64  `json:"id"`
			Price        string `json:"price"`
			Ts           int64  `json:"ts"`
			ContractCode string `json:"contract_code"`
			Quantity     string `json:"quantity"`
		} `json:"data"`
		Id int64 `json:"id"`
		Ts int64 `json:"ts"`
	} `json:"tick"`
	Ts int64 `json:"ts"`
}
