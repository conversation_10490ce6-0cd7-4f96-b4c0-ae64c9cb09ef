/*
@Time : 2/7/20 3:31 下午
<AUTHOR> mocha
@File : main
*/
package huobi_spot

import (
	"context"
	"encoding/json"
	"fmt"
	"spot/libs/compress"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"spot/libs/log"
)

const (
	_wsMarket = "wss://api.huobi.pro/ws"
)

//entries

type MarketWsClient struct {
	dialer                          *websocket.Dialer
	wsPoint                         string
	topics                          []string
	lastError                       error
	lock, errLock, instance, rvLock sync.RWMutex
	status                          bool
	closeHandler                    func(int, string)
	msgHandler                      func([]byte)
	lastRv                          time.Time
	client                          *Client
	ClientStatus                    bool
	lastStart                       time.Time
}

type Config struct {
	WsPoint     string
	dialer      *websocket.Dialer
	timeSeconds int64
}

type Client struct {
	isOk                      bool
	send                      chan []byte
	done                      chan struct{}
	isClose                   bool
	lock, statLock, startLock sync.RWMutex
	mc                        *MarketWsClient
	con                       *websocket.Conn
	ticker                    *time.Ticker
}

func NewClient(mc *MarketWsClient) *Client {
	return &Client{
		send:   make(chan []byte, 128),
		done:   make(chan struct{}),
		mc:     mc,
		con:    nil,
		ticker: time.NewTicker(5 * time.Second),
	}
}

func (c *Client) start() {
	log.Infof("client begin start 0")
Retry:
	conn, rsp, err := websocket.DefaultDialer.Dial(c.mc.wsPoint, nil)
	if err != nil {
		log.Errorf("dial to huobi spot  fail,err:%v,http rsp:%+v", err, rsp)
		time.Sleep(5 * time.Second)
		goto Retry
	}
	c.isOk = true
	c.mc.rvLock.Lock()
	c.mc.lastRv = time.Now()
	c.mc.rvLock.Unlock()
	c.mc.lastStart = time.Now()
	log.Infof("huobi dial over r1")
	c.con = conn
	c.Receive()
	//c.PingLoop()
	//订阅
	c.sub(c.mc.topics)
	log.Infof("huobi sub over r2")
}

func (c *Client) close() {
	c.done <- struct{}{}
	c.statLock.Lock()
	c.isClose = true
	c.statLock.Unlock()
	close(c.send)
	c.con.Close()
	c.ticker.Stop()
}

func (c *Client) sub(topic []string) {
	if c == nil {
		return
	}
	if c.isClose {
		return
	}
	var subData [][]byte
	for _, topic := range topic {
		d := Sub{Sub: topic}
		b, err := json.Marshal(d)
		if err != nil {
			fmt.Errorf("marshal fail,%v", err)
			return
		}
		subData = append(subData, b)
	}

	for _, v := range subData {
		c.Write(v)
	}

}

func (c *Client) unSub(topic []string) {
	if c.isClose {
		return
	}
	var subData [][]byte
	for _, topic := range topic {
		d := UnSub{UnSub: topic}
		b, err := json.Marshal(d)
		if err != nil {
			fmt.Errorf("marshal fail,%v", err)
			return
		}
		subData = append(subData, b)
	}

	for _, v := range subData {
		c.Write(v)
	}

}

func (c *Client) Write(data []byte) {
	if c.isClose {
		return
	}
	c.send <- data
}

func (c *Client) Receive() {
	go func() {
		ctx, cancel := context.WithCancel(context.Background())
		defer cancel()

		msgChan := ReadMessage(ctx, c.con)
		for {
			select {
			case <-c.done:
				log.Infof("ws close")
				return
			case data := <-c.send:
				//c.lock.Lock()
				c.con.WriteMessage(websocket.TextMessage, data)
				//c.lock.Unlock()
			case msg := <-msgChan:
				c.mc.rvLock.Lock()
				c.mc.lastRv = time.Now()
				c.mc.rvLock.Unlock()
				//tick.Reset(time.Second)
				if msg.Err != nil {
					fmt.Printf("接收消息失败 msgType；%v，msg:%s, err:%v", msg.Type, msg.Msg, msg.Err)
					return
				}
				b, err := compress.GzipUnCompress(msg.Msg)
				if err != nil {
					log.Errorf("解压数据失败，%v", err)
					return
				}
				fmt.Printf("收到消息 msgType；%v，msg:%s\n", msg.Type, string(b))
				//log.Infof("msg:%+v,msgType；%v", string(b), msgType)
				ping := new(Ping)
				err = json.Unmarshal(b, ping)
				if err != nil {
					log.Errorf("json unmarshal fail,%v", err)
					return
				}
				if ping.Ping > 0 {
					pong, _ := json.Marshal(&Pong{Pong: ping.Ping})
					c.con.WriteMessage(websocket.TextMessage, pong)

					//c.Write(pb)
				} else {
					if c.mc.msgHandler != nil {
						c.mc.msgHandler(b)
					}
				}
				//case <-tick.C:
				//	//fmt.Printf("未收到数据\n")
				//	fmt.Printf("结束 :%v\n", c.con.Close())
				//	break
			}

		}

	}()
}

func NewMarketWsClient(config *Config, f func([]byte)) *MarketWsClient {
	c := &MarketWsClient{
		dialer:  nil,
		wsPoint: _wsMarket,
		topics:  nil,
	}
	if config != nil {
		if config.WsPoint != "" {
			c.wsPoint = config.WsPoint
		}
		if config.dialer != nil {
			c.dialer = config.dialer
		} else {
			c.dialer = websocket.DefaultDialer
		}
	}
	c.msgHandler = f
	return c
}

func (c *MarketWsClient) CloseHook(f func(int, string)) {
	c.closeHandler = f
}

func (c *MarketWsClient) Start() {
	if c.client != nil {
		c.client.close()
	}
	c.client = NewClient(c)
	c.client.start()
	c.Loop()
}

func (c *MarketWsClient) Subscript(topics []string) {
	if c == nil {
		return
	}
	log.Infof("huobi sub topics:%v", topics)
	c.topics = topics
	c.client.sub(c.topics)

}

func (c *MarketWsClient) unSubscript() {
	c.lock.Lock()
	defer c.lock.Unlock()
}

func (c *MarketWsClient) UnSubscript(topics []string) {
	c.lock.Lock()
	defer c.lock.Unlock()
}

//func (c *MarketWsClient) pong() {
//	c.lock.Lock()
//	defer c.lock.Unlock()
//	pong, err := json.Marshal(&Pong{Pong: compress.GetUinxMillisecond()})
//	if err != nil {
//		log.Errorf("json marshal fail,%v", err)
//		return
//	}
//	c.client.Write(pong)
//}

func (c *MarketWsClient) Loop() {
	log.Infof("begin start market client loop")
	go func() {
		i := 0
		for {
			i++
			log.Debugf("check...................%v", i)
			c.instance.Lock()
			c.check()
			c.instance.Unlock()
			log.Debugf("check over...................%v", i)
			time.Sleep(time.Second)
		}
	}()
}

func (c *MarketWsClient) check() {
	c.rvLock.RLock()
	last := c.lastRv
	c.rvLock.RUnlock()
	if time.Since(last).Seconds() > 20 {
		log.Infof("long time no data for huobiSpot")
		c.Restart()
	}
}

func (c *MarketWsClient) Restart() {
	c.client.close()
	time.Sleep(100 * time.Millisecond)
	c.client = NewClient(c)
	c.client.start()
}

//d := `{"op": "subscribe", "args": ["funding","trade"]}`
type BaseAction struct {
	Action string   `json:"op"`
	Args   []string `json:"args"`
}

//{"success":true,"subscribe":"funding","request":{"op":"subscribe","args":["funding"]}}

type SubRsp struct {
	Success   bool   `json:"success"`
	Subscribe string `json:"subscribe"`
}

type BasicRsp struct {
	Table  string          `json:"table"`
	Action string          `json:"action"`
	Data   json.RawMessage `json:"data"`
}

type Ping struct {
	Ping int64 `json:"ping"`
}

type Pong struct {
	Pong int64 `json:"pong"`
}

type OP struct {
	OP string `json:"op"`
	TS int64  `json:"ts"`
}

type Sub struct {
	Sub string `json:"sub"`
	ID  string `json:"id"`
}

type UnSub struct {
	UnSub string `json:"unSub"`
	ID    string `json:"id"`
}

//{
//"ch":"market.BTC-USDT.trade.detail",
//"ts":1603708208346,
//"tick":{
//"id":131602265,
//"ts":1603708208335,
//"data":[
//{
//"amount":2,
//"ts":1603708208335,
//"id":1316022650000,
//"price":13073.3,
//"direction":"buy"
//}
//]
//}
//}

type DepthTick struct {
	Bids [][]float64 `json:"bids"`
	Asks [][]float64 `json:"asks"`
}

type Ticker struct {
	Channel string    `json:"ch"`
	Ts      int64     `json:"ts"`
	Tick    DepthTick `json:"tick"`
}

type Trade struct {
	Price     float64 `json:"price"`
	Direction string  `json:"direction"`
	Amount    float64 `json:"amount"`
}

type TradeTicker struct {
	Channel string `json:"ch"`
	Ts      int64  `json:"ts"`
	Ticker  struct {
		List []Trade `json:"data"`
	} `json:"tick"`
}

//{
//"ch": "market.btcusdt.trade.detail",
//"ts": 1489474082831, //system update time
//"tick": {
//"id": 14650745135,
//"ts": 1533265950234, //trade time
//"data": [
//{
//"amount": 0.0099,
//"ts": 1533265950234, //trade time
//"id": 146507451359183894799,
//"tradeId": 102043494568,
//"price": 401.74,
//"direction": "buy"
//}
//// more Trade Detail data here
//]
//}
//}

type Message struct {
	Type int
	Msg  []byte
	Err  error
}

func ReadMessage(ctx context.Context, conn *websocket.Conn) <-chan Message {
	var msg Message
	ch := make(chan Message)
	go func() {
		for {
			select {
			case <-ctx.Done():
				close(ch)
				return
			default:
			}

			msg.Type, msg.Msg, msg.Err = conn.ReadMessage()
			select {
			case <-ctx.Done():
				close(ch)
				return
			case ch <- msg:
			}
		}
	}()
	return ch
}

type RspData struct {
	Channel string          `json:"ch"`
	Ts      int64           `json:"ts"`
	Tick    json.RawMessage `json:"tick"`
}
