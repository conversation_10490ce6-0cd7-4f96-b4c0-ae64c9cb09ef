package ipgeo

import (
	"net"
	"strings"

	"github.com/oschwald/geoip2-golang"
	"go.uber.org/zap"
	"spot/libs/log"
)

var reader *geoip2.Reader

func InitIPGeo(path string) {
	if reader == nil {
		var err error
		reader, err = geoip2.Open(path)
		if err != nil {
			log.Fatal("InitIPGeo open db error", zap.String("filePath", path), zap.Error(err))
			return
		}
	}
}

// 解析IP的物理地址,精度到城市
func ParseIP2Geo(ip string) (city *geoip2.City, err error) {
	ipNet := net.ParseIP(ip)

	city, err = reader.City(ipNet)
	if err != nil {
		log.Error("ParseIP2Geo error", zap.String("ip", ip), zap.Error(err))
	}
	return
}

// 解析IP的国家省份信息
func ParseIP2Province(ip string) (location string, err error) {
	city, err := ParseIP2Geo(ip)
	if err != nil {
		return
	}

	var buf strings.Builder
	buf.WriteString(city.Country.Names["zh-CN"])
	if len(city.Subdivisions) > 0 && len(city.Subdivisions[0].Names["zh-CN"]) > 0 {
		buf.WriteString("-")
		buf.WriteString(city.Subdivisions[0].Names["zh-CN"])
	}
	if len(city.City.Names) > 0 && len(city.City.Names["ja"]) > 0 {
		buf.WriteString("-")
		buf.WriteString(city.City.Names["ja"])

	}
	location = buf.String()

	return
}
