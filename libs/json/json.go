package json

import (
	"github.com/json-iterator/go"
)

var ConfigCompatibleWithStandardLibrary = jsoniter.Config{
	EscapeHTML:             true,
	SortMapKeys:            true,
	ValidateJsonRawMessage: true,
	CaseSensitive:          true,
}.Froze()

var ConfigUnescapeHTML = jsoniter.Config{
	EscapeHTML:             false,
	SortMapKeys:            true,
	ValidateJsonRawMessage: true,
	CaseSensitive:          true,
}.Froze()

func Marshal(v interface{}) ([]byte, error) {
	return ConfigCompatibleWithStandardLibrary.Marshal(v)
}

func Unmarshal(data []byte, v interface{}) error {
	return ConfigCompatibleWithStandardLibrary.Unmarshal(data, v)
}

func UnescapeHTMLMarshal(v interface{}) ([]byte, error) {
	return ConfigUnescapeHTML.Marshal(v)
}

func UnescapeHTMLMarshal2String(v interface{}) (string, error) {
	d, e := ConfigUnescapeHTML.Marshal(v)
	return string(d), e
}
