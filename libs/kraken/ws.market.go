/*
@Time : 2/7/20 3:31 下午
<AUTHOR> mocha
@File : main
*/
package kraken

import (
	"bytes"
	"context"
	"fmt"
	"go.uber.org/zap"
	"spot/libs/json"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"spot/libs/log"
)

const (
	_wsMarket = "wss://ws.kraken.com"
)

//entries

type MarketWsClient struct {
	dialer                          *websocket.Dialer
	wsPoint                         string
	topics                          []string
	lastError                       error
	lock, errLock, instance, rvLock sync.RWMutex
	status                          bool
	closeHandler                    func(int, string)
	msgHandler                      func([]byte)
	lastRv                          time.Time
	client                          *Client
	ClientStatus                    bool
	lastStart                       time.Time
}

type Config struct {
	WsPoint     string
	dialer      *websocket.Dialer
	timeSeconds int64
}

type Client struct {
	isOk                      bool
	send                      chan []byte
	done                      chan struct{}
	isClose                   bool
	lock, statLock, startLock sync.RWMutex
	mc                        *MarketWsClient
	con                       *websocket.Conn
	ticker                    *time.Ticker
}

func NewClient(mc *MarketWsClient) *Client {
	return &Client{
		send:   make(chan []byte, 128),
		done:   make(chan struct{}),
		mc:     mc,
		con:    nil,
		ticker: time.NewTicker(5 * time.Second),
	}
}

func (c *Client) start() {
	log.Infof("client begin start 0")
Retry:
	conn, rsp, err := websocket.DefaultDialer.Dial(c.mc.wsPoint, nil)
	if err != nil {
		log.Errorf("dial to huobi fail,err:%v,http rsp:%+v", err, rsp)
		time.Sleep(5 * time.Second)
		goto Retry
	}
	c.isOk = true
	c.mc.rvLock.Lock()
	c.mc.lastRv = time.Now()
	c.mc.rvLock.Unlock()
	c.mc.lastStart = time.Now()
	log.Infof("huobi dial over r1")
	c.con = conn
	c.Receive()
	//c.PingLoop()
	//订阅
	c.sub(c.mc.topics)
	log.Infof("huobi sub over r2")
}

func (c *Client) close() {
	c.done <- struct{}{}
	c.statLock.Lock()
	c.isClose = true
	c.statLock.Unlock()
	close(c.send)
	c.con.Close()
	c.ticker.Stop()
}

//func (c *Client) PingLoop() {
//	go func() {
//		for range c.ticker.C {
//			c.lock.Lock()
//			c.con.WriteMessage(websocket.TextMessage, []byte("ping"))
//			c.lock.Unlock()
//		}
//	}()
//}

//str := `{
//	 "event": "subscribe",
//	 "pair": [
//	   "BTC/USD","ETH/USD"
//	 ],
//	 "subscription": {
//	   "name": "trade"
//	 }
//	}`
func (c *Client) sub(topic []string) {
	if c == nil {
		return
	}
	if c.isClose {
		return
	}
	s := Sub{
		Event: "subscribe",
		Pair:  topic,
		Subs:  map[string]string{"name": "trade"},
	}
	b, err := json.Marshal(s)
	if err != nil {
		log.Error("kraken spot json marshal error", zap.Error(err), zap.Any("data", s))
		return
	}
	log.Infof("sub data:%v", string(b))
	c.Write(b)

}

func (c *Client) Write(data []byte) {
	if c.isClose {
		return
	}
	c.send <- data
}

func (c *Client) Receive() {
	go func() {
		ctx, cancel := context.WithCancel(context.Background())
		defer cancel()

		msgChan := ReadMessage(ctx, c.con)
		for {
			select {
			case <-c.done:
				log.Infof("ws close")
				return
			case data := <-c.send:
				c.con.WriteMessage(websocket.TextMessage, data)
			case msg := <-msgChan:
				c.mc.rvLock.Lock()
				c.mc.lastRv = time.Now()
				c.mc.rvLock.Unlock()
				if msg.Err != nil {
					fmt.Printf("接收消息失败 msgType；%v，msg:%s, err:%v", msg.Type, msg.Msg, msg.Err)
					return
				}
				fmt.Printf("收到消息 msgType；%v，msg:%s\n", msg.Type, string(msg.Msg))
				if bytes.Contains(msg.Msg, []byte("heartbeat")) {
					continue
				}
				c.mc.msgHandler(msg.Msg)
			}

		}

	}()
}

func NewMarketWsClient(config *Config, f func([]byte)) *MarketWsClient {
	c := &MarketWsClient{
		dialer:  nil,
		wsPoint: _wsMarket,
		topics:  nil,
	}
	if config != nil {
		if config.WsPoint != "" {
			c.wsPoint = config.WsPoint
		}
		if config.dialer != nil {
			c.dialer = config.dialer
		} else {
			c.dialer = websocket.DefaultDialer
		}
	}
	c.msgHandler = f
	return c
}

func (c *MarketWsClient) CloseHook(f func(int, string)) {
	c.closeHandler = f
}

func (c *MarketWsClient) Start() {
	if c.client != nil {
		c.client.close()
	}
	c.client = NewClient(c)
	c.client.start()
	c.Loop()
}

func (c *MarketWsClient) Subscript(topics []string) {
	if c == nil {
		return
	}
	log.Infof("%v sub topics:%v", "kraken", topics)
	c.topics = topics
	c.client.sub(c.topics)

}

func (c *MarketWsClient) unSubscript() {
	c.lock.Lock()
	defer c.lock.Unlock()
}

func (c *MarketWsClient) UnSubscript(topics []string) {
	c.lock.Lock()
	defer c.lock.Unlock()
}

//func (c *MarketWsClient) pong() {
//	c.lock.Lock()
//	defer c.lock.Unlock()
//	pong, err := json.Marshal(&Pong{Pong: compress.GetUinxMillisecond()})
//	if err != nil {
//		log.Errorf("json marshal fail,%v", err)
//		return
//	}
//	c.client.Write(pong)
//}

func (c *MarketWsClient) Loop() {
	log.Infof("begin start market client loop")
	go func() {
		i := 0
		for {
			i++
			log.Debugf("check...................%v", i)
			c.instance.Lock()
			c.check()
			c.instance.Unlock()
			log.Debugf("check over...................%v", i)
			time.Sleep(time.Second)
		}
	}()
}

func (c *MarketWsClient) check() {
	c.rvLock.RLock()
	last := c.lastRv
	c.rvLock.RUnlock()
	if time.Since(last).Seconds() > 20 {
		log.Infof("long time no data")
		c.Restart()
	}
}

func (c *MarketWsClient) Restart() {
	c.client.close()
	time.Sleep(100 * time.Millisecond)
	c.client = NewClient(c)
	c.client.start()
}

type Message struct {
	Type int
	Msg  []byte
	Err  error
}

func ReadMessage(ctx context.Context, conn *websocket.Conn) <-chan Message {
	var msg Message
	ch := make(chan Message)
	go func() {
		for {
			select {
			case <-ctx.Done():
				close(ch)
				return
			default:
			}

			msg.Type, msg.Msg, msg.Err = conn.ReadMessage()
			select {
			case <-ctx.Done():
				close(ch)
				return
			case ch <- msg:
			}
		}
	}()
	return ch
}

//TestClient: ws.client_test.go:52: 1 [321,[["40070.50000","0.00325732","1610102283.798330","s","m",""],["40070.50000","0.15000000","1610102283.802688","s","m",""],["40060.50000","0.00352238","1610102283.804686","s","m",""],["40050.50000","0.00365491","1610102283.806181","s","m",""],["40038.30000","0.10945200","1610102283.807551","s","m",""],["40035.90000","0.03011339","1610102283.809636","s","m",""]],"trade","XBT/USD"] <nil>

//str := `{
//	 "event": "subscribe",
//	 "pair": [
//	   "BTC/USD","ETH/USD"
//	 ],
//	 "subscription": {
//	   "name": "trade"
//	 }
//	}`

type Sub struct {
	Event string            `json:"event"`
	Pair  []string          `json:"pair"`
	Subs  map[string]string `json:"subscription"`
}

//[
//0,
//[
//[
//"5541.20000",
//"0.15850568",
//"1534614057.321597",
//"s",
//"l",
//""
//],
//[
//"6060.00000",
//"0.02455000",
//"1534614057.324998",
//"b",
//"l",
//""
//]
//],
//"trade",
//"XBT/USD"
//]

//1-数据 2-topic 3-合约名
//1-数组 0-价格 1-量 2-时间 3-方向s-b
