package log

import (
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"time"

	"github.com/robfig/cron"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

var levelMap = map[string]zapcore.Level{
	"debug": zapcore.DebugLevel,
	"info":  zapcore.InfoLevel,
	"warn":  zapcore.WarnLevel,
	"error": zapcore.ErrorLevel,
	"fatal": zapcore.FatalLevel,
}

type Logger struct {
	cr *cron.Cron
	lc *LoggerCore
	le map[string]*ExtraLogger
}

func (l *Logger) newLogger(key, file string, onlyFile bool, value zapcore.Level, multiple bool) (*lumberjack.Logger, []zapcore.Core) {
	fileName := file + "_" + key + ".log"
	priority := zap.LevelEnablerFunc(func(lvl zapcore.Level) bool {
		if !multiple {
			return lvl >= value
		} else {
			return lvl == value
		}
	})

	lj := &lumberjack.Logger{
		Filename:  fileName,
		MaxSize:   1 << 7, //128m
		LocalTime: true,
		Compress:  true,
	}

	cores := make([]zapcore.Core, 0, 2)
	cores = append(cores, newLoggerFileWriterCore(lj, multiple && value < limitLevel, priority))
	if !onlyFile {
		cores = append(cores, newLoggerConsoleWriterCore(value, multiple && value < limitLevel, priority))
	}
	return lj, cores
}

func (l *Logger) cronRotate() {
	l.cr = cron.New()
	err := l.cr.AddFunc("59 59 23 * * *", l.Rotate) // 秒,分,时,日,月,周 每日23点59分59秒执行
	if err != nil {
		fmt.Printf("cronRotate add task failed, err:%v\n", err)
		panic(err)
	}
	l.cr.Start()
}

func (l *Logger) Rotate() {
	l.lc.Rotate()

	// 额外日志
	for key := range l.le {
		el := l.le[key]
		go func() {
			_ = el.lj.Rotate()
		}()
	}
}

var (
	limitLevel zapcore.Level
	logger     = Logger{
		lc: NewLoggerCore(),
		le: make(map[string]*ExtraLogger),
	}
)

func InitLogger(file, level string, onlyFile bool, extra ...string) {
	if file == "" {
		panic("undefined log path")
	}

	// 创建日志文件夹
	err := os.MkdirAll(filepath.Dir(file), 0755)
	if err != nil {
		fmt.Printf("InitLogger create logDir failed, path:%s, err:%v\n", filepath.Base(file), err)
		panic(err)
	}

	limitLevel = getLoggerLevel(level)
	var cores []zapcore.Core
	for key, value := range levelMap {
		fd, cs := logger.newLogger(key, file, onlyFile, value, true)
		logger.lc.fd[value] = fd
		cores = append(cores, cs...)
	}
	core := zapcore.NewTee(cores...)
	logger.lc.l = zap.New(
		core,
		zap.AddCaller(),
		zap.AddCallerSkip(2),
	)
	logger.lc.sl = logger.lc.l.Sugar()

	// 初始化额外日志
	for _, key := range extra {
		fd, cs := logger.newLogger(key, file, onlyFile, zapcore.DebugLevel, false)
		el := &ExtraLogger{
			l: zap.New(
				zapcore.NewTee(cs...),
				zap.AddCaller(),
				zap.AddCallerSkip(1),
			),
			lj: fd,
		}
		el.sl = el.l.Sugar()
		logger.le[key] = el
	}

	// 启动定时器,每日0点前进行日志文件刷新
	logger.cronRotate()
}

func Close() {
	logger.cr.Stop()
	logger.lc.Close()
	for _, el := range logger.le {
		el.Close()
	}
}

func newLoggerFileWriterCore(mlogger *lumberjack.Logger, ignore bool, priority zap.LevelEnablerFunc) zapcore.Core {
	// 打印在文件中
	var syncWriter zapcore.WriteSyncer

	if ignore {
		syncWriter = zapcore.AddSync(ioutil.Discard)
	} else {
		syncWriter = zapcore.AddSync(mlogger)
	}

	encoder := zap.NewProductionEncoderConfig()
	encoder.EncodeTime = timeEncoder
	return zapcore.NewCore(zapcore.NewJSONEncoder(encoder), syncWriter, priority)
}

func newLoggerConsoleWriterCore(lv zapcore.Level, ignore bool, priority zap.LevelEnablerFunc) zapcore.Core {
	// 打印在控制台
	var console zapcore.WriteSyncer

	if ignore {
		console = zapcore.Lock(zapcore.AddSync(ioutil.Discard))
	} else {
		if lv > zapcore.InfoLevel {
			console = zapcore.Lock(os.Stderr)
		} else {
			console = zapcore.Lock(os.Stdout)
		}
	}

	consoleEncoder := zap.NewDevelopmentEncoderConfig()
	consoleEncoder.EncodeTime = timeEncoder
	return zapcore.NewCore(zapcore.NewConsoleEncoder(consoleEncoder), console, priority)
}

func getLoggerLevel(lvl string) zapcore.Level {
	if level, ok := levelMap[lvl]; ok {
		return level
	}
	return zapcore.InfoLevel
}

func timeEncoder(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
	enc.AppendString(t.Format("2006-01-02 15:04:05.000"))
}

func Debug(msg string, fields ...zap.Field) {
	logger.lc.Debug(msg, fields...)
}

func Debugf(template string, args ...interface{}) {
	logger.lc.Debugf(template, args...)
}

func Info(msg string, fields ...zap.Field) {
	logger.lc.Info(msg, fields...)
}

func Infof(template string, args ...interface{}) {
	logger.lc.Infof(template, args...)
}

func Warn(msg string, fields ...zap.Field) {
	logger.lc.Warn(msg, fields...)
}

func Warnf(template string, args ...interface{}) {
	logger.lc.Warnf(template, args...)
}

func Error(msg string, fields ...zap.Field) {
	logger.lc.Error(msg, fields...)
}

func Errorf(template string, args ...interface{}) {
	logger.lc.Errorf(template, args...)
}

func Fatal(msg string, fields ...zap.Field) {
	logger.lc.Fatal(msg, fields...)
}

func Fatalf(template string, args ...interface{}) {
	logger.lc.Fatalf(template, args...)
}

func GetLogger(key string) *ExtraLogger {
	return logger.le[key]
}
