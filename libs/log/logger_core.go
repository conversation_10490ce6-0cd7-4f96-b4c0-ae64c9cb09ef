package log

import (
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

func NewLoggerCore() *LoggerCore {
	return &LoggerCore{fd: make(map[zapcore.Level]*lumberjack.Logger)}
}

type LoggerCore struct {
	l  *zap.Logger
	sl *zap.SugaredLogger
	fd map[zapcore.Level]*lumberjack.Logger
}

func (lc *LoggerCore) Rotate() {
	for _, lv := range levelMap {
		if lv < limitLevel {
			continue
		}
		if fd, ok := lc.fd[lv]; ok {
			go func() {
				_ = fd.Rotate()
			}()
		}
	}
}

func (lc *LoggerCore) Close() {
	_ = lc.l.Sync()
	_ = lc.sl.Sync()
	for _, fd := range lc.fd {
		_ = fd.Close()
	}
}

func (lc *LoggerCore) Debug(msg string, fields ...zap.Field) {
	if lc != nil {
		lc.l.Debug(msg, fields...)
	}
}

func (lc *LoggerCore) Debugf(template string, args ...interface{}) {
	if lc != nil {
		lc.sl.Debugf(template, args...)
	}
}

func (lc *LoggerCore) Info(msg string, fields ...zap.Field) {
	if lc != nil {
		lc.l.Info(msg, fields...)
	}
}

func (lc *LoggerCore) Infof(template string, args ...interface{}) {
	if lc != nil {
		lc.sl.Infof(template, args...)
	}
}

func (lc *LoggerCore) Warn(msg string, fields ...zap.Field) {
	if lc != nil {
		lc.l.Warn(msg, fields...)
	}
}

func (lc *LoggerCore) Warnf(template string, args ...interface{}) {
	if lc != nil {
		lc.sl.Warnf(template, args...)
	}
}

func (lc *LoggerCore) Error(msg string, fields ...zap.Field) {
	if lc != nil {
		lc.l.Error(msg, fields...)
	}
}

func (lc *LoggerCore) Errorf(template string, args ...interface{}) {
	if lc != nil {
		lc.sl.Errorf(template, args...)
	}
}

func (lc *LoggerCore) Fatal(msg string, fields ...zap.Field) {
	if lc != nil {
		lc.l.Fatal(msg, fields...)
	}
}

func (lc *LoggerCore) Fatalf(template string, args ...interface{}) {
	if lc != nil {
		lc.sl.Fatalf(template, args...)
	}
}
