package log

import (
	"go.uber.org/zap"
	"gopkg.in/natefinch/lumberjack.v2"
)

type ExtraLogger struct {
	l  *zap.Logger
	sl *zap.SugaredLogger
	lj *lumberjack.Logger
}

func (el *ExtraLogger) Close() {
	_ = el.l.Sync()
	_ = el.sl.Sync()
	_ = el.lj.Close()
}

func (el *ExtraLogger) Debug(msg string, fields ...zap.Field) {
	if el != nil {
		el.l.Debug(msg, fields...)
	}
}

func (el *ExtraLogger) Debugf(template string, args ...interface{}) {
	if el != nil {
		el.sl.Debugf(template, args...)
	}
}

func (el *ExtraLogger) Info(msg string, fields ...zap.Field) {
	if el != nil {
		el.l.Info(msg, fields...)
	}
}

func (el *ExtraLogger) Infof(template string, args ...interface{}) {
	if el != nil {
		el.sl.Infof(template, args...)
	}
}

func (el *ExtraLogger) Warn(msg string, fields ...zap.Field) {
	if el != nil {
		el.l.Warn(msg, fields...)
	}
}

func (el *ExtraLogger) Warnf(template string, args ...interface{}) {
	if el != nil {
		el.sl.Warnf(template, args...)
	}
}

func (el *ExtraLogger) Error(msg string, fields ...zap.Field) {
	if el != nil {
		el.l.Error(msg, fields...)
	}
}

func (el *ExtraLogger) Errorf(template string, args ...interface{}) {
	if el != nil {
		el.sl.Errorf(template, args...)
	}
}

func (el *ExtraLogger) Fatal(msg string, fields ...zap.Field) {
	if el != nil {
		el.l.Fatal(msg, fields...)
	}
}

func (el *ExtraLogger) Fatalf(template string, args ...interface{}) {
	if el != nil {
		el.sl.Fatalf(template, args...)
	}
}
