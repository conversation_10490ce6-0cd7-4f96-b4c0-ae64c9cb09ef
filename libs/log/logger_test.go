package log

import (
	"testing"
	"time"

	"go.uber.org/zap"
)

var _fileName = "demo"

func TestLog(t *testing.T) {
	InitLogger(_fileName, "info", false, "extra1", "extra2")
	defer Close()

	now := time.Now()
	Debug("TestLog Debug", zap.String("time", now.String()))
	Debugf("TestLog Debugf Format, time:%s", now.String())
	Info("TestLog Info", zap.String("time", now.String()))
	Infof("TestLog Infof Format, time:%s", now.String())
	Warn("TestLog Warn", zap.String("time", now.String()))
	Warnf("TestLog Warnf Format, time:%s", now.String())
	Error("TestLog Error", zap.String("time", now.String()))
	Errorf("TestLog Errorf Format, time:%s", now.String())
	//Fatal("TestLog Fatal", zap.String("time", now.String()))
	//Fatalf("TestLog Fatalf Format, time:%s", now.String())

	elog1 := GetLogger("extra1")
	elog1.Debugf("TestLog Debugf Format with elog1, time:%s", now.String())
	elog1.Info("TestLog Info with elog1", zap.String("time", now.String()))

	elog2 := GetLogger("extra2")
	elog2.Debugf("TestLog Debugf Format with elog2, time:%s", now.String())
	elog2.Info("TestLog Info with elog2", zap.String("time", now.String()))

	// 对于没有初始化的logger进行使用,直接跳过
	elog3 := GetLogger("extra3")
	elog3.Debugf("TestLog Debugf Format with elog3, time:%s", now.String())
	elog3.Info("TestLog Info with elog3", zap.String("time", now.String()))
}

func TestLog2(t *testing.T) {
	InitLogger(_fileName, "info", false, "extra1")
	defer Close()

	elog1 := GetLogger("extra1")
	elog2 := GetLogger("extra2")

	go func() {
		time.Sleep(time.Second * 5)
		_ = elog1.lj.Rotate()
	}()

	for i := 0; i < 10; i++ {
		time.Sleep(time.Second)

		now := time.Now()
		Info("TestLog2 Info", zap.String("time", now.String()))
		Errorf("TestLog2 Errorf Format, time:%s", now.String())

		elog1.Debugf("TestLog2 Debugf Format with elog1, time:%s", now.String())
		elog1.Info("TestLog2 Info with elog1", zap.String("time", now.String()))

		// 对于没有初始化的logger进行使用,直接跳过
		elog2.Debugf("TestLog2 Debugf Format with elog2, time:%s", now.String())
		elog2.Info("TestLog2 Info with elog2", zap.String("time", now.String()))
	}
}
