package mdb

import (
	"context"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/jmoiron/sqlx"
	"go.uber.org/zap"
	"spot/libs/define"
	"spot/libs/log"
)

func InitMultipleDB(ctx context.Context, opts map[string]define.DBConf) {
	ctx, m.cancel = context.WithCancel(ctx)
	for name, conf := range opts {
		var read, write *conn
		if len(conf.Read.DSN) > 0 {
			read = &conn{
				dsn: conf.Read.DSN,
				DB:  connectDB(conf.Read),
			}
		}
		if len(conf.Write.DSN) > 0 {
			write = &conn{
				dsn: conf.Write.DSN,
				DB:  connectDB(conf.Write),
			}
		}
		m.addClient(name, &Client{
			name:      name,
			readConn:  read,
			writeConn: write,
		})
	}

	m.ping(ctx)
}

func connectDB(c define.SubConf) *sqlx.DB {
	db, err := sqlx.Open("mysql", c.<PERSON>)
	if err != nil {
		log.Fatal("connectDB Open sqlx connect error", zap.Error(err))
	}
	db.SetMaxOpenConns(c.MaxOpen)
	db.SetMaxIdleConns(c.MaxIdle)
	db.SetConnMaxIdleTime(time.Hour * 2)
	return db
}

func AllDbs() map[string]*Client {
	return m.m
}

func GetClient(name string) *Client {
	return m.getClient(name)
}

func Close() {
	m.Close()
}

type mdb struct {
	cancel context.CancelFunc
	m      map[string]*Client
}

func (m mdb) ping(ctx context.Context) {
	for _, c := range m.m {
		c.ping(ctx)
	}
}

func (m mdb) Close() {
	if m.cancel != nil {
		m.cancel()
	}

	for _, c := range m.m {
		c.close()
	}
}

func (m mdb) addClient(name string, cli *Client) {
	m.m[name] = cli
}

func (m mdb) getClient(name string) *Client {
	cli, ok := m.m[name]
	if !ok {
		log.Fatal(name + " client not init")
	}
	return cli
}

var m = mdb{m: make(map[string]*Client)}

type Client struct {
	name      string
	readConn  *conn
	writeConn *conn
}

func (c *Client) RConn() *conn {
	return c.readConn
}

func (c *Client) RWConn() *sqlx.DB {
	return c.writeConn.DB
}

func (c *Client) Begin() (tx *sqlx.Tx, err error) {
	return c.RWConn().Beginx()
}

func (c *Client) ping(ctx context.Context) {
	if c.readConn != nil {
		go c.pingConn(ctx, c.readConn)
	}
	if c.writeConn != nil {
		go c.pingConn(ctx, c.writeConn)
	}
}

func (c *Client) pingConn(ctx context.Context, conn *conn) {
	var err error
	output := true
	tk := time.NewTicker(time.Second * 5)
	for range tk.C {
		select {
		case <-ctx.Done():
			tk.Stop()
			return
		default:
			err = conn.DB.Ping()
			if err != nil {
				log.Fatal("db ping failed", zap.String("name", c.name), zap.String("address", conn.dsn), zap.Error(err))
			} else {
				if output {
					log.Info("连接mysql成功", zap.String("name", c.name), zap.String("address", conn.dsn))
					output = false
				}
			}
		}
	}
}

func (c *Client) close() {
	if c.readConn != nil {
		_ = c.readConn.DB.Close()
	}
	if c.writeConn != nil {
		_ = c.writeConn.DB.Close()
	}
}

// XaRecover
// 返回处于Prepared状态的xa事务id列表,酌情进行commit或rollback
func (c *Client) XaRecover(ctx context.Context) ([]string, error) {
	rows, err := c.RWConn().QueryxContext(ctx, "XA RECOVER;")
	if err != nil {
		return nil, err
	}
	defer CloseRows(rows)

	var ids []string
	var data XaRecoverData
	for rows.Next() {
		err = rows.StructScan(&data)
		if err != nil {
			return nil, err
		}
		ids = append(ids, data.Data)
	}
	return ids, nil
}

type conn struct {
	dsn string
	*sqlx.DB
}
