package messagequeue

import (
	"spot/libs/conf"
	"spot/libs/log"
	"spot/libs/mq"
	"spot/libs/nums"
)

var (
	MqProduct     *mq.MessageQueue
	MqConsumerPad map[string]*mq.MessageQueue
)

func NewMqProduct(address, workerId string) {
	mq.SetIdWorker(nums.String2Int64(workerId))
	MqProduct = mq.NewMessageQueue(address)
	MqProduct.Ping()
}

// NewMqConsumer
//结算服务，多个服务消费同一队列
func NewMqConsumer(exchangeName, queueName, bindKey, msgQueueId string, handler map[string]mq.MsgHandleFunc) {
	NewConsumer(exchangeName, queueName, bindKey, msgQueueId, false, handler)
}

func NewConsumer(exchangeName, queueName, bindKey, msgQueueId string, isSim bool, handler map[string]mq.MsgHandleFunc) {
	consumer := mq.NewMessageQueue(conf.MQ())
	consumer.IsForce = true
	mqName := queueName + msgQueueId
	if isSim {
		mqName = "sim:" + mqName
	}
	consumerName := bindKey + ":" + mqName

	err := consumer.Consumer(exchangeName, mq.ExchangeTypeDirect, mqName, bindKey)
	if err != nil {
		log.Errorf("创建mq消费者失败,%v", err)
	}
	for name, handleFunc := range handler {
		mq.DefaultHandleMap[name] = handleFunc
	}
	log.Infof("消费者队列完毕，queueName；%+v，bindKey:%v", queueName, bindKey)
	consumer.Ping()
	if MqConsumerPad == nil {
		MqConsumerPad = make(map[string]*mq.MessageQueue)
	}
	MqConsumerPad[consumerName] = consumer
}

func StopMQProduct() {
	if MqProduct == nil {
		return
	}
	MqProduct.ShutDown()
}

func StopMQConsumer() {
	if MqConsumerPad != nil {
		for _, queue := range MqConsumerPad {
			queue.ShutDown()
		}
	}
}
