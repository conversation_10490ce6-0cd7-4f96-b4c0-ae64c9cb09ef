package messagequeue

import (
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/mq"
)

func sendMessage(exchangeName, bindKey string, msg mq.MessagePack) {
	err := MqProduct.Publish(exchangeName, mq.ExchangeTypeDirect, bind<PERSON>ey, msg.<PERSON>(), define.MQReliable)
	if err != nil {
		log.Errorf("发送mq消息失败,err:%v", err)
		return
	}
}

func SendMessage(msg mq.MessagePack) {
	sendMessage(define.MQDefaultExchangeName, define.MQDefaultMQBindKey, msg)
}

func SendMessageForClosing(msg mq.MessagePack) {
	sendMessage(define.MQDefaultExchangeName, define.MQDefaultMQBindKeyClosing, msg)
}
