package messagequeue

import (
	"spot/libs/cache"
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/mq"
	"spot/libs/proto"
)

//发送订单完成消息
func MQTopicOrderOverMsg(d *proto.MatchOverMsg) {
	b, err := json.Marshal(d)
	if err != nil {
		log.Errorf("MQTopicOrderOverMsg json marshal fail,%v", err)
		return
	}
	msg := mq.MessagePack{Topic: define.MQTopicUserOrderOver, Code: d.Code, Data: b, Identifier: d.Identifier, UserId: d.UserId}
	msg.SetExtra(d.OrderId, cache.IncrementOrderSeqId(d.OrderId))
	log.Infof("开始发送mq消息：%+v,topic:%v", string(b), msg.Topic)
	SendMessageForClosing(msg)
}

//发送订单成交消息
func MQTopicOrderTradeMsg(d *proto.OrderTrade) {
	b, err := json.Marshal(d)
	if err != nil {
		log.Errorf("MQTopicOrderTradeMsg json marshal fail,%v", err)
		return
	}
	msg := mq.MessagePack{Topic: define.MQTopicUserOrderTrade, Code: d.Code, Data: b, Identifier: d.Order.Identifier, UserId: d.Order.UserId}
	id := d.Order.OrderId
	msg.SetExtra(id, cache.IncrementOrderSeqId(id))
	log.Infof("开始发送mq消息：%+v,topic:%v", string(b), msg.Topic)
	SendMessageForClosing(msg)
}
