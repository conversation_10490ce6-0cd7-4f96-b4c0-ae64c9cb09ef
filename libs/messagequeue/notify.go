package messagequeue

import (
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/mq"
	"spot/libs/proto"
)

//发送用户信息
func NotifyUserMessage(d *proto.Message) {
	b, err := json.Marshal(d)
	if err != nil {
		log.Errorf("NotifyUserMessage json marshal fail,%v", err)
		return
	}
	msg := mq.MessagePack{Topic: define.MQTopicMessage, Data: b}
	log.Infof("开始发送mq消息：%+v,topic:%v", string(b), msg.Topic)
	SendMessage(msg)
}

// NotifyMqEntrustOrderStatusUpdate 通知委托订单变化
func NotifyMqEntrustOrderStatusUpdate(trade *proto.EntrustOrder) {
	log.Infof("通知委托订单变化：%+v", *trade)
	b, err := json.Marshal(*trade)
	if err != nil {
		log.Errorf("NotifyMqEntrustOrderStatusUpdate json marshal fail,%v", err)
		return
	}
	msg := mq.MessagePack{Topic: define.MQTopicOrderStatusChange, Data: b}
	SendMessage(msg)
}

//通知限价单开仓
func NotifyMqEntrustLimitOrderPlace(trade *proto.EntrustOrder) {
	b, err := json.Marshal(*trade)
	if err != nil {
		log.Errorf("NotifyMqEntrustLimitOrderPlace json marshal fail,%v", err)
		return
	}
	msg := mq.MessagePack{Topic: define.MQTopicLimitOrderAdd, Data: b}
	SendMessage(msg)
}

//通知限价单撤销
func NotifyMqEntrustLimitOrderCancel(trade *proto.EntrustOrder) {
	b, err := json.Marshal(*trade)
	if err != nil {
		log.Errorf("NotifyMqEntrustLimitOrderCancel json marshal fail,%v", err)
		return
	}
	msg := mq.MessagePack{Topic: define.MQTopicLimitOrderRemove, Data: b}
	SendMessage(msg)
}

//通知最新成交到kline
func NotifyPriceIndex(market *proto.IndexHistory) {
	b, err := json.Marshal(market)
	if err != nil {
		log.Errorf("notifyMarketPrice json marshal fail,%v", err)
		return
	}
	msg := mq.MessagePack{Topic: define.MQTopicPriceIndex, Data: b}
	SendMessage(msg)
}

//通知最新成交到push
func NotifyMqNewTrade(trade *proto.MatchOrder) {
	b, err := json.Marshal(*trade)
	if err != nil {
		log.Errorf("notifyMqTradeMatchMsg json marshal fail,%v", err)
		return
	}
	msg := mq.MessagePack{Topic: define.MQTopicNewTrade, Data: b}
	SendMessage(msg)
}

//计划开仓单
func NotifyMqPlanOpenAdd(order *proto.ConditionOrder) {
	b, err := json.Marshal(order)
	if err != nil {
		log.Errorf("NotifyMqPlanOpenDel json marshal fail,%v", err)
		return
	}
	msg := mq.MessagePack{Topic: define.MQTopicPlanOpenAdd, Data: b}
	SendMessage(msg)
}

//移除计划开舱单
func NotifyMqPlanOpenDel(order *proto.ConditionOrder) {
	b, err := json.Marshal(order)
	if err != nil {
		log.Errorf("NotifyMqPlanOpenDel json marshal fail,%v", err)
		return
	}
	msg := mq.MessagePack{Topic: define.MQTopicPlanOpenDel, Data: b}
	SendMessage(msg)
}

func NotifyMqContractChange() {
	msg := mq.MessagePack{Topic: define.MQTopicContractChange}
	SendMessage(msg)
}
