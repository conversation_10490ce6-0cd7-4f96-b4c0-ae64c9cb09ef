package migration

import (
	"crypto/sha512"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"sort"
	"spot/libs/conf"
	"strings"
)

const (
	SideBuy  = "BUY"
	SideSell = "SELL"

	MigrationApiUrl   = "https://api.mercuryo.io"
	MigrationRestiUrl = "https://exchange.mercuryo.io"
	MigrationId       = "283ab74c-9d19-44c5-8607-7ac57929d2e8"
	MigrationKey      = "pan2iskeyhakcnksoqkauehe&01sjk"
)

const (
	CurrencyLimit = "/v1.6/public/currency-limits" //获取最大最小限制
	CurrencyRates = "/v1.6/public/rates"           //交易汇率
)

func GetCurrencyLimit(from, to, sid string) string {
	var params = map[string]string{
		"from":      from,
		"to":        to,
		"type":      sid,
		"widget_id": conf.MigrationId(),
	}
	restsult := request("GET", CurrencyLimit, params)
	return restsult
}

func GETCurrencyRates() string {
	var params = map[string]string{
		"widget_id": conf.MigrationId(),
	}
	restsult := request("GET", CurrencyRates, params)
	return restsult
}

//amout数量，currency币种，fiat_currency法币，address钱包地址，merchantid地址，side方向 buy sell
func GETRestURl(amout, currency, fiatcurrency, merchantid, side, address string, isFiat bool) string {
	//sign := Sign(address)
	var params = map[string]string{
		"type":                    side,
		"currency":                currency,
		"fiat_currency":           fiatcurrency,
		"address":                 address,
		"widget_id":               conf.MigrationId(),
		"merchant_transaction_id": merchantid,
		"fix_fiat_amount":         "true",
		"fix_fiat_currency":       "true",
		"fix_amount":              "true",
		"fix_currency":            "true",
		//"signature":               "6f05a77a5e7b5cca54a7206fcdf7a163dcbf72f98a7be0c05d1069790082ec6ad6cb265e5c1c5ffca0a5ebe5da5db3d8ec2b07882ccb67744199e7ee0e5ac90b",
	}
	if isFiat {
		params["amount"] = amout
	} else {
		params["fiat_amount"] = amout
	}

	sorted := SortParams(params)
	return conf.MigrationRestUrl() + "?" + sorted
}

func request(method string, path string, params map[string]string) string {
	httpClient := &http.Client{}
	//nonce := fmt.Sprintf("%d", time.Now().Unix()*1000)
	sorted := SortParams(params)
	var req *http.Request
	if method == "POST" {
		req, _ = http.NewRequest(method, conf.MigrationApiUrl()+path, strings.NewReader(sorted))
		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	} else {
		req, _ = http.NewRequest(method, conf.MigrationApiUrl()+path+"?"+sorted, strings.NewReader(""))
	}
	content := strings.Join([]string{method, path, sorted}, "?")

	if conf.DebugEnv() {
		fmt.Println("request >>>>>>>>")
		fmt.Println(method, "\n", path, "\n", params, "\n", content, "\n", req.Header)
	}
	resp, _ := httpClient.Do(req)

	defer resp.Body.Close()

	body, _ := ioutil.ReadAll(resp.Body)

	if conf.DebugEnv() {
		fmt.Println("response <<<<<<<<")
		fmt.Println(string(body))
	}

	return string(body)
}

func SortParams(params map[string]string) string {
	keys := make([]string, len(params))
	i := 0
	for k := range params {
		keys[i] = k
		i++
	}
	sort.Strings(keys)
	sorted := make([]string, len(params))
	i = 0
	for _, k := range keys {
		sorted[i] = k + "=" + url.QueryEscape(params[k])
		//sorted[i] = k + "=" + params[k]
		i++
	}
	return strings.Join(sorted, "&")
}

func Sign(message string) string {

	return Hash512(message + conf.MigrationKey())
}

func Hash512(s string) string {
	hashResult := sha512.Sum512([]byte(s))
	hashString := string(hashResult[:])
	return hashString
}
