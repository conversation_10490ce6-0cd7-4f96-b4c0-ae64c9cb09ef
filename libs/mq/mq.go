/*
@Time : 2019-12-30 17:49
<AUTHOR> mocha
@File : mq
*/
package mq

import (
	"errors"
	"fmt"
	"go.uber.org/zap"
	"sync"
	"time"

	"github.com/streadway/amqp"
	"spot/libs/convert"
	"spot/libs/json"
	"spot/libs/log"
)

const (
	ExchangeTypeDirect  = "direct"
	ExchangeTypeFanOut  = "fanout"
	ExchangeTyeTopic    = "topic"
	ExchangeTypeXCustom = "x-custom"
)

type MessageQueue struct {
	address string
	channel *amqp.Connection
	*amqp.Connection
	Ok   bool
	Stop bool //仅仅为系统服务停止标志
	Over bool

	//consumer
	isConsumer                             bool
	exchange, exchangeType, queueName, key string
	lock                                   sync.RWMutex
	lastTs                                 time.Time
	IsForce                                bool
}

func NewMessageQueue(address string) *MessageQueue {
	mq := new(MessageQueue)
	mq.address = address
	conn, err := amqp.Dial(address)
	if err != nil {
		log.Errorf("dial mq address fail,%v", err)
		return mq
	}
	log.Info("NewMessageQueue success", zap.String("address", address), zap.String("mq.address", mq.address))
	mq.Connection = conn
	mq.Ok = true
	mq.lastTs = time.Now()
	return mq
}

func (m *MessageQueue) setLast(t time.Time) {
	m.lock.Lock()
	defer m.lock.Unlock()
	m.lastTs = t
}

func (m *MessageQueue) getLast() (t time.Time) {
	m.lock.RLock()
	defer m.lock.RUnlock()
	t = m.lastTs
	return
}

//此操作不会检测重连
func (m *MessageQueue) ShutDown() {
	if m == nil {
		return
	}
	m.Stop = true
	m.Ok = false
	if m.Connection != nil || !m.Connection.IsClosed() {
		m.Connection.Close()
	}
	if m.isConsumer {
		for {
			if m.Over {
				log.Errorf("消费者消息处理结束")
				return
			}
			//log.Error("等待消费处理完毕")
		}
	}
}

func (m *MessageQueue) Ping() {
	go func() {
		t := time.Tick(1 * time.Second)
		for {
			select {
			case <-t:
				if m.Stop { //只有系统竹筒停止才会stop
					continue
				}
				isForce := false
				if m.IsForce && m.isConsumer {
					if time.Since(m.getLast()).Seconds() > 30 {
						isForce = true
						log.Errorf("mq超过30s未收到消息，将重启mq消费连接")
					}
				}
				if m.Connection == nil || m.Connection.IsClosed() || !m.Ok || isForce {
					if m.Connection != nil && !m.Connection.IsClosed() {
						m.Connection.Close()
						m.Connection = nil
						m.Ok = false
					}
					conn, err := amqp.Dial(m.address)
					if err != nil {
						log.Error("mq Ping", zap.String("address", m.address), zap.Error(err))
						continue
					}
					m.Connection = conn
					m.Ok = true
					m.lastTs = time.Now()
					if m.isConsumer {
						go m.StartConsumer()
					}
					log.Info("mq reconnect success", zap.String("address", m.address))
					log.Error("mq reconnect success", zap.String("address", m.address))
				}
				if m.isConsumer {
					log.Debugf("当前mq状态,消费者;%v,ok:%v", m.isConsumer, m.Ok)
				}
			}
		}
	}()

}

func (m *MessageQueue) Consumer(exchange, exchangeType, queueName, key string) (err error) {
	m.isConsumer = true
	m.exchange, m.exchangeType, m.queueName, m.key = exchange, exchangeType, queueName, key
	if m.Connection == nil || !m.Ok {
		return
	}
	err = m.StartConsumer()
	return err
}

//exchange-type  direct|fanout|topic|x-custom
//noinspection GoErrorStringFormat
func (m *MessageQueue) Publish(exchange, exchangeType, routingKey string, msg []byte, reliable bool) error {
	log.Debugf("consumer exchange:%v,exchangeType:%v,key:%v", exchange, exchangeType, routingKey)
	ch, err := m.Channel()
	if err != nil {
		log.Errorf("get channel fail,%v", err)
		return errors.New("cannot get channel")
	}
	defer ch.Close()
	//log.Warnf("got Channel, declaring %q Exchange (%q)", exchangeType, exchange)
	if err := ch.ExchangeDeclare(
		exchange,     // name
		exchangeType, // type
		true,         // durable
		false,        // auto-deleted
		false,        // internal
		false,        // noWait
		nil,          // arguments
	); err != nil {
		return fmt.Errorf("Exchange Declare: %v", err)
	}

	// reliable publisher confirms require confirm.select support from the
	// connection.
	if reliable {
		log.Debugf("enabling publishing confirms.")
		if err := ch.Confirm(false); err != nil {
			//noinspection GoErrorStringFormat
			return fmt.Errorf("Channel could not be put into confirm mode: %s", err)
		}

		confirms := ch.NotifyPublish(make(chan amqp.Confirmation, 1))

		defer confirmOne(confirms)
	}

	log.Debugf("declared Exchange, publishing %dB body (%q)", len(msg), convert.Bytes2Str(msg))
	if err = ch.Publish(
		exchange,   // publish to an exchange
		routingKey, // routing to 0 or more queues
		false,      // mandatory
		false,      // immediate
		amqp.Publishing{
			Headers:         amqp.Table{},
			ContentType:     "text/plain",
			ContentEncoding: "",
			Body:            msg,
			DeliveryMode:    amqp.Transient, // 1=non-persistent, 2=persistent
			Priority:        0,              // 0-9
			// a bunch of application/implementation-specific fields
		},
	); err != nil {
		return fmt.Errorf("Exchange Publish: %v", err)
	}

	return nil
}

// One would typically keep a channel of publishings, a sequence number, and a
// set of unacknowledged sequence numbers and loop until the publishing channel
// is closed.
func confirmOne(confirms <-chan amqp.Confirmation) {
	log.Debugf("waiting for confirmation of one publishing")

	if confirmed := <-confirms; confirmed.Ack {
		log.Debugf("confirmed delivery with delivery tag: %d", confirmed.DeliveryTag)
	} else {
		log.Errorf("failed delivery of delivery tag: %d", confirmed.DeliveryTag)
	}
}

func (m *MessageQueue) StartConsumer() (err error) {
	exchange, exchangeType, queueName, key := m.exchange, m.exchangeType, m.queueName, m.key

	m.Connection.IsClosed()
	log.Infof("consumer exchange:%v,exchangeType:%v,queueName:%v,key:%v", exchange, exchangeType, queueName, key)
	defer func() {
		if err != nil {
			log.Errorf("mq consumer happen error,begin close:%v", err)
			m.Ok = false
			m.Connection.Close()
		}
	}()

	log.Infof(" consumer got Connection, getting Channel")
	ch, err := m.Connection.Channel()
	if err != nil {
		log.Errorf("consumer get channel error:%v", err)
		return fmt.Errorf("channel: %v", err)
	}

	log.Infof("got Channel, declaring Exchange (%q)", exchange)
	if err = ch.ExchangeDeclare(
		exchange,     // name of the exchange
		exchangeType, // type
		true,         // durable
		false,        // delete when complete
		false,        // internal
		false,        // noWait
		nil,          // arguments
	); err != nil {
		return fmt.Errorf("exchange Declare: %s", err)
	}

	log.Infof("declared Exchange, declaring Queue %q", queueName)
	queue, err := ch.QueueDeclare(
		queueName, // name of the queue
		true,      // durable
		false,     // delete when unused
		false,     // exclusive
		false,     // noWait
		nil,       // arguments
	)
	if err != nil {
		return fmt.Errorf("queue Declare: %s", err)
	}

	log.Infof("declared Queue (%q %d messages, %d consumers), binding to Exchange (key %q)",
		queue.Name, queue.Messages, queue.Consumers, key)

	if err = ch.QueueBind(
		queue.Name, // name of the queue
		key,        // bindingKey
		exchange,   // sourceExchange
		false,      // noWait
		nil,        // arguments
	); err != nil {
		return fmt.Errorf("queue Bind: %s", err)
	}

	deliveries, err := ch.Consume(
		queue.Name, // name
		"",         // consumerTag,
		false,      // noAck
		false,      // exclusive
		false,      // noLocal
		false,      // noWait
		nil,        // arguments
	)
	if err != nil {
		return fmt.Errorf("queue Consume: %s", err)
	}
	log.Debugf("declared Queue (%q %d messages, %d consumers), binding to Exchange (key %q)",
		queue.Name, queue.Messages, queue.Consumers, key)

	go m.handle(deliveries)

	return nil
}

func (m *MessageQueue) handle(deliveries <-chan amqp.Delivery) {
	start := time.Now()
	defer func() {
		m.Over = true
		log.Warn("处理消费完毕", zap.Duration("cost", time.Since(start).Truncate(time.Microsecond)))
	}()
	for d := range deliveries {
		m.setLast(time.Now())
		log.Debugf(
			"Message Get message %dB delivery: [%v] %q",
			len(d.Body),
			d.DeliveryTag,
			d.Body,
		)
		d.Ack(true)
		//continue
		msPack := new(MessagePack)
		err := json.Unmarshal(d.Body, msPack)
		if err != nil {
			log.Errorf("handle mq message json unmarsha fail,%v", err)
			continue
		}
		f, ok := DefaultHandleMap[msPack.Topic]
		if !ok {
			//log.Warnf("mq cannot find topic handler,topic:%v ", msPack.Topic)
			continue
		}
		log.Warn("mq message", zap.Any("data", msPack))

		f(*msPack)
	}
}
