package msg

import (
	"errors"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
	"spot/libs/log"
)

const (
	testPrefix = "13800"

	_sendSMSByMsgService           = "/msg/v1/sms/send"
	_sendEmailByMsgService         = "/msg/v1/email/send"
	_sendAuthCodeEmailByMsgService = "/msg/v1/email/sendCode"
)

type Msg struct {
	offline   bool
	url       string
	appID     string
	secretKey string
}

var msgInstance Msg

func InitNewMsg(offline bool, url, appID, secret string) {
	msgInstance.offline = offline
	msgInstance.url = url
	msgInstance.appID = appID
	msgInstance.secretKey = secret
}

func IgnoreErrSendSms(reqID int64, phone, msg, code string) {
	log.Info("IgnoreErrSendSms",
		zap.String("phone", phone),
		zap.String("code", code),
		zap.String("msg", msg))
	if msgInstance.offline && strings.HasPrefix(phone, testPrefix) {
		// 测试阶段,测试手机号不需要实际发送短信
		return
	}
	SendSmsByMsgServiceIgnore(reqID, phone, msg, code)
}

type SendMsgArg struct {
	AppID     string `json:"app_id"`
	Account   string `json:"account"`
	Title     string `json:"title"`
	Content   string `json:"content"`
	Signature string `json:"signature"`
	Timestamp string `json:"timestamp"`
}

func SendSmsByMsgServiceIgnore(reqID int64, account, content, code string) {
	msg, err := SendSmsByMSGService(account, content, code)
	if err != nil {
		log.Error("SendSmsByMsgServiceIgnore error", zap.Int64("reqID", reqID), zap.String("msg", msg), zap.Error(err))
	}
}

func SendSmsByMSGService(account, content, code string) (string, error) {
	if len(account) == 0 {
		return "", nil
	}
	return msgInstance.Do(_sendSMSByMsgService, msgInstance.signBody(account, "", content, code))
}

func SendEmailByMsgServiceIgnore(reqID int64, account, title, content string) {
	msg, err := SendEmailByMSGService(account, title, content)
	if err != nil {
		log.Error("SendEmailByMsgServiceIgnore error", zap.Int64("reqID", reqID), zap.String("msg", msg), zap.Error(err))
	}
}

func SendEmailByMSGService(account, title, content string) (string, error) {
	if len(account) == 0 {
		return "", nil
	}
	return msgInstance.Do(_sendEmailByMsgService, msgInstance.signBody(account, title, content, ""))
}

func SendAuthCodeEmailByMsgServiceIgnore(reqID int64, account, title, content string) {
	msg, err := SendAuthCodeEmailByMSGService(account, title, content)
	if err != nil {
		log.Error("SendAuthCodeEmailByMsgServiceIgnore error", zap.Int64("reqID", reqID), zap.String("msg", msg), zap.Error(err))
	}
}

func SendAuthCodeEmailByMSGService(account, title, content string) (string, error) {
	if len(account) == 0 {
		return "", nil
	}
	return msgInstance.Do(_sendAuthCodeEmailByMsgService, msgInstance.signBody(account, title, content, ""))
}

func (m *Msg) signBody(account, title, content, code string) io.Reader {
	var buf []string

	values := make(url.Values)
	values.Set("app_id", m.appID)
	buf = append(buf, "app_id="+m.appID)
	values.Set("secret_key", m.secretKey)
	buf = append(buf, "secret_key="+m.secretKey)
	values.Set("account", account)
	buf = append(buf, "account="+account)
	values.Set("content", content)
	buf = append(buf, "content="+content)
	ts := strconv.FormatInt(time.Now().Unix(), 10)
	values.Set("timestamp", ts)
	buf = append(buf, "timestamp="+ts)
	if title != "" {
		values.Set("title", title)
		buf = append(buf, "title="+title)
	}
	if code != "" {
		values.Set("area_code", code)
		buf = append(buf, "area_code="+code)
	}
	buf = append(buf, "signature="+Sha256(Md5(values.Encode())))
	return strings.NewReader(strings.Join(buf, "&"))
}

func (m *Msg) Do(url string, body io.Reader) (string, error) {
	req, err := http.NewRequest(http.MethodPost, m.url+url, body)
	if err != nil {
		log.Error("msg.Do create new request failed", zap.Error(err))
		return "", err
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		log.Error("msg.Do request failed", zap.Error(err))
		return "", err
	}
	defer resp.Body.Close()
	data, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Error("msg.Do read response body failed", zap.Error(err))
		return "", err
	}
	if resp.StatusCode != http.StatusOK {
		log.Error("msg.Do response code unexpected", zap.Int("code", resp.StatusCode))
		return string(data), errors.New(http.StatusText(resp.StatusCode))
	}
	return string(data), nil
}
