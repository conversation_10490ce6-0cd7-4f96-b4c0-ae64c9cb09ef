package notice

import (
	"fmt"
	"spot/libs/xrpcclient/user_rpc"
	"time"

	"go.uber.org/zap"
	"spot/libs/check"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/msg"
	"spot/libs/proto"
)

const (
	_contractTriggerNoticeSubjectCN = "%s触发通知"
	_contractTriggerNoticeSubjectEN = "%s Trigger notification"
	_contractTriggerNoticeSubjectTC = "%s觸發通知"
	_contractTriggerNoticeSubjectKR = "%s 알림 트리거"
	_contractTriggerNoticeContentCN = "您的 %s 交易对 %s 的 %s 已于%s在价格为 %s 时%s。由于行情波动，触发价与实际成交价会有所偏差。"
	_contractTriggerNoticeContentKR = "귀하의 %s 계약 %s  의 %s 이( 가) 이미 값이 %s 때 트리거됩니다."
	_contractTriggerNoticeContentEN = "The %s of your %s contract %s has been triggered on %s at the price of %s."
	_contractTriggerNoticeContentTC = "您的 %s 合約 %s 的 %s 已於 %s 在價格為 %s 時被觸發。"
)

const (
	ContractTriggerNoticeTypePlanCN  = "计划单"
	ContractTriggerNoticeTypePlanEN  = "Plan sheet"
	ContractTriggerNoticeTypePlanTC  = "計畫單"
	ContractTriggerNoticeTypePlanKR  = "계획서"
)

const (
	ContractTriggerNoticeStateSuccess = "触发成功"
	ContractTriggerNoticeStateFail    = "触发失败"
)

func SendContractTriggerNotice(reqID, userID int64, contract, side, triggerType, price, triggerState string, triggerTime time.Time) {
	// 查询用户账号
	user, err := user_rpc.GetInnerUserByID(reqID, userID, false)
	if err != nil {
		log.Error("SendContractTriggerNotice GetInnerUserByID rpc failed",
			zap.Int64("reqID", reqID),
			zap.Error(err))
		return
	}

	var subject, content string
	subject = fmt.Sprintf(_contractTriggerNoticeSubjectCN, triggerType)
	content = fmt.Sprintf(_contractTriggerNoticeContentCN, contract, transTriggerSide(side), triggerType, triggerTime.Format(define.TimeFormatNormal), price, triggerState)
	//switch user.AreaCode {
	//case define.ChineseAreaCode:
	//	subject = fmt.Sprintf(_contractTriggerNoticeSubjectCN, triggerType)
	//	content =fmt.Sprintf(_contractTriggerNoticeContentCN, contract, transTriggerSide(side), triggerType, triggerTime.Format(define.TimeFormatNormal), price)
	//case define.ChineseTWAreaCode, define.ChineseHKAreaCode:
	//	switch triggerType {
	//	case ContractTriggerNoticeTypeLimitCN:
	//		triggerType = ContractTriggerNoticeTypeLimitTC
	//	case ContractTriggerNoticeTypeStopCN:
	//		triggerType = ContractTriggerNoticeTypeStopTC
	//	default:
	//		triggerType = ContractTriggerNoticeTypePlanTC
	//	}
	//	subject = fmt.Sprintf(_contractTriggerNoticeSubjectTC, triggerType)
	//	content =fmt.Sprintf(_contractTriggerNoticeContentTC, contract, transTriggerSide(side), triggerType, triggerTime.Format(define.TimeFormatNormal), price)
	//case define.SouthKoreaAreaCode:
	//	switch triggerType {
	//	case ContractTriggerNoticeTypeLimitCN:
	//		triggerType = ContractTriggerNoticeTypeLimitKR
	//	case ContractTriggerNoticeTypeStopCN:
	//		triggerType = ContractTriggerNoticeTypeStopKR
	//	default:
	//		triggerType = ContractTriggerNoticeTypePlanKR
	//	}
	//	subject = fmt.Sprintf(_contractTriggerNoticeSubjectKR, triggerType)
	//	content =fmt.Sprintf(_contractTriggerNoticeContentKR, contract, transTriggerSide(side), triggerType, triggerTime.Format(define.TimeFormatNormal), price)
	//default:
	//	switch triggerType {
	//	case ContractTriggerNoticeTypeLimitCN:
	//		triggerType = ContractTriggerNoticeTypeLimitEN
	//	case ContractTriggerNoticeTypeStopCN:
	//		triggerType = ContractTriggerNoticeTypeStopEN
	//	default:
	//		triggerType = ContractTriggerNoticeTypePlanEN
	//	}
	//	subject = fmt.Sprintf(_contractTriggerNoticeSubjectEN, triggerType)
	//	content =fmt.Sprintf(_contractTriggerNoticeContentEN, contract, transTriggerSide(side), triggerType, triggerTime.Format(define.TimeFormatNormal), price)
	//}

	go sendMsgNotice(reqID, user, subject, content, false)
}

func transTriggerSide(side string) string {
	if side == define.OrderBuy {
		return "买入"
	}
	return "卖出"
}

func sendMsgNotice(reqID int64, user *proto.InnerUser, subject, content string, trySms bool) {
	log.Info("开始发送用户通知", zap.Any("to", user), zap.String("sub", subject), zap.String("content", content), zap.Bool("trySms", trySms))
	if check.EmailAddr(user.Email) {
		msg.SendEmailByMsgServiceIgnore(reqID, user.Email, subject, content)
	}
	if trySms {
		// 关闭短信通知
		sendSms(reqID, user, content)
	}
}

func sendSms(reqID int64, user *proto.InnerUser, content string) {
	if (user.AreaCode == define.ChineseAreaCode && check.PhoneNumber(user.Phone)) || (user.AreaCode != define.ChineseAreaCode && check.AreaNumber(user.Phone)) {
		// 关闭短信通知
		msg.SendSmsByMsgServiceIgnore(reqID, user.Phone, content, user.AreaCode)
	}
}
