/*
@Time : 2020-01-04 13:56
<AUTHOR> mocha
@File : num
*/
package nums

import (
	"fmt"
	"math"
	"math/rand"
	"sort"
	"spot/libs/log"
	"strconv"
	"time"

	"github.com/shopspring/decimal"
)

func NewFromFloat(f float64) decimal.Decimal {
	if math.IsInf(f, 0) || math.IsNaN(f) {
		return decimal.Zero
	}
	return decimal.NewFromFloat(f)
}

func NewFromString(f string) decimal.Decimal {
	d, err := decimal.NewFromString(f)
	if err != nil {
		log.Debugf("NewFromString fail,%v", err)
		return decimal.Zero
	}
	return d
}

func NewFromInt(i int) decimal.Decimal {
	return NewFromString(strconv.Itoa(i))
}

func NewFromInt64(i int64) decimal.Decimal {
	return NewFromString(strconv.FormatInt(i, 10))
}

func Float(d decimal.Decimal) float64 {
	f, _ := d.Float64()
	return f
}

func Int(d decimal.Decimal) int {
	s := d.Truncate(0)
	return String2Int(s.String())
}

func Int64(d decimal.Decimal) int64 {
	s := d.Truncate(0)
	a, err := strconv.ParseInt(s.String(), 10, 64)
	if err != nil {
		log.Errorf("strconv.ParseInt(s,10,64) fail,%v", err)
		return 0
	}
	return a
}

func String(d decimal.Decimal) string {
	return d.String()
}

func String2Float(amount string) float64 {
	r, _ := strconv.ParseFloat(amount, 64)
	return r
}

func Float2String(amount float64) string {
	return strconv.FormatFloat(amount, 'f', -1, 64)
}

func Int32String(s int32) string {
	return strconv.Itoa(int(s))
}

func Int2String(s int) string {
	return strconv.Itoa(s)
}

func String2Int(s string) int {
	a, _ := strconv.Atoi(s)
	return a
}

func Int64String(n int64) string {
	return strconv.FormatInt(n, 10)
}

func String2Int64(s string) (i int64) {
	if s == "" {
		log.Warnf("传递参数为nil")
		return 0
	}
	i, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		log.Errorf("String2Int64 fail,%v", err)
		return
	}
	return
}

// 取绝对值
func Int64Abs(num int64) int64 {
	return num ^ (num >> 63) - (num >> 63)
}

//计算出减值(最小返回1）
func GetReduceAmount(amount int64, cloneNums int) int64 {
	if cloneNums == 0 {
		cloneNums = 1
	}
	i := NewFromInt64(amount).Div(NewFromInt(cloneNums)).IntPart()
	if i == 0 {
		return 1
	}
	return i
}

func Max(amount []int) int {
	sort.Ints(amount)
	return amount[len(amount)-1]
}

func Min(amount []int) int {
	sort.Ints(amount)
	return amount[0]
}

//number：表示向下舍入的数值。
//
//significance：表示舍入的倍数。
func Floor(num, significance decimal.Decimal) decimal.Decimal {
	if significance.Equal(decimal.Zero) {
		return num
	}
	f := num.Mod(significance)
	return num.Sub(f)
}

func Ceiling(num, significance decimal.Decimal) decimal.Decimal {
	if significance.Equal(decimal.Zero) {
		return num
	}
	quo := num.Div(significance).Truncate(0)
	f := num.Mod(significance)
	if f.Equal(decimal.Zero) {
		return num
	}
	return quo.Add(NewFromInt(1)).Mul(significance)
}

//计算中位数
func Median(list []decimal.Decimal) decimal.Decimal {
	sort.Slice(list, func(i, j int) bool {
		return list[i].LessThan(list[j])
	})
	fmt.Println(list)
	size := len(list)
	if size == 0 {
		return decimal.Zero
	}
	if size == 1 {
		return list[0]
	}
	f := size % 2
	z := size / 2
	if f == 0 {
		first, next := list[z-1], list[z]
		return decimal.Avg(first, next)
	}
	return list[z]
}

func RandIntPart(v decimal.Decimal) decimal.Decimal {
	rand.Seed(time.Now().Unix())
	return NewFromInt64(rand.Int63n(v.IntPart()))
}

func MinDecimalLimitZero(a, b decimal.Decimal) decimal.Decimal {
	return decimal.Max(decimal.Min(a, b), decimal.Zero)
}
