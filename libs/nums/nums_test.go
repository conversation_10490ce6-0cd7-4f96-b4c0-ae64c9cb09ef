/*
@Time : 3/10/20 4:22 下午
<AUTHOR> mocha
@File : nums_test
*/
package nums

import (
	"github.com/shopspring/decimal"
	"testing"
)

func TestFormat(t *testing.T) {
	d := NewFromFloat(100)
	t.Logf("d:%v", d.<PERSON>run<PERSON>(2).StringFixed(2))
}

func TestFloor(t *testing.T) {
	d := NewFromFloat(0.86)
	t.Logf("d:%v", Floor(d, NewFromFloat(0.1)))
}

func TestR(t *testing.T) {
	for i := 0; i < 100; i++ {
		a := RangeRand(-10, 10)
		t.Logf("v:%v", a)
	}

}

func TestMedia(t *testing.T) {
	list := []decimal.Decimal{NewFromInt(1), NewFromInt(7), NewFromInt(9), NewFromInt(4), NewFromInt(5), NewFromInt(100)}
	t.<PERSON>g<PERSON>("%v", Median(list).String())
}

func TestRandDecimal(t *testing.T) {
	for i := 0; i < 100; i++ {
		t.Log(RangDecimal(NewFromString("0.01"), NewFromFloat(0.01), 2))
	}
}
