package nums

import (
	"crypto/rand"
	"github.com/shopspring/decimal"
	"math"
	"math/big"
)

func RangeRand(min, max int64) int64 {
	if min > max {
		panic("the min is greater than max!")
	}

	if min < 0 {
		f64Min := math.Abs(float64(min))
		i64Min := int64(f64Min)
		result, _ := rand.Int(rand.Reader, big.NewInt(max+1+i64Min))

		return result.Int64() - i64Min
	} else {
		result, _ := rand.Int(rand.Reader, big.NewInt(max-min+1))
		return min + result.Int64()
	}
}

func RandFloat(min, max string, radix int32) decimal.Decimal {
	return RangDecimal(NewFromString(min), NewFromString(max), radix)
}

func RangDecimal(min, max decimal.Decimal, radix int32) decimal.Decimal {
	r := NewFromFloat(math.Pow10(int(radix)))
	minD := min.Mul(r)
	maxD := max.Mul(r)
	return decimal.New(RangeRand(minD.IntPart(), maxD.IntPart()), 0).Div(r).Truncate(int32(radix))
}

//func TestRand(t *testing.T) {
//	for i := 0; i < 100; i++ {
//		s := RangeRand(10, 100)
//		t.Log(s)
//	}
//}
//
//
//
//func TestL(t *testing.T) {
//	for i:=0;i<1000;i++{
//		t.Log(RandFloat("0.20","0.9",2).String())
//	}
//}
