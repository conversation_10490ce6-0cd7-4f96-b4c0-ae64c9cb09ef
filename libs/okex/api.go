package okex

import (
	"fmt"
	"go.uber.org/zap"
	"io/ioutil"
	"net/http"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/nums"
	"spot/libs/proto"
	"strings"
	"time"
)

//{"symbol":"GASBTC","price":"0.00004750"}
const (
	url   = "https://www.okx.com/api/v5/market/tickers?instType=%v"
	_name = "okex"
)

func GetTickersForSpot(codes map[string]struct{}) (list []proto.MarketTrade) {
	url := fmt.Sprintf(url, "SPOT")
	return GetTickers(url, codes)
}

func GetTickersForSwap(codes map[string]struct{}) (list []proto.MarketTrade) {
	url := fmt.Sprintf(url, "SWAP")
	return GetTickers(url, codes)
}

func GetTickers(uri string, codes map[string]struct{}) (list []proto.MarketTrade) {
	r, err := http.Get(uri)
	if err != nil {
		log.Error("okex GetTickers fail", zap.String("url", uri), zap.Error(err))
		return
	}
	defer r.Body.Close()

	if r.StatusCode != http.StatusOK {
		log.Errorf("get okex tickers fail,code:%v", r.StatusCode)
		return
	}
	b, err := ioutil.ReadAll(r.Body)
	if err != nil {
		log.Error("okex GetTickers fail", zap.String("url", uri), zap.Error(err))
		return
	}
	//fmt.Println(string(b))
	ticker := new(ORsp)
	err = json.Unmarshal(b, ticker)
	if err != nil {
		log.Error("okex GetTickers fail", zap.String("url", uri), zap.Error(err))
		return
	}

	//log.Info("处理okex合约成交价请求", zap.Any("data", ticker), zap.Any("codes", codes))
	if len(ticker.List) == 0 {
		return
	}

	for _, v := range ticker.List {
		code := strings.ReplaceAll(v.InstId, "-SWAP", "")
		code = strings.ReplaceAll(code, "-", "")

		if v.Ts != "" {
			ts := nums.String2Int64(v.Ts) / 1000
			t := time.Unix(ts, 0)
			if time.Since(t).Seconds() > 60 {
				log.Info("获取okex 数据时间超时", zap.Any("data", v), zap.Any("time", t), zap.Any("seconds", ts))
				continue
			}
		}
		if _, ok := codes[code]; ok {
			mk := proto.MarketTrade{
				Symbol:   code,
				DealTime: time.Now().Unix(),
				Price:    nums.NewFromString(v.Last),
				Ts:       time.Now(),
				Source:   _name,
			}
			list = append(list, mk)
		}
	}
	return
}

type ORsp struct {
	Code string `json:"code"`
	Msg  string `json:"msg"`
	List []Tick `json:"data"`
}

//{
//"code":"0",
//"msg":"",
//"data":[
//{
//"instType":"SWAP",
//"instId":"LTC-USD-SWAP",
//"last":"9999.99",
//"lastSz":"0.1",
//"askPx":"9999.99",
//"askSz":"11",
//"bidPx":"8888.88",
//"bidSz":"5",
//"open24h":"9000",
//"high24h":"10000",
//"low24h":"8888.88",
//"volCcy24h":"2222",
//"vol24h":"2222",
//"sodUtc0":"0.1",
//"sodUtc8":"0.1",
//"ts":"1597026383085"
//},
//]
//}
