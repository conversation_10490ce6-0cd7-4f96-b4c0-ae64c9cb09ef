package okex

import (
	"fmt"
	"go.uber.org/zap"
	"io/ioutil"
	"net/http"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/nums"
	"spot/libs/proto"
	"spot/libs/utils"
	"strings"
	"time"
)

//https://aws.okex.com/api/spot/v3/instruments/ticker//现货价格

//{"symbol":"GASBTC","price":"0.00004750"}
const (
	//baseUrl  = "https://aws.okex.com/api"
	base      = "https://www.okex.com"
	dUrl      = base + "/api/v5/market/books?instId=%s"
	tickerurl = base + "/api/v5/market/tickers"
)

type ReqType string

const (
	ReqTypeSpot ReqType = "SPOT"
	ReqTypeSwap ReqType = "SWAP"
)

func GetTickersForV5(codes map[string]struct{}, ReqType ReqType) (list []proto.MarketTrade) {
	r, err := http.Get(tickerurl + "?instType=" + string(ReqType))
	if err != nil {
		return
	}
	defer r.Body.Close()

	if r.StatusCode != http.StatusOK {
		log.Errorf("get okex tickers fail,code:%v", r.StatusCode)
		return
	}
	b, err := ioutil.ReadAll(r.Body)
	if err != nil {
		return
	}
	fmt.Println(string(b))
	ticker := new(Ticker)
	err = json.Unmarshal(b, ticker)
	if err != nil {
		return
	}
	if len(ticker.Data) == 0 {
		return
	}

	for _, v := range ticker.Data {
		if v.Ts != "" {
			ts := nums.String2Int64(v.Ts) / 1000
			t := time.Unix(ts, 0)
			if time.Since(t).Seconds() > 60 {
				log.Info("获取okex 数据时间超时", zap.Any("data", v), zap.Any("time", t), zap.Any("seconds", ts))
				continue
			}
		}
		code := strings.ReplaceAll(v.InstId, "-SWAP", "")
		code = strings.ReplaceAll(code, "-", "")
		//fmt.Println(code)
		if _, ok := codes[code]; ok {
			mk := proto.MarketTrade{
				Symbol:   code,
				DealTime: nums.NewFromString(v.Ts).Div(nums.NewFromInt(1000)).IntPart(),
				Price:    nums.NewFromString(v.Last),
				Ts:       time.Now(),
				Source:   _name,
			}
			list = append(list, mk)
		}
	}
	return
}

type Ticker struct {
	Code string `json:"code"`
	Msg  string `json:"msg"`
	Data []struct {
		InstType  string `json:"instType"`
		InstId    string `json:"instId"`
		Last      string `json:"last"`
		LastSz    string `json:"lastSz"`
		AskPx     string `json:"askPx"`
		AskSz     string `json:"askSz"`
		BidPx     string `json:"bidPx"`
		BidSz     string `json:"bidSz"`
		Open24H   string `json:"open24h"`
		High24H   string `json:"high24h"`
		Low24H    string `json:"low24h"`
		VolCcy24H string `json:"volCcy24h"`
		Vol24H    string `json:"vol24h"`
		SodUtc0   string `json:"sodUtc0"`
		SodUtc8   string `json:"sodUtc8"`
		Ts        string `json:"ts"`
	} `json:"data"`
}

type TDepth struct {
	Code string `json:"code"`
	Msg  string `json:"msg"`
	Data []struct {
		Asks [][]string `json:"asks"`
		Bids [][]string `json:"bids"`
		Ts   string     `json:"ts"`
	} `json:"data"`
}

//asks	List<String>	卖方深度
//bids	List<String>	买方深度
//timestamp	String	时间戳
func GetSpotDepthForV5(client *http.Client, code string) (data *TDepth) {
	var oName string
	if strings.HasSuffix(code, "USD") {
		oName = strings.ReplaceAll(code, "USD", "-USD-SWAP")
	} else {
		oName = strings.ReplaceAll(code, "USDT", "-USDT")
	}
	u := fmt.Sprintf(dUrl, oName)
	fmt.Println(u)
	b, err := utils.GetUrl(client, u)
	if err != nil {
		fmt.Errorf("%v", err)
		return
	}
	d := new(TDepth)
	err = json.Unmarshal(b, d)
	if err != nil {
		fmt.Println(err)
		log.Error("okex GetSpotDepth json unmarshal fail", zap.Error(err))
		return
	}
	data = d
	return
}
