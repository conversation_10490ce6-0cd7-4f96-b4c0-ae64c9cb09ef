/*
@Time : 2/7/20 3:31 下午
<AUTHOR> mocha
@File : main
*/
package okex

import (
	"encoding/json"
	"errors"
	"fmt"
	"spot/libs/log"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

const (
	_wsMarket     = "wss://ws.okx.com:8443/ws/v5/public"
	_sub          = "subscribe"
	_unSub        = "unsubscribe"
	Contract      = "%s-%s-SWAP"
	SpotContract  = "%s-%s"
	IndexContract = "%s-%s"
)

const (
	ChannelTicker      = "tickers"
	ChannelDepth       = "books"
	ChannelFundingRate = "funding-rate"
	ChannelIndex       = "index-tickers"
	ChannelMarkPrice   = "mark-price"
)

const (
	ActionPartial = "snapshot"
	ActionUpdate  = "update"
)

type MarketWsClient struct {
	dialer        *websocket.Dialer
	wsPoint       string
	topics        []Arg
	con           *websocket.Conn
	lastError     error
	lock, errLock sync.Mutex
	status        bool
	msgHandler    func([]byte)
	closeHandler  func(int, string)
}

type Config struct {
	WsPoint string
	dialer  *websocket.Dialer
}

type Arg struct {
	Channel string `json:"channel"`
	InstId  string `json:"instId"`
}

type OkBasicReq struct {
	Op   string `json:"op"`
	Args []Arg  `json:"args"`
}

type OkexBasicRsp struct {
	Event  string `json:"event"`
	Arg    Arg    `json:"arg"`
	Action string `json:"action"`
	OkexErroRsp
}

type OkexDepthRsp struct {
	Data []OkDepth `json:"data"`
	OkexBasicRsp
}

type OkexErroRsp struct {
	Message string `json:"msg"`
	Code    string `json:"code"`
}

//[{"instrument_id":"BTC-USDT-SWAP","asks":[["6256.4","19","0","2"]]
type OkDepth struct {
	Asks     [][]string `json:"asks"`
	Bids     [][]string `json:"bids"`
	Time     string     `json:"ts"`
	Checksum int64      `json:"checksum"`
}

//{
//"instType": "SWAP",
//"instId": "BTC-USD-SWAP",
//"fundingRate": "0.018",
//"nextFundingRate": "",
//"fundingTime": "1597026383085"
//}
//资金费率
type OkFundingRate struct {
	Type          string `json:"instType"`
	EstimatedRate string `json:"nextFundingRate"` //estimated_rate
	FundingRate   string `json:"fundingRate"`     //当期资金费率
	FundingTime   string `json:"funding_time"`    //当期资金费率时间
	InstrumentId  string `json:"instType"`        //instrumentId
}

type OkexTickerRsp struct {
	Data []Tick `json:"data"`
	OkexBasicRsp
}

//okex ticker数据
type Tick struct {
	InstType  string `json:"instType"`
	InstId    string `json:"instId"`
	Last      string `json:"last"`
	LastSz    string `json:"lastSz"`
	AskPx     string `json:"askPx"`
	AskSz     string `json:"askSz"`
	BidPx     string `json:"bidPx"`
	BidSz     string `json:"bidSz"`
	Open24H   string `json:"open24h"`
	High24H   string `json:"high24h"`
	Low24H    string `json:"low24h"`
	VolCcy24H string `json:"volCcy24h"`
	Vol24H    string `json:"vol24h"`
	SodUtc0   string `json:"sodUtc0"`
	SodUtc8   string `json:"sodUtc8"`
	Ts        string `json:"ts"`
}

//{
//"table": "swap/depth",
//"action": "update",
//"data": [{
//"instrument_id": "BTC-USD-SWAP",
//"asks": [
//["5621.7", "58", "0", "2"],
//["5621.8", "125", "0", "5"],
//["5621.9", "0", "0", "0"],
//["5622", "84", "0", "2"],
//["5623.5", "0", "0", "0"],
//["5624.2", "4", "0", "1"],
//["5625.1", "0", "0", "0"],
//["5625.9", "0", "0", "0"],
//["5629.3", "2", "0", "1"],
//["5650", "187", "0", "8"],
//["5789", "1", "0", "1"]
//],
//"bids": [
//["5621.3", "287", "0", "8"],
//["5621.2", "41", "0", "1"],
//["5621.1", "2", "0", "1"],
//["5621", "26", "0", "2"],
//["5620.8", "194", "0", "2"],
//["5620", "2", "0", "1"],
//["5618.8", "204", "0", "2"],
//["5618.4", "0", "0", "0"],
//["5617.2", "2", "0", "1"],
//["5609.9", "0", "0", "0"],
//["5433", "0", "0", "0"],
//["5430", "0", "0", "0"]
//],
//"timestamp": "2019-05-06T07:03:33.048Z",
//"checksum": -186865074
//}]

type IndexTicker struct {
	ID        string `json:"instId"`
	Last      string `json:"idxPx"` //最新指数价格
	High24H   string `json:"high24h"`
	Low24H    string `json:"low24h"`
	Open24H   string `json:"open24h"`
	SodUtc0   string `json:"sodUtc0"`
	SodUtc8   string `json:"sodUtc8"`
	Timestamp string `json:"ts"`
}

//参数名	参数类型	描述
//instrument_id	String	合约名称，如BTC-USD-SWAP
//mark_price	String	标记价格
//timestamp	String	系统时间戳

type OkexMarkPrice struct {
	InstType  string `json:"instType"`
	ID        string `json:"instId"`
	MarkPrice string `json:"markPx"`
	Timestamp string `json:"ts"`
}

func NewMarketWsClient(config *Config, f func([]byte)) *MarketWsClient {
	c := &MarketWsClient{
		dialer:  nil,
		wsPoint: _wsMarket,
		topics:  nil,
	}
	if config != nil {
		if config.WsPoint != "" {
			c.wsPoint = config.WsPoint
		}
		if config.dialer != nil {
			c.dialer = config.dialer
		} else {
			c.dialer = websocket.DefaultDialer
		}
	}
	c.msgHandler = f
	return c
}

func (c *MarketWsClient) CloseHook(f func(int, string)) {
	c.closeHandler = f
}

func (c *MarketWsClient) Start() {
Retry:
	conn, rsp, err := c.dialer.Dial(c.wsPoint, nil)
	if err != nil {
		log.Errorf("dial to okex fail,err:%v,http rsp:%+v", err, rsp)
		goto Retry
	}
	c.setLastError(nil)
	c.con = conn
	c.subscript(c.topics)
	c.status = true
	log.Infof("success get websocket client conn for okex")
	c.receive()
	//go func() {
	//	err := database.UpdateContractMarketConfig(1)
	//	if err != nil {
	//		log.Errorf("database.UpdateContractMarketConfig fail,%v", err)
	//		return
	//	}
	//}()
}

func (c *MarketWsClient) Subscript(topics []Arg) {
	if c == nil {
		return
	}
	if len(c.topics) > 0 {
		c.ReSubDepth()
	}
	//log.Infof("topics:%v", topics)
	c.topics = topics
	c.subscript(c.topics)
}

func (c *MarketWsClient) ReSubDepth() {
	var topics []Arg
	for _, v := range c.topics {
		if v.Channel == ChannelDepth {
			topics = append(topics, v)
		}
	}
	log.Infof("okex 准备重新订阅合约：%v", topics)
	if len(topics) == 0 {
		return
	}
	c.unSubScript(topics)
	c.subscript(topics)
}

func (c *MarketWsClient) ReSubDepthTopic(topic Arg) {
	if topic.Channel == "" {
		return
	}
	log.Infof("准备重新订阅指定合约：%v", topic)
	topics := []Arg{topic}
	c.subscript(topics)
}

func (c *MarketWsClient) subscript(topics []Arg) {

	if len(topics) == 0 {
		//fmt.Println("okex 订阅topic数量为0,退出订阅")
		return
	}
	log.Infof("开始订阅：%v", topics)
	d := OkBasicReq{Op: _sub, Args: topics}
	b, err := json.Marshal(d)
	if err != nil {
		log.Errorf("Marshal fail,%v", err)
		return
	}
	c.lock.Lock()
	defer c.lock.Unlock()
	if c.con == nil {
		fmt.Printf("okex 连接为空")
		return
	}
	e := c.con.WriteMessage(websocket.TextMessage, b)
	//fmt.Printf("write sub msg:%v", e)
	if e != nil {
		log.Infof("wirte sub msg fail:%v", e)
		fmt.Printf("write sub msg fail,%v\n", e)
		c.setLastError(e)
	}
}

func (c *MarketWsClient) UnSubscript() {
	c.unSubScript(c.topics)
}

func (c *MarketWsClient) unSubScript(topics []Arg) {
	d := OkBasicReq{Op: _unSub, Args: topics}
	b, err := json.Marshal(d)
	if err != nil {
		log.Errorf("Marshal fail,%v", err)
		return
	}
	c.lock.Lock()
	defer c.lock.Unlock()
	if c.con != nil {
		e := c.con.WriteMessage(websocket.TextMessage, b)
		if e != nil {
			c.setLastError(e)
			return
		}
		log.Infof("退订主题：%+v", topics)
	}
}

func (c *MarketWsClient) setLastError(err error) {
	c.errLock.Lock()
	defer c.errLock.Unlock()
	c.lastError = err
}

func (c *MarketWsClient) setConn(conn *websocket.Conn) {
	c.errLock.Lock()
	defer c.errLock.Unlock()
	c.con = conn
}

func (c *MarketWsClient) receive() {
	go func() {
		for {
			if c.reMsg() {
				return
			}
		}
	}()

}

func (c *MarketWsClient) reMsg() bool {
	if !c.status {
		return true
	}
	conn := c.con
	if conn == nil {
		c.setLastError(errors.New("ws conn is null"))
		return true
	}
	conn.SetPingHandler(func(appData string) error {
		c.con.WriteMessage(websocket.PongMessage, nil)
		return nil
	})
	_, b, err := conn.ReadMessage()
	if err != nil {
		log.Errorf("okex readmessage fail,%v", err)
		c.setLastError(err)
		if e, ok := err.(*websocket.CloseError); ok {
			c.closeHandler(e.Code, e.Text)
		}
		return true
	}
	//fmt.Printf("d:%v,err:%v\n", string(b), err)
	if c.msgHandler != nil {
		c.msgHandler(b)
	}
	return false
}

func ReportCloseEvent(code int, text string) {

}

func (c *MarketWsClient) Loop() {
	go func() {
		for {
			if !c.status {
				continue
			}
			c.checkError()
			time.Sleep(time.Second)
		}
	}()
}

func (c *MarketWsClient) checkError() {
	if c.con == nil || c.lastError != nil {
		c.status = false
		if c.con != nil {
			c.con = nil
		}
		c.Start()
		return
	}
}
