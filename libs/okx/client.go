package okx

import (
	"context"
	"io"
	"net/http"
	"strings"
	"time"

	"spot/libs/json"
)

var (
	_RESTEndpoint string
	_RESTTimeout  time.Duration
	_Simulated    bool
)

type Config struct {
	ApiKey     string
	PassPhrase string
	SecretKey  string
}

type RESTClient struct {
	Config
	cli *http.Client
}

var DefaultRESTClient = &RESTClient{
	cli: &http.Client{},
}

func SetGlobal(endpoint string, timeout time.Duration, sim bool) {
	_RESTEndpoint = endpoint
	_RESTTimeout = timeout
	_Simulated = sim
}

func NewRESTClient(c Config) *RESTClient {
	return &RESTClient{Config: c, cli: &http.Client{}}
}

func (rc *RESTClient) do(method, uri string, param map[string]interface{}, reply interface{}) (*RESTResult, error) {
	uri, body, err := parseParam(method, uri, param)
	if err != nil {
		return nil, err
	}

	ctx, cancel := context.WithTimeout(context.Background(), _RESTTimeout)
	defer cancel()
	req, err := http.NewRequestWithContext(ctx, method, _RESTEndpoint+uri, strings.NewReader(body))
	if err != nil {
		return nil, err
	}

	ts := IsoTime()
	sign, err := rc.signRequest(method, uri, body, ts)
	if err != nil {
		return nil, err
	}
	rc.setHeaders(req, ts, sign)

	resp, err := rc.cli.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	res, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var result RESTResult
	result.Data = reply
	err = json.Unmarshal(res, &result)
	return &result, err
}

func (rc *RESTClient) signRequest(method, uri, body, ts string) (string, error) {
	preHash := PreHashString(ts, method, uri, body)
	return HmacSha256Base64Signer(preHash, rc.SecretKey)
}

/*
	SetHeaders Set http request headers:
   	Accept: application/json
   	Content-Type: application/json; charset=UTF-8  (default)
   	Cookie: locale=en_US        (English)
   	OK-ACCESS-KEY: (Your setting)
   	OK-ACCESS-SIGN: (Use your setting, auto sign and add)
   	OK-ACCESS-TIMESTAMP: (Auto add)
   	OK-ACCESS-PASSPHRASE: Your setting
*/
func (rc *RESTClient) setHeaders(req *http.Request, ts string, sign string) {
	req.Header.Add("Accept", "application/json")
	req.Header.Add("Content-Type", "application/json; charset=UTF-8")
	req.Header.Add("Cookie", "locale=zh_CN")
	req.Header.Add("OK-ACCESS-KEY", rc.ApiKey)
	req.Header.Add("OK-ACCESS-SIGN", sign)
	req.Header.Add("OK-ACCESS-TIMESTAMP", ts)
	req.Header.Add("OK-ACCESS-PASSPHRASE", rc.PassPhrase)
	if _Simulated {
		req.Header.Add("x-simulated-trading", "1")
	}
	return
}

func signWsRequest(ts, secretKey string) (string, error) {
	method := "GET"
	uri := "/users/self/verify"
	body := ""
	preHash := PreHashString(ts, method, uri, body)
	return HmacSha256Base64Signer(preHash, secretKey)
}
