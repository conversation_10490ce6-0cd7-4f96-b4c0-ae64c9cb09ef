package okx

const (
	RESTAPIGetInstruments = "/api/v5/public/instruments"
	RESTAPIGetTicker      = "/api/v5/market/ticker"

	RESTAPIGetBalance       = "/api/v5/account/balance"
	RESTAPIGetAccountConfig = "/api/v5/account/config"
	RESTAPISetPositionMode  = "/api/v5/account/set-position-mode"
	RESTAPISetLeverage      = "/api/v5/account/set-leverage"
	RESTAPIGetPositions     = "/api/v5/account/positions"

	RESTAPIPlaceOrder   = "/api/v5/trade/order"
	RESTAPIGetOrder     = "/api/v5/trade/order"
	RESTAPIOrderHistory = "/api/v5/trade/orders-history"

	RESTAPICancelOrder = "/api/v5/trade/cancel-order"
	RESTAPITrade       = "/api/v5/trade/fills"
)

// RESTResultCode 错误码
type RESTResultCode string

const (
	RESTResultCodeSuccess             RESTResultCode = "0"     // 操作成功
	RESTResultCodeFrequent            RESTResultCode = "50011" // 用户请求频率过快，超过该接口允许的限额
	RESTResultCodeSign                RESTResultCode = "50113" // 无效的签名
	RESTResultCodeBalanceInsufficient RESTResultCode = "51008" // 委托失败，账户可用余额不足
)

// InstrumentType 产品类型
type InstrumentType string

const (
	InstrumentTypeSPOT    InstrumentType = "SPOT"    // 币币
	InstrumentTypeMARGIN  InstrumentType = "MARGIN"  // 币币杠杆
	InstrumentTypeSWAP    InstrumentType = "SWAP"    // 永续合约
	InstrumentTypeFUTURES InstrumentType = "FUTURES" // 交割合约
	InstrumentTypeOPTION  InstrumentType = "OPTION"  // 期权
)

func (x InstrumentType) Check() bool {
	switch x {
	case InstrumentTypeSPOT, InstrumentTypeMARGIN, InstrumentTypeSWAP, InstrumentTypeFUTURES, InstrumentTypeOPTION:
		return true
	default:
		return false
	}
}

// InstrumentState 产品状态
type InstrumentState string

const (
	InstrumentStateLive    InstrumentState = "live"    // 交易中
	InstrumentStateSuspend InstrumentState = "suspend" // 暂停中
	InstrumentStatePreopen InstrumentState = "preopen" // 预上线
)

func (x InstrumentState) Valid() bool {
	return x == InstrumentStateLive
}

func (x InstrumentState) Check() bool {
	switch x {
	case InstrumentStateLive, InstrumentStateSuspend, InstrumentStatePreopen:
		return true
	default:
		return false
	}
}

// PositionMode 持仓方式
type PositionMode string

const (
	PositionModeLongShort PositionMode = "long_short_mode" // 双向持仓
	PositionModeNet       PositionMode = "net_mode"        // 单向持仓
)

func (x PositionMode) Check() bool {
	switch x {
	case PositionModeLongShort, PositionModeNet:
		return true
	default:
		return false
	}
}

// MarginMode 保证金模式
type MarginMode string

const (
	MarginModeIsolated MarginMode = "isolated" // 逐仓
	MarginModeCross    MarginMode = "cross"    // 全仓
	MarginModeSpot     MarginMode = "cash"     //非保证金 现货
)

func (x MarginMode) Check() bool {
	switch x {
	case MarginModeIsolated, MarginModeCross:
		return true
	default:
		return false
	}
}

// PositionSide 持仓方向
type PositionSide string

const (
	PositionSideLong  PositionSide = "long"  // 双向持仓多头
	PositionSideShort PositionSide = "short" // 双向持仓空头
	PositionSideNet   PositionSide = "net"   // 单向持仓（交割/永续/期权：pos为正代表多头，pos为负代表空头。
)

func (x PositionSide) Check() bool {
	switch x {
	case PositionSideLong, PositionSideShort, PositionSideNet:
		return true
	default:
		return false
	}
}

// OrderSide 订单方向
type OrderSide string

const (
	OrderSideBuy  OrderSide = "buy"  // 买
	OrderSideSell OrderSide = "sell" // 卖
)

func (x OrderSide) Check() bool {
	switch x {
	case OrderSideBuy, OrderSideSell:
		return true
	default:
		return false
	}
}

// OrderType 订单类型
type OrderType string

const (
	OrderTypeMarket          OrderType = "market"            // 市价单
	OrderTypeLimit           OrderType = "limit"             // 限价单
	OrderTypePostOnly        OrderType = "post_only"         // 只做maker单
	OrderTypeFok             OrderType = "fok"               // 全部成交或立即取消
	OrderTypeIoc             OrderType = "ioc"               // 立即成交并取消剩余
	OrderTypeOptimalLimitIoc OrderType = "optimal_limit_ioc" // 市价委托立即成交并取消剩余（仅适用交割、永续）
)

func (x OrderType) Check() bool {
	switch x {
	case OrderTypeMarket, OrderTypeLimit, OrderTypePostOnly, OrderTypeFok, OrderTypeIoc, OrderTypeOptimalLimitIoc:
		return true
	default:
		return false
	}
}

// OrderState 订单状态
type OrderState string

const (
	OrderTypeCanceled        OrderState = "canceled"         // 撤单成功
	OrderTypeLive            OrderState = "live"             // 等待成交
	OrderTypePartiallyFilled OrderState = "partially_filled" // 部分成交
	OrderTypeFilled          OrderState = "filled"           // 完全成交
)

func (x OrderState) Check() bool {
	switch x {
	case OrderTypeCanceled, OrderTypeLive, OrderTypePartiallyFilled, OrderTypeFilled:
		return true
	default:
		return false
	}
}
