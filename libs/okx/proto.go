package okx

import "fmt"

type RESTResultStatus struct {
	Code RESTResultCode `json:"code"` // 状态码
	Msg  string         `json:"msg"`  // 消息
}

func (r *RESTResultStatus) Error() string {
	return fmt.Sprintf("REST fail, code:[%s] msg:[%s]", r.Code, r.Msg)
}

var _ error = &RESTResultStatus{}

type RESTResult struct {
	RESTResultStatus
	Data interface{} `json:"data"` // 数据
}

/*
	仅定义了部分可能用到的字段,实际可能包含其它字段,完整版请阅读官方文档
*/

type Instrument struct {
	InstType  InstrumentType  `json:"instType"`  // 产品类型
	InstId    string          `json:"instId"`    // 产品id， 如 BTC-USD-SWAP
	Uly       string          `json:"uly"`       // 合约标的指数，如 BTC-USD，仅适用于交割/永续/期权
	Category  string          `json:"category"`  // 手续费档位，每个交易产品属于哪个档位手续费
	SettleCcy string          `json:"settleCcy"` // 盈亏结算和保证金币种，如 BTC 仅适用于交割/永续/期权
	CtVal     string          `json:"ctVal"`     // 合约面值，仅适用于交割/永续/期权
	CtMult    string          `json:"ctMult"`    // 合约乘数，仅适用于交割/永续/期权
	CtValCcy  string          `json:"ctValCcy"`  // 合约面值计价币种，仅适用于交割/永续/期权
	Stk       string          `json:"stk"`       // 行权价格，仅适用于期权
	Lever     string          `json:"lever"`     // 该instId支持的最大杠杆倍数，不适用于币币、期权
	TickSz    string          `json:"tickSz"`    // 下单价格精度，如 0.0001
	LotSz     string          `json:"lotSz"`     // 下单数量精度，如 BTC-USDT-SWAP：1
	MinSz     string          `json:"minSz"`     // 最小下单数量
	State     InstrumentState `json:"state"`     // 产品状态 live：交易中 suspend：暂停中 preopen：预上线
}

type Ticker struct {
	InstType InstrumentType `json:"instType"` // 产品类型
	InstId   string         `json:"instId"`   // 产品id， 如 BTC-USD-SWAP
	Last     string         `json:"last"`     // 最新成交价
	Ts       string         `json:"ts"`       // ticker数据产生时间，Unix时间戳的毫秒数格式，如 1597026383085
}

type Balance struct {
	UTime   string          `json:"uTime"`   // 账户信息的更新时间，Unix时间戳的毫秒数格式，如 1597026383085
	Details []BalanceDetail `json:"details"` // 各币种资产详细信息
}

type BalanceDetail struct {
	Ccy           string `json:"ccy"`           // 币种
	Eq            string `json:"eq"`            // 币种总权益
	CashBal       string `json:"cashBal"`       // 币种余额
	AvailEq       string `json:"availEq"`       // 可用保证金 适用于单币种保证金模式和跨币种保证金模式
	FrozenBal     string `json:"frozenBal"`     // 币种占用金额
	OrdFrozen     string `json:"ordFrozen"`     // 挂单冻结数量
	Upl           string `json:"upl"`           // 未实现盈亏 适用于单币种保证金模式和跨币种保证金模式
	MgnRatio      string `json:"mgnRatio"`      // 保证金率 适用于单币种保证金模式
	NotionalLever string `json:"notionalLever"` // 币种杠杆倍数 适用于单币种保证金模式
}

type AccountConfig struct {
	UID      string       `json:"uid"`      // 账户ID，账户uid和app上的一致
	AcctLv   string       `json:"acctLv"`   // 账户层级 1：简单交易模式，2：单币种保证金模式 ，3：跨币种保证金模式 ，4：组合账户
	PosMode  PositionMode `json:"posMode"`  // 持仓方式 long_short_mode：双向持仓 net_mode：单向持仓 仅适用交割/永续
	AutoLoan bool         `json:"autoLoan"` // 是否自动借币 true：自动借币 false：非自动借币
	Level    string       `json:"level"`    // 当前在平台上真实交易量的用户等级，例如 lv1
}

type Position struct {
	InstType InstrumentType `json:"instType"` // 产品类型
	MgnMode  MarginMode     `json:"mgnMode"`  // 保证金模式 cross：全仓 isolated：逐仓
	PosId    string         `json:"posId"`    // 持仓id
	PosSide  PositionSide   `json:"posSide"`  // 持仓方向
	Pos      string         `json:"pos"`      // 持仓数量
	AvailPos string         `json:"availPos"` // 可平仓数量，适用于 币币杠杆,交割/永续（开平仓模式），期权（交易账户及保证金账户逐仓）。
	AvgPx    string         `json:"avgPx"`    // 开仓平均价
	Upl      string         `json:"upl"`      // 未实现收益
	UplRatio string         `json:"uplRatio"` // 未实现收益率
	InstId   string         `json:"instId"`   // 产品ID，如 BTC-USD-180216
	Lever    string         `json:"lever"`    // 杠杆倍数，不适用于期权
	LiqPx    string         `json:"liqPx"`    // 预估强平价 不适用于期权
	Imr      string         `json:"imr"`      // 初始保证金，仅适用于全仓
	Ccy      string         `json:"ccy"`      // 占用保证金的币种
}

type PlaceResult struct {
	OrdId   string `json:"ordId"`   // 订单ID
	ClOrdId string `json:"ClOrdId"` // 客户自定义订单ID
}

type Order struct {
	InstType  InstrumentType `json:"instType"`  // 产品类型
	InstId    string         `json:"instId"`    // 产品id， 如 BTC-USD-SWAP
	Ccy       string         `json:"ccy"`       // 占用保证金的币种
	OrdId     string         `json:"ordId"`     // 订单ID
	ClOrdId   string         `json:"ClOrdId"`   // 客户自定义订单ID
	Px        string         `json:"px"`        // 委托价格
	Sz        string         `json:"sz"`        // 委托数量
	OrdType   OrderType      `json:"ordType"`   // 订单类型
	Side      OrderSide      `json:"side"`      // 订单方向
	PosSide   PositionSide   `json:"posSide"`   // 持仓方向
	AccFillSz string         `json:"AccFillSz"` // 累计成交数量
	AvgPx     string         `json:"avgPx"`     // 成交均价
	State     OrderState     `json:"state"`     // 订单状态
	Lever     string         `json:"lever"`     // 杠杆倍数，0.01到125之间的数值，仅适用于 币币杠杆/交割/永续
	CoinType  string         `json:"tgtCcy"`    //委托数量的类型
	//base_ccy：交易货币 ；quote_ccy：计价货币
}

type Trade struct {
	InstType string `json:"instType"` //产品类型
	InstId   string `json:"instId"`   //产品id
	TradeId  string `json:"tradeId"`  //最新成交id
	OrdId    string `json:"ordId"`    //订单id
	ClOrdId  string `json:"clOrdId"`  //用户自定义订单id
	BillId   string `json:"billId"`   //账单id
	Tag      string `json:"tag"`      //标签
	FillPx   string `json:"fillPx"`   //最新成交价格
	FillSz   string `json:"fillSz"`   //最新成交数量
	Side     string `json:"side"`     //方向
	PosSide  string `json:"posSide"`  //持仓方向
	ExecType string `json:"execType"` //流动性方向 T：taker M:maker
	FeeCcy   string `json:"feeCcy"`   //交易手续费币种
	Fee      string `json:"fee"`      //手续费
	Ts       string `json:"ts"`       //毫秒时间戳
}
