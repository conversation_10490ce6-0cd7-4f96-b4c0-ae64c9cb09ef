/*
	资产相关接口,需要进行api认证
*/

package okx

import (
	"net/http"
	"spot/libs/nums"
	"strings"
)

// GetBalance 获取账户资产
func (rc *RESTClient) GetBalance(coinName string) (*Balance, error) {
	if rc == nil {
		return nil, ClientUninitialized
	}

	params := make(map[string]interface{})
	if coinName != "" {
		params["ccy"] = coinName
	}
	var list []Balance
	result, err := rc.do(http.MethodGet, RESTAPIGetBalance, params, &list)
	if err != nil {
		return nil, err
	}
	if result.Code != RESTResultCodeSuccess {
		return nil, &result.RESTResultStatus
	}
	if len(list) == 0 {
		return nil, nil
	}

	return &list[0], nil
}

// GetAccountConfig 获取账户配置
func (rc *RESTClient) GetAccountConfig() (*AccountConfig, error) {
	if rc == nil {
		return nil, ClientUninitialized
	}

	var list []AccountConfig
	result, err := rc.do(http.MethodGet, RESTAPIGetAccountConfig, nil, &list)
	if err != nil {
		return nil, err
	}
	if result.Code != RESTResultCodeSuccess {
		return nil, &result.RESTResultStatus
	}
	if len(list) == 0 {
		return nil, nil
	}

	return &list[0], nil
}

// SetPositionMode 设置账户持仓模式
func (rc *RESTClient) SetPositionMode(posMode PositionMode) error {
	if rc == nil {
		return ClientUninitialized
	}
	if !posMode.Check() {
		return ParamNotSupport
	}
	result, err := rc.do(http.MethodPost, RESTAPISetPositionMode, map[string]interface{}{"posMode": posMode}, []struct{}{})
	if err != nil {
		return err
	}
	if result.Code != RESTResultCodeSuccess {
		return &result.RESTResultStatus
	}
	return nil
}

// SetLeverage 设置账户杠杆
func (rc *RESTClient) SetLeverage(instId string, lever string, mgnMode MarginMode) error {
	if rc == nil {
		return ClientUninitialized
	}
	if !mgnMode.Check() {
		return ParamNotSupport
	}
	param := map[string]interface{}{
		"instId":  instId,
		"lever":   lever,
		"mgnMode": mgnMode,
	}

	result, err := rc.do(http.MethodPost, RESTAPISetLeverage, param, struct{}{})
	if err != nil {
		return err
	}
	if result.Code != RESTResultCodeSuccess {
		return &result.RESTResultStatus
	}
	return nil
}

// GetPositions 获取持仓列表
func (rc *RESTClient) GetPositions(instId ...string) ([]Position, error) {
	if rc == nil {
		return nil, ClientUninitialized
	}

	param := make(map[string]interface{})
	if len(instId) > 0 && len(instId) <= 10 {
		param["instId"] = strings.Join(instId, ",")
	}

	var list []Position
	result, err := rc.do(http.MethodGet, RESTAPIGetPositions, param, &list)
	if err != nil {
		return nil, err
	}
	if result.Code != RESTResultCodeSuccess {
		return nil, &result.RESTResultStatus
	}

	for i := len(list) - 1; i >= 0; i-- {
		if nums.String2Float(list[i].Pos) == 0 {
			list = list[:i]
		}
	}

	return list, nil
}
