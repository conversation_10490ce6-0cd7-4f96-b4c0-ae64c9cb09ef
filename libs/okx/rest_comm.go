/*
	公共接口,不需要进行api认证
*/

package okx

import (
	"errors"
	"net/http"
)

var ClientUninitialized = errors.New("client is not initialize")
var ParamNotSupport = errors.New("param is not support")

// GetInstruments 获取产品列表
func (rc *RESTClient) GetInstruments(it InstrumentType) ([]Instrument, error) {
	if rc == nil {
		return nil, ClientUninitialized
	}

	var is []Instrument
	result, err := rc.do(http.MethodGet, RESTAPIGetInstruments, map[string]interface{}{"instType": it}, &is)
	if err != nil {
		return nil, err
	}
	if result.Code != RESTResultCodeSuccess {
		return nil, &result.RESTResultStatus
	}
	return is, nil
}

// GetTicker 获取行情
func (rc *RESTClient) GetTicker(instId string) (*Ticker, error) {
	if rc == nil {
		return nil, ClientUninitialized
	}

	var tks []Ticker
	result, err := rc.do(http.MethodGet, RESTAPIGetTicker, map[string]interface{}{"instId": instId}, &tks)
	if err != nil {
		return nil, err
	}
	if result.Code != RESTResultCodeSuccess {
		return nil, &result.RESTResultStatus
	}
	if len(tks) == 0 {
		return nil, nil
	}

	return &tks[0], nil
}
