package okx

import (
	"testing"
	"time"
)

func TestRESTClient_GetInstruments(t *testing.T) {
	InitLogger()
	SetGlobal("https://www.ouyi.icu", time.Second*5, true)
	list, err := DefaultRESTClient.GetInstruments(InstrumentTypeSWAP)
	if err != nil {
		t.<PERSON>(err.<PERSON>rror())
	}
	t.Logf("%+v", list)
}

func TestRESTClient_GetTicker(t *testing.T) {
	InitLogger()
	SetGlobal("https://www.ouyi.icu", time.Second*5, true)
	tk, err := DefaultRESTClient.GetTicker("BTC-USDT-SWAP")
	if err != nil {
		t.<PERSON><PERSON>(err.<PERSON>rror())
	}
	t.Logf("%+v", tk)
}
