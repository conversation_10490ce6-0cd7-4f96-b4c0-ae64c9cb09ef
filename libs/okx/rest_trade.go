/*
	资产相关接口,需要进行api认证
*/

package okx

import (
	"go.uber.org/zap"
	"net/http"
	"spot/libs/log"
)

// Place 下单
func (rc *RESTClient) Place(instId, clOrdId, sz, px string, oSide OrderSide, orderType OrderType) (*PlaceResult, error) {
	if rc == nil {
		return nil, ClientUninitialized
	}
	if !oSide.Check() {
		return nil, ParamNotSupport
	}

	param := map[string]interface{}{
		"instId":  instId,
		"tdMode":  MarginModeCross,
		"clOrdId": clOrdId,
		"side":    oSide,
		"ordType": orderType,
		"sz":      sz,
		"px":      px,
	}
	if oSide == OrderSideBuy && orderType == OrderTypeMarket {
		param["tgtCcy"] = "quote_ccy"
	} else {
		param["tgtCcy"] = "base_ccy"
	}
	log.Info("okx Place begin",
		zap.Any("params", param))
	var list []PlaceResult
	result, err := rc.do(http.MethodPost, RESTAPIPlaceOrder, param, &list)
	if err != nil {
		return nil, err
	}
	log.Info("dealHedgeForOkex Place back",
		zap.Any("result", result))
	if result.Code != RESTResultCodeSuccess {
		return nil, &result.RESTResultStatus
	}
	if len(list) == 0 {
		return nil, nil
	}

	return &list[0], nil
}

// GetOrder 获取订单信息
func (rc *RESTClient) GetOrder(instId, ordId string) (*Order, error) {
	if rc == nil {
		return nil, ClientUninitialized
	}

	param := map[string]interface{}{
		"instId": instId,
		"ordId":  ordId,
	}

	var list []Order
	result, err := rc.do(http.MethodGet, RESTAPIGetOrder, param, &list)
	if err != nil {
		return nil, err
	}
	if result.Code != RESTResultCodeSuccess {
		return nil, &result.RESTResultStatus
	}
	if len(list) == 0 {
		return nil, nil
	}

	return &list[0], nil
}

// 撤销订单
func (rc *RESTClient) Cancel(instId, ordId string) (*Order, error) {
	if rc == nil {
		return nil, ClientUninitialized
	}

	param := map[string]interface{}{
		"instId": instId,
		"ordId":  ordId,
	}

	var list []Order
	result, err := rc.do(http.MethodGet, RESTAPICancelOrder, param, &list)
	if err != nil {
		return nil, err
	}
	if result.Code != RESTResultCodeSuccess {
		return nil, &result.RESTResultStatus
	}
	if len(list) == 0 {
		return nil, nil
	}

	return &list[0], nil
}

//获取历史订单
func (rc *RESTClient) GetOrderHistory(instId, insType, afterOrderId string) ([]Order, error) {
	if rc == nil {
		return nil, ClientUninitialized
	}

	param := map[string]interface{}{
		"instType": insType,
	}

	if instId != "" {
		param["instId"] = instId
	}
	if afterOrderId != "" {
		param["after"] = afterOrderId
	}

	var list []Order
	result, err := rc.do(http.MethodGet, RESTAPIOrderHistory, param, &list)
	if err != nil {
		return nil, err
	}
	if result.Code != RESTResultCodeSuccess {
		return nil, &result.RESTResultStatus
	}
	return list, nil
}

// GetOrderTrades 获取成交明细
func (rc *RESTClient) GetOrderTrades(instId, insType, ordId string) ([]Trade, error) {
	if rc == nil {
		return nil, ClientUninitialized
	}

	param := map[string]interface{}{
		"instType": insType,
		"instId":   instId,
		"ordId":    ordId,
	}

	var list []Trade
	result, err := rc.do(http.MethodGet, RESTAPITrade, param, &list)
	if err != nil {
		return nil, err
	}
	if result.Code != RESTResultCodeSuccess {
		return nil, &result.RESTResultStatus
	}
	return list, nil
}
