package okx

import (
	"testing"
	"time"
)

//func TestRESTClient_Place(t *testing.T) {
//	InitLogger()
//	SetGlobal("https://www.ouyi.icu", time.Second*5, true)
//	cli := NewRESTClient(Config{
//		ApiKey:     "0b7dab4e-1343-4a47-8466-03478fc1301b",
//		PassPhrase: "testtest",
//		SecretKey:  "F2D0F917ADEB15528A9A5C06D607C62B",
//	})
//	pos, err := cli.Place("BTC-USDT-SWAP", "H"+strconv.FormatInt(time.Now().UnixMilli(), 10), "1", "57747", OrderSideSell, PositionSideLong)
//	if err != nil {
//		t.Fatal(err.Error())
//	}
//	t.Logf("%+v", pos)
//}

func TestRESTClient_GetOrder(t *testing.T) {
	InitLogger()
	SetGlobal("https://www.ouyi.icu", time.Second*5, true)
	cli := NewRESTClient(Config{
		ApiKey:     "0b7dab4e-1343-4a47-8466-03478fc1301b",
		PassPhrase: "testtest",
		SecretKey:  "F2D0F917ADEB15528A9A5C06D607C62B",
	})
	pos, err := cli.GetOrder("BTC-USDT-SWAP", "376993222748413954")
	if err != nil {
		t.Fatal(err.Error())
	}
	t.Logf("%+v", pos)
}
