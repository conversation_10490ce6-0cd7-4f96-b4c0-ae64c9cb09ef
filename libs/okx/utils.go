package okx

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"spot/libs/json"
)

/*
	IsoTime
 	Get a iso time
  	eg: 2018-03-16T18:02:48.284Z
*/
func IsoTime() string {
	return time.Now().UTC().Format("2006-01-02T15:04:05.999Z")
}

/*
	PreHashString the pre hash string
  	eg:
    	timestamp = 2018-03-08T10:59:25.789Z
    	method  = POST
    	request_path = /orders?before=2&limit=30
    	body = {"product_id":"BTC-USD-0309","order_id":"377454671037440"}

  return pre hash string = 2018-03-08T10:59:25.789ZPOST/orders?before=2&limit=30{"product_id":"BTC-USD-0309","order_id":"377454671037440"}
*/
func PreHashString(ts string, method string, requestPath string, body string) string {
	return ts + strings.ToUpper(method) + requestPath + body
}

/*
	HmacSha256Base64Signer signing a message
 	using: hmac sha256 + base64
  	eg:
    	message = Pre_hash function comment
    	secretKey = E65791902180E9EF4510DB6A77F6EBAE
		return signed string = TO6uwdqz+31SIPkd4I+9NiZGmVH74dXi+Fd5X0EzzSQ=
*/
func HmacSha256Base64Signer(message string, secretKey string) (string, error) {
	mac := hmac.New(sha256.New, []byte(secretKey))
	_, err := mac.Write([]byte(message))
	if err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString(mac.Sum(nil)), nil
}

func parseParam(method, uri string, param map[string]interface{}) (string, string, error) {
	switch method {
	case http.MethodGet:
		if len(param) == 0 {
			return uri, "", nil
		}
		sli := make([]string, 0, len(param))
		for k, v := range param {
			sli = append(sli, fmt.Sprintf("%s=%v", k, v))
		}
		return uri + "?" + strings.Join(sli, "&"), "", nil
	case http.MethodPost:
		body, err := json.Marshal(param)
		return uri, string(body), err
	default:
		return "", "", errors.New("method not support")
	}
}
