package okx

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/gorilla/websocket"
	"go.uber.org/zap"
	"net/http"
	"spot/libs/log"
	"spot/libs/nums"
	"sync"
	"time"
)

const (
	_wsMarket    = "wss://ws.okx.com:8443/ws/v5/public"
	_sub         = "subscribe"
	_unSub       = "unsubscribe"
	Contract     = "%s-%s-SWAP"
	SpotContract = "%s-%s"
)

const (
	ActionPartial = "snapshot"
	ActionUpdate  = "update"
)

type MarketWsClient struct {
	dialer                          *websocket.Dialer
	wsPoint                         string
	channels                        []Channel
	lastError                       error
	lock, errLock, instance, rvLock sync.RWMutex
	status                          bool
	closeHandler                    func(int, string)
	msgHandler                      func([]byte)
	lastRv                          time.Time
	client                          *Client
	ClientStatus                    bool
	lastStart                       time.Time
	config                          *Config
}

type Client struct {
	isOk                      bool
	send                      chan []byte
	done                      chan struct{}
	isClose                   bool
	lock, statLock, startLock sync.RWMutex
	mc                        *MarketWsClient
	con                       *websocket.Conn
	ticker                    *time.Ticker
	config                    *Config
}

func NewClient(mc *MarketWsClient) *Client {
	return &Client{
		send:   make(chan []byte, 128),
		done:   make(chan struct{}),
		mc:     mc,
		con:    nil,
		ticker: time.NewTicker(5 * time.Second),
		config: mc.config,
	}
}

func (c *Client) start() {
	log.Infof("client begin start 0")
Retry:
	var header http.Header
	if _Simulated {
		header.Add("x-simulated-trading", "1")
	}
	conn, rsp, err := websocket.DefaultDialer.Dial(c.mc.wsPoint, header)
	if err != nil {
		log.Errorf("dial to okex fail,err:%v,http rsp:%+v", err, rsp)
		time.Sleep(5 * time.Second)
		goto Retry
	}
	c.isOk = true
	c.mc.rvLock.Lock()
	c.mc.lastRv = time.Now()
	c.mc.rvLock.Unlock()
	c.mc.lastStart = time.Now()
	log.Infof("ws dial over r1")
	c.con = conn
	c.Receive()
	//c.PingLoop()
	//订阅
	if c.config != nil {
		c.login()
	}
	c.sub(c.mc.channels)
	log.Infof("ws sub over r2")
}

func (c *Client) close() {
	c.done <- struct{}{}
	c.statLock.Lock()
	c.isClose = true
	c.statLock.Unlock()
	close(c.send)
	c.con.Close()
	c.ticker.Stop()
}

func (c *Client) sub(channels []Channel) {
	if c == nil {
		return
	}
	if c.isClose {
		return
	}

	action := Action{
		Op:   _sub,
		Args: channels,
	}
	b, err := json.Marshal(action)
	if err != nil {
		log.Error("json marshal fail", zap.Error(err))
		return
	}
	c.Write(b)
}

func (c *Client) unSub(channels []Channel) {
	if c.isClose {
		return
	}
	action := Action{
		Op:   _unSub,
		Args: channels,
	}
	b, err := json.Marshal(action)
	if err != nil {
		log.Error("json marshal fail", zap.Error(err))
		return
	}
	c.Write(b)

}

func (c *Client) Write(data []byte) {
	if c.isClose {
		return
	}
	c.send <- data
}

func (c *Client) Receive() {
	go func() {
		ctx, cancel := context.WithCancel(context.Background())
		defer cancel()

		msgChan := ReadMessage(ctx, c.con)
		for {
			select {
			case <-c.done:
				log.Infof("ws close")
				return
			case data := <-c.send:
				c.con.WriteMessage(websocket.TextMessage, data)
			case msg := <-msgChan:
				c.mc.rvLock.Lock()
				c.mc.lastRv = time.Now()
				c.mc.rvLock.Unlock()
				if msg.Err != nil {
					fmt.Printf("接收消息失败 msgType；%v，msg:%s, err:%v", msg.Type, msg.Msg, msg.Err)
					return
				}

				fmt.Printf("收到消息 msgType；%v，msg:%s\n", msg.Type, string(msg.Msg))
				if c.mc.msgHandler != nil {
					c.mc.msgHandler(msg.Msg)
				}
			}

		}

	}()
}

func (c *Client) login() {
	ts := nums.Int64String(time.Now().UnixNano())
	sign, err := signWsRequest(ts, c.config.SecretKey)
	if err != nil {

		return
	}
	action := &Action{
		Op: "login",
		Args: []Account{{
			ApiKey:     c.config.ApiKey,
			Passphrase: c.config.PassPhrase,
			Timestamp:  ts,
			Sign:       sign,
		}},
	}
	b, _ := json.Marshal(action)
	c.Write(b)
}

func NewMarketWsClient(config *Config, f func([]byte)) *MarketWsClient {
	c := &MarketWsClient{
		dialer:   nil,
		wsPoint:  _wsMarket,
		channels: nil,
		config:   config,
	}
	c.dialer = websocket.DefaultDialer
	c.msgHandler = f
	return c
}

func (c *MarketWsClient) CloseHook(f func(int, string)) {
	c.closeHandler = f
}

func (c *MarketWsClient) Start() {
	if c.client != nil {
		c.client.close()
	}
	c.client = NewClient(c)
	c.client.start()
	c.Loop()
}

func (c *MarketWsClient) Subscript(chanels []Channel) {
	if c == nil {
		return
	}
	log.Infof("sub channels:%v", chanels)
	c.channels = chanels
	c.client.sub(c.channels)

}

func (c *MarketWsClient) unSubscript() {
	c.lock.Lock()
	defer c.lock.Unlock()
}

func (c *MarketWsClient) UnSubscript(topics []string) {
	c.lock.Lock()
	defer c.lock.Unlock()
}

func (c *MarketWsClient) Loop() {
	log.Infof("begin start market client loop")
	go func() {
		i := 0
		for {
			i++
			log.Debugf("check...................%v", i)
			c.instance.Lock()
			c.check()
			c.instance.Unlock()
			log.Debugf("check over...................%v", i)
			time.Sleep(time.Second)
		}
	}()
}

func (c *MarketWsClient) check() {
	c.rvLock.Lock()
	last := c.lastRv
	c.rvLock.Unlock()
	if time.Since(last).Seconds() > 20 {
		log.Infof("long time no data")
		c.Restart()
	}
}

func (c *MarketWsClient) Restart() {
	log.Infof("开始重启")
	c.client.close()
	c.client = NewClient(c)
	c.client.start()
}

type Message struct {
	Type int
	Msg  []byte
	Err  error
}

func ReadMessage(ctx context.Context, conn *websocket.Conn) <-chan Message {
	var msg Message
	ch := make(chan Message)
	go func() {
		for {
			select {
			case <-ctx.Done():
				close(ch)
				return
			default:
			}

			msg.Type, msg.Msg, msg.Err = conn.ReadMessage()
			select {
			case <-ctx.Done():
				close(ch)
				return
			case ch <- msg:
			}
		}
	}()
	return ch
}

type Action struct {
	Op   string      `json:"op"`
	Args interface{} `json:"args"`
}

type Account struct {
	ApiKey     string `json:"apiKey"`
	Passphrase string `json:"passphrase"`
	Timestamp  string `json:"timestamp"`
	Sign       string `json:"sign"`
}

type Channel struct {
	Channel string `json:"channel"`
	InstId  string `json:"instId"`
}

type WsResponseChannel struct {
	Arg struct {
		Channel  string `json:"channel"`
		InstType string `json:"instType"`
	} `json:"arg"`
	Data []struct {
		AccFillSz       string     `json:"accFillSz"`
		AmendResult     string     `json:"amendResult"`
		AvgPx           string     `json:"avgPx"`
		CTime           string     `json:"cTime"`
		Category        string     `json:"category"`
		Ccy             string     `json:"ccy"`
		ClOrdId         string     `json:"clOrdId"`
		ExecType        string     `json:"execType"`
		Fee             string     `json:"fee"`
		FeeCcy          string     `json:"feeCcy"`
		FillFee         string     `json:"fillFee"`
		FillFeeCcy      string     `json:"fillFeeCcy"`
		FillNotionalUsd string     `json:"fillNotionalUsd"`
		FillPx          string     `json:"fillPx"`
		FillSz          string     `json:"fillSz"`
		FillTime        string     `json:"fillTime"`
		InstId          string     `json:"instId"`
		InstType        string     `json:"instType"`
		Lever           string     `json:"lever"`
		Msg             string     `json:"msg"`
		NotionalUsd     string     `json:"notionalUsd"`
		OrdId           string     `json:"ordId"`
		OrdType         string     `json:"ordType"`
		Pnl             string     `json:"pnl"`
		PosSide         string     `json:"posSide"`
		Px              string     `json:"px"`
		Rebate          string     `json:"rebate"`
		RebateCcy       string     `json:"rebateCcy"`
		ReqId           string     `json:"reqId"`
		Side            string     `json:"side"`
		SlOrdPx         string     `json:"slOrdPx"`
		SlTriggerPx     string     `json:"slTriggerPx"`
		State           OrderState `json:"state"`
		Sz              string     `json:"sz"`
		Tag             string     `json:"tag"`
		TdMode          string     `json:"tdMode"`
		TgtCcy          string     `json:"tgtCcy"`
		TpOrdPx         string     `json:"tpOrdPx"`
		TpTriggerPx     string     `json:"tpTriggerPx"`
		TradeId         string     `json:"tradeId"`
		UTime           string     `json:"uTime"`
	} `json:"data"`
}
