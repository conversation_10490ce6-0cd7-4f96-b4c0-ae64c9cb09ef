/*
<AUTHOR> mocha
@File : plink
*/
package plink

import (
	"fmt"
	"sync"

	"github.com/emirpasic/gods/lists/doublylinkedlist"
	"github.com/emirpasic/gods/maps/treemap"
	"spot/libs/log"
	"spot/libs/proto"
)

type OrderList struct {
	priceMap *treemap.Map
	l        sync.RWMutex
	list     *doublylinkedlist.List
}

func NewOrderList(priceMap *treemap.Map, values *proto.Order) *OrderList {
	return &OrderList{
		priceMap: priceMap,
		list:     doublylinkedlist.New(values),
	}
}

func (o *OrderList) Get(index int) (value *proto.Order, isContain bool) {
	if o == nil {
		return
	}
	o.l.RLock()
	defer o.l.RUnlock()
	oi, ok := o.list.Get(index)
	if !ok {
		return
	}
	fmt.Printf("type:%T", oi)
	value, isContain = oi.(*proto.Order)
	return
}

func (o *OrderList) Add(value *proto.Order) {
	o.l.Lock()
	defer o.l.Unlock()
	o.list.Add(value)
}

func (o *OrderList) Prepend(value *proto.Order) {
	o.l.Lock()
	defer o.l.Unlock()
	o.list.Prepend(value)
}

func (o *OrderList) Size() int {
	o.l.RLock()
	defer o.l.RUnlock()
	return o.list.Size()
}

func (o *OrderList) Set(index int, value *proto.Order) {
	o.l.Lock()
	defer o.l.Unlock()
	o.list.Set(index, value)
}

func (o *OrderList) Remove(value *proto.Order) {
	o.l.Lock()
	defer o.l.Unlock()
	log.Debugf("准备移除订单 id:%v,index:%v", *value, o.list.IndexOf(value))
	if o.list.Size() == 0 {
		return
	}
	o.list.Remove(o.list.IndexOf(value))
	if o.list.Size() == 0 && o.priceMap != nil {
		o.priceMap.Remove(value.Price)
	}
}

func (o *OrderList) GetById(Id int64) (v *proto.Order) {
	index, vaule := o.list.Find(func(i int, v interface{}) bool {
		order, ok := v.(*proto.Order)
		if ok && order.OrderId == Id {
			return true
		}
		return false
	})
	if index == -1 {
		return nil
	}
	va, ok := vaule.(*proto.Order)
	if ok {
		v = va
	}
	return
}

func (o *OrderList) RemoveId(Id int64) {
	v := o.GetById(Id)
	o.Remove(v)
}

func (o *OrderList) Values() []*proto.Order {
	o.l.RLock()
	defer o.l.RUnlock()
	var l []*proto.Order
	list := o.list.Values()
	for _, value := range list {
		order := value.(*proto.Order)
		l = append(l, order)
	}
	return l
}

func (o *OrderList) PValues() []proto.Order {
	o.l.RLock()
	defer o.l.RUnlock()
	var l []proto.Order
	list := o.list.Values()
	for _, value := range list {
		order := value.(*proto.Order)
		l = append(l, *order)
	}
	return l
}

func (o *OrderList) Each(f func(int, *proto.Order)) {
	o.l.RLock()
	defer o.l.RUnlock()
	o.list.Each(func(index int, value interface{}) {
		order := value.(*proto.Order)
		f(index, order)
	})
}

func FloatAscCompartor(a, b interface{}) int {
	aAsserted := a.(float64)
	bAsserted := b.(float64)
	switch {
	case aAsserted > bAsserted:
		return 1
	case aAsserted < bAsserted:
		return -1
	default:
		return 0
	}
}

func FloatDescCompartor(a, b interface{}) int {
	aAsserted := a.(float64)
	bAsserted := b.(float64)
	switch {
	case aAsserted > bAsserted:
		return -1
	case aAsserted < bAsserted:
		return 1
	default:
		return 0
	}
}
