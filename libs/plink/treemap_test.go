/*
@Time : 3/15/20 9:50 上午
<AUTHOR> mocha
@File : treemap_test
*/
package plink

import (
	"fmt"
	"testing"

	"github.com/emirpasic/gods/maps/treemap"
)

func TestTreeMAP(t *testing.T) {
	asc := treemap.NewWith(FloatAscCompartor)
	des := treemap.NewWith(FloatDescCompartor)
	asc.Put(0.01, 1)
	asc.Put(0.02, 1)
	asc.Put(0.03, 1)
	asc.Put(0.04, 1)

	des.Put(0.01, 1)
	des.Put(0.02, 1)
	des.Put(0.03, 1)
	des.Put(0.04, 1)
	t.Logf("asc:%v", asc.Keys())
	t.Logf("desc:%v", des.Keys())

	a := des.Map(func(key1 interface{}, value1 interface{}) (interface{}, interface{}) {
		k, ok := key1.(float64)
		if ok {
			return k * 2, value1
		}
		return key1, value1
	})

	a.Each(func(key interface{}, value interface{}) {
		t.Logf("k:%v,v:%v", key, value)
	})

	fmt.Println("-------------")
	a.All(func(key interface{}, value interface{}) bool {
		i := key.(float64)
		if i > 0.04 {
			fmt.Println(i)
			return true
		}
		return false
	})

}
