package poloniex

import (
	"github.com/shopspring/decimal"
	"io/ioutil"
	"net/http"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/proto"
	"strings"
	"time"
)

//https://poloniex.com/public?command=returnTicker

const (
	name = "poloniex"
)

func GetTickers(codes map[string]struct{}) (list []proto.MarketTrade) {
	list = getTicker(codes)
	return
}

func getTicker(codes map[string]struct{}) (list []proto.MarketTrade) {
	str := "https://poloniex.com/public?command=returnTicker"
	r, err := http.Get(str)
	if err != nil {
		return
	}
	defer r.Body.Close()
	b, err := ioutil.ReadAll(r.Body)
	if err != nil {
		return
	}
	//t.Logf("r:%v",string(b))
	s := make(map[string]Tick)
	e := json.Unmarshal(b, &s)
	if e != nil {
		log.Infof("t:%v", e)
		return
	}
	for k, v := range s {
		sp := strings.Split(k, "_")
		code := sp[1] + sp[0]
		if _, ok := codes[code]; ok {
			mk := &proto.MarketTrade{
				Symbol:   code,
				DealTime: time.Now().Unix(),
				Price:    v.Last,
				Ts:       time.Now(),
				Source:   name,
			}
			list = append(list, *mk)
		}
	}
	return
}

type Tick struct {
	Id   int64           `json:"id"`
	Last decimal.Decimal `json:"last"`
}
