package pool

import "sync"

type Job struct {
	Func func(id int64)
	ID   int64
}

type JobPool struct {
	jobs    chan *Job
	workers int
	wg      sync.WaitGroup
}

func NewPool(workers int, maxJobNums int) *JobPool {
	return &JobPool{
		jobs:    make(chan *Job, maxJobNums),
		workers: workers,
	}
}

func (p *JobPool) Wait() {
	p.wg.Wait()
}

func (p *JobPool) Run() {
	p.wg.Add(p.workers)
	for i := 0; i < p.workers; i++ {
		go func(w *sync.WaitGroup) {
			for job := range p.jobs {
				job.Func(job.ID)
			}
			w.Done()
		}(&p.wg)
	}
}

func (p *JobPool) AddJobs(jobs ...*Job) {
	for _, job := range jobs {
		p.jobs <- job
	}
}

func (p *JobPool) Close() {
	close(p.jobs)
}

func init() {
}
