package pprof

import (
	"net/http"
	"net/http/pprof"

	"github.com/gorilla/mux"
)

// Wrap adds several routes from package `net/http/pprof` to *mux.Router object
func Wrap(router *mux.Router) {
	routers := []struct {
		Method  string
		Path    string
		Handler http.HandlerFunc
	}{
		{"GET", "/debug/pprof/", IndexHandler()},
		{"GET", "/debug/pprof/heap", HeapHandler()},
		{"GET", "/debug/pprof/goroutine", GoroutineHandler()},
		{"GET", "/debug/pprof/allocs", AllocsHandler()},
		{"GET", "/debug/pprof/block", BlockHandler()},
		{"GET", "/debug/pprof/threadcreate", ThreadCreateHandler()},
		{"GET", "/debug/pprof/cmdline", CmdlineHandler()},
		{"GET", "/debug/pprof/profile", <PERSON><PERSON><PERSON><PERSON>()},
		{"GET", "/debug/pprof/symbol", SymbolHandler()},
		{"POST", "/debug/pprof/symbol", SymbolHandler()},
		{"GET", "/debug/pprof/trace", TraceHandler()},
		{"GET", "/debug/pprof/mutex", MutexHandler()},
	}

	for _, r := range routers {
		router.HandleFunc(r.Path, r.Handler).Methods(r.Method)
	}
}

// IndexHandler will pass the call from /debug/pprof to pprof
func IndexHandler() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		pprof.Index(w, r)
	}
}

// HeapHandler will pass the call from /debug/pprof/heap to pprof
func HeapHandler() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		pprof.Handler("heap").ServeHTTP(w, r)
	}
}

// GoroutineHandler will pass the call from /debug/pprof/goroutine to pprof
func GoroutineHandler() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		pprof.Handler("goroutine").ServeHTTP(w, r)
	}
}

// AllocsHandler will pass the call from /debug/pprof/allocs to pprof
func AllocsHandler() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		pprof.Handler("allocs").ServeHTTP(w, r)
	}
}

// BlockHandler will pass the call from /debug/pprof/block to pprof
func BlockHandler() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		pprof.Handler("block").ServeHTTP(w, r)
	}
}

// ThreadCreateHandler will pass the call from /debug/pprof/threadcreate to pprof
func ThreadCreateHandler() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		pprof.Handler("threadcreate").ServeHTTP(w, r)
	}
}

// CmdlineHandler will pass the call from /debug/pprof/cmdline to pprof
func CmdlineHandler() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		pprof.Cmdline(w, r)
	}
}

// ProfileHandler will pass the call from /debug/pprof/profile to pprof
func ProfileHandler() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		pprof.Profile(w, r)
	}
}

// SymbolHandler will pass the call from /debug/pprof/symbol to pprof
func SymbolHandler() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		pprof.Symbol(w, r)
	}
}

// TraceHandler will pass the call from /debug/pprof/trace to pprof
func TraceHandler() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		pprof.Trace(w, r)
	}
}

// MutexHandler will pass the call from /debug/pprof/mutex to pprof
func MutexHandler() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		pprof.Handler("mutex").ServeHTTP(w, r)
	}
}
