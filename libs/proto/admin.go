package proto

// 后台提币请求参数
type AdminWithdrawArg struct {
	CoinID     int     `json:"coin_id"`     // 币种id
	CoinFlag   string  `json:"coin_flag"`   // 币种标识
	Operator   string  `json:"operator"`    // 操作员
	Address    string  `json:"address"`     // 到账地址
	Amount     float64 `json:"amount"`      // 提现金额
	PlatformID int     `json:"platform_id"` //平台id
}

type WithdrawOperateArg struct {
	Id       int64  `json:"id"`       // 订单id
	Operator string `json:"operator"` // 操作员
	Pass     bool   `json:"pass"`     // 是否通过 true 通过 false 驳回
}

type APIArg struct {
	GroupID    int    `json:"group_id"`    // 组id
	ConfigId   int    `json:"config_id"`   //非必须，修改时传入
	PublicKey  string `json:"public_key"`  //公钥
	PrivateKey string `json:"private_key"` //私钥
	Phrase     string `json:"phrase"`      //短语
}

type MainHedgeArg struct {
	ConfigId     int    `json:"config_id"`     //tb_hedge_contract_config 主对冲配置id
	ContractCode string `json:"contract_code"` //合约名称
	HedgeAccount string `json:"hedge_account"` //合约账户
}
