package proto

import (
	"time"

	"spot/libs/define"
)

type VersionInfo struct {
	PlatformID    int                       `db:"platform_id" json:"platform_id"`       // 平台id
	ChannelID     int                       `db:"channel_id" json:"channel_id"`         // 渠道id
	OSType        define.OsType             `db:"os_type" json:"os_type"`               // 平台类型
	Version       int                       `db:"version" json:"version"`               // 数字版本号
	VersionString string                    `db:"version_string" json:"version_string"` // 文字版本号
	ForceUpgrade  bool                      `db:"force_upgrade" json:"force_upgrade"`   // 是否强制升级
	Link          string                    `db:"link" json:"link"`                     // 下载页面
	URL           string                    `db:"url" json:"url"`                       // 下载链接
	CreateTime    time.Time                 `db:"create_time" json:"create_time"`       // 更新时间
	LastForce     int                       `db:"last_force" json:"last_force"`         // 最后一个强制更新版本
	Content       map[define.ReqLang]string `json:"content"`                            // 更新文案
}
