package proto

import (
	"github.com/shopspring/decimal"
	"spot/libs/alias"
	"spot/libs/define"
)

type ApiSymbolList struct {
	Symbol           string          `json:"symbol"`            // 交易对
	Icon             string          `json:"icon"`              // 图标
	CoinName         string          `json:"coin_name"`         // 币种名称
	MarketName       string          `json:"market_name"`       // 结算币种名
	Digit            int32           `json:"digit"`             // 精度
	VolumeDigit      int32           `json:"volume_digit"`      // 交易对数量精度
	Price            decimal.Decimal `json:"price"`             // 成交价格
	PriceCNY         decimal.Decimal `json:"price_cny"`         // 成交价格(人民币)
	ChangeRatio      decimal.Decimal `json:"change_ratio"`      // 涨跌幅百分比
	Change           decimal.Decimal `json:"change"`            // 涨跌
	TradeVolume      decimal.Decimal `json:"trade_volume"`      // 成交量
	TradeValue       decimal.Decimal `json:"trade_value"`       // 成交额
	FeeMaker         decimal.Decimal `json:"fee_maker"`         // maker手续费率
	FeeTaker         decimal.Decimal `json:"fee_taker"`         // 手续费
	IOCLimit         decimal.Decimal `json:"ioc_limit"`         // ioc委托限价
	IOCBuyLimit      decimal.Decimal `json:"ioc_buy_limit"`     // ioc买方价格限制
	IOCSellLimit     decimal.Decimal `json:"ioc_sell_limit"`    // ioc卖方价格限制
	LockFloatFactor  decimal.Decimal `json:"lock_float_factor"` // 资产冻结上浮系数
	RedundancyFactor decimal.Decimal `json:"redundancy_factor"` // 市价冗余系数
	HighPrice        decimal.Decimal `json:"high_price"`        // 最高价
	LowPrice         decimal.Decimal `json:"low_price"`         // 最低价
	Delisted         alias.NumBool   `json:"delisted"`          // 是否已下架
	IsMaintenance    alias.NumBool   `json:"is_maintenance"`    // 是否系统维护
}

type ApiSymbolDetail struct {
	Symbol           string          `json:"symbol"`            // 交易对名称
	Icon             string          `json:"icon"`              // 图标
	Digit            int32           `json:"digit"`             // 价格精度
	VolumeDigit      int32           `json:"volume_digit"`      // 数量精度
	CoinName         string          `json:"coin_name"`         // 币种名称
	MarketName       string          `json:"market_name"`       // 结算币种名
	Step             decimal.Decimal `json:"step"`              // 下单价格步长
	MinOrderVolume   decimal.Decimal `json:"min_order_volume"`  // 单笔最小下单量
	MaxOrderVolume   decimal.Decimal `json:"max_order_volume"`  // 单笔最大下单量
	MinOrderMoney    decimal.Decimal `json:"min_order_money"`   // 单笔最小下单金额
	MaxOrderMoney    decimal.Decimal `json:"max_order_money"`   // 单笔最大下单金额
	FeeMaker         decimal.Decimal `json:"fee_maker"`         // maker手续费率
	FeeTaker         decimal.Decimal `json:"fee_taker"`         // taker手续费率
	Price            decimal.Decimal `json:"price"`             // 价格
	PriceCNY         decimal.Decimal `json:"price_cny"`         // 折合人民币价格
	BuyPrice         decimal.Decimal `json:"buy_price"`         // 买入指数价格
	SellPrice        decimal.Decimal `json:"sell_price"`        // 卖出指数价格
	HighPrice        decimal.Decimal `json:"high_price"`        // 最高价
	LowPrice         decimal.Decimal `json:"low_price"`         // 最低价
	ChangeDaily      decimal.Decimal `json:"change_daily"`      // 当日涨跌幅
	Change24h        decimal.Decimal `json:"change_24h"`        // 24小时涨跌幅
	Change8h         decimal.Decimal `json:"change_8h"`         // 8小时涨跌幅
	Change4h         decimal.Decimal `json:"change_4h"`         // 4小时涨跌幅
	Change2h         decimal.Decimal `json:"change_2h"`         // 2小时涨跌幅
	Change1h         decimal.Decimal `json:"change_1h"`         // 1小时涨跌幅
	Change30m        decimal.Decimal `json:"change_30m"`        // 30分钟涨跌幅
	Change10m        decimal.Decimal `json:"change_10m"`        // 10分钟涨跌幅
	ChangeVolume     decimal.Decimal `json:"change_volume"`     // 当日涨跌量
	Trade24h         decimal.Decimal `json:"trade_24h"`         // 24小时交易量
	TradeValue24h    decimal.Decimal `json:"trade_value_24h"`   // 24小时交易额
	IOCLimit         decimal.Decimal `json:"ioc_limit"`         // ioc委托限价
	IOCBuyLimit      decimal.Decimal `json:"ioc_buy_limit"`     // ioc买方价格限制
	IOCSellLimit     decimal.Decimal `json:"ioc_sell_limit"`    // ioc卖方价格限制
	LockFloatFactor  decimal.Decimal `json:"lock_float_factor"` // 资产冻结上浮系数
	RedundancyFactor decimal.Decimal `json:"redundancy_factor"` // 市价冗余系数
	Delisted         alias.NumBool   `json:"delisted"`          // 是否下架 false-上架,true-下架
	IsMaintenance    alias.NumBool   `json:"is_maintenance"`    // 是否系统维护
}

type ApiKLineArg struct {
	Symbol    string `json:"symbol"`     // 交易对
	Duration  string `json:"duration"`   // k线时间段
	StartTime int64  `json:"start_time"` // 开始时间
	EndTime   int64  `json:"end_time"`   // 结束时间
}

type ApiKLineData struct {
	Symbol     string  `json:"symbol"`      // 交易对
	Duration   string  `json:"duration"`    // k线时间段
	ServerTime int64   `json:"server_time"` // 服务器时间
	List       []KLine `json:"list"`        // k线数据
}

type ApiRecentOrder struct {
	TradeID      int64           `json:"trade_id" db:"trade_id"`           // 订单id
	ContractCode string          `json:"contract_code" db:"contract_code"` // 合约code
	Price        string          `json:"price" db:"price"`                 // 价格
	Volume       decimal.Decimal `json:"volume" db:"volume"`               // 数量
	TradeValue   decimal.Decimal `db:"trade_value" json:"trade_value"`     // 成交额
	Side         string          `json:"side" db:"side"`                   // 成交方向
	OrderTime    int64           `json:"order_time" db:"order_time"`       // 成交时间
}

type QuoteChange struct {
	ContractCode string          `json:"contract_code" db:"contract_code"` // 合约代码
	Index        float64         `json:"index" db:"index"`                 // 合约指数
	IndexStr     string          `json:"index_str" db:"index_str"`         // 合约指数(字符串)
	IndexCNY     decimal.Decimal `db:"index_cny" json:"index_cny"`         // 人民币指数价格
	QuoteChange  string          `json:"quote_change" db:"quote_change"`   // 合约涨跌幅
}

type ContractPageArg struct {
	ContractCode string `json:"contract_code"` // 合约代码
	define.Page
}

type HourFactor struct {
	A decimal.Decimal `json:"a"`
	B decimal.Decimal `json:"b"`
}

type CoinMarketConf struct {
	CoinName  string          `db:"coin_name" json:"coin_name"`   // 市场币种名
	MinAmount decimal.Decimal `db:"min_amount" json:"min_amount"` // 最小成交额
	Delisted  alias.NumBool   `db:"delisted" json:"delisted"`     // 是否下架
	OrderBy   float64         `db:"order_by" json:"order_by"`     // 排序权重,从大到小
}

type APISymbolMarketReply struct {
	List []APISymbolMarket `json:"list" extensions:"x-order=01"` // 数据列表
}

type APISymbolMarket struct {
	Symbol      string          `json:"symbol" example:"BTCUSDT" extensions:"x-order=01"`                            // 合约/交易对
	Icon        string          `json:"icon" example:"https://www.a.cn/btc.png" extensions:"x-order=02"`             // 图标
	Price       decimal.Decimal `json:"price" swaggertype:"string" example:"123.123" extensions:"x-order=03"`        // 最新价
	ChangeRatio decimal.Decimal `json:"change_ratio" swaggertype:"string" example:"123.123" extensions:"x-order=04"` // 涨跌幅
	HighPrice   decimal.Decimal `json:"high_price" swaggertype:"string" example:"123.123" extensions:"x-order=05"`   // 最高价
	LowPrice    decimal.Decimal `json:"low_price" swaggertype:"string" example:"123.123" extensions:"x-order=06"`    // 最低价
	TradeVolume decimal.Decimal `json:"trade_volume" swaggertype:"string" example:"123.123" extensions:"x-order=07"` // 成交量
}
