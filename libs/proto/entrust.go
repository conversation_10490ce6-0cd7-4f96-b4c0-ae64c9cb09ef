package proto

import (
	"github.com/shopspring/decimal"
	"spot/libs/define"
	"time"
)

type EntrustSymbol struct {
	Lang define.ReqLang //请求语言
	Code string         `json:"code"` //合约名，以合约名区分服务
}

type EntrustBase struct {
	EntrustSymbol
	UserID          int64 `json:"user_id"`          //委托用户id
	OrderID         int64 `json:"order_id"`         //委托id
	EntrustIdentity int   `json:"entrust_identity"` //委托身份 0-普通用户 1-做市商(无账户）
}

//判断用户是否为带账户用户
func (m *EntrustBase) IsUserWithAccount() bool {
	return isUserWithAccount(m.EntrustIdentity)
}

type EntrustPlaceBatchArg struct {
	RequestId int64 `json:"request_id"`
	EntrustBase
	Bids            []EntrustNode       `json:"bids"`              //买单
	Asks            []EntrustNode       `json:"asks"`              //卖单
	Cancels         []EntrustCancelNode `json:"cancels"`           //做市撤销订单列表
	CancelRequestId int64               `json:"cancel_request_id"` //撤销id
	IpAddress       string              `json:"ip_address"`
	DeviceId        string              `json:"device_id"`
	APPID           int                 `json:"appid"`

	DepthOrderConfig
}

type DepthOrderConfig struct {
	BuyHighPrice    decimal.Decimal `json:"buy_high_price"`
	SellHighPrice   decimal.Decimal `json:"sell_high_price"`
	SingleMaxVolume int64           `json:"single_max_volume"`
	RCDealMaxVolume int64           `json:"rc_deal_max_volume"`
}

type EntrustDepthBatchArg struct {
	RequestId int64 `json:"request_id"`
	EntrustBase
	Places  []MOrder `json:"places"`  //做市开仓订单列表
	Cancels []MOrder `json:"cancels"` //做市撤销订单列表
	DepthOrderConfig

	BDepthReplyChan chan BDepthReply `json:"-"`
}

type EntrustNode struct {
	ID     int64           `json:"id"`     //委托id
	Price  decimal.Decimal `json:"price"`  //限价单委托价
	Volume decimal.Decimal `json:"volume"` //挂单张数
	Lever  int             `json:"lever"`  //杠杠
}

type EntrustCancelNode struct {
	BsId  int64           `json:"bs_id"` //批量撤单id
	ID    int64           `json:"id"`    //委托id
	Price decimal.Decimal `json:"price"` //撤销的价格
	Side  string          `json:"side"`  //成交方向
}

type EntrustDetail struct {
	IsReturn        bool            `json:"is_return"`       //是否回挂单
	ReturnMatchId   int64           `json:"return_match_id"` //回挂撮合id
	EntrustType     int             `json:"entrust_type"`    //委托类型 0-市价 1-限价
	MatchType       int             `json:"match_type"`      //0-普通委托 1-强平 2-止盈止损
	Side            string          `json:"side"`
	Price           decimal.Decimal `json:"price"`            //限价单委托价
	Volume          decimal.Decimal `json:"volume"`           //挂单张数
	Offset          string          `json:"offset"`           //开平仓
	Mode            int             `json:"mode"`             //委托模式 1-对手价 2-最优3挡 3-最优5挡
	EntrustStrategy int             `json:"entrust_strategy"` //限价策略 0-默认 1-FOK 2-FAK 3-Maker  6-延迟撤销（CancelDuration需设置） 100-剩余立刻协议平仓
	CreateTime      time.Time       `json:"create_time"`
	CancelDuration  time.Duration   `json:"cancel_duration"` //限价单自动挂单撤销周期(强平及做市单用）
	IsOrderFollow   bool            `json:"is_order_follow"` //是否跟单
}

type EntrustPlaceMatchArg struct {
	RequestId int64 `json:"request_id"`
	EntrustBase
	EntrustDetail
	IsRecover bool //是否系统恢复
}

type EntrustCancelArg struct {
	RequestId int64 `json:"request_id"`
	EntrustBase
	Side  string          `json:"side"`
	Price decimal.Decimal `json:"price"`
	Mark  int             `json:"mark"`
}

type MatchUser struct {
	UserId     int64 ` json:"user_id"`
	BusinessId int64 `json:"business_id"`
	OrderId    int64 `json:"order_id"`
	Identifier int   `json:"identifier"`
	IsMaker    bool  `json:"is_maker"`
	IsReturn   bool  `json:"is_return"`
}

//判断用户是否为带账户用户
func (m *MatchUser) IsUserWithAccount() bool {
	return isUserWithAccount(m.Identifier)
}

func isUserWithAccount(identifier int) bool {
	return identifier != define.IdentifierMarketRobot
}

func IsUserWithAccount(identifier int) bool {
	return identifier != define.IdentifierMarketRobot
}

type MatchBatchMatchRecord struct {
	Code      string             `json:"contract_code"` //合约名，必须字段
	List      []PriceMatchRecord `json:"list"`
	CurPrice  decimal.Decimal    `json:"cur_price"`
	CurVolume decimal.Decimal    `json:"cur_volume"`
	Ts        time.Time          `json:"ts"`
}

type PriceMatchRecord struct {
	Code   string          `json:"contract_code"` //合约名，必须字段
	Price  decimal.Decimal `json:"price"`
	Amount decimal.Decimal `json:"amount"`
	List   []MatchRecord   `json:"list"`
}

type MatchRecord struct {
	Code       string          `json:"contract_code"` //合约名，必须字段
	MatchID    int64           `json:"match_id"`
	DealPrice  decimal.Decimal `json:"deal_price"`   //成交价
	DealVolume decimal.Decimal `json:"deal_volume" ` //成交张数
	//PreDealVolume   decimal.Decimal `json:"pre_deal_volume"` //预成交数量（结算时使用）
	MatchTime       time.Time `json:"match_time"` //撮合时间
	Buy             MatchUser `json:"buy"`
	Sell            MatchUser `json:"sell"`
	IsProtocalTrade bool      //协议成交
	Level           int       `json:"level"`
}

type OrderTrade struct {
	MsgId           int64           `json:"msg_id"`
	IsRecover       bool            `json:"is_recover"`
	Code            string          `json:"contract_code"` //合约名，必须字段
	MatchID         int64           `json:"match_id"`
	DealPrice       decimal.Decimal `json:"deal_price"`   //成交价
	DealVolume      decimal.Decimal `json:"deal_volume" ` //成交张数
	MatchTime       time.Time       `json:"match_time"`   //撮合时间
	Order           MatchUser       `json:"order"`
	IsProtocalTrade bool            //协议成交
	Level           int             `json:"level"`
	DealId          int64           `json:"deal_id"`
	MsgSeqId        int64           `json:"msg_seq_id"`
	TradeFinish     bool            `json:"trade_finish"`
}

type MRecord struct {
	ThirdOrderId    int64           `json:"third_order_id"`
	OrderId         int64           `json:"order_id"`
	TradeId         int64           `json:"trade_id"`
	MatchID         int64           `json:"match_id"`
	Code            string          `json:"contract_code"`
	DealPrice       decimal.Decimal `json:"deal_price"`        //成交价
	DealVolume      decimal.Decimal `json:"deal_volume" `      //成交量
	MatchTime       time.Time       `json:"match_time"`        //撮合时间
	IsProtocalTrade bool            `json:"is_protocal_trade"` //协议成交
	Level           int             `json:"level"`
	IsMaker         bool            `json:"is_maker"`
	IsReturn        bool            `json:"is_return"`
	UserID          int64           `json:"user_id"`
	TradeFinish     bool            `json:"trade_finish"` //标记订单完成
}

type MCancel struct {
	OrderId    int64 `json:"order_id"`
	CancelMark int   `json:"cancel_mark"`
	TradeMark  int   `json:"trade_mark"`
}

type MatchBatchOverMsg struct {
	Code string         `json:"contract_code"` //合约名，必须字段
	List []MatchOverMsg `json:"list"`
	Ts   time.Time      `json:"ts"`
}

type MatchOverMsg struct {
	MsgId       int64     `json:"msg_id"`
	IsRecover   bool      `json:"is_recover"`
	Code        string    `json:"contract_code"` //合约名，必须字段
	OrderId     int64     `json:"order_id"`
	UserId      int64     `json:"user_id"`
	Identifier  int       `json:"identifier"`   //委托身份 0-普通用户 1-做市商
	DealerState string    `json:"dealer_state"` //"freeCancel"-策略撤销，剩余的部分撤销  freeProtocolClose-强平 "tradeAll"-全部成交
	Ts          time.Time `json:"ts"`
	DealID      int64     `json:"deal_id"`
}

func (m *MatchOverMsg) IsUserWithAccount() bool {
	return isUserWithAccount(m.Identifier)
}

type DealInfo struct {
	MsgId          int64
	BuyFirst       decimal.Decimal
	SellFirst      decimal.Decimal
	BuyThree       decimal.Decimal
	SellThree      decimal.Decimal
	BuyFive        decimal.Decimal
	SellFive       decimal.Decimal
	Code           string
	Contract       *Contract       `json:"-"`
	CurTradeVolume decimal.Decimal `json:"cur_trade_volume"`
	LastDealPrice  decimal.Decimal
	LastDealTime   time.Time
	CreateTime     time.Time `json:"create_time"`
}
