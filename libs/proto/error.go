package proto

import (
	"spot/libs/define"
	"time"
)

type ApiError struct {
	ID         int64         `db:"id" json:"id"`
	OwnDay     string        `db:"own_day" json:"own_day"`         // 所属日期
	ReqID      int64         `db:"req_id" json:"req_id"`           // 请求id
	ReqURI     string        `db:"req_uri" json:"req_uri"`         // 请求接口
	ErrCode    int           `db:"err_code" json:"err_code"`       // 错误代码
	ErrMsg     string        `db:"err_msg" json:"err_msg"`         // 错误内容
	UserID     int64         `db:"user_id" json:"user_id"`         // 用户id
	IPAddress  string        `db:"ip_address" json:"ip_address"`   // ip地址
	ClientOS   define.OsType `db:"client_os" json:"client_os"`     // 设备
	CreateTime time.Time     `db:"create_time" json:"create_time"` // 创建时间
}

type SysError struct {
	ID           int64               `db:"id" json:"id"`
	OwnDay       string              `db:"own_day" json:"own_day"`             // 所属日期
	ErrType      define.SysErrorType `db:"err_type" json:"err_type"`           // 错误类型 1-行情断开
	ErrMsg       string              `db:"err_msg" json:"err_msg"`             // 错误内容
	ContractCode string              `db:"contract_code" json:"contract_code"` // 合约代码
	UserID       int64               `db:"user_id" json:"user_id"`             // 用户id
	CreateTime   time.Time           `db:"create_time" json:"create_time"`     // 创建时间
}

type ExternalError struct {
	ID         int64                  `db:"id" json:"id"`
	OwnDay     string                 `db:"own_day" json:"own_day"`         // 所属日期
	SysType    define.ExternalSysType `db:"sys_type" json:"sys_type"`       // 系统类型 1-商汤 2-非小号 3-币虎
	ReqID      int64                  `db:"req_id" json:"req_id"`           // 请求id
	ReqURI     string                 `db:"req_uri" json:"req_uri"`         // 请求接口
	ErrCode    int                    `db:"err_code" json:"err_code"`       // 错误代码
	ErrMsg     string                 `db:"err_msg" json:"err_msg"`         // 错误内容
	UserID     int64                  `db:"user_id" json:"user_id"`         // 用户id
	CreateTime time.Time              `db:"create_time" json:"create_time"` // 创建时间
}

type UserError struct {
	ID          int           `db:"id" json:"id"`                     // 记录id,当前采用库自增id
	ReqID       int64         `db:"req_id" json:"req_id"`             // 请求id
	UserID      int64         `db:"user_id" json:"user_id"`           // 用户id
	UserName    string        `db:"user_name" json:"user_name"`       // 用户名
	OpType      int           `db:"op_type" json:"op_type"`           // 操作类型
	ErrCode     int           `db:"err_code" json:"err_code"`         // 错误码
	ErrMsg      string        `db:"err_msg" json:"err_msg"`           // 错误信息
	IP          string        `db:"ip" json:"ip"`                     // id地址
	Device      string        `db:"device" json:"device"`             // 设备信息
	IMEI        string        `db:"imei" json:"imei"`                 // 设备序列号
	OSType      define.OsType `db:"os_type" json:"os_type"`           // 设备类型
	Version     string        `db:"version" json:"version"`           // 客户端版本号
	CreatedTime time.Time     `db:"created_time" json:"created_time"` // 创建时间
}
