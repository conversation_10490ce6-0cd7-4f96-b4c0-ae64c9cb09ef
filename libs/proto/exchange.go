package proto

import (
	"strings"
	"time"

	"github.com/shopspring/decimal"
	"spot/libs/define"
)

type ExchangePreOrderArg struct {
	UserID     int64           `json:"user_id"`      // 用户id
	Base       string          `json:"base"`         // 兑出币种
	Target     string          `json:"target"`       // 兑入币种
	Amount     decimal.Decimal `json:"amount"`       // 兑出币种使用数量
	BasicCoin  string          `json:"basic_coin"`   // 用于获取配置和汇率信息的基准币种
	IsBuyBasic bool            `json:"is_buy_basic"` // 是否是兑入非USDT币种
}

// AnalyzeBasicCoin 解析获取非USDT的币种,用于获取配置和汇率信息
func (a *ExchangePreOrderArg) AnalyzeBasicCoin() {
	a.Base = strings.ToUpper(a.Base)
	a.Target = strings.ToUpper(a.Target)

	if a.Base != define.CoinNameUsdt && a.Target == define.CoinNameUsdt {
		a.BasicCoin = a.Base
		a.IsBuyBasic = false
	} else if a.Target != define.CoinNameUsdt && a.Base == define.CoinNameUsdt {
		a.BasicCoin = a.Target
		a.IsBuyBasic = true
	} else {
		a.BasicCoin = ""
	}
}

type ExchangePreOrder struct {
	PreOrderID    string          `json:"pre_order_id"`             // 预兑换id
	Base          string          `json:"base"`                     // 兑出币种
	Target        string          `json:"target"`                   // 兑入币种
	BasicCoin     string          `json:"basic_coin,omitempty"`     // 基准币种
	BaseAmount    decimal.Decimal `json:"base_amount"`              // 兑出币种数量
	TargetAmount  decimal.Decimal `json:"target_amount"`            // 兑入币种数量
	OriginalPrice decimal.Decimal `json:"original_price,omitempty"` // 原始汇率
	ExchangePrice decimal.Decimal `json:"exchange_price,omitempty"` // 兑换汇率
	PriceRate     decimal.Decimal `json:"price_rate"`               // 单价
	Fee           decimal.Decimal `json:"fee"`                      // 手续费
	FeeRate       decimal.Decimal `json:"fee_rate,omitempty"`       // 手续费率
	ActualArrival decimal.Decimal `json:"actual_arrival"`           // 实际到账数量
}

type ExchangeActualOrderArg struct {
	UserID     int64  `json:"user_id"`      // 用户id
	PreOrderID string `json:"pre_order_id"` // 预兑换id
}

type ExchangeOrder struct {
	ID            string                    `db:"id" json:"id"`                         // 订单号
	UserID        int64                     `db:"user_id" json:"user_id"`               // 用户id
	HedgeOrderID  int64                     `db:"hedge_order_id" json:"hedge_order_id"` // 对冲订单id
	FromCoinID    int                       `db:"from_coin_id" json:"from_coin_id"`     // 兑换源币种id
	ToCoinID      int                       `db:"to_coin_id" json:"to_coin_id"`         // 兑换目标币种id
	FromCoinName  string                    `db:"from_coin_name" json:"from_coin_name"` // 来源币种名称
	ToCoinName    string                    `db:"to_coin_name" json:"to_coin_name"`     // 目标币种名称
	FromAmount    decimal.Decimal           `db:"from_amount" json:"from_amount"`       // 来源数量
	ToAmount      decimal.Decimal           `db:"to_amount" json:"to_amount"`           // 目标数量
	OriginalPrice decimal.Decimal           `db:"original_price" json:"original_price"` // 原始汇率
	ExchangePrice decimal.Decimal           `db:"exchange_price" json:"exchange_price"` // 兑换汇率
	Price         decimal.Decimal           `db:"price" json:"price"`                   // 单价(前端显示汇率)
	Fee           decimal.Decimal           `db:"fee" json:"fee"`                       // 手续费
	FeeRate       decimal.Decimal           `db:"fee_rate" json:"fee_rate"`             // 手续费率
	RealAmount    decimal.Decimal           `db:"real_amount" json:"real_amount"`       // 到账数量
	HedgeAmount   decimal.Decimal           `db:"hedge_amount" json:"hedge_amount"`     // 对冲成功数量
	RefundAmount  decimal.Decimal           `db:"refund_amount" json:"refund_amount"`   // 退回数量
	State         define.ExchangeOrderState `db:"state" json:"state"`                   // 订单状态 0-等待确认 100-已取消 200-已完成
	OsType        define.OsType             `db:"os_type" json:"os_type"`               // 请求设备类型
	IMEI          string                    `db:"imei" json:"imei"`                     // 设备id
	IPAddr        string                    `db:"ip_addr" json:"ip_addr"`               // 设备ip
	CreateTime    time.Time                 `db:"create_time" json:"create_time"`       // 创建时间
	UpdateTime    time.Time                 `db:"update_time" json:"update_time"`       // 更新时间
	PlatformID    int                       `db:"platform_id" json:"platform_id"`       //平台id
}

type ExchangeOrderHistoryArg struct {
	UserID int64                     `json:"user_id"` // 用户id
	State  define.ExchangeOrderState `json:"state"`   // 记录状态 -1-已结束 0-全部 1-未完成 100-已取消 200-已完成
}

type ExchangeOrderHistory struct {
	FromCoinName   string                    `db:"from_coin_name" json:"from_coin_name"` // 来源币种名称
	ToCoinName     string                    `db:"to_coin_name" json:"to_coin_name"`     // 目标币种名称
	FromAmount     decimal.Decimal           `db:"from_amount" json:"from_amount"`       // 来源数量
	ToAmount       decimal.Decimal           `db:"to_amount" json:"to_amount"`           // 目标数量
	Price          decimal.Decimal           `db:"price" json:"price"`                   // 单价
	RealAmount     decimal.Decimal           `db:"real_amount" json:"real_amount"`       // 到账数量
	RefundAmount   decimal.Decimal           `db:"refund_amount" json:"refund_amount"`   // 退回数量
	Fee            decimal.Decimal           `db:"fee" json:"fee"`                       // 手续费
	UpdateTime     time.Time                 `db:"update_time" json:"-"`                 // 时间
	UpdateTimeUnix int64                     `json:"update_time"`                        // 时间
	ID             string                    `db:"id" json:"id"`                         // 订单号
	State          define.ExchangeOrderState `db:"state" json:"state"`                   // 订单状态 0-等待确认 100-已取消 200-已完成
}
