package proto

import (
	"github.com/shopspring/decimal"
	"time"
)

type ContractMark struct {
	ContractCode string
	ClosePrice   decimal.Decimal
	LowPrice     decimal.Decimal
	HighPrice    decimal.Decimal
	CurTime      time.Time
	LastTime     time.Time
}

func (c *ContractMark) Reset(mk ComplexPrice) {
	c.ClosePrice, c.LowPrice, c.HighPrice = mk.Price, mk.Price, mk.Price
	//c.ClosePrice, c.LowPrice, c.HighPrice = decimal.Zero, decimal.Zero, decimal.Zero
	c.LastTime = c.CurTime
	c.CurTime = time.Now()
}

func (c *ContractMark) Store(mk ComplexPrice) {
	c.ContractCode = mk.ContractCode
	c.ClosePrice = mk.Price
	if c.LowPrice.LessThanOrEqual(decimal.Zero) {
		c.LowPrice = mk.Price
	}
	if c.HighPrice.LessThanOrEqual(decimal.Zero) {
		c.HighPrice = mk.Price
	}

	if mk.Price.LessThan(c.LowPrice) {
		c.LowPrice = mk.Price
	}
	if mk.Price.GreaterThan(c.HighPrice) {
		c.HighPrice = mk.Price
	}
	c.CurTime = time.Now()
}
