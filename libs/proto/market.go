package proto

import (
	"fmt"
	"github.com/shopspring/decimal"
	"spot/libs/crypto"
	"time"
)

type DepthData struct {
	Price  decimal.Decimal `json:"price"`
	Volume decimal.Decimal `json:"amount"`
}

//type DepthData struct {
//	Price           decimal.Decimal `json:"price"`
//	Volume          decimal.Decimal `json:"amount"`
//	PriceDiff       decimal.Decimal `json:"price_diff"`
//	IsBaitWarnLevel bool            `json:"-"`
//	BaitLevel       bool            `json:"-"`
//	CanTrade        bool            `json:"-"`
//	IsFlow          bool            `json:"-"`
//}

type DepthHolder struct {
	Symbol     string      `json:"symbol"`
	Asks       []DepthData `json:"asks"`
	Bids       []DepthData `json:"bids"`
	Source     string      `json:"source"`
	Ts         time.Time   `json:"ts"`
	LastUpdate int64
}

func (d *DepthHolder) Hash() string {
	return crypto.Md5(fmt.Sprintf("s:%v,asks:%v,bids:%v", d.Sym<PERSON>, d.<PERSON><PERSON>, d.B<PERSON>))
}

type MarketTrade struct {
	Symbol          string          `json:"symbol,omitempty"`
	DealTime        int64           `json:"deal_time"`
	Price           decimal.Decimal `json:"price"`
	Volume          decimal.Decimal `json:"volume,omitempty"`
	Side            string          `json:"side,omitempty"`
	Ts              time.Time       `json:"ts,omitempty"`
	Source          string          `json:"source,omitempty"`
	IsConfigSource  bool            `json:"is_config_source"`
	Weight          float64         `json:"weight,omitempty"`
	Trade24         decimal.Decimal `json:"trade_24,omitempty"`
	Trade24M        decimal.Decimal `json:"trade_24_m,omitempty"`
	SDistance       decimal.Decimal `json:"-"`
	IsRestAPI       bool            `json:"is_rest_api,omitempty"`
	IsMarkException bool            `json:"-"`
}

func (mt *MarketTrade) String() string {
	return fmt.Sprintf("{\"source:\"%v,\"price\":%v,\"weight\":%v,\"isMarkExcpetion\":%v}", mt.Source, mt.Price.String(), mt.Weight, mt.IsMarkException)
}

type Market24Volume struct {
	Symbol         string          `json:"symbol"`
	Volume24       decimal.Decimal `json:"amount"` //成交张数
	Volume24Symbol decimal.Decimal `json:"money"`  //成交金额
	Source         string          `json:"source"`
	Ts             time.Time       `json:"ts"`
}

//func (mt *MarketTrade) IsValidTrade(duration time.Duration) bool {
//	return time.Since(mt.Ts).Seconds() < duration.Seconds()
//}

func (mt *MarketTrade) IsValidTrade(duration time.Duration, bTime time.Time) bool {
	return bTime.Sub(mt.Ts).Seconds() < duration.Seconds()
}

type HourRandV struct {
	Code string `json:"code"`
	RA   []int  `json:"ra"`
	RB   []int  `json:"rb"`
}

type Market24hVolumeHistory struct {
	Code       string          `json:"code"`
	Amount     decimal.Decimal `json:"amount"`
	ThisAmount decimal.Decimal `json:"this_amount"`
	Ts         time.Time       `json:"ts"`
}

type IndexPriceProtect struct {
	Code          string          `json:"code"`
	LastMarkPrice decimal.Decimal `json:"last_mark_price"`
	ProtectMin    decimal.Decimal `json:"protect_min"`
	ProtectMax    decimal.Decimal `json:"protect_max"`
	TS            time.Time       `json:"ts"`
}

type DepthSimpleInfo struct {
	ContractCode string          `json:"contract_code"`
	BuyFirst     decimal.Decimal `json:"buy_first"`
	SellFirst    decimal.Decimal `json:"sell_first"`
	TS           time.Time       `json:"ts"`
}

type IndexBaseHistory struct {
	Code           string          `json:"code"`
	SpotIndexPrice decimal.Decimal `json:"spot_index_price"`
	TradePrice     decimal.Decimal `json:"trade_price"`
	BuyFirst       decimal.Decimal `json:"buy_first"`
	SellFirst      decimal.Decimal `json:"sell_first"`
	MidPrice       decimal.Decimal `json:"mid_price"`
	BaseDiff       decimal.Decimal `json:"base_diff"` //合约中间价-现货指数
	TS             time.Time       `json:"ts"`

	DepthInfo     DepthSimpleInfo `json:"depth_simple_info"` //铺单简易信息
	DepthBase     *ComplexPrice   `json:"depth_base"`        //铺单基准
	MarketPrice   decimal.Decimal `json:"market_price"`      //标记价格
	DepthBaseDiff decimal.Decimal `json:"depth_base_diff"`   //铺单基准价移动基差 (铺单基准价格-现货指数）
}

func (i *IndexBaseHistory) CalBaseDiff() {
	if i == nil {
		return
	}
	if i.SpotIndexPrice.LessThanOrEqual(decimal.Zero) {
		return
	}
	i.MidPrice = decimal.Avg(i.DepthInfo.BuyFirst, i.DepthInfo.SellFirst)
	i.BaseDiff = i.MidPrice.Sub(i.SpotIndexPrice)
	if i.DepthBase == nil {
		return
	}
	i.DepthBaseDiff = i.DepthBase.Price.Sub(i.SpotIndexPrice)
}

type ContractDistance struct {
	Code     string          `json:"code"`
	Distance decimal.Decimal `json:"distance"`
	Ts       int64           `json:"ts"`
}

type TradeNode struct {
	Id           int64
	ContractCode string
	Price        float64
	Volume       int64
	Ts           int64
}
