package proto

import (
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"spot/libs/define"
	"spot/libs/log"
	"time"
)

// MOrder match order
type MOrder struct {
	BusinessId      int64               `json:"business_id"`   //业务作市id
	OrderId         int64               `json:"order_id"`      //订单id
	Code            string              `json:"contract_code"` //合约名
	UserId          int64               `json:"user_id"`       //用户id
	Price           decimal.Decimal     `json:"price"`         //限价单挂单价
	Volume          decimal.Decimal     `json:"volume"`        // 限价单剩余的挂单量
	Side            string              `json:"side"`          //方向
	Offset          string              `json:"offset"`        //开平仓方向
	IsReturn        bool                //是否回挂单
	EntrustType     int                 //委托类型 0-市价 1-限价
	Mode            int                 //委托模式 1-对手价 2-最优3挡 3-最优5挡
	MatchType       int                 //0-普通委托 1-强平 2-止盈止损
	EntrustIdentity int                 `json:"entrust_identity"` //委托身份 0-普通用户 1-做市商
	CreateTime      time.Time           `json:"create_time"`
	EnQueueTime     time.Time           `json:"-"`
	EntryTime       time.Time           `json:"entry_time"`      //进入撮合时间
	CancelDuration  time.Duration       `json:"cancel_duration"` //超时撤销周期
	MarketSource    define.MarketSource `json:"market_source"`   //市场来源

}

func (m *MOrder) IsMarkingRobot() bool {
	return m.EntrustIdentity == 1
}

type BDepthReply struct {
	Id  int64
	Err error
}

type ActionOrder struct {
	Action        string
	MatchStrategy int //撮合成交策略
	IsRecover     bool
	MOrder
}

type UserAction struct {
	Action string      `json:"action"`
	Data   interface{} `json:"data"`
}

func (u *UserAction) GetPlaceData() (reply *EntrustPlaceMatchArg, err error) {
	if u.Data == nil {
		return
	}

	reply, ok := u.Data.(*EntrustPlaceMatchArg)
	if !ok {
		log.Error("GetPlaceData fail,data is not *proto.EntrustPlaceMatchArg", zap.Any("data", u))
	}
	return
}

func (u *UserAction) GetCancelData() (reply *EntrustCancelArg) {
	if u.Data == nil {
		return
	}

	reply, ok := u.Data.(*EntrustCancelArg)
	if !ok {
		log.Error("GetCancelData fail,data is not *proto.EntrustPlaceMatchArg")
	}
	return
}

type ContractDepthSource struct {
	ContractCode string `db:"contract_code" json:"contract_code"` //合约名
	Source       int    `db:"depth_source" json:"source"`         //来源
	IsOpen       bool   `db:"is_open" json:"is_open"`             //开关状态
}

type SourceState struct {
	Source                   int  `json:"source"`
	OK                       bool `json:"ok"`                           //状态
	IsFatFinger              bool `json:"is_fat_finger"`                //是否乌龙指
	IsBookPriceDiffException bool `json:"is_book_price_diff_exception"` //盘口价差异常
}
