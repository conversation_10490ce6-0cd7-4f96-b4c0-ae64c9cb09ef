/*
@Time : 2019-12-30 11:18
<AUTHOR> mocha
@File : model
*/
package proto

import (
	"github.com/shopspring/decimal"
	"spot/libs/alias"
	"spot/libs/define"
	"time"
)

type MarginCurrency struct {
	CanTransfer  int     `db:"can_transfer" json:"can_transfer"`   //0: 禁止划转  1:支持划转
	CurrencyId   int     `db:"currency_id" json:"currency_id"`     //
	CurrencyName string  `db:"currency_name" json:"currency_name"` //币种名称
	MaxTransfer  float64 `db:"max_transfer" json:"max_transfer"`   //资产划转单笔最大数量
	MinTransfer  float64 `db:"min_transfer" json:"min_transfer"`   //资产划转单笔最小数量
	Precision    int     `db:"precision" json:"precision"`         //精度（小数位）
	Status       int     `db:"status" json:"status"`               //0：下架 1：正常
}

type Coin struct {
	ExchangeCoinConfig
	CanTransfer      bool             `db:"can_transfer" json:"can_transfer"`             //0: 不支持划转 1:支持划转
	CurrencyId       int              `db:"currency_id" json:"currency_id"`               //
	CurrencyName     string           `db:"currency_name" json:"currency_name"`           //币种名称
	FullName         string           `db:"full_name" json:"full_name"`                   //币全称
	Icon             string           `db:"icon" json:"icon"`                             //币图标
	MaxTransfer      float64          `db:"max_transfer" json:"max_transfer"`             //资产划转单笔最大数量
	MaxWithdraw      float64          `db:"max_withdraw" json:"max_withdraw"`             //单笔最大提币
	MinTransfer      float64          `db:"min_transfer" json:"min_transfer"`             //资产划转单笔最小数量
	MinWithdraw      decimal.Decimal  `db:"min_withdraw" json:"min_withdraw"`             //单笔最小提币
	BasicWithdraw    decimal.Decimal  `db:"basic_withdraw" json:"basic_withdraw"`         // 基础单日限额(未认证用户)
	DailyWithdraw    decimal.Decimal  `db:"daily_withdraw" json:"daily_withdraw"`         // 高级单日限额
	MinerFee         float64          `db:"miner_fee" json:"miner_fee"`                   //单笔提币手续费
	PayConfirms      int              `db:"pay_confirms" json:"pay_confirms"`             //入账需要确认数
	Precision        int32            `db:"precision" json:"precision"`                   //精度（小数位）
	RechargeEnable   bool             `db:"recharge_enable" json:"recharge_enable"`       //充值 1：开启 0：禁止
	Status           int              `db:"status" json:"status"`                         //0：下架 1：正常
	Tag              string           `db:"tag" json:"tag"`                               //币种标识 如usdt ERC20
	WalletName       string           `db:"wallet_name" json:"wallet_name"`               //钱包名称
	WithdrawEnable   bool             `db:"withdraw_enable" json:"withdraw_enable"`       //提现 1：开启 0：禁止
	WithdrawReview   float64          `db:"withdraw_review" json:"withdraw_review"`       //单次提现审核数量
	WithdrawTime     int              `db:"withdraw_time" json:"withdraw_time"`           //单日提币次数
	SortWeight       float64          `db:"sort_weight" json:"sort_weight"`               // 资产显示排序权重
	MarketSortWeight float64          `db:"market_sort_weight" json:"market_sort_weight"` // 作为市场时的排序权重
	ReverseEnable    bool             `db:"reverse_enable" json:"reverse_enable"`         // 是否可用于反向合约
	Multiple         bool             `db:"multiple" json:"multiple"`                     // 多协议币种
	NeedTag          bool             `db:"need_tag" json:"need_tag"`                     // 是否需要标签
	Protocols        []WalletCoinConf `json:"protocols"`                                  // 协议列表
}

type ExchangeCoinConfig struct {
	IsExchange               bool            `db:"is_exchange" json:"is_exchange"`                                 //是否支持兑换
	MinExchange              decimal.Decimal `db:"min_exchange" json:"min_exchange"`                               //最小交易量
	MaxExchange              decimal.Decimal `db:"max_exchange" json:"max_exchange"`                               //最大交易量
	MinExchangeMoney         decimal.Decimal `db:"min_exchange_money" json:"min_exchange_money"`                   //最大交易量
	DailyExchangeMoney       decimal.Decimal `db:"daily_exchange_money" json:"daily_exchange_money"`               //每日最大交易额
	MaxExchangeCoinPoint     decimal.Decimal `db:"exchange_coin_point" json:"exchange_coin_point"`                 //币本位加点
	MaxExchangeUsdtPoint     decimal.Decimal `db:"exchange_usdt_point" json:"exchange_usdt_point"`                 //兑换美元加点
	ExchangeFeeRate          decimal.Decimal `db:"exchange_fee_rate" json:"exchange_fee_rate"`                     //兑换费率
	ExchangePriceRange       decimal.Decimal `db:"exchange_price_range" json:"exchange_price_range"`               //兑换价格范围
	ExchangePriceStep        decimal.Decimal `db:"exchange_price_step" json:"exchange_price_step"`                 // 兑换价格步长(该币种兑USDT)
	ExchangeReversePriceStep decimal.Decimal `db:"exchange_reverse_price_step" json:"exchange_reverse_price_step"` // 反向兑换价格步长(USDT兑该币种)
	ExchangePrecision        int32           `db:"exchange_precision" json:"exchange_precision"`                   // 兑币精度
	IsHedge                  bool            `db:"is_hedge" json:"is_hedge"`                                       //是否支持对冲
	HedgeWarnThreshold       decimal.Decimal `db:"hedge_warn_threshold" json:"hedge_warn_threshold"`               //对冲账户余额不足报警阈值
}

type CommissionGroup struct {
	CommissionId  int     `db:"commission_id" json:"commission_id"`
	GroupId       int64   `db:"group_id" json:"group_id"`
	FeeMaker      float64 `db:"fee_maker" json:"fee_maker"`
	FeeTaker      float64 `db:"fee_taker" json:"fee_taker"`
	ContractCodes string  `db:"contract_codes" json:"contract_codes"` // '合约代码（以逗号分割）',
}

type FundingRateHistory struct {
	ContractCode  string    `db:"contract_code" json:"contract_code"`   //合约代码
	CreatedBy     time.Time `db:"created_by" json:"created_by"`         //时间
	EstimatedRate float64   `db:"estimated_rate" json:"estimated_rate"` //预测资金费率
	FundingRate   float64   `db:"funding_rate" json:"funding_rate"`     //当期资金费率
}

type FundingFeeDetail struct {
	AfterBalance      decimal.Decimal `db:"after_balance" json:"after_balance"`           //扣除后 账户资产
	AfterCloseProfit  decimal.Decimal `db:"after_close_profit" json:"after_close_profit"` //扣除后 已实现盈亏
	BuyPosiVolume     decimal.Decimal `db:"position_value_diff" json:"position_value_diff"`
	Amount            decimal.Decimal `db:"amount" json:"amount"`                           //扣除或奖励费用
	BeforeBalance     decimal.Decimal `db:"before_balance" json:"before_balance"`           //扣除前 账户资产
	BeforeCloseProfit decimal.Decimal `db:"before_close_profit" json:"before_close_profit"` //扣除前 已实现盈亏
	CoinId            int             `db:"coin_id" json:"coin_id"`                         //
	CoinName          string          `db:"coin_name" json:"coin_name"`                     //结算币种
	ContractCode      string          `db:"contract_code" json:"contract_code"`             //合约代码
	CreatedBy         time.Time       `db:"created_by" json:"created_by"`                   //触发时间
	DetailId          int             `db:"detail_id" json:"detail_id"`                     //
	FundingRate       decimal.Decimal `db:"funding_rate" json:"funding_rate"`               //资金费率
	UserId            int64           `db:"user_id" json:"user_id"`                         //
	FundingType       int             `db:"funding_type" json:"funding_type"`
}

type FundingFee struct {
	FundingType       int       `db:"funding_type" json:"funding_type"`
	BuyToSell         float64   `db:"buy_to_sell" json:"buy_to_sell"`                 //多向空支付费用
	CoinId            int       `db:"coin_id" json:"coin_id"`                         //
	CoinName          string    `db:"coin_name" json:"coin_name"`                     //保证金币种
	ContractCode      string    `db:"contract_code" json:"contract_code"`             //合约代码
	CreatedBy         time.Time `db:"created_by" json:"created_by"`                   //触发时间
	FeeId             int       `db:"fee_id" json:"fee_id"`                           //
	FundingRate       float64   `db:"funding_rate" json:"funding_rate"`               //资金费率
	Interval          int       `db:"interval" json:"interval"`                       //时间间隔
	SellToBuy         float64   `db:"sell_to_buy" json:"sell_to_buy"`                 //空向多支付费用
	TotalAmount       float64   `db:"total_amount" json:"total_amount"`               //账户总资产
	PositionValueDiff float64   `db:"position_value_diff" json:"position_value_diff"` //持仓价值差值
}

// 合约
type Contract struct {
	ContractId     int             `db:"contract_id" json:"contract_id"`           //
	ContractCode   string          `db:"contract_code" json:"contract_code"`       //合约代码
	CoinName       string          `db:"coin_name" json:"coin_name"`               //计价币种
	BaseCoinName   string          `db:"base_coin_name" json:"base_coin_name"`     //基础币种
	PriceStep      decimal.Decimal `db:"price_step" json:"price_step"`             //合约价格波动步长
	Digit          int32           `db:"digit" json:"digit"`                       //交易对价格精度
	VolumeDigit    int32           `db:"volume_digit" json:"volume_digit"`         //交易对数量精度
	ContractIcon   string          `db:"contract_icon" json:"contract_icon"`       //合约图标
	MinOrderVolume decimal.Decimal `db:"min_order_volume" json:"min_order_volume"` //单笔最小下单量
	MaxOrderVolume decimal.Decimal `db:"max_order_volume" json:"max_order_volume"` //单笔最大下单量
	MinOrderMoney  decimal.Decimal `db:"min_order_money" json:"min_order_money"`   //单笔最小下单量
	MaxOrderMoney  decimal.Decimal `db:"max_order_money" json:"max_order_money"`   //单笔最大下单量
	FeeMaker       decimal.Decimal `db:"fee_maker" json:"fee_maker"`               //maker手续费率
	FeeTaker       decimal.Decimal `db:"fee_taker" json:"fee_taker"`               //taker手续费

	LabelMaxOrderVolume decimal.Decimal `db:"label_max_order_volume" json:"label_max_order_volume"` //标签可设置最大下单量
	LabelFee            string          `db:"label_fee" json:"label_fee"`
	LabelSlippage       string          `db:"label_slippage" json:"label_slippage"`

	MaxSideAmount      int             `db:"max_side_amount" json:"max_side_amount"` //单合约单边最大挂单量
	LimitBuyPrice      decimal.Decimal `db:"limit_buy_price" json:"limit_buy_price"`
	LimitSellPrice     decimal.Decimal `db:"limit_sell_price" json:"limit_sell_price"`
	LockFloatFactor    decimal.Decimal `db:"lock_float_factor" json:"lock_float_factor"`       //资产冻结上浮系数
	RedundancyFactor   decimal.Decimal `db:"redundancy_factor" json:"redundancy_factor"`       //市价冗余系数
	PriceProtectFactor decimal.Decimal `db:"price_protect_factor" json:"price_protect_factor"` //价差保护系数

	IOCLimit     decimal.Decimal `db:"ioc_limit" json:"ioc_limit"`           //ioc委托限价
	IOCBuyLimit  decimal.Decimal `db:"ioc_buy_limit" json:"ioc_buy_limit"`   //ioc买方价格限制
	IOCSellLimit decimal.Decimal `db:"ioc_sell_limit" json:"ioc_sell_limit"` //ioc卖方价格限制
	MaxIocFactor decimal.Decimal `db:"max_ioc_factor" json:"max_ioc_factor"`

	Delisted        alias.NumBool       `db:"delisted" json:"delisted"`                 //0：上架  1：下架 // 是否下架 false-上架,true-下架
	IsTradeEnable   alias.NumBool       `db:"trade_enable" json:"trade_enable"`         //可交易 true 可开
	IsMaintenance   alias.NumBool       `db:"is_maintenance" json:"is_maintenance"`     //是否系统维护
	OrderBy         float64             `db:"order_by" json:"order_by"`                 //排序
	Recommend       alias.NumBool       `json:"recommend" db:"recommend"`               // 是否为推荐合约
	IsShow          alias.NumBool       `json:"is_show" db:"is_show"`                   // 是否显示
	MarketSource    define.MarketSource `db:"market_source" json:"market_source"`       //对冲下单配置
	BucketingSource define.MarketSource `db:"bucketing_source" json:"bucketing_source"` //铺单源
	IsThirdMarket   alias.NumBool       `db:"is_third_market" json:"is_third_market"`   //是否第三方市场

}

type ContractPrice struct {
	FundingRate     float64 `json:"fund_fee"`
	NextFundingRate float64 `json:"estimate_fund_fee"`
	TradePrice      float64 `json:"last_price"`
	IndexPrice      float64 `json:"index_price"`
	MarketPrice     float64 `json:"market_price"`
}

// 条件单
type ConditionOrder struct {
	PlanOrderId     int64           `db:"plan_order_id" json:"plan_order_id"`       //
	PlatformID      int             `db:"platform_id" json:"platform_id"`           //平台id
	Condition       int             `db:"condition" json:"condition"`               //1 >=, 2 <=
	ContractCode    string          `db:"contract_code" json:"contract_code"`       //合约代码
	CoinName        string          `db:"coin_name" json:"coin_name"`               // 交易币种名
	MarketName      string          `db:"market_name" json:"market_name"`           // 市场币种名
	Imei            string          `db:"imei" json:"imei"`                         //设备识别码
	IpAddress       string          `db:"ip_address" json:"ip_address"`             //委托客户IP
	Amount          decimal.Decimal `db:"amount" json:"amount"`                     //下单张数
	Money           decimal.Decimal `db:"money" json:"money"`                       //市价下单金额
	OrderClient     int             `db:"order_client" json:"order_client"`         //委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)
	CreateTime      time.Time       `db:"create_time" json:"create_time_t"`         //提交时间
	OrderTime       time.Time       `db:"order_time" json:"order_time_t"`           //触发时间
	CancelTime      time.Time       `db:"cancel_time" json:"cancel_time_t"`         //取消时间
	CreateTimeStamp int64           `json:"create_time"`                            //提交时间
	OrderTimeStamp  int64           `json:"order_time"`                             //触发时间
	CancelTimeStamp int64           `json:"cancel_time"`                            //取消时间
	Side            string          `db:"side" json:"side"`                         //B买S卖
	Status          int             `db:"status" json:"status"`                     //条件单状态(1: 未触发 0：取消 2：已触发 3: 触发失败）
	TriggerPrice    decimal.Decimal `db:"trigger_price" json:"trigger_price"`       //触发价格
	UserId          int64           `db:"user_id" json:"user_id"`                   //
	EntrustType     int             `db:"entrust_type" json:"entrust_type"`         //委托类型 0-市价 1-限价
	EntrustStrategy int             `db:"entrust_strategy" json:"entrust_strategy"` //委托策略 0-默认 1-fok 2-ioc 3-maker
	EntrustPrice    decimal.Decimal `db:"entrust_price" json:"entrust_price"`       //委托执行价
	Mode            int             `json:"mode" db:"mode" `                        //委托模式
}

// order
type Order struct {
	OrderId      int64   `db:"order_id" json:"order_id"`
	ContractCode string  `db:"contract_code" json:"contract_code"`
	UserId       int64   `db:"user_id" json:"user_id"`
	CoinId       int     `db:"coin_id" json:"coin_id"`
	CoinName     string  `db:"coin_name" json:"coin_name"`
	Side         string  `db:"side" json:"side"`
	Type         int     `db:"type" json:"type"` //1.限价,2.最优委托
	Volume       int     `db:"volume" json:"volume"`
	Price        float64 `db:"price" json:"price"`
	Status       int     `db:"status" json:"status"`
	//OrderStatusDefault = 0 //未成交
	//OrderStatusFull    = 1 //全部成交
	//OrderStatusPart    = 2 //部分成交
	//OrderStatusCancel  = 3 //订单取消
	OrderValue    float64       `db:"order_value" json:"order_value"`
	OrderCost     float64       `db:"order_cost" json:"order_cost"`
	FreeCost      float64       `db:"free_cost" json:"free_cost"`
	CreateTime    int64         `json:"create_time"`
	OrderTime     time.Time     `db:"order_time" json:"order_time"`
	TriggleTime   int64         `json:"triggle_time"`
	AccountType   int           `db:"account_type" json:"account_type"`     //'账户模式 1：全仓 2：逐仓'
	Lever         int           `db:"lever" json:"lever"`                   //杠杆
	InitMargin    float64       `db:"init_margin" json:"init_margin"`       //起始保证金
	MarkPrice     float64       `db:"mark_price" json:"mark_price"`         //'标记价格(发起委托时的此合约标记价格)',
	ContractIndex float64       `db:"contract_index" json:"contract_index"` //'合约指数(发起委托时的此合约合约指数)',
	LastPrice     float64       `db:"last_price" json:"last_price"`         //'最新价(发起委托时，此合约的最新价格)',
	IpAddress     string        `db:"ip_address" json:"-"`
	CancelTime    time.Time     `db:"cancel_time" json:"cancel_time"`
	Iemi          string        `db:"iemi" json:"-"`
	OrderClient   define.OsType `db:"order_client" json:"-"` //'委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)',

	TradeVolume int     `db:"trade_volume" json:"trade_volume"` //成交数量
	TradeValue  float64 `db:"trade_value" json:"trade_value"`   //成交价值
	Commission  float64 `db:"commission" json:"commission"`     //手续费

	//其他到撮合引擎所需参数
	Digit int `db:"digit" json:"digit"` //交易对精度
	//ParValue float64 `db:"par_value" json:"par_value"` //面值

	//maker标记
	IsMaker bool `json:"is_maker"`

	//后台操作类型
	OrderType int `db:"order_type" json:"order_type"` //订单操作类型,0-普通,1-强制平仓
}

// ws结构,用户持有
type UserHolderWithAsset struct {
	//UserPosition
	ApiAsset
	UserId       int64  `json:"user_id"`
	ContractCode string `json:"contract_code" db:"contract_code"` // 合约code
	Side         string `json:"side" db:"side"`                   // 合约方向
	//HoldVolume       int     `json:"hold_volume" db:"volume"`                             // 持有张数
	ForceClosePrice float64 `db:"force_close_price" json:"liquidation_price,string"`
	NominalPrice    float64 `db:"nominal_close_price" json:"nominal_close_price,string"` //名义开仓均价
	//FloatProfit      float64 `db:"float_profit" json:"unrealized_profit,string"`          //'未实现盈亏',
	//UnrealizedProfit string  `json:"unrealized_profit" db:"float_profit"`                 // 未实现盈亏
	ProfitRatio  float64 `db:"profit_ratio" json:"profit_ratio,string"` //'盈亏率',
	Roic         float64 `db:"roic" json:"repay_rate,string"`           //回报率
	AdjustMargin string  `json:"adjust_margin" db:"adjust_margin"`      // 调整保证金
	//DepositRatio     string `json:"deposit_ratio" db:"maintenance_margin_ratio"` // 保证金率
	HoldType   int8    `json:"hold_type" db:"account_type"` // 持仓类型 1全仓 2逐仓
	Lever      int     `json:"lever" db:"leverage"`         // 杠杆倍数
	InitMargin float64 `db:"init_margin" json:"-,string"`   //起始保证金
}

// 此后台使用,暂不使用
type UserMarginStat struct {
	UserId    int64  `db:"user_id" json:"user_id"`
	CoinName  string `db:"coin_name" json:"coin_name"`
	CreatedBy time.Time
	//.....
}

// 用户资产信息
type UserWallet struct {
	UserWalletId int     `db:"user_wallet_id" json:"user_wallet_id"` // 钱包id
	UserId       int     `db:"user_id" json:"user_id"`               // 用户id
	CurrencyId   int     `db:"currency_id" json:"currency_id"`       // 币种id
	Balance      float64 `db:"balance" json:"balance"`               // 资产可用数量
	WithdrawLock float64 `db:"withdraw_lock" json:"withdraw_lock"`   // 提币冻结数量
	Address      string  `db:"address" json:"address"`               // 充币地址
	PlatformID   int     `db:"platform_id" json:"platform_id"`
}

type FinanceHistory struct {
	HistoryId      int64         `db:"history_id" json:"history_id"`
	UserId         int64         `db:"user_id" json:"user_id"`
	CoinId         int           `db:"coin_id" json:"coin_id"`
	CoinName       string        `db:"coin_name" json:"coin_name"`
	Type           int           `db:"type" json:"type"`                       //类型
	Amount         float64       `db:"amount" json:"amount"`                   //数量
	Balance        float64       `db:"balance" json:"balance"`                 //账户资产
	Available      float64       `db:"available" json:"available"`             //可用余额
	OrderMargin    float64       `db:"order_margin" json:"order_margin"`       //委托保证金
	PositionMargin float64       `db:"position_margin" json:"position_margin"` //仓位保证金
	IPAddress      string        `db:"ip_address" json:"ip_address"`           // ip地址
	IMEI           string        `db:"imei" json:"imei"`                       // 设备识别码
	OS             define.OsType `db:"os" json:"os"`                           // 客户端类型
	CreatedBy      time.Time     `db:"created_by" json:"created_by"`           //入库时间,自增
}

// IndexHistory 合约成交价及买一卖一综合信息
type IndexHistory struct {
	IndexId      int64           `db:"index_id" json:"index_id"`           // 记录id
	ContractCode string          `db:"contract_code" json:"contract_code"` // 交易对
	TradePrice   decimal.Decimal `json:"trade_price"`                      // 成交价
	TradePriceCn decimal.Decimal `json:"trade_price_cn"`                   // 成交价(cny)
	DealAmount   decimal.Decimal `json:"amount"`                           // 成交量
	Side         string          `json:"side"`                             // 交易方向 B-买 S-卖
	DealTime     int64           `json:"deal_time"`                        // 成交时间
	CreateBy     time.Time       `db:"created_by" json:"create_by"`        // 创建时间
	IsMain       bool            `json:"is_main"`                          // false-由数据源生成，true-自有成交
	BuyFirst     decimal.Decimal `json:"buy_first"`                        // 买一价
	SellFirst    decimal.Decimal `json:"sell_first"`                       // 卖一价
}

type IndexDetail struct {
	DetailId   int64   `db:"detail_id"`
	IndexId    int64   `db:"index_id"`
	Level      int     `db:"level"`
	BuyPrice   float64 `db:"buy_price"`
	BuyVolume  int     `db:"buy_volume"`
	SellPrice  float64 `db:"sell_price"`
	SellVolume int     `db:"sell_volume"`
}

type Symbol struct {
	SymbolId int    `db:"symbol_id" json:"symbol_id"`
	Symbol   string `db:"symbol" json:"symbol"`
}

type KLine struct {
	ID         int64           `db:"id" json:"id"`
	Symbol     string          `db:"contract_code" json:"symbol"`
	StartTime  int64           `db:"start_time" json:"start_time"`
	EndTime    int64           `db:"end_time" json:"end_time"`
	OpenPrice  decimal.Decimal `db:"open_price" json:"open_price"`
	ClosePrice decimal.Decimal `db:"close_price" json:"close_price"`
	HighPrice  decimal.Decimal `db:"high_price" json:"high_price"`
	LowPrice   decimal.Decimal `db:"low_price" json:"low_price"`
	Volume     decimal.Decimal `db:"volume" json:"volume,omitempty"` // 成交量(币种)
	TradeValue decimal.Decimal `db:"trade_value" json:"trade_value"` // 成交额(市场)
	PVolume    decimal.Decimal `json:"p_volume,omitempty"`
	Ts         int64           `json:"ts"`
}

type Account struct {
	AccountId    int             `db:"account_id" json:"account_id"`       //
	Available    decimal.Decimal `db:"available" json:"available"`         //可用余额
	LockAmount   decimal.Decimal `db:"lock_amount" json:"lock_amount"`     //委托冻结保证金
	Balance      decimal.Decimal `db:"balance" json:"balance"`             //账户资产
	CurrencyName string          `db:"currency_name" json:"currency_name"` //币种名称
	TotalProfit  decimal.Decimal `db:"total_profit" json:"total_profit"`   //累计已实现盈亏
	UserId       int64           `db:"user_id" json:"user_id"`             //
	PlatformID   int             `db:"platform_id" json:"platform_id"`     //平台id
}

// 账户明细
type AccountHistory struct {
	Id           int64                  `db:"id" json:"id"` //流水号
	OrderId      int64                  `db:"order_id" json:"order_id"`
	UserId       int64                  `db:"user_id" json:"user_id"` //
	Amount       float64                `db:"amount" json:"amount"`   //数量
	RawAmount    decimal.Decimal        `db:"raw_amount" json:"raw_amount"`
	Available    decimal.Decimal        `db:"available" json:"available"`         //可用余额
	Balance      decimal.Decimal        `db:"balance" json:"balance"`             //账户资产
	CreatedTime  time.Time              `db:"created_time" json:"created_time"`   //创建时间
	CurrencyName string                 `db:"currency_name" json:"currency_name"` //币种
	Imei         string                 `db:"imei" json:"-"`                      //设备识别码
	IpAddress    string                 `db:"ip_address" json:"-"`                //客户IP
	OrderClient  int                    `db:"order_client" json:"-"`              //委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)
	Type         define.AccountBillType `db:"type" json:"type"`
	PlatformID   int                    `db:"platform_id" json:"platform_id"`     //平台id
	Momo         string                 `db:"momo" json:"momo"`                   //备注
	ExtraID      int64                  `db:"extra_id" json:"extra_id,omitempty"` // 额外标记id
	TradeId      int64                  `db:"trade_id" json:"trade_id"`
}

type CoinArg struct {
	CurrencyID   int    `db:"currency_id" json:"currency_id"`     // 币种id
	CurrencyName string `db:"currency_name" json:"currency_name"` // 币种名称
}

type CoinRate struct {
	Symbol    string          `json:"symbol"`
	PriceCNY  decimal.Decimal `json:"price_cny"`
	PriceUSD  decimal.Decimal `json:"price_usd"`
	PriceBTC  decimal.Decimal `json:"price_btc"`
	PriceUSDT decimal.Decimal `json:"price_usdt"`
}

type WithdrawInfo struct {
	Balance   string `json:"balance"`   // 可用余额
	Min       string `json:"min"`       // 最小提币数量
	Fee       string `json:"fee"`       // 手续费率
	Start     int64  `json:"start"`     // 当日开始时间
	End       int64  `json:"end"`       // 当日结束时间
	Precision int32  `json:"precision"` // 币种精度
}

type YiDunRequest struct {
	CaptchaID string `json:"captcha_id"` // 验证码id
	Validate  string `json:"validate"`   // 提交二次校验的验证数据
	UserCode  int64  `json:"user_code"`  // 用户id
}

// 标记价格
type MarkPrice struct {
	ContractCode string          `json:"contract_code"`
	Price        decimal.Decimal `json:"price"`
	PriceStr     string
	Time         time.Time `json:"time"`
	Source       string    `json:"source"`
}

// 指数价格
type IndexPrice struct {
	ContractCode string          `json:"contract_code"`
	Price        decimal.Decimal `json:"price"`
	PriceStr     string          `json:"price_str"`
	Time         time.Time       `json:"time"`
	Source       string          `json:"source"`
	Weight       int             `json:"weight"`
}

func (mt *IndexPrice) IsValidTrade(duration time.Duration) bool {
	return time.Since(mt.Time).Seconds() < duration.Seconds()
}

// 成交价格
type TradePrice struct {
	ContractCode string          `json:"contract_code"`
	Price        decimal.Decimal `json:"price"`
	Side         string          `json:"side"`
	Time         time.Time       `json:"time"`
}

// 资金汇率
type CPriceRate struct {
	Id              int64 `json:"id"`
	ContractCode    string
	FundingRate     string    //当期资产费率
	ThisFundingTime time.Time //当期资产费率时间
	EstimatedRate   string    //下期资产费率
	SettlementTime  time.Time //结算时间
	Time            time.Time //收到时间
}

type LoginCache struct {
	Os         int8   `json:"os"`
	Ip         string `json:"ip"`
	Token      string `json:"tk"`
	ConnectKey string `json:"sk"`
	IsOnline   int    `json:"ol"`
	LoginTime  int64  `json:"lt"`
}

type FsWallet struct {
	//CoinId     int     `db:"coin_id" json:"coin_id"`
	CoinName   string  `db:"coin_name" json:"coin_name"`
	Type       int     `db:"type" json:"type"`
	Amount     float64 `db:"amount" json:"amount"`
	AmountLock float64 `db:"amount_lock" json:"amount_lock"`
	MixCharge  float64 `db:"mix_charge" json:"mix_charge"`
	MixWith    float64 `db:"mix_with" json:"mix_with"`
	Fee        float64 `db:"fee" json:"fee"`
	Address    string  `db:"address" json:"address"`
	AssetAward float64 `db:"asset_award" json:"asset_award"`
	PlatformID int     `db:"platform_id" json:"platform_id"`
}

type FsWalletHistory struct {
	Id     int64 `db:"id" json:"id"`
	UserId int64 `db:"user_id" json:"user_id"`
	//CoinId      int       `db:"coin_id" json:"coin_id"`
	CoinName    string    `db:"coin_name" json:"coin_name"`
	Amount      float64   `db:"amount" json:"amount"`
	AmountAfter float64   `db:"amount_after" json:"amount_after"`
	OpType      int       `db:"op_type" json:"op_type"`
	Remark      string    `db:"remark" json:"remark"`
	SourceId    int64     `db:"source_id" json:"source_id"`
	CreateAt    time.Time `db:"create_at" json:"create_at"`
	Memo        string    `db:"memo" json:"memo"`
	OpUserName  string    `db:"op_user_name" json:"op_user_name"`
	PlatformID  int       `db:"platform_id" json:"platform_id"`
	IsMaker     bool      `db:"is_maker" json:"is_maker"`
}

type UserArg struct {
	UserID int64 `json:"user_id" db:"user_id"`
}

type UserSymbol struct {
	UserID   int64  `json:"user_id"`
	Symbol   string `json:"symbol"`    // 交易对
	CoinName string `json:"coin_name"` // 币种名称
}

type CoinIDArg struct {
	CoinID int `json:"coin_id"`
}

type CoinNameArg struct {
	CoinName string `json:"coin_name"` // 币种名称
}

type ReqPayload struct {
	ReqID  int64  `json:"req_id"`
	UserID int64  `json:"user_id"`
	IPAddr string `json:"ip_addr"`
	IMEI   string `json:"imei"`
}

type SimpleOrder struct {
	OrderId int64   `json:"order_id"`
	Side    string  `json:"side"`
	Price   float64 `json:"price"`
	Volume  int     `json:"volume"`
	Free    int     `json:"free"`
}

func (o *Order) GetSimpleOrder() *SimpleOrder {
	order := new(SimpleOrder)
	order.OrderId = o.OrderId
	order.Side = o.Side
	order.Price = o.Price
	order.Volume = o.Volume
	order.Free = o.Volume - o.TradeVolume
	return order
}

type Trade struct {
	TradeId       int64           `db:"id" json:"id"`                       //成交编号
	UserId        int64           `db:"user_id" json:"user_id"`             //
	ContractCode  string          `db:"contract_code" json:"contract_code"` //合约代码
	Imei          string          `db:"imei" json:"-"`                      //设备识别码
	IpAddress     string          `db:"ip_address" json:"-"`                //委托客户IP
	OrderClient   int             `db:"order_client" json:"order_client"`   //委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)
	OrderId       int64           `db:"order_id" json:"order_id"`           //报单编号
	OrderType     int             `db:"order_type" json:"order_type"`       //0:   市价单 1：计划单
	CurrentPrice  decimal.Decimal `db:"current_price" json:"current_price"` // 当前价格
	Side          string          `db:"side" json:"side"`                   //B买S卖
	Commission    decimal.Decimal `db:"commission" json:"commission"`       //手续费
	Price         decimal.Decimal `db:"price" json:"price"`                 //成交价格
	Volume        decimal.Decimal `db:"volume" json:"volume"`               //成交张数
	TradeTime     time.Time       `db:"trade_time" json:"trade_time"`       //成交时间
	TradeAmount   decimal.Decimal `db:"trade_amount" json:"trade_amount"`   //成交金额
	ShareResource *ShareResource  `json:"share_resource"`                   // 分享资源
	PlatformID    int             `db:"platform_id" json:"platform_id"`
	ExtraID       int64           `db:"extra_id" json:"extra_id,omitempty"` // 额外标记id

	EntrustOrderId int64             `db:"entrust_order_id" json:"entrust_order_id"` //委托订单id
	EntrustType    int               `db:"entrust_type" json:"entrust_type"`         //委托类型 0-市价单 1-限价单 2-强平单
	EntrustMode    int               `db:"entrust_mode" json:"entrust_mode"`         //委托模式 1-对手价 2-最优3挡 3-最优5挡
	EntrustVolume  decimal.Decimal   `db:"entrust_volume" json:"entrust_volume"`     //委托量
	EntrustStatus  define.OrderState `db:"entrust_status" json:"entrust_status"`     //委托状态

	MatchType int   `db:"match_type" json:"match_type"` //撮合类型 0-市价 1-限价 2-协议成交
	MatchId   int64 `db:"match_id" json:"match_id" `
	IsMaker   bool  `db:"is_maker" json:"is_maker"`

	CurrencyName      string `db:"currency_name" json:"currency_name"`
	PriceCurrencyName string `db:"price_currency_name" json:"price_currency_name"`

	OutBalance   decimal.Decimal `db:"out_balance" json:"out_balance"`
	OutAvailable decimal.Decimal `db:"out_available" json:"out_available"`

	InBalance   decimal.Decimal `db:"in_balance" json:"in_balance"`
	InAvailable decimal.Decimal `db:"in_available" json:"in_available"`

	MarketSource int   `db:"market_source" json:"market_source"`
	ThirdTradeId int64 `db:"third_trade_id" json:"third_trade_id"`
}

type CheckReply struct {
	Status bool `json:"status"`
}

type UserIDGenBasic struct {
	Prefix int `json:"prefix"` // 前缀
	Serial int `json:"serial"` // 序号
}

type IDListDate struct {
	ID       int    `json:"id" db:"id"`
	IDSuffix string `json:"id_suffix" db:"id_suffix"`
}

// 验证码
type AuthCodeInfo struct {
	Code    string `json:"code"`    // 短信验证码
	Mode    uint8  `json:"mode"`    // 操作 0 常规请求 1 找回密码
	Counter int    `json:"counter"` // 计数
}

// 短信验证码
type AuthCodeArg struct {
	Account  string `json:"account"`  // 账号
	Mode     uint8  `json:"mode"`     // 验证码用途 0 常规请求
	AreaCode string `json:"areacode"` // 区号
	YiDunRequest
}

// 校验短信验证码请求参数
type CheckCodeArg struct {
	Account string `json:"account"` // 账号
	Code    string `json:"code"`    // 短信验证码
	Mode    uint8  `json:"mode"`    // 操作 0 常规请求 1 找回密码
}

type IDArg struct {
	ID int64 `json:"id"`
}

type ChainHistory struct {
	ID       int64     `json:"id" db:"id"`
	CoinName string    `json:"coin_name" db:"coin_name"`
	Balance  string    `json:"balance" db:"balance"`
	OwnDay   time.Time `json:"own_day" db:"own_day"`
}

type IndexPriceAndSells struct {
	Index   IndexHistory  `json:"index"`
	List    []IndexDetail `json:"list"`
	Markets []MarketTrade `json:"markets"`
}

type FundingRateReply struct {
	ContractCode  string `json:"contract_code"`  // 合约代码
	FundingRate   string `json:"funding_rate"`   //	当期资产费率
	CurrentTime   int64  `json:"current_time"`   //	当期资产费率时间
	EstimatedRate string `json:"estimated_rate"` //	下期资产费率
	NextTime      int64  `json:"next_time"`      // 下期
}

type MarketConfig struct {
	ID               int64  `db:"id" json:"id"`
	APIName          string `db:"api_name" json:"api_name"`
	ContractCodes    string `db:"contract_codes" json:"contract_codes"`
	NoticeSwitch     bool   `db:"notice_switch" json:"notice_switch"`
	WarningThreshold int    `db:"warning_threshold" json:"warning_threshold"`
	ApiStatus        int    `db:"api_status" json:"api_status"`
}

type Message struct {
	NotifyMessage
	Extra map[string]interface{} `json:"extra"`
}

type MessageArg struct {
	MsgId             int64
	Lang              define.ReqLang
	From, To          int64
	NickName          string
	Category          uint8
	TemplateTitleId   int
	TemplateContentId int
	TemplateData      map[string]interface{}
	NoticeFunc        func(message *Message)
}

func (ma *MessageArg) SetNickName(name string) {
	ma.NickName = name
}

type MarketDepth struct {
	Price  decimal.Decimal
	Volume decimal.Decimal
}
type ContractDepth struct {
	ContractId   int    `db:"contract_id" json:"contract_id"`
	ContractCode string `db:"contract_code" json:"contract_code"`

	IndexInvalidValue decimal.Decimal `db:"index_invalid_value" json:"index_invalid_value"` //无效剔除边界参数
	ReasonRange       decimal.Decimal `db:"reason_range" json:"reason_range"`               //合理标记价格幅度
	DepartureFactor   decimal.Decimal `db:"departure_factor" json:"departure_factor"`       //偏离参数
	IndexDiffSwitch   int             `db:"index_diff_switch" json:"index_diff_switch"`     //合约基差算法开关

	IndexRefValue        decimal.Decimal `db:"index_ref_value" json:"index_ref_value"`        //历史参考值
	IndexExceptionAdjust decimal.Decimal `db:"index_exception_adjust" json:"index_exception"` //异常数据调整参数

	ExceptionCheckFactor   decimal.Decimal `db:"exception_check_factor" json:"exception_check_factor"` // 异常检测比例系数
	DepthSwitch            int             `db:"depth_switch" json:"depth_switch"`                     // 铺单基准价格开关 0-3家均值 1-火币 2-币安 3-okex 4-3家合约最新成交
	TradeDiffWarn          decimal.Decimal `db:"trade_diff_warn" json:"trade_diff_warn"`               //盘口偏离报警值
	SpotExceptionTime      int             `db:"spot_exception_time" json:"spot_exception_time"`
	SpotExceptionThreshold decimal.Decimal `db:"spot_exception_threshold" json:"spot_exception_threshold"` //现货指数异常检测阈值
	IsDepthAutoSwitch      bool            `db:"is_depth_switch" json:"is_depth_switch"`                   //合约人工干预开关

	LargeTradeFactor decimal.Decimal `db:"large_trade_factor" json:"large_trade_factor"` //大额成交系数

	//-----新增参数
	DepthExceptionThreshold decimal.Decimal `db:"depth_exception_threshold" json:"depth_exception_threshold"`   //铺单异常检测阈值
	DepthPriceDiffThreshold decimal.Decimal `db:"depth_price_diff_threshold" json:"depth_price_diff_threshold"` //数据源盘口价差异常阈值
	DepthExceptionTime      int             `db:"depth_exception_time" json:"depth_exception_time"`             //铺单异常持续时间
	IsDepthBackupOn         bool            `db:"is_depth_backup_on" json:"is_depth_backup_on"`

	BucketConfig
	MatchDepthConfig
}

type BucketConfig struct {
	BucketBase        decimal.Decimal `db:"bucket_base" json:"bucket_base"`                 //对敲基准
	BucketPinFactor   decimal.Decimal `db:"bucket_pin_factor" json:"bucket_pin_factor"`     //插针比列
	BucketPriceUpdate decimal.Decimal `db:"bucket_price_update" json:"bucket_price_update"` //价格修正限定值
	BucketThreshold   decimal.Decimal `db:"bucket_threshold" json:"bucket_threshold"`       //对敲累加阈值
	BucketAmount      decimal.Decimal `db:"bucket_amount" json:"bucket_amount"`             //对敲数量振幅限制
	BucketMaxAmount   decimal.Decimal `db:"bucket_max_amount" json:"bucket_max_amount"`     //放量上线系数

	PreBucketBase decimal.Decimal `db:"pre_bucket_base" json:"pre_bucket_base"`
	//预设对敲基准
	MaxBucketBase      decimal.Decimal `db:"max_bucket_base" json:"max_bucket_base"`           //最大对敲基准
	BaseFactor         decimal.Decimal `db:"base_factor" json:"base_factor"`                   //基准倍数
	BucketBaseDuration decimal.Decimal `db:"bucket_base_duration" json:"bucket_base_duration"` //对敲基础放量周期
	//BaseGivingFactor   decimal.Decimal `db:"base_giving_factor" json:"base_giving_factor"`     //对敲基础放量倍数
	Min24HAmount   decimal.Decimal `db:"min_24h_amount" json:"min_24h_amount"`
	Max24HAmount   decimal.Decimal `db:"max_24h_amount" json:"max_24h_amount"`
	MinDepthVolume int             `db:"min_depth_volume" json:"min_depth_volume"` //最小铺单量
	MaxDepthVolume int             `db:"max_depth_volume" json:"max_depth_volume"` //最大铺单量
}

type MatchDepthConfig struct {
	ForceHop               decimal.Decimal `db:"force_hop" json:"force_hop"`     //强制跳数
	BuyFactor              decimal.Decimal `db:"buy_factor" json:"buy_factor"`   //买方伪装系数
	SellFactor             decimal.Decimal `db:"sell_factor" json:"sell_factor"` //卖方伪装系数
	BuyPriceFactor         decimal.Decimal `db:"buy_price_factor" json:"buy_price_factor"`
	SellPriceFactor        decimal.Decimal `db:"sell_price_factor" json:"sell_price_factor"`
	DiscountPremiumRate    decimal.Decimal `db:"discount_premium_rate" json:"discount_premium_rate"`       //折溢价率
	LevelMergeThreshold    decimal.Decimal `db:"level_merge_threshold" json:"level_merge_threshold"`       //近端档位区间合并控制阈值
	ExtraMergeHop          int             `db:"extra_merge_hop" json:"extra_merge_hop"`                   //额外档位合并跳数
	BuyLevelMaxLimit       int             `db:"buy_level_max_limit" json:"buy_level_max_limit"`           //卖方铺单档位上限
	SellLevelMaxLimit      int             `db:"sell_level_max_limit" json:"sell_level_max_limit"`         //买方铺单档位上限
	FlowCompensationStart  decimal.Decimal `db:"flow_compensation_start" json:"flow_compensation_start"`   //流动性补偿区间起始定位值
	FlowCompensationEnd    decimal.Decimal `db:"flow_compensation_end" json:"flow_compensation_end"`       //流动性补偿区间终止定位
	FlowCompensationAmount decimal.Decimal `db:"flow_compensation_amount" json:"flow_compensation_amount"` //流动性补偿总量
	StrategyFactor         decimal.Decimal `db:"strategy_factor" json:"strategy_factor"`                   //策略均分系数
	DepthSource            int             `db:"depth_source" json:"depth_source"`                         //深度开关
	BuyProtectFactor       decimal.Decimal `db:"buy_protect_factor" json:"buy_protect_factor"`             //买方护盘系数
	SellProtectFactor      decimal.Decimal `db:"sell_protect_factor" json:"sell_protect_factor"`           //卖方护盘系数
	ProtectAmount          int64           `db:"protect_amount" json:"protect_amount"`                     //护盘委托量
	InitialDepthLevels     int             `db:"initial_depth_levels" json:"initial_depth_levels"`         //初始铺单列表数量异常临界值
	MarketTradeMaxLevel    int             `db:"market_trade_max_level" json:"market_trade_max_level"`     //市价成交最大档位
	DataValidSeconds       int64           `db:"data_valid_seconds" json:"data_valid_seconds"`             //数据有效时间
	RCDealMaxVolume        int64           `db:"rc_deal_max_volume" json:"rc_deal_max_volume"`             //风控处理累计上限
}

type ContractNetPos struct {
	ContractCode string          `db:"contract_code" json:"contract_code"`
	Buy          decimal.Decimal `db:"buy" json:"buy"`
	Sell         decimal.Decimal `db:"sell" json:"sell"`
	Volume       decimal.Decimal `db:"volume" json:"volume"`
}

type GetDepositAddressArg struct {
	CoinId   int    `json:"coin_id"`  // 币种id
	Protocol string `json:"protocol"` // 币种协议,没有可传空
}

type GetDepositAddressReply struct {
	CoinId       int    `json:"coin_id"`       // 币种id
	VerifyNumber int    `json:"verify_number"` // 充提币确认数
	Address      string `json:"address"`       // 地址
	Tag          string `json:"tag"`           // tag
	Protocol     string `json:"protocol"`      // 协议
}

// 钱包币种配置
type WalletCoinConf struct {
	WalletName        string          `db:"wallet_name" json:"wallet_name"`               // 大钱包对应币种名
	CoinID            int             `db:"coin_id" json:"coin_id"`                       // 币种id
	CoinName          string          `db:"coin_name" json:"coin_name"`                   // 币种名称
	Protocol          string          `db:"protocol" json:"protocol"`                     // 币种协议
	CanDeposit        bool            `db:"can_deposit" json:"can_deposit"`               // 支持充币
	CanWithdraw       bool            `db:"can_withdraw" json:"can_withdraw"`             // 支持提币
	MinWithdraw       decimal.Decimal `db:"min_withdraw" json:"min_withdraw"`             // 最小提现数量
	MaxWithdraw       decimal.Decimal `db:"max_withdraw" json:"max_withdraw"`             // 单次最大提现数量
	BasicWithdraw     decimal.Decimal `db:"basic_withdraw" json:"basic_withdraw"`         // 基础单日限额(未认证用户)
	DailyWithdraw     decimal.Decimal `db:"daily_withdraw" json:"daily_withdraw"`         // 高级单日限额
	WithdrawFee       decimal.Decimal `db:"withdraw_fee" json:"withdraw_fee"`             // 提现手续费
	WithdrawReview    decimal.Decimal `db:"withdraw_review" json:"withdraw_review"`       // 提现需要审核数
	WithdrawTime      int             `db:"withdraw_time" json:"withdraw_time"`           // 每日提币次数限制
	WithdrawPrecision int32           `db:"withdraw_precision" json:"withdraw_precision"` // 提币输入最大精度位数
	ConfirmNum        int             `db:"confirm_num" json:"confirm_num"`               // 充提币确认数
	Weight            float64         `db:"weight" json:"weight"`                         // 排序权重 从大到小排序
}

type Contract24Giving struct {
	Contract  string          `json:"contract"`
	MaxVolume decimal.Decimal `json:"max_volume"`
	MinVolume decimal.Decimal `json:"min_volume"`
}

// 联系我们参数
type ContactUsArg struct {
	ContactUS
	YiDunRequest
}

type ContactUS struct {
	RealName   string    `db:"relname" json:"real_name"`      // 姓名
	Mobile     string    `db:"mobile" json:"mobile"`          // 手机号
	Email      string    `db:"email" json:"email"`            // 邮箱
	Content    string    `db:"content" json:"content"`        // 内容
	IPAddress  string    `db:"ip_address" json:"ip_address"`  // IP地址
	CreateTime time.Time `db:"creat_time" json:"create_time"` // 创建时间
}

type Summary struct {
	Trade24HValue decimal.Decimal `json:"trade_24h_value"` // 24小时成交额
}
