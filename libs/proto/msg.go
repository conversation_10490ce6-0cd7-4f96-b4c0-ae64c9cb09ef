/*
@Time : 2019-12-30 14:44
<AUTHOR> mocha
@File : msg
*/
package proto

import (
	"github.com/shopspring/decimal"
	"spot/libs/define"
	"time"
)

type SymbolArg struct {
	Symbol string `json:"symbol"`
}

type SymbololWarnArg struct {
	SymbolArg
	Gap int `json:"gap"`
}

type KLineArg struct {
	SymbolArg
	Size int `json:"size"`
}

type NewTradeReply struct {
	Ts     int64         `json:"ts"`
	Symbol string        `json:"symbol"`
	List   []TradeDetail `json:"list"`
}

type TradeDetail struct {
	ID      int64           `json:"-"`        // 成交ID
	Price   string          `json:"price"`    // 成交价
	PriceCn string          `json:"price_cn"` // 成交价(cny)
	Amount  decimal.Decimal `json:"amount"`   // 成交量
	Side    string          `json:"side"`     // 主动成交方向
	Ts      int64           `json:"ts"`       // 成交时间
	Source  string          `json:"-"`
}

//合约涉及涨跌幅结构
type Applies struct {
	ContractCode string          `json:"contract_code"`
	ChangeDaily  decimal.Decimal `json:"change_daily"`
	Change       decimal.Decimal `json:"change"`
	Change24h    decimal.Decimal `json:"change_24h"`
	Change8h     decimal.Decimal `json:"change_8h"`
	Change4h     decimal.Decimal `json:"change_4h"`
	Change2h     decimal.Decimal `json:"change_2h"`
	Change1h     decimal.Decimal `json:"change_1h"`
	Change30m    decimal.Decimal `json:"change_30m"`
	Change10m    decimal.Decimal `json:"change_10m"`
	HighPrice    decimal.Decimal `json:"high_price" db:"high_price"`
	LowPrice     decimal.Decimal `json:"low_price" db:"low_price"`
	TradeV24h    decimal.Decimal `json:"trade_24h"`
	TradeM24h    decimal.Decimal `json:"trade_money_24h"`
}

type NotifyMessage struct {
	ID             int64          `db:"id" json:"id"`
	SenderID       int64          `db:"sender_id" json:"sender_id"`             // 发送者用户id
	ReceiverID     int64          `db:"receiver_id" json:"receiver_id"`         // 接收者用户id
	SenderNickname string         `db:"sender_nickname" json:"sender_nickname"` // 发送者昵称
	Title          string         `db:"title" json:"title"`                     // 标题
	Content        string         `db:"content" json:"content"`                 // 内容
	Category       uint8          `db:"category" json:"category"`               // 消息类型
	LanguageType   define.ReqLang `db:"language_type" json:"language_type"`     // 语言类型
	CreateTime     time.Time      `db:"create_time" json:"create_time"`         // 创建时间
}
