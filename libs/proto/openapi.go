package proto

type UserAccountApikey struct {
	Id          int    `db:"id" json:"id"`
	PlatformId  int    `db:"platform_id" json:"platform_id"`
	AppName     string `db:"app_name" json:"app_name"`
	UserId      int64  `db:"user_id" json:"user_id"`
	A<PERSON><PERSON>ey      string `db:"app_key" json:"app_key"`
	AppSecret   string `db:"app_secret" json:"app_secret"`
	AuthIps     string `db:"auth_ips" json:"auth_ips"`
	Status      int    `db:"state" json:"state"`
	ExpiredAt   int64  `db:"expired_at" json:"expired_at"`
	CreatedTime int64  `db:"create_time" json:"create_time"`
}
