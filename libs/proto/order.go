package proto

import (
	"time"

	"github.com/shopspring/decimal"
	"spot/libs/define"
)

type ApiCurrentPosition struct {
	TotalRights decimal.Decimal   `json:"total_rights" db:"total_rights"` // 总权益
	TotalFloat  decimal.Decimal   `json:"total_float" db:"total_float"`   // 总浮动盈亏
	List        []CurrentPosition `json:"list"`                           // 持仓列表
}

type CurrentPosition struct {
	PositionId    int64           `db:"id" json:"position_id"`                // 用户持仓ID
	ContractCode  string          `json:"contract_code" db:"contract_code"`   // 合约code
	Side          string          `json:"side" db:"side"`                     // 合约方向
	AccountType   int8            `json:"account_type" db:"account_type"`     // 持仓类型 1全仓 2逐仓
	Lever         int             `json:"lever" db:"lever"`                   // 杠杆倍数
	Price         decimal.Decimal `db:"price" json:"price"`                   // 持仓均价
	Volume        decimal.Decimal `db:"volume" json:"volume"`                 // 持仓张数
	VolumeLock    decimal.Decimal `db:"volume_lock" json:"volume_lock"`       // 持仓冻结额张数
	ForcePrice    decimal.Decimal `db:"force_price" json:"force_price"`       // 强平价
	Limit         decimal.Decimal `db:"limit" json:"limit"`                   // 止盈价
	Stop          decimal.Decimal `db:"stop" json:"stop"`                     // 止损价
	Margin        decimal.Decimal `db:"margin" json:"margin"`                 // 保证金
	AdjustMargin  decimal.Decimal `db:"adjust_margin" json:"adjust_margin"`   // 调整保证金
	InitMargin    decimal.Decimal `db:"init_margin" json:"init_margin"`       // 初始保证金
	FloatProfit   decimal.Decimal `db:"float_profit" json:"float_profit"`     // 未实现盈亏
	Available     decimal.Decimal `db:"available" json:"available"`           // 可用余额
	ProfitRatio   decimal.Decimal `db:"profit_ratio" json:"profit_ratio"`     // 盈亏率
	MarginRatio   decimal.Decimal `db:"margin_ratio" json:"margin_ratio"`     // 保证金率
	ContractIndex decimal.Decimal `db:"contract_index" json:"contract_index"` // 合约指数
	BuyPrice      decimal.Decimal `db:"buy_price" json:"buy_price"`           // 买入价
	SellPrice     decimal.Decimal `db:"sell_price" json:"sell_price"`         // 卖出价
	CurrentPrice  decimal.Decimal `db:"current_price" json:"current_price"`   // 当前价
	Commission    decimal.Decimal `db:"commission" json:"commission"`         // 手续费
	UserId        int64           `db:"user_id" json:"user_id"`               // 用户id
	PlatformID    int             `db:"platform_id" json:"platform_id"`       // 平台id
	CurrencyName  string          `db:"currency_name" json:"currency_name"`   // 币种名
	ShareResource *ShareResource  `json:"share_resource"`                     // 分享资源
}

type ApiSoldTrade struct {
	Trade
}

type ApiConditionOrder struct {
	CurrencyName    string          `db:"currency_name" json:"currency_name"` // 币种名
	ContractCode    string          `db:"contract_code" json:"contract_code"` // 合约code
	PlanOrderID     int64           `db:"plan_order_id" json:"plan_order_id"` // 条件单id
	Status          int             `db:"status" json:"status"`               // 条件单状态(1: 未触发 0：取消 2：已触发 3: 触发失败）
	Side            string          `db:"side" json:"side"`                   // 委托方向
	CreateTime      int64           `db:"create_time" json:"create_time"`     // 提交时间
	Condition       int8            `db:"condition" json:"condition"`         // 条件 1 >=, 2 <=
	Volume          decimal.Decimal `db:"volume" json:"volume"`               // 下单张数
	Money           decimal.Decimal `db:"money" json:"money"`                 //下单金额，市价买必须
	TriggerPrice    decimal.Decimal `db:"trigger_price" json:"trigger_price"` // 触发价格
	OrderTime       int64           `db:"order_time" json:"order_time"`       // 触发时间 10位秒时间戳 未触发时为0
	TriggerType     int             `db:"trigger_type" json:"trigger_type"`   // 触发类型 1-条件单 2-止盈单 4-止损单
	Mode            int             `db:"mode" json:"mode"`                   //委托类型，1-对手价，2-最优3挡 3-最后5挡
	EntrustType     int             `db:"entrust_type" json:"entrust_type"`
	EntrustPrice    decimal.Decimal `db:"entrust_price" json:"entrust_price"`
	EntrustStrategy int             `db:"entrust_strategy" json:"entrust_strategy"`
}

type UserOrderState struct {
	AccountType int `json:"account_type" db:"account_type"` // 账户类型
	Lever       int `json:"lever" db:"lever"`               // 杠杠倍数
	HoldBuy     int `json:"hold_buy" db:"hold_buy"`         // 多单持仓数
	HoldSell    int `json:"hold_sell" db:"hold_sell"`       // 空单持仓数
	UnsoldBuy   int `json:"unsold_buy" db:"unsold_buy"`     // 多单未成交数
	UnsoldSell  int `json:"unsold_sell" db:"unsold_sell"`   // 空单未成交数
}

type LevelArg struct {
	Level     int8 `json:"level"`      // 等级
	ShareType int  `json:"share_type"` // 分享类型
}

type ShareResource struct {
	ImageBackground string `json:"image_background"` // 背景图
	TitleColor      string `json:"title_color"`      // 标题颜色
	TitleVertical   uint16 `json:"title_vertical"`   // 标题垂直位置
	ContentColor    string `json:"content_color"`    // 内容区文字颜色
	TitleText       string `json:"title_text"`       // 标题内容
	TitleSize       uint16 `json:"title_size"`       // 标题字号
}

type ShareImage struct {
	ID              int    `json:"id" db:"id"`
	ImageBackground string `json:"image_background" db:"image_background"` // 背景图
	TitleColor      string `json:"title_color" db:"title_color"`           // 标题文本颜色
	TitleVertical   uint16 `db:"title_vertical" json:"title_vertical"`     // 标题垂直位置
	ContentColor    string `json:"content_color" db:"content_color"`       // 内容区文字颜色
	ShareType       int    `json:"share_type" db:"share_type"`             // 分享类型 0-持仓分享 1-成交记录分享
	LevelType       int8   `json:"level_type" db:"level_type"`             // 等级类型
}

type ShareText struct {
	ID        int    `json:"id" db:"id"`
	TitleText string `json:"title_text" db:"title_text"` // 标题内容
	TitleSize uint16 `json:"title_size" db:"title_size"` // 标题字号
	ShareType int    `json:"share_type" db:"share_type"` // 分享类型 0-持仓分享 1-成交记录分享
	LevelType int8   `json:"level_type" db:"level_type"` // 等级类型
}

type UserOrder struct {
	UserID       int64  `db:"user_id" json:"user_id"`
	OrderID      int64  `db:"order_id" json:"order_id"`
	ContractCode string `db:"contract_code" json:"contract_code"`
}

// EntrustOrder 委托订单
type EntrustOrder struct {
	ID              int64           `db:"id" json:"order_id"`
	UserID          int64           `db:"user_id" json:"user_id"`
	UserType        int             `db:"user_type" json:"user_type"`
	PlatformID      int             `db:"platform_id" json:"platform_id"`   // 平台id
	ContractCode    string          `db:"contract_code" json:"symbol"`      // 交易对
	EntrustType     int             `db:"entrust_type" json:"entrust_type"` // 委托类型 0-市价 1-限价
	EntrustStrategy int             `db:"entrust_strategy" json:"entrust_strategy"`
	Mode            int             `db:"mode" json:"mode"`             // 下单模式 1-对手价 2-最优3挡 3-最优5挡
	Side            string          `db:"side" json:"side"`             // 委托方向 B/S
	OrderType       int             `db:"order_type" json:"order_type"` // 订单类型 0: 市价单 1：计划单
	Price           decimal.Decimal `db:"price" json:"entrust_price"`   // 委托价格;限价单传递价格
	Volume          decimal.Decimal `db:"volume" json:"volume"`         // 委托张数
	Money           decimal.Decimal `db:"money" json:"money"`           // 委托金额
	AssetLock       decimal.Decimal `db:"asset_lock" json:"asset_lock"` // 账户锁定金额
	/* 成交信息*/
	TradeVolume        decimal.Decimal     `db:"trade_volume" json:"trade_volume"`                 // 成交量
	TradePrice         decimal.Decimal     `db:"trade_price" json:"trade_price"`                   // 成交均价
	TradeMoney         decimal.Decimal     `db:"cost_asset" json:"cost_asset"`                     // 成交金额
	TradeFee           decimal.Decimal     `db:"cost_fee" json:"cost_fee"`                         // 成交手续费
	LastMatchPrice     decimal.Decimal     `db:"last_match_price" json:"last_match_price"`         // 最后撮合价
	State              define.OrderState   `db:"state"`                                            // 状态
	CreateTime         time.Time           `db:"create_time" json:"create_time"`                   // 委托时间
	UpdateTime         time.Time           `db:"update_time" json:"update_time"`                   // 更新时间
	Mark               int                 `db:"mark" json:"mark"`                                 // 用户标记 1-用户标记撤销
	ExtraID            int64               `db:"extra_id" json:"extra_id"`                         // 源id
	TriggerEntrustType int                 `db:"trigger_entrust_type" json:"trigger_entrust_type"` // 委托类型 0-市价 1-限价
	PlanTriggerPrice   decimal.Decimal     `db:"plan_trigger_price" json:"plan_trigger_price"`     // 计划单触发价
	Imei               string              `db:"imei" json:"-"`                                    // 设备识别码
	IpAddress          string              `db:"ip_address" json:"-"`                              // 客户IP
	OrderClient        int                 `db:"order_client" json:"-"`                            // 委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)
	ClientOrderId      int64               `db:"client_order_id" json:"client_order_id"`
	ExpectPrice        decimal.Decimal     `db:"expect_price" json:"expect_price"`     // 预计成交价
	BaseCoinName       string              `db:"base_coin_name" json:"coin_name"`      // 基础币种
	CoinName           string              `db:"coin_name" json:"market_name"`         // 计价币种
	ThirdOrderId       int64               `db:"third_order_id" json:"third_order_id"` // 第三方订单id
	ThirdMark          int                 `db:"third_mark" json:"third_mark"`
	MarketSource       define.MarketSource `db:"market_source" json:"market_source"`             // 对冲来源
	LastThirdTradeId   int64               `db:"last_third_trade_id" json:"last_third_trade_id"` // 最近成交第三方id
}

func (o *EntrustOrder) IsFinished() bool {
	return o.State != define.OrderStatusPart && o.State != define.OrderStatusDefault && o.State != define.OrderStatusTemp
}

func (o *EntrustOrder) IsEntrustMarket() bool {
	return o.EntrustType == define.EntrustTypeMarket
}

func (o *EntrustOrder) TradeMatchType(isProtocal bool) int {
	if isProtocal {
		return define.EntrustTradeProtocol
	}
	if o.IsEntrustMarket() {
		return define.EntrustTradeMarket
	}
	return define.EntrustTradeLimit
}

// MatchType 生成撮合服务支持的类型
func (o *EntrustOrder) MatchType() int {
	orderType := o.OrderType
	if orderType == define.OrderTypeLimit || orderType == define.OrderTypeStop || orderType == define.OrderTypeFollowLimit || orderType == define.OrderTypeFollowStop {
		return define.MatchTypeLimitStop
	}
	if orderType == define.OrderTypeForceCloseOut {
		return define.MatchTypeForce
	}
	return define.MatchTypeDefault
}

type HistoryEntrustOrderShareResource struct {
	ContractCode string          `db:"contract_code" json:"contract_code"`   // 合约代码
	CurrencyName string          `db:"currency_name" json:"currency_name"`   // 币种名
	Side         string          `db:"side" json:"side"`                     // 多空方向 B-平空 S-平多
	OpenAvgPrice decimal.Decimal `db:"open_avg_price" json:"open_avg_price"` // 开仓均价
	TradePrice   decimal.Decimal `db:"trade_price" json:"trade_price"`       // 成交均价
	CloseProfit  decimal.Decimal `db:"close_profit" json:"close_profit"`     // 收益
	ProfitRatio  decimal.Decimal `db:"profit_ratio" json:"profit_ratio"`     // 收益率
	CurrentPrice decimal.Decimal `json:"current_price"`                      // 当前最新价
	//ParValue      decimal.Decimal `db:"par_value" json:"par_value"`           // 合约面值
	ShareResource *ShareResource `json:"share_resource"` // 分享资源

	AccountType define.AccountType `db:"account_type" json:"-"` // 账户类型
	TradeVolume decimal.Decimal    `db:"trade_volume" json:"-"` // 成交张数
	Lever       decimal.Decimal    `db:"lever" json:"-"`        // 杠杆
}
