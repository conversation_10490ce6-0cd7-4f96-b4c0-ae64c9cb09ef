package proto

import "time"

type PlatformInfo struct {
	PlatformID   int       `db:"platform_id" json:"platform_id"`
	PlatformName string    `db:"platform_name" json:"platform_name"`
	DownUrl      string    `db:"down_url" json:"down_url"`
	CreateTime   time.Time `db:"create_time" json:"create_time"`
	StatusType   int       `db:"status_type" json:"status_type"`
	SuperAdmin   string    `db:"superadmin" json:"super_admin"`
	SuperAdminID int64     `db:"superadmin_id" json:"super_admin_id"`
	PlatformCode string    `db:"platform_code" json:"platform_code"`
	CreateManage string    `db:"create_manage" json:"create_manage"`
}
