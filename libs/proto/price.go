package proto

import "github.com/shopspring/decimal"

type LegalPrice struct {
	Rate       decimal.Decimal `db:"rate" json:"rate"`
	UpdateTime int64           `db:"update_time" json:"update_time"`
}

type CoinLegalPrice struct {
	USD        decimal.Decimal `json:"usd"`         // 美元价格
	CNY        decimal.Decimal `json:"cny"`         // 人民币价格
	KRW        decimal.Decimal `json:"krw"`         // 韩元价格
	VND        decimal.Decimal `json:"vnd"`         // 越南盾价格
	IDR        decimal.Decimal `json:"idr"`         // 印尼盾价格
	UpdateTime int64           `json:"update_time"` // 更新时间
}
