package proto

import (
	"github.com/shopspring/decimal"
	"spot/libs/define"
)

type ThirdOrderPlaceArg struct {
	define.ReqLang
	RequestId    int64               `json:"request_id"`
	ThirdOrderId int64               `json:"third_order_id"`
	OrderId      int64               `json:"order_id"`
	UserId       int64               `json:"user_id"`
	ContractCode string              `json:"contract_code"`
	EntrustType  int                 `json:"entrust_type"` //委托类型 0-市价 1-限价
	Side         string              `json:"side"`
	Price        decimal.Decimal     `json:"price"`
	Volume       decimal.Decimal     `json:"volume"`
	Money        decimal.Decimal     `json:"money"`
	MarketSource define.MarketSource `json:"market_source"`
}

type ThirdOrderCancelArg struct {
	define.ReqLang
	RequestId    int64               `json:"request_id"`
	ThirdOrderId int64               `json:"third_order_id"`
	OrderId      int64               `json:"order_id"`
	ContractCode string              `json:"contract_code"`
	MarketSource define.MarketSource `json:"hedge_source"`
}
