package proto

import "spot/libs/define"

type SafeVerifyCode struct {
	PhoneCode      string `json:"phone_code"`       // 两步验证 短信验证码
	EmailCode      string `json:"email_code"`       // 两步验证 邮箱验证码
	SpareEmailCode string `json:"spare_email_code"` // 两步验证 备用邮箱验证码
	TotpCode       string `json:"totp_code"`        // 两步验证 令牌验证码
	FundPassword   string `json:"fund_password"`    // 两步验证 资金密码
}

func (svc *SafeVerifyCode) IsTwoStep() bool {
	// 目前资金密码只在提现中使用,不需要加入判断中
	return len(svc.PhoneCode) > define.EmptyStrSize ||
		len(svc.EmailCode) > define.EmptyStrSize ||
		len(svc.SpareEmailCode) > define.EmptyStrSize ||
		len(svc.TotpCode) > define.EmptyStrSize
}
