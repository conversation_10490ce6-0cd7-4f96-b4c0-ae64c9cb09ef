package proto

type SystemValue struct {
	Key   string `db:"key_name" json:"key"`
	Value string `db:"content" json:"value"`
}

type SystemTradeConfig struct { // true关闭
	Opening  bool `json:"close_opening"`  // 所有开仓
	Closeing bool `json:"close_closeing"` // 所有平仓
	Withdraw bool `json:"close_withdraw"` // 所有提币
	Profit   bool `json:"close_profit"`   // 所有止盈单
	Loss     bool `json:"close_loss"`     // 所有止损单
	Plans    bool `json:"close_plans"`    // 所有计划单
}
