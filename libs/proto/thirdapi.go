package proto

import (
	"spot/libs/define"
	"time"
)

type ThirdMarketConfig struct {
	SourceId   define.MarketSource `db:"source_id" json:"source_id"`
	Name       string              `db:"name" json:"name"`
	<PERSON><PERSON>ey  string              `db:"public_key" json:"public_key"`
	PrivateKey string              `db:"private_key" json:"private_key"`
	HitPhrase  string              `db:"hint_phrase" json:"hint_phrase"`
	ApiStatus  bool                `db:"api_status" json:"api_status"`
	CreateTime time.Time           `db:"create_time" json:"create_time"`
	UpdateTime time.Time           `db:"update_time" json:"update_time"`
	IsTest     bool                `db:"is_test" json:"is_test"`
}
