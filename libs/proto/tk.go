package proto

import (
	"github.com/shopspring/decimal"
	"time"
)

type ContractIndicator struct {
	ID           int64     `db:"id" json:"id"`
	ContractCode string    `db:"contract_code" json:"contract_code"`
	Side         string    `db:"side" json:"side"`
	Index        string    `json:"index"`
	IndexCNY     string    `json:"index_cny"`
	Price        float64   `db:"price" json:"price"`
	Timestamp    int64     `db:"timestamp" json:"timestamp"`
	CreateTime   time.Time `db:"create_time" json:"create_time"`
}

type ContractSupport struct {
	ID            int64           `db:"id" json:"id"`
	ContractCode  string          `db:"contract_code" json:"contract_code"`
	OpenPrice     float64         `db:"open_price" json:"-"`
	ClosePrice    float64         `db:"close_price" json:"-"`
	HighPrice     float64         `db:"high_price" json:"-"`
	LowPrice      float64         `db:"low_price" json:"-"`
	Volume        float64         `db:"volume" json:"-"`
	Drag          float64         `db:"drag" json:"drag_float"`
	DragStr       string          `json:"drag"`
	DragCNYStr    string          `json:"drag_cny"`
	Support       float64         `db:"support" json:"support_float"`
	SupportStr    string          `json:"support"`
	SupportCNYStr string          `json:"support_cny"`
	Timestamp     int64           `db:"timestamp" json:"timestamp"`
	CreateTime    time.Time       `db:"create_time" json:"create_time"`
	TradeVolume   decimal.Decimal `db:"trade_volume" json:"trade_volume"`
}
