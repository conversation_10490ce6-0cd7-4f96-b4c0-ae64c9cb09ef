/*
@Time : 3/7/20 10:18 上午
<AUTHOR> mocha
@File : order
*/
package proto

import (
	"fmt"
	"github.com/shopspring/decimal"
	"spot/libs/crypto"
	"spot/libs/nums"
	"time"

	"spot/libs/define"
)

type ApiPlaceArg struct {
	Symbol       string          `json:"symbol"`        // 交易对
	Side         string          `json:"side"`          // 买卖方向 B-买 S-卖
	NToken       string          `json:"ncr"`           // 随机字符串,用于昉连点
	Amount       decimal.Decimal `json:"amount"`        // 下单张数
	Money        decimal.Decimal `json:"money"`         // 下单金额，市价必须
	EntrustPrice decimal.Decimal `json:"entrust_price"` // 委托价格;限价单传递价格
	EntrustType  int             `json:"entrust_type"`  // 委托类型 0-市价 1-限价
	Mode         int             `json:"mode"`          // 下单模式 1-对手价 2-最优3挡 3-最优5挡
}

func (oa *ApiPlaceArg) Convert() *OrderOpenArgs {
	return &OrderOpenArgs{
		ContractCode: oa.Symbol,
		Side:         oa.Side,
		NToken:       oa.NToken,
		Amount:       oa.Amount,
		Money:        oa.Money,
		Price:        oa.EntrustPrice,
		EntrustType:  oa.EntrustType,
		Mode:         oa.Mode,
	}
}

type OrderCancelArgs struct {
	RequestId int64          `json:"request_id"`
	Lang      define.ReqLang `json:"lang"`
	UserId    int64          `json:"user_id"`
	ID        string         `json:"id"` //委托订单id
}

//订单列表与合约联查
type OrderBatchCancelArgs struct {
	RequestId int64          `json:"request_id"`
	Lang      define.ReqLang `json:"lang"`
	UserId    int64          `json:"user_id"`
	OrderIds  []string       `json:"order_ids"` // 委托订单id列表
	Symbol    string         `json:"symbol"`    // 交易对
}

type OrderOpenArgs struct {
	UserId    int64
	RequestId int64
	Lang      define.ReqLang
	NToken    string `json:"ncr"`

	PlanOrderId  int64
	ContractCode string          `json:"contract_code"`
	Side         string          `json:"side"`
	Amount       decimal.Decimal `json:"amount"` //下单量
	Money        decimal.Decimal `json:"money"`  //市价买必须
	OrderType    int             //订单类型 0: 用户委托 1：计划单

	IpAddress    string
	DeviceId     string
	APPID        int
	TriggerPrice decimal.Decimal `json:"trigger_price"` //计划单实际触发价格

	EntrustType     int             `db:"entrust_type" json:"entrust_type"`         //委托类型 0-市价 1-限价
	EntrustStrategy int             `db:"entrust_strategy" json:"entrust_strategy"` //委托策略 1-fok 2-ioc 3-maker
	Price           decimal.Decimal `db:"price" json:"entrust_price"`               //委托价格;限价单传递价格
	Mode            int             `db:"mode" json:"mode"`                         //下单模式 1-对手价 2-最优3挡 3-最优5挡
	ClientOrderId   int64           `db:"client_order_id" json:"client_order_id"`
}

type OrderOpenRsp struct {
	Order     *EntrustOrder `json:"order"`
	Balance   string        `json:"-"`
	Available string        `json:"available,omitempty"`
}

type ComplexPrice struct {
	ContractCode      string          `json:"contract_code"`
	Price             decimal.Decimal `json:"Price,omitempty"` //标记价格
	PriceStr          string          `json:"price"`
	SpotIndexPrice    decimal.Decimal `json:"spot_index_price_original,omitempty"` //现货指数价格
	SpotIndexPriceStr string          `json:"spot_index_price,omitempty"`          //现货指数价格
	TS                int64           `json:"ts"`
}

type MatchOrder struct {
	Id             int64           `json:"id"`               //成交id
	EntrustOrderId int64           `json:"entrust_order_id"` //委托id
	Symbol         string          `json:"symbol"`
	Side           string          `json:"side"`
	TradeVolume    decimal.Decimal `json:"trade_volume"`
	TradePrice     decimal.Decimal `json:"price"`
	TradePriceCn   decimal.Decimal `json:"trade_price_cn"`
	TradeTime      time.Time       `json:"trade_time"`
	IsBaitTrade    bool            `json:"is_bait_trade"`
	Digit          int32           `json:"digit"`
	VolumeDigit    int32           `json:"volume_digit"`
	MatchPrice
}

//{
//"digit":2,
//"contract_code":"btc/usdt", //交易对名称
//"buy":[
//{
//"price": "39.20", //价格
//"amount": "1000" //数量,张
//"total_amount":1500,//累计张
//}
//],
//"sell":[
//{
//"price": "40.20",
//"amount": "1000"
//"total_amount":1500,
//}
//]}
type DepthTradeContainer struct {
	DepthContainer
	CurDepthPrice decimal.Decimal `json:"cur_depth_price"`
	BuyMax        decimal.Decimal `json:"buy_max"`  //鱼饵档位最高价
	SellMax       decimal.Decimal `json:"sell_max"` //鱼饵档位最高价
	BuyFirst      decimal.Decimal `json:"buy_first"`
	SellFirst     decimal.Decimal `json:"sell_first"`
}

type DepthContainer struct {
	Id           int64   `json:"id"`
	TS           int64   `json:"ts"`
	Level        int32   `json:"level"`
	Digit        int32   `json:"digit"`
	PriceLimit   int32   `json:"price_limit"`
	VolumeDigit  int32   `json:"volume_digit"`
	ContractCode string  `json:"contract_code"`
	Buy          []Depth `json:"buy"`
	Sell         []Depth `json:"sell"`
}

func (r *DepthContainer) GetDepthContainerPub() (final *DepthContainerPub) {
	final = &DepthContainerPub{
		Id:          r.Id,
		TS:          r.TS,
		Level:       r.Level,
		Digit:       r.Digit,
		PriceLimit:  r.PriceLimit,
		VolumeDigit: r.VolumeDigit,
		Symbol:      r.ContractCode,
		Buy:         r.CovertDepthPub(r.Buy),
		Sell:        r.CovertDepthPub(r.Sell),
	}
	return
}

func (r *DepthContainer) CovertDepthPub(data []Depth) (list []DepthPub) {
	for _, item := range data {
		dp := DepthPub{
			Price:       item.Price.StringFixedBank(r.Digit),
			Amount:      item.Amount.StringFixedBank(r.VolumeDigit),
			TotalAmount: item.TotalAmount.StringFixedBank(r.VolumeDigit),
		}
		list = append(list, dp)
	}
	return
}

type DepthInfo struct {
	BuyFirst  decimal.Decimal
	SellFirst decimal.Decimal
	BuyThree  decimal.Decimal
	SellThree decimal.Decimal
	BuyFive   decimal.Decimal
	SellFive  decimal.Decimal
}

func (r DepthContainer) GetFirstPrice() (buy, sell decimal.Decimal) {
	if len(r.Buy) > 0 {
		price := r.Buy[0].Price
		if price.LessThanOrEqual(decimal.Zero) {
			price = r.Buy[0].Price
		}
		buy = price
	}

	if len(r.Sell) > 0 {
		price := r.Sell[0].Price
		if price.LessThanOrEqual(decimal.Zero) {
			price = r.Sell[0].Price
		}
		sell = price
	}
	return
}

func (r DepthContainer) GetDepthSimpleInfo() (ds *DepthInfo) {
	ds = &DepthInfo{}
	for index, item := range r.Buy {
		price := item.Price
		if price.LessThanOrEqual(decimal.Zero) {
			price = item.Price
		}
		if index == 0 {
			ds.BuyFirst = price
		}
		if index < 3 {
			ds.BuyThree = price
		}
		if index < 5 {
			ds.BuyFive = price
		}
	}
	for index, item := range r.Sell {
		price := item.Price
		if price.LessThanOrEqual(decimal.Zero) {
			price = item.Price
		}
		if index == 0 {
			ds.SellFirst = price
		}
		if index < 3 {
			ds.SellThree = price
		}
		if index < 5 {
			ds.SellFive = price
		}
	}
	return
}

func (r *DepthContainer) Hash() string {
	return crypto.Md5(fmt.Sprintf("%v,%v", r.Buy, r.Sell))
}

type Depth struct {
	Price       decimal.Decimal `json:"price"`
	Amount      decimal.Decimal `json:"amount"`
	TotalAmount decimal.Decimal `json:"total_amount"`
}

//推送给客户端
type DepthContainerPub struct {
	Id          int64      `json:"id"`
	TS          int64      `json:"ts"`
	Level       int32      `json:"-"`
	Digit       int32      `json:"digit"`
	PriceLimit  int32      `json:"price_limit"`
	VolumeDigit int32      `json:"volume_digit"`
	Symbol      string     `json:"symbol"`
	Buy         []DepthPub `json:"buy"`
	Sell        []DepthPub `json:"sell"`
}

func (r *DepthContainerPub) GetFirstPrice() (buy, sell decimal.Decimal) {
	if len(r.Buy) > 0 {
		price := nums.NewFromString(r.Buy[0].Price)
		if price.LessThanOrEqual(decimal.Zero) {
			price = nums.NewFromString(r.Buy[0].Price)
		}
		buy = price
	}

	if len(r.Sell) > 0 {
		price := nums.NewFromString(r.Sell[0].Price)
		if price.LessThanOrEqual(decimal.Zero) {
			price = nums.NewFromString(r.Sell[0].Price)
		}
		sell = price
	}
	return
}

type DepthPub struct {
	Price       string `json:"price"`
	Amount      string `json:"amount"`
	TotalAmount string `json:"total_amount"`
}

type ApiOrderPlaceArg struct {
	ReqID        int64         `json:"req_id"`        // 请求id
	UserID       int64         `json:"user_id"`       // 用户id
	ContractCode string        `json:"contract_code"` // 合约code
	OrderType    int           `json:"order_type"`    // 订单烈性 1:限价单 2:最优价 3:条件单
	HoldType     int           `json:"hold_type"`     // 持仓类型 1:全仓 2:逐仓
	Side         string        `json:"side"`          // 方形
	Price        string        `json:"price"`         // 价格
	Amount       int           `json:"amount"`        // 数量
	Leverage     int           `json:"leverage"`      // 杠杠
	IPAddr       string        `json:"ip_addr"`       // 委托客户ip
	IEMI         string        `json:"iemi"`          // 设备识别码
	Os           define.OsType `json:"os"`            // 设备类型
}

type ApiOrderPlaceReply struct {
	OrderID int64 `json:"order_id" db:"order_id"` // 订单id
	ApiAsset
}

type ApiOrderCancelArg struct {
	ReqID        int64  `json:"req_id"`        // 请求id
	UserID       int64  `json:"user_id"`       // 用户id
	ContractCode string `json:"contract_code"` // 合约code
	OrderID      int64  `json:"order_id"`      // 订单id
	OrderType    int8   `json:"order_type"`    // 订单类型 1-委托 2-条件单
}

type ApiOrderLiquidateArg struct {
	ReqID        int64         `json:"req_id"`        // 请求id
	UserID       int64         `json:"user_id"`       // 用户id
	ContractCode string        `json:"contract_code"` // 合约code
	Amount       int           `json:"amount"`        // 数量
	IPAddr       string        `json:"ip_addr"`       // 委托客户ip
	IEMI         string        `json:"iemi"`          // 设备识别码
	Os           define.OsType `json:"os"`            // 设备类型
}

type ApiAddDepositArg struct {
	ReqID        int64         `json:"req_id"`                     // 请求id
	UserID       int64         `json:"user_id"`                    // 用户id
	ContractCode string        `json:"contract_code"`              // 合约code
	Amount       string        `json:"amount"`                     // 数量
	IPAddress    string        `db:"ip_address" json:"ip_address"` // ip地址
	IMEI         string        `db:"imei" json:"imei"`             // 设备识别码
	OS           define.OsType `db:"os" json:"os"`                 // 客户端类型
}

type BackendUserContractArg struct {
	ContractCode string `json:"contract_code"` // 合约code
}

type ForceLiquidateArg struct {
	UserID       int64  `json:"user_id"`       // 用户id
	ContractCode string `json:"contract_code"` // 合约code
}

type LiquidateData struct {
	UserID      int64  `json:"user_id" db:"user_id"`           // 用户id
	AccountType int    `json:"account_type" db:"account_type"` // 账户类型
	Side        string `json:"side" db:"side"`                 // 持仓方向
	Volume      int    `json:"volume" db:"volume"`             // 持仓张数
	Lever       int    `json:"lever" db:"lever"`               // 杠杆倍数
}

//"contract_code": "BTCUSD", // 合约code
//"change_ratio": "12.23", // 当日涨跌幅 12.23%
//"change":"-0.237",//涨幅
//"high_price": "3000",//最高价(请与最新成交比较，取大）
//"low_price": "3000",//最低价（请于最新成交比较，取小）
type ContractApply struct {
	Symbol      string          `json:"symbol"`
	ChangeRatio decimal.Decimal `json:"change_ratio"`
	Change      decimal.Decimal `json:"change"`
	HighPrice   decimal.Decimal `json:"high_price"`
	LowPrice    decimal.Decimal `json:"low_price"`
	TradeV24h   decimal.Decimal `json:"trade_24h"`
	TradeM24h   decimal.Decimal `json:"trade_money_24h"`
}

type UserContractMark struct {
	AccountType define.AccountType `json:"account_type" db:"account_type"`
	CrossLever  int                `db:"cross_lever" json:"cross_lever"`
	LongLever   int                `db:"long_lever" json:"long_lever"`
	ShortLever  int                `db:"short_lever" json:"short_lever"`
}

type CloseOrderEvent struct {
	Event int
	Data  PlanCloseOrder
}

type PlanCloseOrder struct {
	ReqLang          define.ReqLang     `json:"req_lang"`
	PlanCloseOrderId int64              `db:"plan_close_order_id" json:"plan_close_order_id"`
	AccountType      define.AccountType `db:"account_type"` //账户类 0-普通 1-跟单
	PositionId       int64              `db:"position_id" json:"position_id"`
	UserId           int64              `db:"user_id" json:"user_id"`
	ContractCode     string             `db:"contract_code" json:"contract_code"`
	Side             string             `db:"side" json:"side"`
	Amount           decimal.Decimal    `db:"amount" json:"amount"`
	Limit            decimal.Decimal    `db:"limit"`
	ConditionLimit   int                `db:"condition_limit" `
	Stop             decimal.Decimal    `db:"stop"`
	ConditionStop    int                `db:"condition_stop"`
	CreateTime       time.Time          `db:"create_time" json:"create_time"`
	PlatformID       int                `db:"platform_id" json:"platform_id"` //平台id
	Lever            int                `db:"lever" json:"lever"`
	TriggerType      int                `db:"trigger_type" json:"trigger_type"` //1-止盈 2-止损
	TriggerPrice     decimal.Decimal    `db:"trigger_price" json:"trigger_price"`
	TriggerTime      time.Time          `db:"trigger_time" json:"trigger_time"`
	TriggerStatus    int                `db:"trigger_status" json:"trigger_status"`
	Mode             int                `db:"mode" json:"mode"`
	EntrustType      int                `db:"entrust_type" json:"entrust_type"`
	EntrustLimit     decimal.Decimal    `db:"entrust_limit" json:"entrust_limit"`
	EntrustStop      decimal.Decimal    `db:"entrust_stop" json:"entrust_stop"`
	IPAddress        string             `db:"ip_address" json:"ip_address"`
	EntrustOrderId   int64              `db:"entrust_order_id" json:"entrust_order_id"`
	CurrencyName     string             `db:"currency_name" json:"currency_name"`
}

type PlanBasic struct {
	PlanCloseOrderId int64           `db:"plan_close_order_id" json:"plan_close_order_id"`
	AccountType      int             `db:"account_type"` //账户类 0-普通 1-跟单
	PositionId       int64           `db:"position_id" json:"position_id"`
	UserId           int64           `db:"user_id" json:"user_id"`
	ContractCode     string          `db:"contract_code" json:"contract_code"`
	Side             string          `db:"side" json:"side"`
	Amount           int             `db:"amount" json:"amount"`
	Price            decimal.Decimal `db:"limit"`
	Condition        int             `db:"condition" `
	Lever            int             `db:"lever" json:"lever"`
	TriggerPrice     decimal.Decimal `db:"trigger_price" json:"trigger_price"`
	TriggerTime      time.Time       `db:"trigger_time" json:"trigger_time"`
}

type ContractTick struct {
	ID           int64     `json:"id"`
	ContractCode string    `json:"contract_code"`
	TradePrice   float64   `json:"trade_price"`
	TradeAmount  int64     `json:"trade_amount"`
	BuyPrice     float64   `json:"buy_price"`
	SellPrice    float64   `json:"sell_price"`
	IndexPrice   float64   `json:"index_price"`
	CreateTime   time.Time `json:"create_time"`
}

type AllLiquidateArg struct {
	UserID       int64  `json:"user_id" db:"user_id"`
	ContractCode string `json:"contract_code" db:"contract_code"`
	FundPassword string `json:"fund_password"` // 资金密码
}

//撮合成交价信息
type MatchPrice struct {
	TradePrice  decimal.Decimal   `json:"avg_price"`        //撮合成交价
	TradeVolume decimal.Decimal   `json:"trade_volume"`     //成交数量
	Status      define.OrderState `json:"status"`           //成交状态
	DepthPrice  decimal.Decimal   `json:"depth_base_price"` //铺单基准价
	BuyFirst    decimal.Decimal   `json:"buy_first"`        //当前买一
	SellFirst   decimal.Decimal   `json:"sell_first"`       //当前卖一
	BuyMax      decimal.Decimal   `json:"buy_max"`          //鱼饵档位最高价
	SellMax     decimal.Decimal   `json:"sell_max"`         //鱼饵档位最高价
	//TradeList []MatchDetail `json:"list"`
	Depth DepthTradeContainer
}

type MatchDetail struct {
	TradePrice  decimal.Decimal `json:"price"`        //撮合成交价
	TradeVolume int64           `json:"trade_volume"` //成交数量
	Ts          int64           `json:"ts"`           //成交时间
}
