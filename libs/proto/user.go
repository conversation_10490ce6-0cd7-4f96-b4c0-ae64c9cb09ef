package proto

import (
	"github.com/shopspring/decimal"
	"spot/libs/define"
	"time"
)

type User struct {
	UserID              int64                  `json:"user_id" db:"user_id"`                               // 用户id
	UserName            string                 `json:"user_name" db:"user_name"`                           // 用户名
	Nickname            string                 `db:"nickname" json:"nickname"`                             // 中文昵称
	NicknameEn          string                 `db:"nickname_en" json:"nickname_en"`                       // 英文昵称
	Introduce           string                 `db:"introduce" json:"introduce"`                           // 中文介绍
	IntroduceEn         string                 `db:"introduce_en" json:"introduce_en"`                     // 英文介绍
	Avatar              string                 `db:"avatar" json:"avatar"`                                 // 头像
	InviteCode          string                 `json:"invite_code" db:"invite_code"`                       // 邀请码
	EnableLogin         bool                   `json:"enable_login" db:"enable_login"`                     // 是否可登录
	EnableWithdraw      bool                   `json:"enable_withdraw" db:"enable_withdraw"`               // 是否可提现
	EnableTrade         bool                   `json:"enable_trade" db:"enable_trade"`                     // 是否可交易
	ForbidOpenClose     uint8                  `db:"forbid_open_close" json:"-"`                           // 开平仓权限 0: 都不禁止 1: 禁止开仓 2: 禁止平仓
	Phone               string                 `json:"phone" db:"phone"`                                   // 手机号
	Email               string                 `json:"email" db:"email"`                                   // 邮箱
	SpareEmail          string                 `json:"spare_email" db:"spare_email"`                       // 备用邮箱
	TotpSecret          string                 `db:"totp_secret" json:"-"`                                 // totp验证器私钥
	Verify              define.UserVerifyState `json:"verify" db:"verify"`                                 // 认证状态: 0-未认证 1-身份信息认证中 2-身份信息认证失败 3-身份信息认证通过 4-人脸信息认证中 5-人脸信息认证失败 6-人脸信息认证通过
	StaticIdentity      bool                   `json:"-" db:"static_identity"`                             // 是否固定身份信息
	LastVerifyID        int64                  `json:"-" db:"last_verify_id"`                              // 最后一条认证申请id
	LoginPasswd         string                 `json:"-" db:"login_passwd"`                                // 登录密码
	FundPasswd          string                 `json:"-" db:"fund_passwd"`                                 // 资金密码
	InviteParent        string                 `json:"-" db:"invite_parent"`                               // 邀请链
	LastLoginIP         string                 `json:"-" db:"last_login_ip"`                               // 最后登录的ip
	RealName            string                 `json:"-" db:"real_name"`                                   // 用户真实姓名
	CardNo              string                 `json:"-" db:"card_no"`                                     // 证件号码
	CreatedTime         time.Time              `json:"-" db:"created_time"`                                // 注册时间
	LastLoginTime       time.Time              `json:"-" db:"last_login_time"`                             // 最后登录时间
	RestFundTime        time.Time              `json:"-" db:"rest_fund_time"`                              // 最后一次更新资金密码时间
	RestLoginTime       time.Time              `json:"-" db:"rest_login_time"`                             // 最后一次更新登录密码时间
	AreaCode            string                 `json:"area_code" db:"area_code"`                           // 地区区号
	CountryCode         string                 `json:"country_code" db:"country_code"`                     // 国家代码
	DealerState         define.DealerState     `db:"dealer_state" json:"dealer_state"`                     // 交易员状态 -2:未申请 -1:注销 0:暂停 1:是交易员 2:提交申请待审核
	Rebate              decimal.Decimal        `db:"rebate" json:"rebate"`                                 // 分佣比例
	FollowApproved      bool                   `db:"follow_approved" json:"follow_approved"`               // 是否已经接受了跟单协议
	PlatformID          int                    `db:"platform_id" json:"platform_id"`                       // 平台id
	LabelID             int                    `db:"label_id" json:"-"`                                    // 标签id
	Content             string                 `db:"content" json:"content"`                               // 备注
	IsAgent             bool                   `db:"is_agent" json:"is_agent"`                             // 是否是代理
	WithdrawVerify      int                    `db:"withdraw_verify" json:"withdraw_verify"`               // 提币活体验证状态
	TradeApproved       bool                   `db:"trade_approved" json:"trade_approved"`                 // 是否已同意交易协议
	EnableSimulator     bool                   `db:"enable_simulator" json:"enable_simulator,omitempty"`   // 是否开启模拟盘功能
	TradeConfirm        bool                   `db:"trade_confirm" json:"trade_confirm"`                   // 是否开启交易二次确认
	LoginVerifyPhone    bool                   `db:"login_verify_phone" json:"login_verify_phone"`         // 登录两步验证手机号开关
	LoginVerifyEmail    bool                   `db:"login_verify_email" json:"login_verify_email"`         // 登录两步验证邮箱开关
	LoginVerifyTotp     bool                   `db:"login_verify_totp" json:"login_verify_totp"`           // 登录两步验证验证器开关
	TradeVerifyFund     uint8                  `db:"trade_verify_fund" json:"trade_verify_fund"`           // 交易资金验证码验证方式 0-永不验证 1-24小时内验证一次 2-永远验证
	ChangeStyle         int                    `db:"change_style" json:"change_style"`                     // 涨跌样式 0-绿涨红跌 1-红涨绿跌
	ProfitStyle         int                    `db:"profit_style" json:"profit_style"`                     // 盈亏计算方式 0-使用标记价格 1-使用成交价格
	IsOpenAPI           bool                   `db:"is_open_api" json:"is_open_api,omitempty"`             // 是否有权限开通api
	ShowAgentRatio      bool                   `db:"show_agent_ratio" json:"show_agent_ratio"`             // 是否显示代理返佣比例
	ISShowDealer        bool                   `db:"is_show_dealer" json:"is_show_dealer"`                 // 是否显示申请交易员
	IsShowDealerSetting bool                   `db:"is_show_dealer_setting" json:"is_show_dealer_setting"` // 是否显示交易员设置
	FastCloseoutConfirm bool                   `db:"fast_closeout_confirm" json:"fast_closeout_confirm"`   // 是否开启极速平仓二次确认
	ChannelID           int                    `db:"channel_id" json:"channel_id"`                         // 渠道id
	LimitState          define.UserLimitState  `db:"limit_state" json:"limit_state"`                       // 用户限制状态
}

func (u *User) IsTrader() bool {
	return u.DealerState == define.DealerStateNormal
}

// 设置密码请求参数
type ModifyPwdArg struct {
	NewPassword string `json:"new_password"` // 新密码
	ModifyType  int8   `json:"modify_type"`  // 更改类型 0-登陆密码 1-安全密码
}

// 用户信息
type ApiUserInfo struct {
	HasPassword        bool `json:"has_password"`         // 是否已经设置了登录密码
	HasFundPassword    bool `json:"has_fund_password"`    // 是否已经设置了资金密码
	HasTotpBind        bool `json:"has_totp_bind"`        // 是否已经绑定了验证器
	LegalWithdrawLimit bool `json:"legal_withdraw_limit"` // 法币入金提币限制
	User
}

// 改绑账号请求参数
type ModifyAccountArg struct {
	NewAccount string `json:"new_account"` // 新账号
	Code       string `json:"code"`        // 验证码
}

type InviteListArg struct {
	Extra    string `json:"extra"`     // 子用户邀请码,用于查询二级用户邀请列表
	Page     int    `json:"page"`      // 页码从0开始
	PageSize int    `json:"page_size"` // 单页数据量
}

type InvitedInfo struct {
	HasInvite   bool   `db:"has_invite" json:"has_invite"`     // 是否有下级
	UserID      int64  `db:"user_id" json:"user_id"`           // 被邀请者
	Code        string `db:"code" json:"code"`                 // 用户的邀请码
	CreatedTime int64  `db:"created_time" json:"created_time"` // 创建时间
}

type UserContractConfig struct {
	UserId       int64  `json:"user_id"`
	ContractCode string `json:"contract_code"`
	Lever        int    `json:"lever"`
}

type TradeLeverReply struct {
	AccountType   int  `json:"account_type"`
	MaxLever      int  `db:"max_lever" json:"max_lever"`
	FullBuyLever  int  `json:"full_buy_lever" db:"full_buy_lever"`
	FullSellLever int  `json:"full_sell_lever" db:"full_sell_lever"`
	PartBuyLever  int  `json:"part_buy_lever" db:"part_buy_lever"`
	PartSellLever int  `json:"part_sell_lever" db:"part_sell_lever"`
	Holding       bool `json:"holding" db:"holding"`
}

type NewTradeLeverReply struct {
	AccountType  define.AccountType `json:"account_type"`  // 账户模式 1-全仓 2-逐仓
	MaxLever     int                `json:"max_lever"`     // 用户最大可设置杠杆倍数 0-不限制
	CrossLever   int                `json:"cross_lever"`   // 全仓杠杆
	LongLever    int                `json:"long_lever"`    // 逐仓多方向杠杆
	ShortLever   int                `json:"short_lever"`   // 逐仓空方向杠杆
	HoldingLong  bool               `json:"holding_long"`  // 是否持有多方向订单
	HoldingShort bool               `json:"holding_short"` // 是否持有空方向订单
}

func (tr *NewTradeLeverReply) Input(existLong, existShrot bool, crossLever, longLever, shortLever int) {
	if existLong {
		tr.HoldingLong = existLong
	}
	if existShrot {
		tr.HoldingShort = existShrot
	}
	if crossLever > 0 {
		tr.CrossLever = crossLever
	}
	if longLever > 0 {
		tr.LongLever = longLever
	}
	if shortLever > 0 {
		tr.ShortLever = shortLever
	}
}

type SwitchLeverArg struct {
	ContractCode string `json:"contract_code"`
	Side         string `json:"side"`
	AccountType  int    `json:"account_type"`
	Lever        int    `json:"lever"`
	UserID       int64  `json:"user_id"`
}

type NewSwitchLeverArg struct {
	ContractCode string             `json:"contract_code"` // 合约代码
	CoinName     string             `json:"coin_name"`     // 币种名称
	AccountType  define.AccountType `json:"account_type"`  // 账户模式 1-全仓 2-逐仓
	CrossLever   int                `json:"cross_lever"`   // 全仓杠杆
	LongLever    int                `json:"long_lever"`    // 逐仓多方向杠杆
	ShortLever   int                `json:"short_lever"`   // 逐仓空方向杠杆
	UserID       int64              `json:"user_id"`
}

type FaceVerifyArg struct {
	ProtoBuf    []byte `json:"proto_buf"`     // 活体数据二进制数据
	ProtoBufHex string `json:"proto_buf_hex"` // 活体数据16进制字符串
}

type FaceVerifyH5Arg struct {
	Base64Image string `json:"base64_image"` // h5静默活体认证通过后返回的base64_image(活体认证中的一帧人脸图片)
}

type VerifyStateInfo struct {
	VerifyID       int64                  `json:"verify_id" db:"id"`                    // 认证记录id
	State          define.UserVerifyState `json:"state" db:"state"`                     // 认证状态
	ErrCode        int                    `json:"err_code" db:"err_code"`               // 认证错误码
	ErrMsg         string                 `json:"err_msg" db:"err_msg"`                 // 认证错误信息
	Name           string                 `json:"name" db:"real_name"`                  // 姓名
	Surname        string                 `json:"surname" db:"surname"`                 // 姓
	Forename       string                 `json:"forename" db:"forename"`               // 名
	Number         string                 `json:"number" db:"id_number"`                // 证件号码
	CardType       int                    `json:"card_type" db:"card_type"`             // 证件类型 0-大陆证件 1-非大陆证件
	StaticIdentity bool                   `json:"static_identity" db:"static_identity"` // 是否固定了身份信息
}

type VerifyTask struct {
	UserID     int64                  `json:"user_id" db:"user_id"`         // 认证者用户id
	PlatformID int                    `db:"platform_id" json:"platform_id"` // 平台id
	VerifyID   int64                  `json:"verify_id" db:"id"`            // 认证记录id
	Name       string                 `json:"name" db:"real_name"`          // 认证的姓名
	Surname    string                 `json:"surname" db:"surname"`         // 姓
	Forename   string                 `json:"forename" db:"forename"`       // 名
	Number     string                 `json:"number" db:"id_number"`        // 认证的证件号码
	CardType   int                    `json:"card_type" db:"card_type"`     // 证件类型 0-大陆证件 1-非大陆证件
	State      define.UserVerifyState `json:"state" db:"state"`             // 认证状态
	ErrCode    int                    `json:"err_code" db:"err_code"`       // 认证错误码
}

// 身份认证参数(姓名和身份证号)
type VerifyByIdNameArg struct {
	Name   string `json:"name"`   // 姓名
	Number string `json:"number"` // 身份证号
	YiDunRequest
}

// 身份认证返回值(姓名和身份证号)
type VerifyByIdNameReply struct {
	RequestID string `json:"request_id"` // 本次请求id
	Code      int    `json:"code"`       // 系统响应码
	Validity  bool   `json:"validity"`   // 判断结果
}

// 用户邀请信息
type ApiUserInviteInfo struct {
	RebateRate   decimal.Decimal `db:"rebate_rate" json:"rebate_rate"`     // 返佣比例
	LastReward   string          `json:"last_reward" db:"last_reward"`     // 昨日返佣
	TotalReward  string          `json:"total_reward" db:"total_reward"`   // 累计返佣
	LastInvited  int             `json:"last_invited" db:"last_invited"`   // 今日邀请
	TotalInvited int             `json:"total_invited" db:"total_invited"` // 累计邀请
}

type UserContractMarkArg struct {
	CoinName     string `json:"coin_name"`
	ContractCode string `json:"contract_code"`
}

// 用户区号
type AreaCodeUserInfo struct {
	SpareEmail string `db:"spare_email" json:"spare_email"` // 备用邮箱
	AreaCode   string `json:"area_code" db:"area_code"`     // 区号
	UserId     int64  `json:"user_id" db:"user_id"`         // 累计返佣
}

// 身份认证参数(海外)
type ForeignVerifyArg struct {
	Surname  string `json:"surname"`   // 姓
	Forename string `json:"forename"`  // 名
	FullName string `json:"full_name"` // 全名
	Number   string `json:"number"`    // 身份证号
	//AreaCode    string `json:"area_code"`    // 区号
	CountryCode string `json:"country_code"` // 国家编号
	Photo       string `json:"photo"`        // 证件照
	//YiDunRequest
}

// 身份认证参数(人工活体)
type ManualVerifyArg struct {
	AvatarPhoto string `json:"avatar_photo"` // 人像面
	EmblemProto string `json:"emblem_proto"` // 国徽面
	HoldProto   string `json:"hold_proto"`   // 手持证件照
}

// SELECT t1.id, t1.label_id, t1.contract_code, t1.max_lever, t1.max_order_volume, t1.min_order_volume, t1.max_posi_volume, t1.fee, t1.funding, t1.slippage, t1.creat_time, t1.status_stype, t1.risk_rate
type UserLabel struct {
	ID             int             `db:"id" json:"id"`
	LabelID        int             `db:"label_id" json:"label_id"`
	ContractCode   string          `db:"contract_code" json:"contract_code"`
	MaxLever       int             `db:"max_lever" json:"max_lever"`
	MaxOrderVolume decimal.Decimal `db:"max_order_volume" json:"max_order_volume"`
	MinOrderVolume decimal.Decimal `db:"min_order_volume" json:"min_order_volume"`
	MaxPosiVolume  decimal.Decimal `db:"max_posi_volume" json:"max_posi_volume"`
	OrderDelayTime int             `db:"order_delay_time" json:"order_delay_time"` // 订单委托延迟
	Fee            decimal.Decimal `db:"fee" json:"fee"`
	Funding        decimal.Decimal `db:"funding" json:"funding"`
	MaxSlippage    decimal.Decimal `db:"slippage" json:"slippage"`         //
	MinSlippage    decimal.Decimal `db:"min_slippage" json:"min_slippage"` //
	RiskRate       decimal.Decimal `db:"risk_rate" json:"risk_rate"`       //风险率加点
	PlatformId     int             `db:"platform_id" json:"platform_id"`
	StatusStype    int             `db:"status_stype" json:"status_stype"`
}

// 用户绑定支付方式
type UserPayment struct {
	ID             int       `db:"id" json:"id"`
	UserId         int64     `db:"user_id" json:"user_id"`
	Type           int       `db:"type" json:"type"`
	BankName       string    `db:"bank_name" json:"bank_name"`
	BankBranchName string    `db:"bank_branch_name" json:"bank_branch_name"`
	BankNumb       string    `db:"bank_numb" json:"bank_numb"`
	AccountHolder  string    `db:"account_holder" json:"account_holder"`
	OcrAddress     string    `db:"ocr_address" json:"ocr_address"`
	CreatedTime    time.Time `db:"created_time" json:"created_time"`
}

type UserQuotaConfig struct {
	WithdrawOnce      decimal.Decimal `json:"withdraw_once"`        // 提币单笔限额
	WithdrawDaily     decimal.Decimal `json:"withdraw_daily"`       // 提币每日限额
	WithdrawBasic     decimal.Decimal `json:"withdraw_basic"`       // 基本限额(非认证用户每日可提)
	LegalQuotaMinCNY  decimal.Decimal `json:"legal_quota_min_cny"`  // 法币交易单笔最小限额(CNY)
	LegalQuotaMinUSDT decimal.Decimal `json:"legal_quota_min_usdt"` // 法币交易单笔最小限额(USDT)
	LegalQuotaCNY     decimal.Decimal `json:"legal_quota_cny"`      // 法币交易单笔最大限额(CNY)
	LegalQuotaUSDT    decimal.Decimal `json:"legal_quota_usdt"`     // 法币交易单笔最大限额(USDT)
}

type UserQuotaReply struct {
	WithdrawMaxQuota decimal.Decimal `json:"withdraw_max_quota"` // 本次提币最大额度
	UserQuotaConfig
}

var DefaultUserQuotaConfig = &UserQuotaConfig{
	WithdrawOnce:      decimal.NewFromInt(10000), // 默认单笔提币限额
	WithdrawDaily:     decimal.NewFromInt(20000), // 默认每日提币限额
	WithdrawBasic:     decimal.NewFromInt(10000), // 基本限额(非认证用户每日可提)
	LegalQuotaMinCNY:  decimal.NewFromInt(100),   // 默认单笔法币交易最小限额(cny)
	LegalQuotaMinUSDT: decimal.NewFromInt(20),    // 默认单笔法币交易最小限额(usdt)
	LegalQuotaCNY:     decimal.NewFromInt(50000), // 默认单笔法币交易最大限额(cny)
	LegalQuotaUSDT:    decimal.NewFromInt(7000),  // 默认单笔法币交易最大限额(usdt)
}

type RemainQuota struct {
	Remain decimal.Decimal `json:"remain"` // 剩余数量
}

type CountryAreacode struct {
	ID             int64  `db:"id" json:"id"`                           // 数据id
	CountryEn      string `db:"country_en" json:"country_en"`           // 英文
	CountryCn      string `db:"country_cn" json:"country_cn"`           // 中文
	CountryTw      string `db:"country_tw" json:"country_tw"`           // 繁体
	CountryCode    string `db:"country_code" json:"country_code"`       // 编号
	CountryEncrypt string `db:"country_encrypt" json:"country_encrypt"` // 国家代码
}
