package proto

import "spot/libs/define"

type Version struct {
	Content       string `db:"content" json:"content"`               //更新文案
	CreatedTime   int64  `db:"created_time" json:"created_time"`     //上线时间
	ForceUpgrade  int    `db:"force_upgrade" json:"force_upgrade"`   //是否强制更新
	Link          string `db:"link" json:"link"`                     //下载页面
	OsType        int    `db:"os_type" json:"os_type"`               //客户端 1: ios,  2: android
	Url           string `db:"url" json:"url"`                       //下载链接
	Version       int    `db:"version" json:"version"`               //数字版本号
	VersionId     int    `db:"version_id" json:"version_id"`         //
	VersionString string `db:"version_string" json:"version_string"` //文字版本号
}

type HotfixPatch struct {
	OsType     define.OsType `db:"os_type" json:"os_type"`         // 客户端类型
	Version    int           `db:"version" json:"version"`         // 客户端版本
	PatchName  string        `db:"patch_name" json:"patch_name"`   // 补丁名称
	PatchURL   string        `db:"patch_url" json:"patch_url"`     // 补丁下载地址
	CreateTime int64         `db:"create_time" json:"create_time"` // 创建时间
}
