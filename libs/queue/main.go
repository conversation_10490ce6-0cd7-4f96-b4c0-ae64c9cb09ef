package queue

import (
	"time"
)

type RedisQueue struct {
	name     string
	shutdown bool
}

func NewRQueue(name string) (rq *RedisQueue) {
	return &RedisQueue{name: name}
}

func (r RedisQueue) Push(data interface{}) {
	Push(r.name, data)
}

func (r RedisQueue) Pop() (p []byte, err error) {
	return Pop(r.name)
}

func (r RedisQueue) Len() (int64, error) {
	return Len(r.name)
}

func (r RedisQueue) Clear() {
	Clear(r.name)
}

func (r RedisQueue) Consumer() (ch chan []byte) {
	ch = make(chan []byte, 1)
	go func() {
		for {
			//l, err := r.Len()
			//if err != nil {
			//	log.Error("reidis queue len fail", zap.Error(err))
			//}
			//log.Info("consumer", zap.String("name", r.name),zap.Int64("size",l), zap.Bool("s", r.shutdown))
			if r.shutdown {
				close(ch)
				return
			}
			//log.Info("当前长度",zap.Int64("size",l))
			data, err := r.Pop()
			if err != nil {
				//log.Info("consumer pop", zap.Error(err))
				time.Sleep(1 * time.Second)
				continue
			}
			//log.Info("consumer", zap.String("data", string(data)))
			if len(data) == 0 {
				time.Sleep(20 * time.Millisecond)
				continue
			}
			ch <- data
		}
	}()
	return ch
}

func (r RedisQueue) CloseConsumer() {
	r.shutdown = true
}
