package queue

import (
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/utils"
)

const _prefix = "r_queue"

func Push(queueName string, data interface{}) (err error) {
	key := utils.StrBuilderBySep(":", queueName)
	b, err := json.Marshal(data)
	if err != nil {
		log.Error("Push json marshal error", zap.Error(err))
		return
	}
	err = cache.DefaultRedis().RPush(key, b).Err()
	return
}

// Pop 移除头部
func Pop(queueName string) (data []byte, err error) {
	key := utils.StrBuilderBySep(":", queueName)
	data, err = cache.DefaultRedis().LPop(key).Bytes()
	if err != nil {
		return
	}
	return
}

// Len 查询队列长度
func Len(queueName string) (count int64, err error) {
	key := utils.StrBuilderBySep(":", queueName)
	count, err = cache.DefaultRedis().LLen(key).Result()
	if err != nil {
		log.Error("查询队列长度出错", zap.Error(err), zap.String("key", key))
		return
	}
	return
}

func Clear(queueName string) {
	key := utils.StrBuilderBySep(":", queueName)
	err := cache.DefaultRedis().Del(key).Err()
	if err != nil {
		log.Error("清除redis出错", zap.Error(err), zap.String("key", key))
	}
}
