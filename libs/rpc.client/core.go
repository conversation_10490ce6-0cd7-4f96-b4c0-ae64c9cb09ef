package rpcclient

import (
	"time"

	"go.uber.org/zap"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
	"spot/libs/xrpc"
)

var coreClient = new(xrpc.RClient)

const (
	_entrustOpen = "RPC.EntrustOpen"
)

func InitRpcCore(address string) {
	var err error
	coreClient, err = xrpc.NewClient(address)
	if err == nil {
		log.Info("Init coreClient connect success", zap.String("address", address))
		return
	}
	go func() {
		t := time.Tick(time.Second)
		var err error
		for range t {
			coreClient, err = xrpc.NewClient(address)
			if err != nil {
				log.Error("init coreClient connect failed", zap.String("address", address), zap.Error(err))
				continue
			} else {
				log.Info("init coreClient connect success", zap.String("address", address))
				break
			}
		}
	}()
}

func EntrustOpen(request *proto.OrderOpenArgs, reply *define.Reply) (err error) {
	log.Info("EntrustOpen", zap.Int64("reqID", request.RequestId), zap.Any("request", request))
	defer log.Info("EntrustOpen", zap.Int64("reqID", request.RequestId), zap.Any("request", request), zap.Any("resp", reply))
	err = coreClient.CallRPC(_entrustOpen, request, reply)
	return
}
