package sensetime

const (
	IdNumberVerifyAPI = "/validity/idnumber_verification"                    // 用于验证身份证上的姓名与身份证号是否匹配
	FaceVerifyH5API   = "/identity/idnumber_verification/stateless"          // 使用姓名和身份证号及人脸图像数据进行认证
	FaceVerifyAPI     = "/identity/liveness_idnumber_verification/stateless" // 使用姓名和身份证号及活体数据进行认证
)

const (
	RequestSuccess             = 1000
	RateLimitExceeded          = 1002
	InvalidCrendential         = 1100
	ExpiredCrendential         = 1101
	UrlNoPermission            = 1103
	InvalidArgument            = 1200
	FaceDataError              = 2007
	ExternalServiceUnavailable = 3000
	ExternalServiceTimeout     = 3001
	ExternalServiceError       = 3002
	NameIdMismatched           = 3003
	InvalidIdNumber            = 3004
	ProtoNotExist              = 3005
	DetectionFailed            = 4000
)

var sensetimeErrText = map[int]string{
	RateLimitExceeded:          "使用频率超过限制",
	InvalidCrendential:         "账号密码不匹配或签名认证生成有误",
	ExpiredCrendential:         "账号过期",
	UrlNoPermission:            "该接口没有权限",
	InvalidArgument:            "输入参数无效",
	FaceDataError:              "活体数据损坏",
	ExternalServiceUnavailable: "外部服务不可用",
	ExternalServiceTimeout:     "外部服务超时",
	ExternalServiceError:       "外部服务出错",
	NameIdMismatched:           "姓名和身份证号不匹配",
	InvalidIdNumber:            "身份证号无效",
	ProtoNotExist:              "第三方地图不存在",
	DetectionFailed:            "提取特征失败,没有检测到人脸",
}

var undefinedErrText = "未定义错误"

func GetSenseTimeErr(code int) string {
	if _, ok := sensetimeErrText[code]; ok {
		return sensetimeErrText[code]
	}
	return undefinedErrText
}

type IDNumberVerifyArg struct {
	Name     string `json:"name"`     // 姓名
	IDNumber string `json:"idnumber"` // 身份证号码
}

type IDNumberVerifyReply struct {
	RequestID string `json:"request_id"` // 相应id
	Code      int    `json:"code"`       // 响应码
	Validity  bool   `json:"validity"`   // 是否匹配
	Message   string `json:"message"`    // 错误信息
}

type CheckReservedHeadReply struct {
	RequestId         string  `json:"request_id"`         // 请求id
	Code              int     `json:"code"`               // 响应码
	VerificationScore float64 `json:"verification_score"` // 人脸对比得分 0-1
	Message           string  `json:"message"`            // 错误信息
}

type IDNumberFaceVerifyArg struct {
	Name        string `json:"name"`         // 姓名
	IDNumber    string `json:"idnumber"`     // 身份证号码
	ImageBase64 string `json:"image_base64"` // 经过base64编码的人脸照片
}
