package sensetime

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"io"
	"io/ioutil"
	"net/http"
	"sort"
	"spot/libs/commonsrv"
	"spot/libs/define"
	"strconv"
	"strings"
	"time"

	"spot/libs/utils"
)

/*
	https://v2-devcenter.visioncloudapi.com/#/doc/v2/index
*/

const (
	senseApiKey    = "4b8c74b521634cb598f6206fc0362ef9"
	senseApiSecret = "13780492ee5144d18f33dde895f6de79"
	senseApiUrl    = "https://v2-auth-api.visioncloudapi.com"
)

type SenseTime struct {
	Key    string
	Secret string
	Url    string
}

var senseTime = SenseTime{
	Key:    senseApiKey,
	Secret: senseApiSecret,
	Url:    senseApiUrl,
}

func Init(c SenseTime) {
	if c.Key != "" {
		senseTime.Key = c.Key
	}
	if c.Secret != "" {
		senseTime.Secret = c.Secret
	}
	if c.Url != "" {
		senseTime.Url = c.Url
	}
}

// 请求商汤接口
func SenseTimeApi(runID, userID int64, method, url, contentType string, reader io.Reader) ([]byte, error) {
	req, err := http.NewRequest(method, senseTime.Url+url, reader)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", contentType)
	req.Header.Set("Authorization", generateHeader())
	client := new(http.Client)
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		commonsrv.SaveExternalErrorRecord(nil, runID, userID, define.ExternalSysTypeSenseTime, resp.StatusCode, http.StatusText(resp.StatusCode), url)
		err = errors.New(http.StatusText(resp.StatusCode))
		return nil, err
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	return body, nil
}

// 生成请求参数及签名
func generateHeader() string {
	// 获取毫秒时间戳
	timestamp := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	// 获取随机字符串
	nonce := utils.RandStringRunes(32)
	// 拼接字符串
	list := make([]string, 0, 3)
	list = append(list, timestamp, nonce, senseTime.Key)
	sort.Strings(list)
	joinStr := strings.Join(list, "")
	// 生成签名
	h := hmac.New(sha256.New, []byte(senseTime.Secret))
	h.Write([]byte(joinStr))
	signature := hex.EncodeToString(h.Sum(nil))

	header := make(map[string]string, 4)
	header["key"] = senseTime.Key
	header["timestamp"] = timestamp
	header["nonce"] = nonce
	header["signature"] = signature

	return parseMap(header)
}

func parseMap(headerMap map[string]string) string {
	var header []string
	for key, value := range headerMap {
		header = append(header, key+"="+value)
	}
	return strings.Join(header, ",")
}
