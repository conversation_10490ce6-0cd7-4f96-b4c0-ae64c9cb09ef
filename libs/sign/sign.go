package sign

import (
	"crypto/md5"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"net/url"
	"sort"
	"strconv"
	"strings"

	"spot/libs/convert"
)

func NormalEncode(origin string) string {
	return mySha256(myMd5(myMd5(origin)))
}

func AdminEncode(origin, key string) string {
	return myMd5(myMd5(origin) + key)
}

func mySha256(s string) string {
	h := sha256.New()
	h.Write(convert.Str2Bytes(s))
	return hex.EncodeToString(h.Sum(nil))
}

func myMd5(s string) string {
	h := md5.New()
	h.Write(convert.Str2Bytes(s))
	return hex.EncodeToString(h.Sum(nil))
}

func GetParasStr(params map[string]interface{}) string {
	paramsSli := make([]string, 0, len(params))
	getParamsSli(&paramsSli, params)
	sort.SliceStable(paramsSli, func(i, j int) bool {
		return paramsSli[i] < paramsSli[j]
	})
	return strings.Join(paramsSli, "&")
}

func GetParasStrV2(params map[string]string) string {
	paramsSli := make([]string, 0, len(params))
	for key, val := range params {
		if len(val) == 0 {
			continue
		}
		paramsSli = append(paramsSli, key+"="+url.QueryEscape(val))
	}

	sort.SliceStable(paramsSli, func(i, j int) bool {
		return paramsSli[i] < paramsSli[j]
	})
	return strings.Join(paramsSli, "&")
}

func getParamsSli(paramsSli *[]string, params map[string]interface{}) {
	for k, v := range params {
		switch val := v.(type) {
		case string:
			if len(val) == 0 {
				continue
			}
			//urlVal := url.Values{k: []string{val}}
			//fmt.Println(urlVal.Encode())
			//*paramsSli = append(*paramsSli, urlVal.Encode())
			tmp := k + "=" + url.QueryEscape(val)
			//fmt.Println(tmp)
			*paramsSli = append(*paramsSli, tmp)
		case int, uint, int8, uint8, int16, uint16, int32, uint32, int64, uint64:
			*paramsSli = append(*paramsSli, fmt.Sprintf("%s=%d", k, val))
		case bool:
			*paramsSli = append(*paramsSli, k+"="+strconv.FormatBool(val))
		case json.Number:
			*paramsSli = append(*paramsSli, k+"="+val.String())
		case float32:
			f := strconv.FormatFloat(float64(val), 'f', -1, 32)
			*paramsSli = append(*paramsSli, k+"="+f)
		case float64:
			f := strconv.FormatFloat(val, 'f', -1, 64)
			*paramsSli = append(*paramsSli, k+"="+f)
		case []interface{}:
		case map[string]interface{}:
			getParamsSli(paramsSli, val)
		}
	}
}

func Struct2Map(obj interface{}) (map[string]interface{}, error) {
	data, err := json.Marshal(obj)
	if err != nil {
		log.Printf("Struct2Map marshal error, obj:%+v, err:%v\n", obj, err)
		return nil, err
	}

	m := make(map[string]interface{})
	err = json.Unmarshal(data, &m)
	if err != nil {
		log.Printf("Struct2Map unmarshal error, obj:%+v, err:%v\n", obj, err)
		return nil, err
	}
	return m, nil
}
