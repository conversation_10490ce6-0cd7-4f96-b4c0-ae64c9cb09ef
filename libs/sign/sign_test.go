package sign

import (
	"testing"
	"time"
)

func TestNormalEncode(t *testing.T) {
	secretKeyAndroid := "826b03d3f108a2663fb1acf2d603eca60d1a51ac8491a98d5bf54cd17762316d"

	param := make(map[string]interface{})
	param["verNum"] = 10
	param["verStr"] = "1.0.1 哈哈哈"
	param["req_lang"] = 0
	param["req_os"] = 1
	param["device"] = "XIAOMI MIX 2S"
	param["device_id"] = "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5"
	param["version"] = "10"
	param["ts"] = time.Now().Unix()

	origin := GetParasStr(param)
	t.Logf("origin: %s", origin)

	origin += secretKeyAndroid
	t.Logf("origin: %s", origin)

	t.Log("\norigin:", origin, "\nsign:  ", NormalEncode(origin))
}
