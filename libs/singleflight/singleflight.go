package singleflight

import (
	"fmt"
	"runtime"

	"golang.org/x/sync/singleflight"
)

var sf singleflight.Group

func MergeDo[T any](key string, fn func() (any, error)) (res T, err error) {
	// 获取调用者的函数名,拼接到key中,防止不同地方调用使用了同一个锁
	pc, _, _, _ := runtime.Caller(1)
	key = runtime.FuncForPC(pc).Name() + ":" + key
	//log.Info("TEST LOG MergeDo", zap.String("key", key))

	r, err, _ := sf.Do(key, fn)
	if err != nil || r == nil {
		return
	}
	res, ok := r.(T)
	if !ok {
		err = fmt.Errorf("want type [%T], but actual [%T]", res, r)
		return
	}
	//log.Info("TEST LOG", zap.String("key", fnName+":"+key), zap.Bool("shared", shared))

	return
}

func MergeDoEx[T any](key string, fn func() (any, error)) (res T, shared bool, err error) {
	r, err, shared := sf.Do(key, fn)
	if err != nil {
		return
	}
	res, ok := r.(T)
	if !ok {
		err = fmt.Errorf("want type [%T], but actual [%T]", res, r)
		return
	}
	return
}
