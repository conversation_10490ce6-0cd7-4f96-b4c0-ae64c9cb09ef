package singleflight

import (
	"errors"
	"sync"
	"testing"
	"time"
)

func TestMergeDo(t *testing.T) {
	var wg sync.WaitGroup
	for i := 0; i < 3; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			res, err := MergeDo[string]("test", func() (any, error) {
				t.Logf("do with id:%d", id)
				time.Sleep(time.Second)
				return "success", nil
			})

			if err != nil {
				t.<PERSON>rro<PERSON>("error, id:%d, err:%v", id, err)
				return
			}

			t.Logf("done, id:%d, res:%s", id, res)
		}(i)
	}

	wg.Wait()
}

func TestMergeDo_Fail1(t *testing.T) {
	var wg sync.WaitGroup
	for i := 0; i < 3; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			res, err := MergeDo[string]("test", func() (any, error) {
				t.Logf("do with id:%d", id)
				time.Sleep(time.Second)
				return "", errors.New("fail do")
			})

			if err != nil {
				t.Logf("error, id:%d, err:%v", id, err)
				return
			}

			t.Errorf("done, id:%d, res:%s", id, res)
		}(i)
	}

	wg.Wait()
}

func TestMergeDo_Fail2(t *testing.T) {
	var wg sync.WaitGroup
	for i := 0; i < 3; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			res, err := MergeDo[string]("test", func() (any, error) {
				t.Logf("do with id:%d", id)
				time.Sleep(time.Second)
				return 1, nil
			})
			if err != nil {
				t.Logf("error, id:%d, err:%v", id, err)
				return
			}

			t.Errorf("done, id:%d, res:%s", id, res)
		}(i)
	}

	wg.Wait()
}
