package tds

import (
	"github.com/go-redis/redis"
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/nums"
	"spot/libs/utils"
	"time"
)

func getNodeSrvKey(srvName string) string {
	return utils.StrBuilderBySep(":", BaseSrvKey, "nodes", srvName)
}
func setNode(srvName, node string) (err error) {
	err = cache.DefaultRedis().HSet(getNodeSrvKey(srvName), node, time.Now().Unix()).Err()
	return
}

func getValidCount(srvName string) (count int64, err error) {
	list, err := cache.DefaultRedis().HVals(getNodeSrvKey(srvName)).Result()
	if err != nil {
		return
	}
	t := time.Now().Unix()
	for _, s := range list {
		g := t - nums.String2Int64(s)
		if g < 5 {
			count++
		}
	}
	return
}

func getValidNode(srvName string) (m map[string]struct{}, err error) {
	m = make(map[string]struct{})
	list, err := cache.DefaultRedis().HGetAll(getNodeSrvKey(srvName)).Result()
	if err != nil {
		return
	}
	t := time.Now().Unix()
	for k, s := range list {
		g := t - nums.String2Int64(s)
		if g < 5 {
			m[k] = struct{}{}
		}
	}
	return
}

func isNodeValid(srvName, nodeName string) bool {
	m, _ := getValidNode(srvName)
	_, ok := m[nodeName]
	return ok
}

func removeNode(srvName, node string) (err error) {
	err = cache.DefaultRedis().HDel(getNodeSrvKey(srvName), node).Err()
	return
}

func Push(taskName string, job *Job) (err error) {
	key := getTaskQueue(taskName)
	b, err := json.Marshal(job)
	if err != nil {
		log.Error("Push json marshal error", zap.Error(err))
		return
	}
	err = cache.DefaultRedis().RPush(key, b).Err()
	return
}

// Pop 移除头部
func Pop(taskName string) (job *Job, err error) {
	key := getTaskQueue(taskName)
	b, err := cache.DefaultRedis().LPop(key).Bytes()
	if err != nil {
		if err != redis.Nil {
			log.Error("Pop lPop fail", zap.Error(err))
		}
		return
	}
	if b != nil && len(b) > 0 {
		j := new(Job)
		err = json.Unmarshal(b, j)
		if err != nil {
			log.Error("pop json unmarshal error", zap.Error(err))
			return
		}
		job = j
	}
	return
}

// Len 查询队列长度
func Len(taskName string) (count int64, err error) {
	key := getTaskQueue(taskName)
	count, err = cache.DefaultRedis().LLen(key).Result()
	if err != nil {
		log.Error("查询队列长度出错", zap.Error(err), zap.String("key", key))
		return
	}
	return
}

//
func SetLockWithExp(exp time.Duration, key, value string) bool {
	res, err := cache.DefaultRedis().SetNX(key, value, exp).Result()
	if err != nil {
		log.Error("SetLockWithExp redis error", zap.String("key", key), zap.Error(err))
		return false
	}
	return res
}

func ExpKey(exp time.Duration, key string) {
	err := cache.DefaultRedis().Expire(key, exp).Err()
	if err != nil {
		log.Error("SetLockWithExp redis error", zap.String("key", key), zap.Error(err))
		return
	}
	return
}

func GetKey(key string) string {
	s, err := cache.DefaultRedis().Get(key).Result()
	if err != nil {
		return ""
	}
	return s
}

func GetKeyBytes(key string) []byte {
	s, err := cache.DefaultRedis().Get(key).Bytes()
	if err != nil {
		return nil
	}
	return s
}

// SetTaskInfo 设置任务公共基础信息
func SetTaskInfo(key string, data []byte) (err error) {
	err = cache.DefaultRedis().Set(key, data, 1*time.Minute).Err()
	if err != nil {
		log.Error("SetTaskInfo fail", zap.Error(err))
		return
	}
	return nil
}
