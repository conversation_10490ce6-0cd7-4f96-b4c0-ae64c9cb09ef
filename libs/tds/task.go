package tds

import (
	"encoding/json"
	"spot/libs/cache"
	"spot/libs/nums"
	"spot/libs/utils"
	"time"
)

type Task struct {
	ID   int64
	Name string
}

func NewTask(id int64, name string) *Task {
	return &Task{
		ID:   id,
		Name: name,
	}
}

func (t *Task) SetTaskInfo(data interface{}, duration time.Duration) {
	key := TaskInfo + ":" + t.Name + ":" + nums.Int64String(t.ID)
	b, err := json.Marshal(data)
	if err != nil {
		return
	}
	_, err = cache.DefaultRedis().Set(key, b, duration).Result()
	if err != nil {
		return
	}
}

func GetTaskInfo(taskName string, id int64) []byte {
	key := TaskInfo + ":" + taskName + ":" + nums.Int64String(id)
	b, _ := cache.DefaultRedis().Get(key).Bytes()
	return b
}

//任务分发
func (t *Task) Distribute(jobs ...*Job) {
	//key := utils.StrBuilderBySep(":", TaskDistributeLock, t.Name)
	//if !SetLockWithExp(10*time.Second, key, t.Name) {
	//	log.Info("没有获取到任务分发锁",zap.String("name",t.Name))
	//	return
	//}
	//defer cache.SetRedisUnLockStr(key)
Retry:
	count, _ := Len(t.Name)
	if count > 100000 {
		time.Sleep(100 * time.Millisecond)
		goto Retry
	}
	for _, job := range jobs {
		Push(t.Name, job)
	}
}

//等待任务完成
func (t *Task) Wait() {
	done := make(chan int)
	go func() {
		for {
			//检查任务是否完成
			a, _ := Len(t.Name)
			if a == 0 {
				done <- 1
				close(done)
				break
			}
		}
	}()
	<-done
}

type Job struct {
	Ts       time.Time       `json:"ts"`
	Id       int64           `json:"id"`        //job id
	TaskId   int64           `json:"task_id"`   //任务id
	TaskType int             `json:"task_type"` //任务类型
	Data     json.RawMessage `json:"data"`
}

//获取任务队列redis key
func getTaskQueue(taskName string) string {
	return utils.StrBuilderBySep(":", _name, _queueName, taskName)
}

//获取leaderName key
func getLeaderNodeNameKey(srv string) string {
	return utils.StrBuilderBySep(":", BaseSrvKey, "leader", srv)
}
