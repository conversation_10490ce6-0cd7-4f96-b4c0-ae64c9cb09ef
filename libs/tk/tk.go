package tk

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"spot/libs/log"
	"spot/libs/utils"
)

const (
	_requestUrl = "https://services.tokenview.com/"
	_apiBs      = "financeapi/v001/history/bs?"
	_apiKline   = "financeapi/v001/history/kline?"
	_apiKey     = "SwNapkmIT4me6c4GCtiGWbn2"
)

type TkReply struct {
	Code int             `json:"code"`
	Msg  string          `json:"msg"`
	Data json.RawMessage `json:"data"`
}
type BsIndicator struct {
	Symbol    string  `json:"symbol"`
	Status    int     `json:"status"`
	Timestamp int64   `json:"timestamp"`
	Price     float64 `json:"price"`
}

type KlineSupport struct {
	Symbol     string  `json:"symbol"`
	Timestamp  int64   `json:"timestamp"`
	High       float64 `json:"high"`
	Close      float64 `json:"close"`
	Open       float64 `json:"open"`
	Low        float64 `json:"low"`
	Volume     float64 `json:"volume"`
	Support    float64 `json:"support"`
	Resistence float64 `json:"resistence"`
}

func GetBsData(startTime, endTime string, symbol string) (list []BsIndicator, err error) {
	url := utils.StrBuilder(_requestUrl, _apiBs, "symbol=", symbol, "&starttime=", startTime, "&endtime=", endTime, "&apikey=", _apiKey)
	log.Infof("开始请求bs数据，url:%v", url)
	//fmt.Printf("url:%v\n", url)
	var (
		rsp *http.Response
		b   []byte
	)
	rsp, err = http.Get(url)
	if err != nil {
		return
	}
	if rsp.StatusCode != http.StatusOK {
		err = fmt.Errorf("网络故障,code；%v", rsp.StatusCode)
		return
	}

	b, err = ioutil.ReadAll(rsp.Body)
	if err != nil {
		return
	}
	log.Infof("获取bs:%+v", string(b))
	reply := new(TkReply)
	err = json.Unmarshal(b, reply)
	if err != nil {
		return
	}
	if reply.Code != 1 {
		err = fmt.Errorf("返回数据不成功；%+v", reply)
		return
	}

	err = json.Unmarshal(reply.Data, &list)
	if err != nil {
		log.Errorf("GetBsData json.Unmarshal fai,%v", err)
		return
	}
	return
}

func GetTKlineData(startTime, endTime string, symbol string) (list []KlineSupport, err error) {
	url := utils.StrBuilder(_requestUrl, _apiKline, "symbol=", symbol, "&starttime=", startTime, "&endtime=", endTime, "&apikey=", _apiKey)
	log.Infof("开始请求bs kline数据，url:%v", url)
	var (
		rsp *http.Response
		b   []byte
	)
	rsp, err = http.Get(url)
	if err != nil {
		return
	}
	if rsp.StatusCode != http.StatusOK {
		err = fmt.Errorf("网络故障,code；%v", rsp.StatusCode)
		return
	}
	b, err = ioutil.ReadAll(rsp.Body)
	if err != nil {
		return
	}
	log.Infof("获取bs kline:%+v", string(b))
	reply := new(TkReply)
	err = json.Unmarshal(b, reply)
	if err != nil {
		return
	}
	if reply.Code != 1 {
		err = fmt.Errorf("返回数据不成功；%+v", reply)
		return
	}

	err = json.Unmarshal(reply.Data, &list)
	if err != nil {
		return
	}
	return
}
