package tk

import (
	"spot/libs/convert"
	"testing"
	"time"
)

func TestBsData(t *testing.T) {
	//获取5分钟之前的数据
	be := time.Now().Add(-48 * time.Hour).Unix()
	b, e := convert.Int64String(be), convert.Int64String(time.Now().Unix())
	list, err := GetBsData(b, e, "eth")
	if err != nil {
		t.Logf("err:%v", err)
		return
	}
	t.Logf("list:%+v", list)
	t.Log(len(list))
}

func TestSupportData(t *testing.T) {
	be := time.Now().Add(-5 * time.Hour).Unix()
	b, e := convert.Int64String(be), convert.Int64String(time.Now().Unix())
	list, err := GetTKlineData(b, e, "eth")
	if err != nil {
		t.Logf("err:%v", err)
		return
	}
	t.Logf("list:%+v", list)
	t.Log(len(list))
}
