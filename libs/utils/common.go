/*
@Time : 2019-12-30 17:34
<AUTHOR> mocha
@File : common
*/
package utils

import (
	"strconv"

	"spot/libs/log"
)

func GetValidPrice(price float64, priceLimit int) float64 {
	if price <= 0 || priceLimit < 0 || priceLimit > 8 || priceLimit%2 != 0 {
		return price
	}
	s := strconv.FormatFloat(price, 'f', 8, 64)
	offset := 8 - priceLimit
	if offset < 0 {
		return price
	}
	if len(s) <= offset {
		return price
	}
	vps := s[:len(s)-offset]
	f64, err := strconv.ParseFloat(string(vps), 64)
	if err != nil {
		log.Errorf("strconv.ParseFloat err: %v", err)
		return price
	}
	return f64
}
