/*
@Time : 2020-01-03 11:18
<AUTHOR> mocha
@File : contractId
*/
package utils

import (
	"strconv"
	"strings"
	"sync"
	"time"
)

var cLock = contractId{mTime: time.Now().Format("200601021504")}

type contractId struct {
	mutex sync.Mutex
	mTime string
	seq   int
}

func GetSequenceId() string {
	cLock.mutex.Lock()
	defer cLock.mutex.Unlock()
	now := time.Now()
	id := now.Format("200601021504")
	if cLock.mTime != id {
		cLock.mTime = id
		cLock.seq = 0
	}
	cLock.seq++
	numStr := strconv.Itoa(cLock.seq)
	if len(numStr) < 3 {
		numStr = strings.Repeat("0", 3-len(numStr)) + numStr
	}
	cId := id + numStr
	return cId
}
