package utils

import (
	"crypto/tls"
	"errors"
	"fmt"
	"github.com/PuerkitoBio/goquery"
	"io/ioutil"
	"net"
	"net/http"
	"spot/libs/log"
	"time"
)

func GetDocument(url string) (document *goquery.Document, err error) {
	client := http.Client{
		Timeout: 20 * time.Second,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}
	defer time.Sleep(1 * time.Second)
	request, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		log.Errorf("=http.NewRequest fail,%v", err)
		return
	}
	//request.Header.Set("User-Agent","Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.26 Safari/537.36 Edg/85.0.564.13")
	res, err := client.Do(request)
	if err != nil {
		log.Errorf("get url data fail,%v,url:%v", err, url)
		return
	}
	defer res.Body.Close()
	if res.StatusCode != 200 {
		err = fmt.Errorf("http status fail,%v", res.Status)
		log.Errorf("status code error: %d %s", res.StatusCode, res.Status)
		return
	}

	// Load the HTML document
	doc, err := goquery.NewDocumentFromReader(res.Body)
	if err != nil {
		log.Errorf("go query fail,%v", err)
		return
	}
	return doc, err
}

var HTTPTransport = &http.Transport{
	DialContext: (&net.Dialer{
		Timeout:   30 * time.Second, // 连接超时时间
		KeepAlive: 60 * time.Second, // 保持长连接的时间
	}).DialContext, // 设置连接的参数
	MaxIdleConns:          500,              // 最大空闲连接
	IdleConnTimeout:       60 * time.Second, // 空闲连接的超时时间
	ExpectContinueTimeout: 30 * time.Second, // 等待服务第一个响应的超时时间
	MaxIdleConnsPerHost:   100,              // 每个host保持的空闲连接数
}

func NewHttpClient() *http.Client {
	return &http.Client{Transport: HTTPTransport}
}

func GetUrl(client *http.Client, url string) (b []byte, err error) {
	req, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		return
	}
	if client == nil {
		client = NewHttpClient()
	}
	resp, err := client.Do(req)
	if err != nil {
		return
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		err = errors.New(fmt.Sprintf("get http code fail,code:%v", resp.StatusCode))
		return
	}
	b, err = ioutil.ReadAll(resp.Body)
	return
}
