package utils

import (
	"fmt"
	"math/rand"
	"time"

	"spot/libs/convert"
)

func init() {
	rand.Seed(time.Now().UnixNano())
}

const digitBytes = "0123456789"

//生成随机字符串
func RandDigitString(n int) string {
	b := make([]byte, n)
	for i := range b {
		b[i] = digitBytes[rand.Intn(len(digitBytes))]
	}
	return convert.Bytes2Str(b)
}

// return len=6 salt
func GetRandomSalt() string {
	return RandDigitString(6)
}

// GenerateOrderNO 生成唯一订单号
func GenerateOrderNO() string {
	now := time.Now()
	rnd := rand.Intn(10000) + 10000*(1+rand.Intn(9))
	return fmt.Sprintf("%s%d%d", now.Format("20060102"), now.UnixNano(), rnd)
}

var letterRunes = []rune("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ")

func RandStringRunes(n int) string {
	b := make([]rune, n)
	for i := range b {
		b[i] = letterRunes[rand.Intn(len(letterRunes))]
	}
	return string(b)
}
