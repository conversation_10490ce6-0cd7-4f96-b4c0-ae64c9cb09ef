// Copyright 2013 The Gorilla WebSocket Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

package ws

import (
	"golang.org/x/time/rate"
	"net/http"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/zip"
	"time"

	"github.com/gorilla/websocket"
)

const (
	// Time allowed to write a message to the peer.
	writeWait = 10 * time.Second

	// Time allowed to read the next pong message from the peer.
	pongWait = 60 * time.Second

	// Send pings to peer with this period. Must be less than pongWait.
	pingPeriod = (pongWait * 9) / 10

	// Maximum message size allowed from peer.
	maxMessageSize = 512
)

var (
	newline = []byte{'\n'}
	space   = []byte{' '}
)

var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
}

// Client is a middleman between the websocket connection and the hub.
type Client struct {
	hub *Hub
	// The websocket connection.
	conn *websocket.Conn
	// Buffered channel of outbound messages.
	send chan []byte
	//send chan cap
	sendCap int
	//UserId
	UserId int64
	//Token
	Token string
	//current symbol
	CurSymbol string
	//curDepth level
	CurDepthLevel int
	//
	isCanWrite bool

	isRateLimit bool
	limiter     *rate.Limiter
}

type ClientConfig struct {
	Hub  *Hub
	Conn *websocket.Conn
	//send chan cap
	SendCap int
	//Token
	Token string
	//current symbol
	SendRate int
}

func NewClient(config *ClientConfig) *Client {
	c := new(Client)
	if config != nil {
		c.hub = config.Hub
		c.conn = config.Conn
		c.send = make(chan []byte, config.SendCap)
		c.limiter = rate.NewLimiter(rate.Every(1*time.Second), config.SendRate)
	}
	return c
}

func (c *Client) IsAllow() bool {
	return c.limiter.Allow()
}

func (c *Client) CanWrite() bool {
	return c.isCanWrite
	//return c.isCanWrite && len(c.send) < c.sendCap
}

func (c *Client) SendMsg(data []byte) {
	c.send <- data
}

func (c *Client) Register2Room(name string) {
	c.hub.registerClient2Room(name, c)
}

func (c *Client) UnRegisterFromRoom(name string) {
	c.hub.unRegisterFromRoom(name, c)
}

func (c *Client) RegisterUserClient() {
	log.Infof("begin reg user,userid:%v", c.UserId)
	c.hub.clientRegisterUserInfo(c)
}

func (c *Client) SetRecHeart() {
	c.conn.SetReadDeadline(time.Now().Add(pongWait))
}

// readPump pumps messages from the websocket connection to the hub.
//
// The application runs readPump in a per-connection goroutine. The application
// ensures that there is at most one reader on a connection by executing all
// reads from this goroutine.
func (c *Client) readPump() {
	defer func() {
		c.hub.unregister <- c
		c.conn.Close()
	}()
	c.conn.SetReadLimit(maxMessageSize)
	c.conn.SetReadDeadline(time.Now().Add(pongWait))
	c.conn.SetPongHandler(func(string) error {
		c.conn.SetReadDeadline(time.Now().Add(pongWait))
		return nil
	})
	for {
		_, message, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Errorf("error: %v", err)
				//fmt.Printf("error:%v\n", err)
			}
			break
		}
		//message = bytes.TrimSpace(bytes.Replace(message, newline, space, -1))
		if !c.isRateLimit {
			c.hub.handleMessage(c, message)
			continue
		}
		if c.IsAllow() {
			c.hub.handleMessage(c, message)
		} else {
			c.sendRateLimitMsg()
			time.Sleep(2 * time.Second)
			break
		}

	}
}

func (c *Client) sendRateLimitMsg() {
	log.Warnf("client:%p request fast", c)
	msg := WMessagePack{}
	msg.Action = define.WsActionNotify
	msg.Ts = time.Now().Unix()
	msg.Code = Fail
	msg.ErrMsg = ErrRateLimit

	c.send <- msg.Marsha()
}

func (c *Client) resetSendChan() {
	c.isCanWrite = false
	c.send = make(chan []byte, c.sendCap)
	c.isCanWrite = true
}

// writePump pumps messages from the hub to the websocket connection.
//
// A goroutine running writePump is started for each connection. The
// application ensures that there is at most one writer to a connection by
// executing all writes from this goroutine.
func (c *Client) writePump() {
	ticker := time.NewTicker(pingPeriod)
	defer func() {
		ticker.Stop()
		c.conn.Close()
	}()
	for {
		if len(c.send) == cap(c.send) {
			log.Warnf("发送队列满了,重置队列,client:%p,len:%v", c, len(c.send))
			c.resetSendChan()
		}
		select {
		case message, ok := <-c.send:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if !ok {
				// The hub closed the channel.
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			if c.hub.isSendCompress {
				message = zipData(message)
			}
			w.Write(message)

			if err := w.Close(); err != nil {
				return
			}
		case <-ticker.C:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			//c.conn.WriteMessage(websocket.TextMessage, []byte("ping"))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

func zipData(d []byte) []byte {
	d, err := zip.ZipByte(d)
	if err != nil {
		log.Infof("压缩数据出错,%v", err)
	}
	return d
}

// serveWs handles websocket requests from the peer.
func ServeWs(hub *Hub, w http.ResponseWriter, r *http.Request) {
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Infof("server ws err:%v", err)
		return
	}
	client := NewClient(&ClientConfig{Hub: hub, Conn: conn, SendCap: 256, SendRate: 10})
	//client := &Client{hub: hub, conn: conn, send: make(chan []byte, 256), sendCap: 256}
	client.hub.register <- client
	log.Infof("ws connect:%p", client)
	//fmt.Printf("connect:%p\n", client)

	// Allow collection of memory referenced by the caller by doing all work in
	// new goroutines.
	go client.writePump()
	go client.readPump()
}
