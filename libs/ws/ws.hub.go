// Copyright 2013 The Gorilla WebSocket Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

package ws

import (
	"errors"
	"spot/libs/define"
	"spot/libs/log"
	"time"
)

// Hu<PERSON> maintains the set of active clients and broadcasts messages to the
// clients.
type Hub struct {
	//roomManager
	rooms map[string]map[*Client]bool

	// Registered clients
	clients map[*Client]bool

	//user clients
	users map[int64]map[*Client]bool

	// Inbound messages from the clients.
	broadcast chan []byte

	// Register requests from the clients.
	register chan *Client

	// Unregister requests from clients.
	unregister chan *Client

	// register user
	registerUser chan *Client

	//init room chan
	opRoom chan []string

	//client register/unRegister to/from room
	clientOpRoom chan *ClientRoom

	//Is the data compression
	isSendCompress bool
}

func NewHub() *Hub {
	return &Hub{
		rooms:          NewRooms(),
		broadcast:      make(chan []byte),
		register:       make(chan *Client),
		registerUser:   make(chan *Client),
		clientOpRoom:   make(chan *ClientRoom),
		unregister:     make(chan *Client),
		opRoom:         make(chan []string),
		clients:        make(map[*Client]bool),
		users:          make(map[int64]map[*Client]bool),
		isSendCompress: false,
	}
}

type Rooms map[string]map[*Client]bool

func NewRooms() Rooms {
	return make(Rooms, 0)
}

//hub 初始化注册房间
func (h *Hub) AddNewRooms(topics []string) {
	log.Infof("初始化房间:%+v", topics)
	if len(topics) == 0 {
		return
	}
	//fmt.Println(topics)
	h.opRoom <- topics
}

type ClientRoom struct {
	*Client
	Name string
	Op   int //操作类型,0-注册,1-退订
}

//向hub注册client到rooms
func (h *Hub) registerClient2Room(topic string, client *Client) {
	h.clientOpRoom <- &ClientRoom{client, topic, define.RoomOpRegister}
}

func (h *Hub) unRegisterFromRoom(topic string, client *Client) {
	h.clientOpRoom <- &ClientRoom{client, topic, define.RoomOpUnRegister}
}

func (h *Hub) registerCliRoom(topic string, client *Client) {
	h.clientOpRoom <- &ClientRoom{client, topic, define.RoomOpRegister}
}

func (h *Hub) Run() {
	monitor := time.Tick(5 * time.Minute)
	for {
		select {
		case client := <-h.register:
			client.isCanWrite = true
			h.clients[client] = true
		case client := <-h.unregister:
			h.clientUnregister(client)
		case client := <-h.registerUser:
			h.clientRegisterUserInfo(client)
		case client := <-h.clientOpRoom:
			h.clientOperateWithRoom(client)
		case message := <-h.broadcast:
			h.broadcastMessage(message)
		case topics := <-h.opRoom:
			for _, v := range topics {
				_, ok := h.rooms[v]
				if !ok {
					h.rooms[v] = make(map[*Client]bool, 0)
				}
			}
		case <-monitor:
			h.monitoring()
		}

	}
}

func (h *Hub) broadcastMessage(message []byte) {
	for client := range h.clients {
		select {
		case client.send <- message:
		default:
			close(client.send)
			delete(h.clients, client)
		}
	}
}

//user operate room
func (h *Hub) clientOperateWithRoom(op *ClientRoom) {
	v, ok := h.rooms[op.Name]
	if !ok {
		log.Errorf("无效room,name:%v", op.Name)
		return
	}
	if op.Op == define.RoomOpRegister {
		v[op.Client] = true
		h.rooms[op.Name] = v
		return
	}
	delete(h.rooms[op.Name], op.Client)
}

func (h *Hub) clientRegisterUserInfo(client *Client) {
	_, ok := h.users[client.UserId]
	if !ok {
		h.users[client.UserId] = make(map[*Client]bool, 0)
	}
	h.users[client.UserId][client] = true
	log.Infof("husers:%+v", h.users)
}

func (h *Hub) clientUnregister(client *Client) {
	client.isCanWrite = false
	if _, ok := h.clients[client]; ok {
		delete(h.clients, client)
		for _, v := range h.users {
			delete(v, client)
		}
		for _, v := range h.rooms {
			delete(v, client)
		}
	}
	close(client.send)
}

func (h *Hub) SendBroadcast(msg WMessagePack) {
	if h == nil {
		return
	}
	h.broadcast <- msg.Marsha()
}

func (h *Hub) SendUser(userId int64, msg WMessagePack) {
	if h == nil {
		return
	}
	cMap := h.users[userId]
	log.Debugf("users:%v", h.users)
	for c := range cMap {
		if c == nil || !c.CanWrite() {
			continue
		}
		c.send <- msg.Marsha()
	}
}

func (h *Hub) SendRoomMsg(roomId string, msg WMessagePack) {
	if h == nil {
		return
	}
	msg.Action = "notify"
	//log.Debugf("收到roomId:%v消息,clientSize:%v", roomId, len(h.rooms[roomId]))
	for c := range h.rooms[roomId] {
		//log.Errorf("是否可写:%v",!c.isCanWrite)
		if c == nil || !c.CanWrite() {
			log.Errorf("连接为空,不可写,退出...")
			continue
		}
		data := msg.Marsha()
		//log.Errorf("向客户端%v发送数据...",c)
		c.send <- data
	}
}

func (h *Hub) AddMsgHandler(wsPro string, f MsgHandleFunc) {
	DefaultHandleMap[wsPro] = f
}

func (h *Hub) handleMessage(c *Client, msg []byte) error {
	//fmt.Println(string(msg))
	log.Debugf("handleMessage:%v", string(msg))
	var wsPackage WMessageRequest
	wsPackage, err := UnMarshaWsMsg(msg)
	if err != nil {
		log.Infof("not support data type,%v", err)
		return err
	}
	//判断token是否有效
	topic := wsPackage.Topic
	//处理先听的消息
	f, ok := DefaultHandleMap[topic]
	if !ok {
		if wsPackage.Action == define.WsActionSub {
			c.Register2Room(topic)
		} else if wsPackage.Action == define.WsActionUnSub {
			c.UnRegisterFromRoom(topic)
		}
		return errors.New("lack msg handler")
	}
	f(c, wsPackage)
	//DefaultHandleMap[wsPackage.Pro](c, wsPackage.Data)
	return nil
}

func (h *Hub) monitoring() {
	if h == nil {
		return
	}
	log.Infof("[WS Monitor] monitoring,room size:%v", len(h.rooms))
	log.Infof("[WS Monitor] monitoring,user size:%v", len(h.users))
	log.Infof("[WS Monitor] monitoring,user:%+v", h.users)
	for topic, v := range h.rooms {
		log.Infof("[WS Monitor] room:%v,client size:%v", topic, len(v))
		//for k := range v {
		//	log.Infof("room-name:%v,c:%v", topic, k)
		//}
	}
}
