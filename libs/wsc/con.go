package wsc

import (
	"context"
	"github.com/gorilla/websocket"
	"go.uber.org/atomic"
	"go.uber.org/zap"
	"spot/libs/log"
	"time"
)

type Message struct {
	Type int
	Msg  []byte
	Err  error
}

type ClientManager struct {
	send    chan []byte
	conn    *websocket.Conn
	wsPoint string
	isAlive *atomic.Bool
	errChan chan error
	timeout time.Duration

	msgHandler   func(Message)
	startHandler func()
}

func New(wsPoint string, msgHandler func(Message), startHandler func()) *ClientManager {
	return &ClientManager{
		send:         make(chan []byte, 1),
		wsPoint:      wsPoint,
		isAlive:      atomic.NewBool(false),
		err<PERSON>han:      make(chan error),
		timeout:      2 * time.Second,
		startHandler: startHandler,
		msgHandler:   msgHandler,
	}
}

func (cm *ClientManager) SetMsgHandler(msgHandler func(Message)) {
	cm.msgHandler = msgHandler
}

func (cm *ClientManager) Start() {
	go func() {
		var (
			ctx    context.Context
			cancel context.CancelFunc
		)
		for {
			time.Sleep(cm.timeout)
			if cm.isAlive.Load() {
				continue
			}
			if cancel != nil {
				cancel()
			}
			ctx, cancel = context.WithCancel(context.Background())

			if cm.dial() == nil {
				cm.dealReceive(ctx)
				cm.startHandler()
			}
		}
	}()
}

func (cm *ClientManager) dial() error {
	log.Info("开始连接websocket", zap.String("address", cm.wsPoint))
	var err error
	cm.conn, _, err = websocket.DefaultDialer.Dial(cm.wsPoint, nil)
	if err != nil {
		log.Error("连接websocket 失败", zap.Error(err), zap.Any("address", cm.wsPoint))
		return err
	}
	cm.isAlive.Store(true)
	log.Info("连接成功", zap.String("address", cm.wsPoint))
	return nil
}

//func (cm *ClientManager) dealSend() {
//	go func() {
//		for msg := range cm.send {
//			if cm.isAlive {
//				err := cm.conn.WriteMessage(websocket.TextMessage, msg)
//				if err != nil {
//					log.Error("向websocket服务器发送消息失败", zap.Error(err))
//					continue
//				}
//			}
//		}
//	}()
//}

func (cm *ClientManager) dealReceive(ctx context.Context) {
	go func() {
		msgChan := cm.ReadMessage(cm.conn)
		for {
			if cm == nil || cm.conn == nil {
				return
			}
			if !cm.isAlive.Load() {
				time.Sleep(cm.timeout)
				continue
			}
			select {
			case msg := <-cm.send:
				if cm.isAlive.Load() {
					err := cm.conn.WriteMessage(websocket.TextMessage, msg)
					if err != nil {
						log.Error("向websocket服务器发送消息失败", zap.Error(err))
						continue
					}
				}
			case msg := <-msgChan:
				log.Debug("ws receive data", zap.Any("data", msg))
				if cm.msgHandler != nil {
					cm.msgHandler(msg)
				}
				if msg.Err != nil {
					log.Error("receive ws error", zap.Error(msg.Err))
					cm.isAlive.Store(false)
					return
				}
			case <-ctx.Done():
				return
			}
		}
	}()
}

func (cm *ClientManager) Send(ctx context.Context, msg []byte) {
	select {
	case <-ctx.Done():
		return
	case cm.send <- msg:
		return
	}
}

func (cm *ClientManager) ReadMessage(conn *websocket.Conn) <-chan Message {
	if cm.isAlive.Load() {
		conn.SetPingHandler(func(appData string) error {
			conn.WriteMessage(websocket.PongMessage, nil)
			return nil
		})
	}
	var msg Message
	ch := make(chan Message)
	go func() {
		for {
			if conn != nil && cm.isAlive.Load() {
				_ = conn.SetReadDeadline(time.Now().Add(time.Minute))
				msg.Type, msg.Msg, msg.Err = conn.ReadMessage()
				ch <- msg
				if msg.Err != nil {
					close(ch)
					return
				}
			}
		}
	}()
	return ch
}
