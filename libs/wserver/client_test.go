/*
@Time : 2019-12-17 19:06
<AUTHOR> mocha
@File : client_test
*/
package ws

import (
	"encoding/json"
	"fmt"
	"github.com/gorilla/websocket"
	"io/ioutil"
	"testing"
	"time"
)

func TestClient(te *testing.T) {
	//w, rsp, err := websocket.DefaultDialer.Dial("ws://47.241.18.141:443/pro/v1/ws", nil)
	w, rsp, err := websocket.DefaultDialer.Dial("ws://127.0.0.1:8080/ws", nil)
	//w, rsp, err := websocket.DefaultDialer.Dial("ws://127.0.0.1:8080/ws", nil)
	if err != nil {
		fmt.Println(err)
	}
	b, err := ioutil.ReadAll(rsp.Body)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(string(b))

	go func() {
		for {
			w.<PERSON>(func(appData string) error {
				//te.Log(appData)
				w.WriteMessage(websocket.PongMessage, nil)
				return nil
			})
			t, b, err := w.ReadMessage()
			//d, _ := zip.UnzipByte(b)
			te.Log(t, string(b), err)
		}
	}()

	go func() {
		t := time.Tick(200 * time.Millisecond)
		for range t {
			msg := WMessageRequest{Action: "heart", Topic: "heart"}
			d, _ := json.Marshal(msg)
			fmt.Println(string(d))
			w.WriteMessage(websocket.TextMessage, d)
		}
		//a := model.AuthArg{Token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************.BMg8cJIsYNxXK3bUAiMPQV163XJlWXI6OjSgD2z7plc", AppId: 0, Ts: 1585113074, Sign: "4be4862287a11d84c16698d7c49703d4ecee26e22896d65c94ffbee6a793f427"}
		//b, _ := json.Marshal(a)
		//msg := WMessageRequest{Action: "auth", Topic: "auth", Data: b}
		//d, _ := json.Marshal(msg)
		//w.WriteMessage(websocket.TextMessage, d)
		//t := proto.SubSymbolArg{Symbol: ""}
		////RE:
		//t1, _ := json.Marshal(t)
		//sub := WMessageRequest{Action: "sub", Topic: "market.symbol.switch", Data: t1}
		//t2, _ := json.Marshal(sub)
		//w.WriteMessage(websocket.TextMessage, t2)
		//
		//sub2 := WMessageRequest{Action: "sub", Topic: "symbols.market", Data: t1}
		//t3, _ := json.Marshal(sub2)
		//w.WriteMessage(websocket.TextMessage, t3)

		//time.Sleep(1 * time.Minute)
		//t = model.SubSymbolArg{ContractCode: "eth/usdt"}
		//goto RE
	}()
	select {}

}
