/*
@Time : 2019-06-27 10:21
<AUTHOR> mocha
@File : msg
*/
package ws

import (
	"encoding/json"
	"errors"
	"time"
)

const _wsWorkId = 1001

var (
	ErrParam = errors.New("invalid params")
)

const (
	Success             = 0
	Fail                = -1
	ErrNotSupportSymbol = "notSupportContractCode"
	ErrInvalidAction    = "invalid action"
	ErrActionNotSupport = "not support action"
	ErrParamsString     = "invalid params"
	ErrRateLimit        = "request so fast"
	ErrDuplicateSwitch  = "repeat operation"
)

type MsgHandleFunc func(c *Client, message WMessageRequest)

var DefaultHandleMap = make(map[string]MsgHandleFunc, 0)

type WMessageRequest struct {
	Action string          `json:"action"` //动作,[req,sub]
	Topic  string          `json:"topic"`
	Data   json.RawMessage `json:"data"`
}

type WMessagePack struct {
	WMsgId int64           `json:"msg_id"`
	Action string          `json:"action"` //动作,[req,sub]
	Topic  string          `json:"topic"`
	ErrMsg string          `json:"err_msg,omitempty"`
	Code   int             `json:"code"`
	Ts     int64           `json:"ts,omitempty"`
	Data   json.RawMessage `json:"data,omitempty"`
}

var Ids *IdWorker

func init() {
	Ids, _ = NewIdWorker(_wsWorkId)
	DefaultHandleMap["heart"] = handleHeart
}

func getNextId() int64 {
R:
	id, err := Ids.NextId()
	if err != nil {
		goto R
	}
	return id
}

func (msg WMessagePack) Marsha() []byte {
	msg.WMsgId = getNextId()
	b, _ := json.Marshal(msg)
	return b
}

func UnMarshaWsMsg(b []byte) (ws WMessageRequest, err error) {
	err = json.Unmarshal(b, &ws)
	return
}

func handleHeart(c *Client, message WMessageRequest) {
	msg := WMessagePack{}
	msg.Action = message.Action
	msg.Topic = message.Topic
	msg.Ts = time.Now().Unix()
	defer func() {
		c.SendMsg(msg.Marsha())
	}()
	c.SetRecHeart()
}
