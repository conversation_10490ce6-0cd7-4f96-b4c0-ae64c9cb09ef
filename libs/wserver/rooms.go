package ws

import (
	"context"
	"fmt"
	"runtime"
	"runtime/debug"
	"spot/libs/container/safemap"
	"spot/libs/log"
	"time"
)

var null = struct {
}{}

type Room struct {
	name    string
	clients *safemap.Map
}

func NewRoom(name string) *Room {
	return &Room{name: name, clients: safemap.New()}
}

func (r *Room) AddClient(c *Client) {
	r.clients.Set(c, null)
}

func (r *Room) RemoveClient(c *Client) {
	r.clients.Remove(c)
}

func (r *Room) BroadCast(msg []byte) {
	r.clients.Each(func(c interface{}, _ interface{}) {
		client, ok := c.(*Client)
		if ok {
			ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
			defer cancel()

			select {
			case <-ctx.Done():
				return
			case client.send <- msg:
				return
			}
		}

	})
}

type RoomManager struct {
	rooms *safemap.Map
}

func NewRoomManager() *RoomManager {
	return &RoomManager{rooms: safemap.New()}
}

func (r *RoomManager) AddRoom(name string) {
	if r.rooms.IsExist(name) {
		return
	}
	r.rooms.Set(name, NewRoom(name))
}

func (r *RoomManager) RemoveRoom(name string) {
	if r.rooms.IsExist(name) {
		return
	}
	r.rooms.Set(name, NewRoom(name))
}

func (r *RoomManager) AddClient(name string, c *Client) {
	room, ok := r.rooms.Get(name)
	if !ok {
		r.AddRoom(name)
		r.AddClient(name, c)
		return
	}

	if ro, ok := room.(*Room); ok {
		ro.AddClient(c)
	}

}

func (r *RoomManager) RemoveRoomClient(name string, c *Client) {
	room, ok := r.rooms.Get(name)
	if !ok {
		return
	}
	if ro, ok := room.(*Room); ok {
		ro.RemoveClient(c)
	}
}

func (r *RoomManager) RemoveClient(c *Client) {
	r.rooms.Each(func(_ interface{}, i interface{}) {
		room, ok := i.(*Room)
		if ok {
			room.RemoveClient(c)
		}
	})
}

func (r *RoomManager) GetAllRooms() []string {
	var list []string
	r.rooms.Each(func(i interface{}, _ interface{}) {
		room, ok := i.(string)
		if ok {
			list = append(list, room)
		}
	})
	return list
}

func (r *RoomManager) SendMsgRoom(name string, msg []byte) {
	i, ok := r.rooms.Get(name)
	if !ok {
		return
	}
	if room, ok := i.(*Room); ok {
		room.BroadCast(msg)
	}
}

func (r *RoomManager) Size() int {
	return r.rooms.Size()
}

type UserClients struct {
	userId  int64
	clients *safemap.Map
}

func NewUserClients(userId int64) *UserClients {
	return &UserClients{userId: userId, clients: safemap.New()}
}

func (r *UserClients) AddClient(c *Client) {
	r.clients.Set(c, null)
}

func (r *UserClients) RemoveClient(c *Client) {
	r.clients.Remove(c)
}

func (r *UserClients) BroadCast(msg []byte) {
	r.clients.Each(func(c interface{}, _ interface{}) {
		client, ok := c.(*Client)
		if ok && client != nil && !client.isInvalid {
			ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
			defer cancel()

			select {
			case <-ctx.Done():
				return
			case client.send <- msg:
				return
			}
		}
		if client == nil {
			return
		}
	})
}

type UserManager struct {
	users *safemap.Map
}

func NewUserManager() *UserManager {
	return &UserManager{users: safemap.New()}
}

func (r *UserManager) AddUserClient(c *Client) {
	var clients *UserClients
	cInterface, ok := r.users.Get(c.UserId)
	if !ok {
		clients = NewUserClients(c.UserId)
		clients.AddClient(c)
		r.users.Set(c.UserId, clients)
	} else {
		clients, ok = cInterface.(*UserClients)
		if !ok {
			fmt.Printf("cInterface is not *UserClients \n")
			return
		}
		clients.AddClient(c)
	}
}

func (r *UserManager) RemoveUser(userId int64) {
	if !r.users.IsExist(userId) {
		return
	}
	r.users.Remove(userId)
}

func (r *UserManager) RemoveClient(c *Client) {
	//查询用户
	clients, ok := r.users.Get(c.UserId)
	if !ok {
		return
	}
	if cs, ok := clients.(*UserClients); ok {
		cs.RemoveClient(c)
	}
}

func (r *UserManager) Size() int {
	return r.users.Size()
}

func (r *UserManager) SendMsgUser(userId int64, msg []byte) {
	log.Infof("发送用户消息，userID；%v,msg:%+v", string(msg))
	clients, ok := r.users.Get(userId)
	if !ok {
		log.Infof("没有查到该用户，用户不在线，uid；%v,msg:%+v", userId, string(msg))
		return
	}
	defer func() {
		if e := recover(); e != nil {
			_, file, line, _ := runtime.Caller(3)
			log.Errorf("SendMsgUser revover,file:%v,line:%v,%v", file, line, e)
			log.Errorf("market stack err:%v", string(debug.Stack()))
		}
	}()

	if cs, ok := clients.(*UserClients); ok {
		cs.BroadCast(msg)
	}
}

func (r *UserManager) BroadCastMsg(msg []byte) {
	r.users.Each(func(_ interface{}, c interface{}) {
		if clients, ok := c.(*UserClients); ok {
			clients.BroadCast(msg)
		}
	})
}

func (r *UserManager) IsUserOnline(userId int64) bool {
	_, ok := r.users.Get(userId)
	return ok
}
