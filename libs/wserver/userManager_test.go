package ws

import (
	"testing"
	"time"
)

var _testUm = NewUserManager()

func TestUser(t *testing.T) {
	c1, c2, c3 := NewClient(&ClientConfig{userId: 1}), NewClient(&ClientConfig{userId: 1}), NewClient(&ClientConfig{userId: 1})
	go _testUm.AddUserClient(c1)
	go _testUm.AddUserClient(c2)
	go _testUm.AddUserClient(c3)
	time.Sleep(3 * time.Second)
	_testUm.users.Each(func(i interface{}, i2 interface{}) {
		t.Logf("k:%+v,v:%+v", i, i2)
		c, ok := i2.(*UserClients)
		if !ok {
			return
		}
		c.clients.Each(func(i2 interface{}, i interface{}) {
			t.Logf("client:%+v", i2)
		})
	})

	//t.Logf("remove:%+v",c2)
	//_testUm.RemoveClient(c2)
	//_testUm.users.Each(func(i interface{}, i2 interface{}) {
	//	t.Logf("k:%+v,v:%+v",i,i2)
	//	c,ok:=i2.(*UserClients)
	//	if !ok{
	//		return
	//	}
	//	c.clients.Each(func(i2 interface{}, i interface{}) {
	//		t.Logf("client,id:%p:%+v",i2,i2)
	//	})
	//})

	//t.Log("----------")
	//_testUm.RemoveUser(1)
	//_testUm.users.Each(func(i interface{}, i2 interface{}) {
	//	t.Logf("k:%+v,v:%+v",i,i2)
	//	c,ok:=i2.(*UserClients)
	//	if !ok{
	//		return
	//	}
	//	c.clients.Each(func(i2 interface{}, i interface{}) {
	//		t.Logf("client,id:%p:%+v",i2,i2)
	//	})
	//})

	//t.Logf("r:%p,%p,%p",c1,c2,c3)
	//t.Logf("r:%v,%v,%v",c1,c2,c3)
	//t.Logf("r:%+v,%+v,%+v",c1,c2,c3)
}
