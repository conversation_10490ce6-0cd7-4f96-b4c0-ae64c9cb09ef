package xerror

import (
	"fmt"
	"runtime"

	"go.uber.org/zap"
	"spot/libs/log"
)

func CheckErr(err *error, reqID int64, f func() error) {
	if *err != nil {
		// 之前的调用已经出错,跳过后续的操作,返回最初的错误
		return
	}
	*err = f()
	if *err != nil {
		_, file, line, _ := runtime.Caller(1)
		log.Error("CheckErr error not empty", zap.Int64("reqID", reqID), zap.String("file", fmt.Sprintf("%s:%d", file, line)), zap.Error(*err))
	}
}
