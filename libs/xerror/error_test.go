package xerror

import (
	"errors"
	"testing"
	"time"

	"spot/libs/log"
)

func TestCheckErr(t *testing.T) {
	// 初始化日志
	log.InitLogger("test_log", "debug", false)

	success := func() error {
		t.Log("success")
		return nil
	}

	failed := func() error {
		t.Log("error")
		return errors.New("error")
	}

	reqID := time.Now().Unix()
	var err error
	CheckErr(&err, reqID, func() error {
		return success()
	})

	CheckErr(&err, reqID, func() error {
		return failed()
	})

	CheckErr(&err, reqID, func() error {
		return success()
	})

	t.Log(err)
}
