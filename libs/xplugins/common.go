package xplugins

import (
	"context"
	"strconv"
	"sync"
	"time"
)

const (
	WrapRequestKey = "request_payload_key"

	_spanKeyRequestID    = "request_id"
	_spanKeyRequestTime  = "request_time"
	_spanKeyRequestNode  = "request_node"
	_spanKeyResponseNode = "response_node"

	_timeFormatMillisecond = "2006-01-02 15:04:05.999" // 带毫秒日期格式化
)

var _nodeName string

func Init(nodeName string) {
	_nodeName = nodeName
}

type SpanValues struct {
	m  map[string]string
	mu sync.RWMutex
}

func NewSpan() *SpanValues {
	v := &SpanValues{
		m: make(map[string]string),
	}
	v.SetRequestTime()
	v.SetRequestNode(_nodeName)

	return v
}

func (sv *SpanValues) load(key string) string {
	if sv == nil {
		return ""
	}

	sv.mu.RLock()
	defer sv.mu.RUnlock()

	return sv.m[key]
}

func (sv *SpanValues) store(key, value string) {
	if sv == nil {
		return
	}

	sv.mu.Lock()
	defer sv.mu.Unlock()

	if sv.m == nil {
		sv.m = make(map[string]string)
	}
	sv.m[key] = value
}

func (*SpanValues) int64(s string) int64 {
	n, _ := strconv.ParseInt(s, 10, 64)
	return n
}

func (sv *SpanValues) SetRequestID(id int64) *SpanValues {
	sv.store(_spanKeyRequestID, strconv.FormatInt(id, 10))
	return sv
}

func (sv *SpanValues) GetRequestID() int64 {
	return sv.int64(sv.load(_spanKeyRequestID))
}

func (sv *SpanValues) SetRequestTime() *SpanValues {
	sv.store(_spanKeyRequestTime, time.Now().Format(_timeFormatMillisecond))
	return sv
}

func (sv *SpanValues) GetRequestTime() string {
	return sv.load(_spanKeyRequestTime)
}

func (sv *SpanValues) SetRequestNode(node string) *SpanValues {
	sv.store(_spanKeyRequestNode, node)
	return sv
}

func (sv *SpanValues) GetRequestNode() string {
	return sv.load(_spanKeyRequestNode)
}

func (sv *SpanValues) SetResponseNode(node string) *SpanValues {
	sv.store(_spanKeyResponseNode, node)
	return sv
}

func (sv *SpanValues) GetResponseNode() string {
	return sv.load(_spanKeyResponseNode)
}

func (sv *SpanValues) GetValues() map[string]string {
	if sv == nil {
		return nil
	}

	sv.mu.RLock()
	defer sv.mu.RUnlock()

	m := make(map[string]string, len(sv.m))
	for k, v := range sv.m {
		m[k] = v
	}
	return m
}

func convertSpanValues(m map[string]string) *SpanValues {
	span := NewSpan()
	if m == nil {
		return span
	}

	id, _ := strconv.ParseInt(m[_spanKeyRequestID], 10, 64)
	return span.SetRequestID(id).SetRequestNode(m[_spanKeyRequestNode]).SetResponseNode(m[_spanKeyResponseNode])
}

func GetSpanValues(ctx context.Context) *SpanValues {
	v, ok := ctx.Value(WrapRequestKey).(*SpanValues)
	if !ok {
		return NewSpan()
	}
	return v
}
