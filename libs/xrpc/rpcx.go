package xrpc

import (
	"context"
	"errors"
	"net/url"
	"reflect"
	"strings"
	"time"

	"github.com/rcrowley/go-metrics"
	"github.com/smallnest/rpcx/client"
	"github.com/smallnest/rpcx/protocol"
	"github.com/smallnest/rpcx/server"
	"github.com/smallnest/rpcx/serverplugin"
	"github.com/valyala/fastrand"
	"spot/libs/xplugins"
)

/*** 提供rpcx 服务创建及客户端创建基本封装 ***/

type ServerOption struct {
	ServiceName string      //*服务名
	NodeName    string      //*节点名称
	Rev         interface{} //*recvr 接收指针

	Address   string //* rpc地址
	BasePath  string //* 挂载位置
	Discovery string //* 注册服务地址

	UpdateInterval time.Duration //注册发现间隔时间(最小10秒,最大24小时)

	Meta     string                                                               //元数据
	AuthFunc func(ctx context.Context, req *protocol.Message, token string) error //校验权限方法

	Extra interface{} //附加数据

	Limiter *Limiter //频率限制
}

type Limiter struct {
	duration time.Duration
	capacity int
}

type Server struct {
	*server.Server
	option *ServerOption
}

func (s *Server) GetExtra() interface{} {
	return s.option.Extra
}

func NewXServer(so *ServerOption) (*Server, error) {
	if so == nil || so.ServiceName == "" || so.NodeName == "" || so.Rev == nil || so.Address == "" || so.BasePath == "" || so.Discovery == "" {
		return nil, errors.New("invalid parameters")
	}
	xplugins.Init(so.NodeName)

	s := &Server{option: so}
	s.Server = server.NewServer()
	s.Plugins.Add(xplugins.ServerPlugin{})

	if so.AuthFunc != nil {
		s.AuthFunc = so.AuthFunc
	}
	return s, nil
}

func (s *Server) registerRateLimiter() {
	if s.option.Limiter != nil {
		limit := serverplugin.NewReqRateLimitingPlugin(time.Second, 1, true)
		s.Plugins.Add(limit)
	}
}

func (s *Server) registerDiscover() (err error) {
	r := &serverplugin.ConsulRegisterPlugin{
		ServiceAddress: "tcp@" + s.option.Address,
		ConsulServers:  []string{s.option.Discovery},
		BasePath:       s.option.BasePath,
		Metrics:        metrics.NewRegistry(),
	}
	if s.option.UpdateInterval == 0 {
		r.UpdateInterval = 10 * time.Second
	} else {
		r.UpdateInterval = s.option.UpdateInterval
	}
	err = r.Start()
	if err != nil {
		return
	}
	s.Plugins.Add(r)
	return nil
}

func (s *Server) RegisterOnShutdown(f func()) {
	s.Server.RegisterOnShutdown(func(s *server.Server) {
		f()
	})
}

func (s *Server) Shutdown(ctx context.Context) error {
	return s.Server.Shutdown(ctx)
}

func (s *Server) Start() (err error) {
	s.registerRateLimiter()
	err = s.registerDiscover()
	if err != nil {
		return
	}
	s.RegisterName(s.option.ServiceName, s.option.Rev, s.option.Meta)
	s.Serve("tcp", s.option.Address)
	return nil
}

/*** 客户端请求 ***/

type ClientOption struct {
	BasePath    string //*
	Discovery   string //* 注册发现地址
	ServiceName string //* 服务名称
	NodeName    string //* 节点名称
	Option      *client.Option
	FailMod     client.FailMode
	SelectMode  client.SelectMode
	Auth        string //权限token
	PoolSize    int    //客户端池个数
}

type Client struct {
	option ClientOption
	pool   *client.XClientPool
	c      client.XClient
}

// NewXClient 使用池方式创建客户端
func NewXClient(co ClientOption) (c *Client) {
	xplugins.Init(co.NodeName)
	d, _ := client.NewConsulDiscovery(co.BasePath, co.ServiceName, []string{co.Discovery}, nil)
	if co.Option == nil {
		co.Option = &client.DefaultOption
	}
	option := co.Option
	if option == nil {
		option = &client.DefaultOption
		option.Heartbeat = true
		option.HeartbeatInterval = 5 * time.Second
	}
	if co.PoolSize == 0 {
		co.PoolSize = 1
	}
	pool := client.NewXClientPool(co.PoolSize, co.ServiceName, co.FailMod, co.SelectMode, d, *option)
	pool.Auth(co.Auth)
	c = &Client{option: co, pool: pool}
	return
}

// NewXOneClient 创建单一客户端
func NewXOneClient(co ClientOption) (c *Client) {
	xplugins.Init(co.NodeName)
	d, _ := client.NewConsulDiscovery(co.BasePath, co.ServiceName, []string{co.Discovery}, nil)
	if co.Option == nil {
		co.Option = &client.DefaultOption
	}
	option := co.Option
	if option == nil {
		option = &client.DefaultOption
		option.Heartbeat = true
		option.HeartbeatInterval = 5 * time.Second
	}
	xClient := client.NewXClient(co.ServiceName, co.FailMod, co.SelectMode, d, *option)
	xClient.Auth(co.Auth)
	c = &Client{option: co, c: xClient}
	return
}

// Get 获取rpx客户端连接
func (c *Client) Get() (xClient client.XClient) {
	defer func() {
		pc := client.NewPluginContainer()
		pc.Add(&xplugins.ClientPlugin{})
		xClient.SetPlugins(pc)
	}()
	if c.pool != nil {
		xClient = c.pool.Get()
	} else {
		xClient = c.c
	}
	return
}

func (c *Client) GetForSelector() (xClient client.XClient) {
	defer func() {
		xClient.SetSelector(&SymbolSelector{})
		pc := client.NewPluginContainer()
		pc.Add(&xplugins.ClientPlugin{})
		xClient.SetPlugins(pc)
	}()
	if c.pool != nil {
		xClient = c.pool.Get()
	} else {
		xClient = c.c
	}
	return
}

func (c *Client) Call(ctx context.Context, serviceMethod string, args interface{}, reply interface{}) error {
	return c.Get().Call(ctx, serviceMethod, args, reply)
}

func (c *Client) CallForSymbol(ctx context.Context, serviceMethod string, args interface{}, reply interface{}) error {
	return c.GetForSelector().Call(ctx, serviceMethod, args, reply)
}

func (c *Client) ClosePool() {
	c.pool.Close()
}

func (c *Client) CloseClient() {
	_ = c.c.Close()
}

// NewXClientForSelectSymbol 针对match服务，根据请求参数，获取相应的服务地址
//args参数中包括"Code"字段，否则修改选择器
func NewXClientForSelectSymbol(co ClientOption) (xClient client.XClient, err error) {
	c := NewXOneClient(co).Get()
	c.SetSelector(&SymbolSelector{})
	return
}

type SymbolSelector struct {
	Group map[string][]string
}

func (s *SymbolSelector) GetLabel(args interface{}) string {
	return reflect.ValueOf(args).Elem().FieldByName("Code").Interface().(string)
}

func (s *SymbolSelector) Select(ctx context.Context, servicePath, serviceMethod string, args interface{}) string {
	//fmt.Println("method", serviceMethod)
	//fmt.Println("args", args)
	code := s.GetLabel(args)
	if code == "" {
		return ""
	}

	list, ok := s.Group[code]
	if !ok || len(list) == 0 {
		return ""
	}

	i := fastrand.Uint32n(uint32(len(list)))
	return list[i]
}

func (s *SymbolSelector) UpdateServer(servers map[string]string) {
	s.Group = make(map[string][]string)
	for address, metadata := range servers {
		if v, err := url.ParseQuery(metadata); err == nil {
			symbols := v.Get("symbols")
			list := strings.Split(symbols, ",")
			for _, code := range list {
				ss, _ := s.Group[code]
				ss = append(ss, address)
				s.Group[code] = ss
			}
		}
	}
}
