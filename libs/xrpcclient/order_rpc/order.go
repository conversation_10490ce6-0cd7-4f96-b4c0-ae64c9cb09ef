package order_rpc

import (
	"context"
	"errors"
	"github.com/smallnest/rpcx/client"
	"github.com/smallnest/rpcx/protocol"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/proto"
	"spot/libs/xplugins"
	"spot/libs/xrpc"
	"time"
)

var rpcClient *xrpc.Client
var ErrNotInitRpcClient = errors.New("srv service not init client") //未初始化客户端
const (
	rcTimeOut = 30 * time.Second
)

const (
	_entrustOpen   = "EntrustOpen"
	_entrustCancel = "EntrustCancel"
)

func InitClient(discovery, nodeName string) {
	option := &client.DefaultOption
	option.SerializeType = protocol.JSON
	co := xrpc.ClientOption{
		Discovery:   discovery,
		BasePath:    define.BasePath,
		ServiceName: define.ServerNameOrder,
		NodeName:    nodeName,
		Option:      option,
		Auth:        "",
		PoolSize:    3,
	}
	rpcClient = xrpc.NewXClient(co)
}

//撮合下单
func EntrustOpen(request *proto.ThirdOrderPlaceArg, reply *define.Reply) (err error) {
	if request.RequestId == 0 {
		request.RequestId = database.NextID()
	}
	if rpcClient == nil {
		return ErrNotInitRpcClient
	}
	ctx, cancel := context.WithTimeout(context.Background(), rcTimeOut)
	defer cancel()
	ctx = context.WithValue(ctx, xplugins.WrapRequestKey, xplugins.NewSpan())
	err = rpcClient.Call(ctx, _entrustOpen, request, reply)
	if err != nil {
		e := define.ErrMsgServerNotAvailable
		reply.Ret = e.Code
		reply.Msg = e.LangErrMsg(request.ReqLang)
		return err
	}
	return
}

//撤单
func EntrustCancel(request *proto.ThirdOrderCancelArg, reply *define.Reply) (err error) {
	if request.RequestId == 0 {
		request.RequestId = database.NextID()
	}
	if rpcClient == nil {
		return ErrNotInitRpcClient
	}
	ctx := context.WithValue(context.Background(), xplugins.WrapRequestKey, xplugins.NewSpan())
	err = rpcClient.Call(ctx, _entrustCancel, request, reply)
	//err = rpcClient.CallForSymbol(ctx, _entrustCancel, request, reply)
	if err != nil {
		e := define.ErrMsgServerNotAvailable
		reply.Ret = e.Code
		reply.Msg = e.LangErrMsg(request.ReqLang)
		return err
	}
	return
}
