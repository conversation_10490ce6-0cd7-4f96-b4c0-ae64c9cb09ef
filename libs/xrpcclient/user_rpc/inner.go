/*
	内部接口,用于内部服务调用,获取用户数据
*/

package user_rpc

import (
	"spot/libs/define"
	"spot/libs/proto"
	"spot/libs/xplugins"
)

const (
	RPCGetInnerUserByID        = "GetInnerUserByID"        // 通过用户id获取用户信息
	RPCGetInnerUserLabels      = "GetInnerUserLabels"      // 获取用户所有合约的标签配置
	RPCGetInnerUserLabelByCode = "GetInnerUserLabelByCode" // 获取用户指定合约的标签配置
	RPCAdminUserState          = "AdminUserState"          // 用户状态更新
	RPCGetTopUserByID          = "GetTopUserByID"          // 获取顶级代理用户信息
)

func GetInnerUserByID(reqID, userID int64, forceDB bool) (*proto.InnerUser, error) {
	arg := &proto.InnerUserArg{
		UserID:  userID,
		ForceDB: forceDB,
	}
	var user proto.InnerUser
	reply := define.Reply{Data: &user}

	span := xplugins.NewSpan().SetRequestID(reqID)

	err := cli.Call(RPCGetInnerUserByID, arg, &reply, span)
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func GetInnerUserLabels(reqID, userID int64, forceDB bool) (map[string]proto.InnerUserLabel, error) {
	arg := &proto.InnerUserArg{
		UserID:  userID,
		ForceDB: forceDB,
	}
	labels := make(map[string]proto.InnerUserLabel)
	reply := define.Reply{Data: labels}

	span := xplugins.NewSpan().SetRequestID(reqID)

	err := cli.Call(RPCGetInnerUserLabels, arg, &reply, span)
	if err != nil {
		return nil, err
	}
	return labels, nil
}

func GetInnerUserLabelByCode(reqID, userID int64, contractCode string, forceDB bool) (*proto.InnerUserLabel, error) {
	arg := &proto.InnerUserLabelArg{
		InnerUserArg: proto.InnerUserArg{
			UserID:  userID,
			ForceDB: forceDB,
		},
		ContractCode: contractCode,
	}
	var label proto.InnerUserLabel
	reply := define.Reply{Data: &label}

	span := xplugins.NewSpan().SetRequestID(reqID)

	err := cli.Call(RPCGetInnerUserLabelByCode, arg, &reply, span)
	if err != nil {
		return nil, err
	}
	return &label, nil
}

func AdminUserState(reqID, userID int64) error {
	arg := &proto.UserArg{UserID: userID}
	span := xplugins.NewSpan().SetRequestID(reqID)

	return cli.Call(RPCAdminUserState, arg, &define.Reply{}, span)
}

func GetTopUserByID(reqID, userID int64) (*proto.InnerTopUser, error) {
	arg := &proto.UserArg{UserID: userID}
	var top proto.InnerTopUser
	reply := define.Reply{Data: &top}

	span := xplugins.NewSpan().SetRequestID(reqID)

	err := cli.Call(RPCGetTopUserByID, arg, &reply, span)
	if err != nil {
		return nil, err
	}
	return &top, nil
}
