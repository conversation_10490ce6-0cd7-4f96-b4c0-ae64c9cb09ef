/*
	内部接口,用于内部服务调用,用于操作资产
*/

package wallet_rpc

import (
	"spot/libs/define"
	"spot/libs/proto"
	"spot/libs/xplugins"
)

const (
	RPCAdminWithdraw        = "AdminWithdraw"        // 管理员提币
	RPCAdminWithdrawOperate = "AdminWithdrawOperate" // 管理员提币审核
	RPCBackendCWSBalance    = "BackendCWSBalance"    // 管理员获取当前大钱包资产
	RPCCheckAsset           = "CheckAsset"           // 检查资产账户(不存在则初始化账户)
)

func AdminWithdraw(reqID int64, arg *proto.AdminWithdrawArg) error {
	span := xplugins.NewSpan().SetRequestID(reqID)

	return cli.Call(RPCAdminWithdraw, arg, &define.Reply{}, span)
}

func AdminWithdrawOperate(reqID int64, arg *proto.WithdrawOperateArg) error {
	span := xplugins.NewSpan().SetRequestID(reqID)

	return cli.Call(RPCAdminWithdrawOperate, arg, &define.Reply{}, span)
}

func BackendCWSBalance(reqID int64) (map[string]string, error) {
	bs := make(map[string]string)
	reply := define.Reply{Data: bs}

	span := xplugins.NewSpan().SetRequestID(reqID)

	err := cli.Call(RPCBackendCWSBalance, &define.NoneArg{}, &reply, span)
	if err != nil {
		return nil, err
	}
	return bs, nil
}

func CheckAsset(reqID, userID int64, platformID int, assetType define.AssetType, coins ...string) error {
	bs := make(map[string]string)
	reply := define.Reply{Data: bs}

	span := xplugins.NewSpan().SetRequestID(reqID)

	err := cli.Call(RPCCheckAsset, &proto.CheckAssetArg{
		UserID:     userID,
		PlatformID: platformID,
		AssetType:  assetType,
		CoinNames:  coins,
	}, &reply, span)
	if err != nil {
		return err
	}
	if reply.Ret != define.ErrCodeNone {
		return define.NewReplyErrorByCode(reply.Ret)
	}
	return nil
}
