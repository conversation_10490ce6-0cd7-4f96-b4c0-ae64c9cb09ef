package yidun

import (
	"bytes"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"math/rand"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"
	"unsafe"
)

const (
	yiDunApi = "http://c.dun.163yun.com/api/v2/verify"

	dunVersion   = "v2"
	dunCaptchaID = "c723f3004c53499ab2c484ee9c09c987"
	dunSecretID  = "05584b494d9a6b4defdd8c2f2572eb57"
	dunSecretKey = "0b9b3b66197ec63b9b09f7daabdaa8c5"
	nonceRandNum = 123456789
)

var (
	r = rand.New(rand.NewSource(time.Now().UnixNano()))
)

type YiDun struct {
	secretID  string
	secretKey string
	version   string
}

type DunReply struct {
	Result    bool   `json:"result"` // 二次校验结果 true:校验通过 false:校验失败
	Error     int    `json:"error"`  // 异常代号
	Msg       string `json:"msg"`    // 错误描述信息
	ExtraData string `json:"extraData"`
}

func NewYiDunObject() *YiDun {
	return &YiDun{
		secretID:  dunSecretID,
		secretKey: dunSecretKey,
		version:   dunVersion,
	}
}

// 设置secretID
func (d *YiDun) SetSecretID(secretID string) {
	d.secretID = secretID
}

// 设置secretKey
func (d *YiDun) SetSecretKey(secretKey string) {
	d.secretKey = secretKey
}

// 设置version
func (d *YiDun) SetVersion(version string) {
	d.version = version
}

// 网易易盾行为式验证码验证
// api: http://support.dun.163.com/documents/15588062143475712?docId=69218161355051008
func (d *YiDun) VerifyActionDun(captchaID, validate string, userID ...int64) (DunReply, error) {
	params := make(map[string]string)
	params["version"] = d.version
	params["secretId"] = d.secretID
	params["captchaId"] = captchaID
	params["validate"] = validate
	if len(userID) > 0 {
		params["user"] = computeMD5(strconv.FormatInt(userID[0], 10))
	} else {
		params["user"] = ""
	}
	params["nonce"] = strconv.Itoa(r.Intn(nonceRandNum) + nonceRandNum)
	params["timestamp"] = strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	params["signature"] = genSignature(d.secretKey, params)
	//fmt.Printf("VerifyActionDun params: %+v\n", params)

	var reply DunReply
	resultByte, err := httpPostRequest(yiDunApi, map2UrlValues(params))
	if err == nil {
		err = json.Unmarshal(resultByte, &reply)
	}

	return reply, err
}

func genSignature(secretKey string, params map[string]string) string {
	var keys []string
	for key := range params {
		keys = append(keys, key)
	}
	sort.Strings(keys)
	buf := bytes.NewBufferString("")
	for _, key := range keys {
		buf.WriteString(key + params[key])
	}
	buf.WriteString(secretKey)
	//fmt.Printf("buf: %s\n", buf.String())
	has := md5.Sum(buf.Bytes())
	return fmt.Sprintf("%x", has)
}

func httpPostRequest(strUrl string, data url.Values) ([]byte, error) {
	//fmt.Printf("data:%+v", data)
	request, err := http.NewRequest(http.MethodPost, strUrl, strings.NewReader(data.Encode()))
	if nil != err {
		return nil, err
	}
	request.Header.Add("Content-Type", "application/x-www-form-urlencoded")

	c := &http.Client{Timeout: time.Second * 3}
	resp, err := c.Do(request)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if nil != err {
		return nil, err
	}
	return body, nil
}

// 将map格式的请求参数转换为values格式的
// params: map格式的参数键值对
// return: 查询字符串
func map2UrlValues(params map[string]string) url.Values {
	data := make(url.Values)
	for key, value := range params {
		data[key] = []string{value}
	}
	return data
}

// MD5摘要
func computeMD5(strData string) string {
	has := md5.Sum(Str2Bytes(strData))
	return fmt.Sprintf("%x", has)
}

// 字符串转切片高效方式
func Str2Bytes(s string) []byte {
	x := (*[2]uintptr)(unsafe.Pointer(&s))
	h := [3]uintptr{x[0], x[1], x[1]}
	return *(*[]byte)(unsafe.Pointer(&h))
}
