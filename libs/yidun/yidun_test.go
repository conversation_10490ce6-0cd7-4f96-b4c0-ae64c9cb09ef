package yidun

import "testing"

func TestNewYiDunObject(t *testing.T) {
	d := NewYiDunObject()
	r, e := d.VerifyActionDun("c723f3004c53499ab2c484ee9c09c987", "NBRIssoJufFyFi7CBIZqIXWHqtA.atOV9-Y4Nh-x9.D4Xz40Wzo4-ftm.Jn6EYzZyNYMPZRilU6YMaVDOPl6JiZ8OMyXZuj9fsOoauBwiwtbU5PTjVb78J1FP5h8RRuiVIq8EwK2B-aERZL1dMrfM4WUF6SbEY8PYCj4.Rh87R_MXcHZUeK-llMTrrGBPx7iH4wImu08OsQk2z52ezRWqXU7rUcmlV6gZ820XgYs_egeRfPWuRBkHeYg.L_7BQf0mLkH9MnQ-ODMlg54WpJssqDqY-YIR2_8dW41cJuB94MmoFRpW6SAPAM8DFVqoA0crJTRzieaInQHKhr9BAM_dYeretRRWKf6VdTXy0BF9lo8xOwW.HTggJTit0o2-UN.Clam6He8tIyjouKo7uc875PpNMay4NDznPW9RPPsSaLyJtOC05eqXl4104INh65diqlxNFc7j8WpopFmMFbF.ov1roCQOcwsMgZcOSFLESiBmovfJFJ-srOQC8a3", 255155415155441257)
	t.Logf("reply:%+v, e:%v", r, e)
}
