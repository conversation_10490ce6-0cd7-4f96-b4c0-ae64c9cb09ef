package zlog

import (
	"fmt"
	"go.uber.org/zap"
	"testing"
)

func TestL(t *testing.T) {
	l := newZapLogger(LoggerOption{FilePath: "", MaxSize: 500, MaxBackups: 30, MaxBackupDays: 1, Compress: true, ServiceName: "testServer"})
	l.Debug("debug", Any("name", "liupeng"), Any("abc", 30))
	l.Info("abc", Any("name", "liupeng"), Any("abc", 30))
	l.Infof("数据有误：%v", "数据库错误")
	l.Warn("警告", Any("file", "/usr/local/"))
}

func TestLevelLogger(t *testing.T) {
	l := NewLevelLogger(&LoggerOption{FilePath: "", MaxSize: 500, MaxBackups: 30, MaxBackupDays: 1, Compress: true, ServiceName: "testServer"})
	l.Info("abc", Any("name", "liupeng"), Any("abc", 30))
	l.Infof("数据有误：%v", "数据库错误")
	l.<PERSON>("警告", Any("file", "/usr/local/"))
}

func TestInfoLog(t *testing.T) {
	Info("abc", Any("name", "liupeng"), Any("abc", 30))
	Infof("数据有误：%v", "数据库错误")
	Warn("警告", Any("file", "/usr/local/"))
}

var file = "market"

func TestLWithManager(t *testing.T) {
	option := NewDefaultOption()
	option.FileName = "coin"
	option.IsSingleLevel = false
	InitLogger(file, "debug")
	//_ = InitLoggerManager(option)
	Debug("abc debug", Any("name", "liupeng"), Any("abc", 30))
	Info("abc", Any("name", "liupeng"), Any("abc", 30))
	//Infof("数据有误：%v", "数据库错误")
	Warn("警告", Any("file", "/usr/local/"))
	Error("警告", Any("file", "/usr/local/"))
	//m.New("core", true).Warn("测试")
	//GetLogger("core").Infof("测试core管理")
	GetLogger("market").Infof("测试core管理")
	GetLogger("market").Infof("测试core管理2")
}

func TestFiled(t *testing.T) {
	option := NewDefaultOption()
	option.FileName = "coin"
	_ = InitLoggerManager(option)
	Debug("检测数据", zap.Float64("id", 1), zap.String("hello", "shuju"))
	Debug("检测数据", Any("id1", 1), Any("hello1", "shuju"))
	DebugField("检测数据", Any("id1", 1), Any("hello1", "shuju"))
}

func TestA(t *testing.T) {
	option := NewDefaultOption()
	option.FileName = "coin"
	option.IsSingleLevel = false
	fmt.Printf("%+v", *option)
	//InitLogger(file, "debug")
	//log1:=GetLogger("price")
	//log2:=GetLogger("bucket")
	Info("test", Any("hello", "field"))
	//Warn("warn",Any("hello","field"))
	//log1.Info("hello",Any("name","liupeng"))
	//log1.Warn("hello log1",Any("name","liupeng"))
	//log2.Info("hello1",Any("name","liupeng"))
}
