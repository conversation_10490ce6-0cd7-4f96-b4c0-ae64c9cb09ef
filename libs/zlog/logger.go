/*
@Time : 2019-07-24 21:00
<AUTHOR> mocha
@File : logdemo
*/
package zlog

import (
	"fmt"
	"github.com/robfig/cron"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
	"os"
	"path/filepath"
	"sync"
	"time"
)

var levelMap = map[string]zapcore.Level{
	"debug": zap.DebugLevel,
	"info":  zap.InfoLevel,
	"warn":  zap.WarnLevel,
	"error": zap.ErrorLevel,
	"fatal": zap.FatalLevel,
}

type Logger interface {
	Info(template string, pair ...*Filed)
	Infof(template string, args ...interface{})
	Debug(template string, pair ...*Filed)
	Debugf(template string, args ...interface{})
	Warn(template string, pair ...*Filed)
	Warnf(template string, args ...interface{})
	Error(template string, pair ...*Filed)
	Errorf(template string, args ...interface{})
	Fatal(template string, pair ...*Filed)
	Fatalf(template string, args ...interface{})
}

type ZapLogger struct {
	log *zap.Logger
}

func newZapLogger(option LoggerOption) *ZapLogger {
	if option.LogLevel == "" {
		option.LogLevel = "debug"
	}
	if option.FileName == "" {
		option.FileName = "log"
	}
	//fmt.Printf("option:%+v\n", option)
	if option.FilePath != "" {
		err := os.MkdirAll(option.FilePath, 0755)
		if err != nil {
			fmt.Printf("make dir fail,%v", err)
			return nil
		}
	}
	logFile := filepath.Join(option.FilePath, option.FileName+".log")
	logger := newLogger(logFile, levelMap[option.LogLevel], option.MaxSize, option.MaxBackups, option.MaxBackupDays, option.Compress, option.DaySplit, option.ServiceName, option.loggerName)
	return &ZapLogger{log: logger}
}

func (l *ZapLogger) Flush() {
	l.log.Sync()
}

func (l *ZapLogger) Debug(template string, pair ...*Filed) {
	l.log.Debug(template, convertZap(pair)...)
}

func (l *ZapLogger) Debugf(template string, args ...interface{}) {
	l.log.Sugar().Debugf(template, args...)
}

func (l *ZapLogger) Info(template string, pair ...*Filed) {
	l.log.Info(template, convertZap(pair)...)
}

func (l *ZapLogger) Infof(template string, args ...interface{}) {
	l.log.Sugar().Infof(template, args...)
}

func (l *ZapLogger) Warn(template string, pair ...*Filed) {
	l.log.Warn(template, convertZap(pair)...)
}

func (l *ZapLogger) Warnf(template string, args ...interface{}) {
	l.log.Sugar().Warnf(template, args...)
}

func (l *ZapLogger) Error(template string, pair ...*Filed) {
	l.log.Error(template, convertZap(pair)...)
}

func (l *ZapLogger) Errorf(template string, args ...interface{}) {
	l.log.Sugar().Errorf(template, args...)
}

func (l *ZapLogger) Fatal(template string, pair ...*Filed) {
	l.log.Error(template, convertZap(pair)...)
}

func (l *ZapLogger) Fatalf(template string, args ...interface{}) {
	l.log.Sugar().Errorf(template, args...)
}

func convertZap(pair []*Filed) (zaps []zap.Field) {
	for _, p := range pair {
		zaps = append(zaps, zap.Any(p.Key, p.Filed))
	}
	return
}

type Filed struct {
	Key   string
	Filed interface{}
}

func Any(key string, filed interface{}) *Filed {
	return &Filed{Key: key, Filed: filed}
}

/**
 * 获取日志
 * filePath 日志文件路径
 * level 日志级别
 * maxSize 每个日志文件保存的最大尺寸 单位：M
 * maxBackups 日志文件最多保存多少个备份
 * maxAge 文件最多保存多少天
 * compress 是否压缩
 * serviceName 服务名
 */
func newLogger(filePath string, level zapcore.Level, maxSize int, maxBackups int, maxAge int, compress, daySplit bool, serviceName, loggerName string) *zap.Logger {
	core := newCore(filePath, level, maxSize, maxBackups, maxAge, compress, daySplit)
	return zap.New(core, zap.AddCaller(), zap.AddCallerSkip(1), zap.Fields(zap.String("srv", serviceName)), zap.Fields(zap.String("logger", loggerName)))
}

func newCron(hook *lumberjack.Logger) {
	c := cron.New()
	_ = c.AddFunc("59 59 23 * * *", func() { _ = hook.Rotate() }) // 秒,分,时,日,月,周 每日23点59分59秒执行
	c.Start()
}

/**
 * zapcore构造
 */
func newCore(filePath string, level zapcore.Level, maxSize int, maxBackups int, maxAge int, compress, daySplit bool) zapcore.Core {
	//日志文件路径配置2
	hook := lumberjack.Logger{
		Filename:   filePath,   // 日志文件路径
		MaxSize:    maxSize,    // 每个日志文件保存的最大尺寸 单位：M
		MaxBackups: maxBackups, // 日志文件最多保存多少个备份
		MaxAge:     maxAge,     // 文件最多保存多少天
		Compress:   compress,   // 是否压缩
		LocalTime:  true,
	}
	if daySplit {
		newCron(&hook)
	}

	// 设置日志级别
	atomicLevel := zap.NewAtomicLevelAt(level)
	//公用编码器
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.EncodeTime = timeEncoder

	return zapcore.NewTee(
		// 打印在文件中
		zapcore.NewCore(
			zapcore.NewJSONEncoder(encoderConfig),                                           // 编码器配置
			zapcore.NewMultiWriteSyncer(zapcore.AddSync(os.Stdout), zapcore.AddSync(&hook)), // 打印到控制台和文件
			atomicLevel, // 日志级别
		),
	)
}

func timeEncoder(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
	enc.AppendString(t.Format("2006-01-02 15:04:05.000"))
}

type LoggerOption struct {
	IsSingleLevel bool   //是否每个日志等级分别生成
	LogLevel      string //日志等级
	FilePath      string //日志保存路径
	FileName      string //日志名
	loggerName    string //日志生成器名称
	MaxSize       int    // 每个日志文件保存的最大尺寸 单位：M
	MaxBackups    int    // 日志文件最多保存多少个备份
	MaxBackupDays int    // 文件最多保存多少天
	Compress      bool   //是否压缩
	DaySplit      bool   //是否每日分隔文件
	ServiceName   string //服务名
}

func (option *LoggerOption) SetFileName(name string) *LoggerOption {
	option.FileName = name
	return option
}

type LevelLogger struct {
	debug *ZapLogger
	info  *ZapLogger
	warn  *ZapLogger
	error *ZapLogger
	fatal *ZapLogger
}

func NewLevelLogger(lo *LoggerOption) *LevelLogger {
	option := *lo
	info := newZapLogger(*option.SetFileName(lo.FileName + "_info"))
	option = *lo
	debug := newZapLogger(*option.SetFileName(lo.FileName + "_debug"))
	option = *lo
	warn := newZapLogger(*option.SetFileName(lo.FileName + "_warn"))
	option = *lo
	error := newZapLogger(*option.SetFileName(lo.FileName + "_error"))
	option = *lo
	fatal := newZapLogger(*option.SetFileName(lo.FileName + "_fatal"))
	return &LevelLogger{
		info: info, debug: debug, warn: warn, error: error, fatal: fatal}
}

func (l *LevelLogger) Debug(template string, pair ...*Filed) {
	l.debug.log.Debug(template, convertZap(pair)...)
}

func (l *LevelLogger) Debugf(template string, args ...interface{}) {
	l.debug.log.Sugar().Debugf(template, args...)
}

func (l *LevelLogger) Info(template string, pair ...*Filed) {
	l.info.log.Info(template, convertZap(pair)...)
}

func (l *LevelLogger) Infof(template string, args ...interface{}) {
	l.info.log.Sugar().Infof(template, args...)
}

func (l *LevelLogger) Warn(template string, pair ...*Filed) {
	l.warn.log.Warn(template, convertZap(pair)...)
}

func (l *LevelLogger) Warnf(template string, args ...interface{}) {
	l.warn.log.Sugar().Warnf(template, args...)
}

func (l *LevelLogger) Error(template string, pair ...*Filed) {
	l.error.log.Error(template, convertZap(pair)...)
}

func (l *LevelLogger) Errorf(template string, args ...interface{}) {
	l.error.log.Sugar().Errorf(template, args...)
}

func (l *LevelLogger) Fatal(template string, pair ...*Filed) {
	l.fatal.log.Error(template, convertZap(pair)...)
}

func (l *LevelLogger) Fatalf(template string, args ...interface{}) {
	l.fatal.log.Sugar().Errorf(template, args...)
}

//通过日志管理器管理日志
type LoggerManager struct {
	l      sync.RWMutex
	maps   map[string]Logger
	option *LoggerOption
}

func NewLoggerManager(option *LoggerOption) *LoggerManager {
	return &LoggerManager{
		option: option,
		maps:   make(map[string]Logger),
	}
}

func (m *LoggerManager) Put(name string, logger Logger) {
	m.l.Lock()
	defer m.l.Unlock()
	m.maps[name] = logger
}

func (m *LoggerManager) New(name string, isSingleLevel bool, option *LoggerOption) Logger {
	m.l.Lock()
	defer m.l.Unlock()
	loggerName := name
	if name == "" {
		loggerName = "default"
	}
	logger, ok := m.maps[loggerName]
	if ok {
		return logger
	}

	configOption := *m.option
	if option != nil {
		configOption = *option
	}
	//fmt.Printf("option1:%+v", m.option)

	fileName := configOption.FileName
	if configOption.FileName != "" && name != "" {
		fileName = configOption.FileName + "_" + name
	}
	configOption.SetFileName(fileName)
	configOption.loggerName = loggerName
	//fmt.Printf("option:%+v", configOption)
	if isSingleLevel {
		logger = NewLevelLogger(&configOption)
	} else {
		logger = newZapLogger(configOption)
	}
	m.maps[loggerName] = logger
	return logger
}

func (m *LoggerManager) Get(name string) Logger {
	if m == nil {
		fmt.Errorf("日志管理器无效，退出")
		return nil
	}
	var loggers map[string]Logger
	m.l.Lock()
	loggers = m.maps
	m.l.Unlock()
	if name == "" {
		if l, ok := loggers["default"]; !ok {
			fmt.Println(loggerM.option.IsSingleLevel)
			return m.New(name, loggerM.option.IsSingleLevel, nil)
		} else {
			return l
		}
	}
	if l, ok := loggers[name]; !ok {
		return m.New(name, false, nil)
	} else {
		return l
	}
}

func NewDefaultOption() *LoggerOption {
	return &LoggerOption{FilePath: "", MaxSize: 500, MaxBackups: 30, MaxBackupDays: 1, Compress: true, ServiceName: "default"}
}

var loggerM *LoggerManager

//初始化logger
func InitLogger(file, level string) {
	// 创建日志文件夹
	fPath := filepath.Dir(file)
	fName := filepath.Base(file)
	option := &LoggerOption{
		IsSingleLevel: true,
		FilePath:      fPath,
		FileName:      fName,
		MaxSize:       500,
		MaxBackups:    100,
		MaxBackupDays: 30,
		Compress:      true,
		ServiceName:   fName,
		LogLevel:      level,
	}
	fmt.Println(fPath, fName)
	InitLoggerManager(option)
}

//初始化日志管理器
//初始化默认的管理器并生成默认logger
func InitLoggerManager(option *LoggerOption) *LoggerManager {
	if loggerM == nil {
		loggerM = NewLoggerManager(option)
	}
	if loggerM == nil {
		fmt.Println("日志管理器为nil")
	} else {
		loggerM.option = option
	}
	//loggerM.New("", IsSingleLevel, nil)
	return loggerM
}

func GetLogger(name string) Logger {
	if loggerM == nil {
		fmt.Println("管理器为nil，以默认参数初始化管理器")
		loggerM = NewLoggerManager(NewDefaultOption())
	}
	return loggerM.Get(name)
}

//对外接口
func Debug(msg string, fields ...interface{}) {
	c := convert(fields)
	GetLogger("").Debug(msg, c...)
}

func Debugf(template string, args ...interface{}) {
	GetLogger("").Debugf(template, args...)
}

func DebugField(template string, args ...*Filed) {
	GetLogger("").Debug(template, args...)
}

func Info(msg string, fields ...interface{}) {
	c := convert(fields)
	GetLogger("").Info(msg, c...)
}

func Infof(template string, args ...interface{}) {
	GetLogger("").Infof(template, args...)
}

func InfoField(template string, args ...*Filed) {
	GetLogger("").Info(template, args...)
}

func Warn(msg string, fields ...interface{}) {
	c := convert(fields)
	GetLogger("").Warn(msg, c...)
}

func Warnf(template string, args ...interface{}) {
	GetLogger("").Warnf(template, args...)
}

func WarnField(template string, args ...*Filed) {
	GetLogger("").Warn(template, args...)
}

func Error(msg string, fields ...interface{}) {
	c := convert(fields)
	GetLogger("").Error(msg, c...)
}

func Errorf(template string, args ...interface{}) {
	GetLogger("").Errorf(template, args...)
}

func ErrorField(template string, args ...*Filed) {
	GetLogger("").Error(template, args...)
}

func Fatal(msg string, fields ...interface{}) {
	c := convert(fields)
	GetLogger("").Fatal(msg, c...)
}

func Fatalf(template string, args ...interface{}) {
	GetLogger("").Fatalf(template, args...)
}

func FatalField(template string, args ...*Filed) {
	GetLogger("").Fatal(template, args...)
}

func convert(fields []interface{}) (list []*Filed) {
	for _, field := range fields {
		if f, ok := field.(zap.Field); ok {
			list = append(list, Any(f.Key, f.String))
			continue
		}
		if f, ok := field.(*Filed); ok {
			list = append(list, f)
			continue
		}
		list = append(list, Any("", field))
	}
	return
}
