run_env: "test" #运行环境，prod-生产,test-测试，dev-开发
dubug: true # 是否启用调试模式
zip_http: false # 是否启用zip压缩
local_name: "Spot-Closing-1" # 服务名称
rpc_addr: "127.0.0.1:18120" # rpc服务监听地址
pprof_addr: "127.0.0.1:18121" # 服务运行状态监听地址
core_rpc_addr: "127.0.0.1:9901" #core rpc服务地址
follow_rpc_addr: "127.0.0.1:9907" #followr pc服务地址
symbols: [ "BTC/USDT","ETH/USDT","ETC/USDT","BASH/USDT","DOT/USDT","UNIK/USDT","FIL/USDT","LTC/USDT","LINK/USDT" ] #服务支持合约
discovery: "127.0.0.1:8500" #服务注册发现地址
mq: "amqp://user:password@localhost:5672" #rabbit mq config
default_db: "root:123456@tcp(127.0.0.1:3306)/futures_basecoin?charset=utf8&parseTime=true&loc=Local" # 数据库连接
#default_db: "root:123456@tcp(127.0.0.1:3306)/new_base?charset=utf8&parseTime=true&loc=Local" # 数据库连接
log_file: "logs/panda/closing" # 日志文件位置
log_level: "info" # 日志等级

read_timeout: 30 # 读超时时间
write_timeout: 40 # 写超时时间

mail_prefix: "【Panda】" # 发送邮件前缀(报警邮件用)
caution_receiver: [ ] # 警告邮件通知列表
sensitive_conf: "" # 敏感信息配置文件位置
depth_height: 10  #深度高度
#主从服务
is_slave: false
clone_nums: 1
clone_id: 0 # 副本id
mq_queue_id: 0 # 消息id

msg_server_url: "http://127.0.0.1:10110" # 新短信邮件服务地址
msg_server_app_id: "025633e8-c0ea-463e-bc27-04c50e8b05b8" # 新短信邮件服务appID
msg_server_secret_key: "954146C06A993123F376A0F3DD7C8076F480B4C837AEB426746410F493800871" # 新短信邮件服务secret

work_id: 17 # work_id

closing_queue_nums: 1024 #队列长度
closing_worker_nums: 10 #worker nums

default_redis_conf: { # 默认redis连接
  "address": "127.0.0.1:6379", # 连接地址
  "password": "", # 连接密码
  "use_tls": false, # 是否使用tls连接
  "default_db": 5, # 使用的db号
  "pool_size": 20, # 连接池数量
  "db_nums": { # 使用的redis库号,仅会初始化这里定义了的db号 key(db号):value(描述)
    #    0: "db 0",
    #    1: "db 1",
    2: "公共数据",
    #    3: "db 3",
#    4: "/USDT合约",
    5: "现货",
    #    6: "db 6",
    #    7: "db 7",
    #    8: "db 8",
    #    9: "db 9",
    #    10: "db 10",
    #    11: "db 11",
    #    12: "db 12",
    #    13: "db 13",
    #    14: "db 14",
    #    15: "db 15",
  },
}
