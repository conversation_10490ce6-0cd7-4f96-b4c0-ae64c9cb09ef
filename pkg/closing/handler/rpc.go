package handler

//
//import (
//	"spot/libs/xrpc"
//	"context"
//	"time"
//
//	"go.uber.org/zap"
//
//	"spot/libs/conf"
//	"spot/libs/define"
//	"spot/libs/log"
//)
//
//type RPC struct {
//	s *xrpc.Server
//}
//
//var rpcServer *RPC
//
//func InitRPC() (err error) {
//	rpcServer = new(RPC)
//	option := &xrpc.ServerOption{
//		ServiceName:    define.ServerNameTask,
//		NodeName:       conf.LocalName(),
//		Rev:            rpcServer,
//		Address:        conf.RPCAddr(),
//		BasePath:       define.BasePath,
//		Discovery:      conf.Discovery(),
//		UpdateInterval: 10 * time.Second,
//		Meta:           "",
//		AuthFunc:       nil,
//	}
//	s, err := xrpc.NewXServer(option)
//	if err != nil {
//		log.Errorf("init Rpc server fail,%v", err)
//		return
//	}
//	rpcServer.s = s
//	s.RegisterOnShutdown(func() {
//	})
//	go func() {
//		e := s.Start()
//		if e != nil {
//			log.Error("启动rpc失败", zap.Error(e))
//			return
//		}
//
//	}()
//	return
//}
//
//func ShutdownServers() {
//	log.Info("follow service begin stop")
//	if rpcServer != nil {
//		rpcServer.s.Shutdown(context.Background())
//	}
//	log.Info("follow service stop")
//}
