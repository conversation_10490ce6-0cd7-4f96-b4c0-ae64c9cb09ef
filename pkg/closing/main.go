package main

import (
	"context"
	"encoding/gob"
	"fmt"
	"os"
	"spot/libs/proto"
	"spot/libs/xrpcclient/user_rpc"

	"spot/libs/cache"
	"spot/libs/conf"
	"spot/libs/database"
	"spot/libs/log"
	"spot/libs/pprof"
	"spot/pkg/closing/server"
	"spot/pkg/closing/service"
)

var (
	gitHash   string
	gitDigest string
	gitBranch string
	buildUser string
	buildTime string
	goVersion string
)

func main() {
	args := os.Args
	if len(args) == 2 && (args[1] == "--version" || args[1] == "-v") {
		fmt.Printf("Git Branch: %s \n", gitBranch)
		fmt.Printf("Git Commit Hash: %s \n", gitHash)
		fmt.Printf("Git Commit Digest: %s \n", gitDigest)
		fmt.Printf("Build User: %s \n", buildUser)
		fmt.Printf("Build TimeStamp: %s \n", buildTime)
		fmt.Printf("GoLang Version: %s \n", goVersion)
		return
	}

	// 初始化配置文件
	conf.Init()

	// 初始化日志
	initLogger()

	service.InitSupportSymbol()

	//初始化缓存
	cache.InitDefaultRedisConn()

	// 初始化数据库
	database.InitDefaultDB()

	// 注册必要rpc传输结构
	RegisterGob()

	//连接core服务
	if !conf.IsDev() && !conf.IsSimulate() {

	}
	user_rpc.InitUserClient(context.Background())

	//初始化清算多线程处理器
	service.StartClosingEngineHandler()

	//初始化消息队列
	service.InitMessageQueue()

	// 初始化服务监控
	pprof.Init(conf.PprofAddr())

	// 监听系统信号
	server.InitSignal()
}

func initLogger() {
	log.InitLogger(conf.LogFile(), conf.LogLevel(), conf.LogOnlyFile())
}

func RegisterGob() {
	gob.Register(make(map[string]string))        // 管理后台获取大钱包资产数据rpc前置
	gob.Register(new(proto.TradeLeverReply))     // 杠杆
	gob.Register(make([]proto.ApiSymbolList, 0)) //
	gob.Register(new(proto.OrderOpenRsp))        //订单处理
	gob.Register(new(proto.ApiCurrentPosition))  //当前持仓
	gob.Register(make([]proto.ApiSoldTrade, 0))  //成交记录
	gob.Register(new(proto.AssetDetail))         //资产详情
	gob.Register(new(proto.EntrustOrder))
}
