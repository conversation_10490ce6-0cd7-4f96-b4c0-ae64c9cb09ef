package service

import (
	"context"
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/conf"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/mq"
	"spot/libs/nums"
	"time"
)

var ClosingEngineHandler *ClosingEngine

func StartClosingEngineHandler() {
	log.Info("结算系统使用用户队列结算", zap.Int("队列长度", GetQueueNums()), zap.Int("workerNums", GetWorkerNums()))
	ClosingEngineHandler = NewClosingEngine(GetQueueNums(), GetWorkerNums())
	ClosingEngineHandler.Run()

}

type ClosingEngine struct {
	cxt        context.Context
	eventChan  chan mq.MessagePack //消息队列
	workerMap  map[int]*Worker     //工作worker
	workerNums int                 //worker nums
	cancel     context.CancelFunc
}

func NewClosingEngine(queueNums, workerNums int) *ClosingEngine {
	ctx, cancel := context.WithCancel(context.Background())
	ce := &ClosingEngine{
		cxt:        ctx,
		eventChan:  make(chan mq.MessagePack, queueNums),
		workerMap:  make(map[int]*Worker, workerNums),
		workerNums: workerNums,
		cancel:     cancel,
	}
	for i := 1; i <= workerNums; i++ {
		w := NewWorker(ce, i)
		ce.workerMap[i] = w
		w.Run()
	}
	return ce
}

type Worker struct {
	Id int
	ce *ClosingEngine
}

func NewWorker(ce *ClosingEngine, id int) *Worker {
	return &Worker{Id: id, ce: ce}
}

func (w *Worker) Run() {
	go func() {
		for {
			select {
			case <-w.ce.cxt.Done():
				log.Info("worker将推出", zap.Int("workerId", w.Id))
				return
			case msg, ok := <-w.ce.eventChan:
				log.Info("开始处理队列消息", zap.Int("workerId", w.Id), zap.Any("msg", msg))
				if !ok {
					log.Info("消息队列关闭,worker将推出", zap.Int("workerId", w.Id))
					return
				}
				w.dealClosingMsg(msg)
			}
		}
		log.Info("worker退出", zap.Int("workerId", w.Id))
	}()
}

func dealWaitFinish() {
	//isCanHandle := cache.SetRedisLockWithExp(10*time.Minute, define.CacheOrderWaitFinishDealLock)
	//if !isCanHandle {
	//	log.Errorf("当前待处理完成消息处理任务key获取失败")
	//	return
	//}
	//defer cache.SetRedisUnLockStr(define.CacheOrderWaitFinishDealLock)
	list := cache.ListOrderWaitFinish()
	//log.Info("开始检查待处理的完成任务", zap.Int("当前待完成数量", len(list)))
	if len(list) == 0 {
		return
	}
	log.Info("开始检查待处理的完成任务,当前待完成的订单数", zap.Any("size", len(list)))
	n := time.Now().Unix()
	for _, pack := range list {
		if isSupport(pack.Code) && n-pack.WaitTs > 20 {
			log.Info("开始检查待处理的完成任务", zap.Any("data", pack))
			dealWaitFinishOrder(pack)
		}
	}
}

func dealWaitFinishOrder(pack mq.MessagePack) {
	isCanHandle := cache.SetRedisLockWithExp(10*time.Minute, define.CacheOrderMsgDealLock, nums.Int64String(pack.Extra.ID))
	if isCanHandle {
		return
	}
	defer cache.SetRedisUnLockStr(define.CacheOrderMsgDealLock, nums.Int64String(pack.Extra.ID))
	//获取所有待处理的消息
	list := cache.ListOrderDealingMsg(pack.Extra.ID)
	for _, msg := range list {
		if msg.Topic == define.MQTopicUserOrderTrade {
			MQMatchTrade(msg)
		}
	}
	MQMatchOver(pack)
	//移除待完成的消息
	cache.DelOrderWaitFinish(pack.Extra.ID)
}

func (ce *ClosingEngine) Push(message mq.MessagePack) { //用户分流器
	ce.eventChan <- message
}

func (ce *ClosingEngine) Run() {
	go func() {
		for {
			b := time.Now()
			dealWaitFinish()
			if time.Since(b).Seconds() > 5 {
				continue
			}
			time.Sleep(5 * time.Second)
		}
	}()
}

func (ce *ClosingEngine) Shutdown() {
	close(ce.eventChan)
	for {
		log.Info("当前队列剩余处理个数", zap.Any("数量", len(ce.eventChan)))
		if len(ce.eventChan) == 0 {
			ce.cancel()
			break
		}
	}
}

func GetWorkerNums() int {
	nums := conf.GetClosingWorkerNums()
	if nums > 0 {
		return nums
	}
	return 10
}

func GetQueueNums() int {
	nums := conf.ClosingQueueNums()
	if nums > 0 {
		return nums
	}
	return 1024
}

func (w *Worker) dealClosingMsg(msg mq.MessagePack) {
	var isHandle bool
	curDealID := int64(w.Id)
	orderId := msg.Extra.ID
	seqId := msg.Extra.SeqId

	log.Info("开始消息处理", zap.Any("data", msg), zap.Any("workerId", curDealID))
	defer func() {
		log.Info("队列消息处理完毕", zap.Any("msgId", msg.MsgId), zap.Any("workerId", curDealID))
		if e := recover(); e != nil {
			log.Info("系统panic恢复，将消息放入待处理队列", zap.Any("data", msg))
			cache.SetOrderDealingMsg(msg)
			log.Error("系统panic恢复", zap.Any("err", e))
		}
	}()
	//锁定订单
	for {
		select {
		case <-w.ce.cxt.Done():
			log.Info("收到推出指令，将订单放入待处理队列，进行备份", zap.Any("workerId", curDealID), zap.Any("data", msg))
			cache.SetOrderDealingMsg(msg)
			return
		default:
			isCanHandle := cache.SetRedisLockWithExp(10*time.Minute, define.CacheOrderMsgDealLock, nums.Int64String(orderId))
			if isCanHandle {
				goto Deal
			}
			time.Sleep(150 * time.Millisecond)
		}
	}

Deal:
	defer func() {
		cache.SetRedisUnLockStr(define.CacheOrderMsgDealLock, nums.Int64String(orderId))
	}()

	//获取系统最新处理序列
	lastSeqId := cache.GetOrderCurDealSeqId(orderId)
	if seqId == lastSeqId+1 {
		isHandle = true
	}

	if msg.Topic == define.MQTopicUserOrderOver || !isHandle {
		//判断是否订单完成,如果完成直接推出
		if checkOrderIsFinish(orderId, curDealID, msg) {
			return
		}
	}

	if !isHandle {
		//缓存待处理序列
		if msg.Topic == define.MQTopicUserOrderOver {
			msg.WaitTs = time.Now().Unix()
			cache.SetOrderWaitFinish(msg)
		}
		log.Info("不进行处理，将订单放入待处理队列", zap.Any("workerId", curDealID), zap.Any("data", msg))
		cache.SetOrderDealingMsg(msg)
		checkAndDealingOrderMsg(orderId, lastSeqId, curDealID)
		return
	}

	switch msg.Topic {
	case define.MQTopicUserOrderTrade:
		MQMatchTrade(msg)
		cache.SetOrderCurDealSeqId(orderId, seqId)
	case define.MQTopicUserOrderOver:
		MQMatchOver(msg)
	}

	//检查是否有未完成序列
	log.Info("开始检查是否有待处理的订单指令数据", zap.Any("workerId", curDealID), zap.Any("orderId", orderId), zap.Bool("是否有", cache.ExistOrderDealingMsg(orderId)))
	if !cache.ExistOrderDealingMsg(orderId) {
		return
	}
	checkAndDealingOrderMsg(orderId, seqId, curDealID)
}

func checkOrderIsFinish(orderId, curDealID int64, msg interface{}) (isFinish bool) {
	order, err := database.GetUserEntrustOrder(nil, orderId)
	if err != nil {
		log.Error("查询订单信息报错 dealClosingMsg database.GetUserEntrustOrderWithLock fail", zap.Error(err), zap.Any("workerId", curDealID), zap.Any("data", msg))
		return
	}
	if order == nil {
		isFinish = true
		cache.DelOrderSeqId(orderId)
		log.Info("订单信息为空,本次消息忽略", zap.Any("workerId", curDealID), zap.Any("data", msg))
		return
	}
	if order.IsFinished() {
		isFinish = true
		cache.DelOrderSeqId(orderId)
		log.Info("订单已完成,本次消息不在处理", zap.Any("workerId", curDealID), zap.Any("data", msg))
		return
	}
	return
}
