package service

import (
	"go.uber.org/zap"
	"spot/libs/conf"
	"spot/libs/define"
	"spot/libs/log"
	"spot/pkg/closing/config"
)

func InitSupportSymbol() {
	list := conf.Symbols()
	if len(conf.Symbols()) == 0 {
		log.Error("当前没有配置支持的交易对")
		panic("请检查配置文件")
	}

	for _, s := range list {
		config.SupportSymbol.Store(s, s)
	}
}
func isSupport(code string) bool {
	log.Debug("return", zap.String("code", code), zap.Any("s", config.SupportSymbol))
	if len(conf.Symbols()) == 0 {
		log.Error("当前没有配置支持的交易对")
	}
	_, ok := config.SupportSymbol.Load(code)
	return ok
}

//判断是否不处理消息
func isNotDealMsg(identity int) bool {
	return identity == define.IdentifierMarketRobot
}

func IsHandleThisUser(userId int64) bool {
	return true
	return userId%int64(conf.CloneNums()) == int64(conf.CloneID())
}
