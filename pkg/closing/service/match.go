package service

import (
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/commonsrv"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
)

func dealUserTrade(match *proto.OrderTrade) {
	if match == nil {
		return
	}
	log.Info("开始处理用户成交数据", zap.Any("data", match))
	if match.DealPrice.LessThanOrEqual(decimal.Zero) {
		log.Info("撮合数据异常", zap.Any("预成交数据", match), zap.Any("委托订单", match))
		return
	}
	if match.DealVolume.LessThanOrEqual(decimal.Zero) {
		log.Info("撮合数据异常", zap.Any("预成交数据", match), zap.Any("委托订单", match))
		return
	}
	order, err := database.GetUserEntrustOrder(nil, match.Order.OrderId)
	if err != nil {
		log.Error("查询订单信息失败，有查询到订单信息或订单已经完成，dealUserTrade database.GetUserEntrustOrder fail", zap.Error(err))
		return
	}

	if order == nil {
		log.Info("没有查询到订单信息或订单已经完成", zap.Any("数据", match))
		cache.DelOrderSeqId(match.Order.OrderId)
		return
	}

	log.Info("处理用户成交", zap.Any("预成交数据", match), zap.Any("委托订单", order))
	//非fok模式，于成交量为0，退出本逻辑
	if order.EntrustStrategy != define.MatchStrategyFOK && match.DealVolume.LessThanOrEqual(decimal.Zero) {
		log.Error("非Fok模式，成交量为0，不做处理", zap.Any("成交", match), zap.Any("订单", order))
		return
	}
	mr := &proto.MRecord{
		UserID:          order.UserID,
		OrderId:         order.ID,
		MatchID:         match.MatchID,
		Code:            match.Code,
		DealPrice:       match.DealPrice,
		DealVolume:      match.DealVolume,
		MatchTime:       match.MatchTime,
		IsProtocalTrade: match.IsProtocalTrade,
		Level:           match.Level,
		IsMaker:         match.Order.IsMaker,
		IsReturn:        match.Order.IsReturn,
		TradeFinish:     match.TradeFinish,
	}
	commonsrv.DealUserTrade(mr)
}
