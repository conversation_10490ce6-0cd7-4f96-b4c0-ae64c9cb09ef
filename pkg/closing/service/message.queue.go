// Package service /*
package service

import (
	"encoding/json"
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/conf"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/messagequeue"
	"spot/libs/mq"
	"spot/libs/nums"
	"spot/libs/proto"
)

func InitMessageQueue() {
	handler := make(map[string]mq.MsgHandleFunc)
	handler[define.MQTopicUserOrderTrade] = MQClosing //处理撮合数据
	handler[define.MQTopicUserOrderOver] = MQClosing  //处理撮合结束 （全部成交）
	//
	messagequeue.NewMqProduct(conf.MQ(), nums.Int64String(conf.WorkerID()))
	messagequeue.NewMqConsumer(define.MQDefaultExchangeName, define.MQConsumerClosingQueueName, define.MQDefaultMQBindKeyClosing, nums.Int2String(conf.MQQueueID()), handler)
}

func StopMQProduct() {
	messagequeue.StopMQProduct()
}

func StopMQConsumer() {
	messagequeue.StopMQConsumer()
}

func MQClosing(msg mq.MessagePack) {
	if !isSupport(msg.Code) {
		log.Info("本服务不支持此消息处理", zap.Any("data", msg))
		return
	}
	if !IsHandleThisUser(msg.UserId) {
		log.Info("当前服务不处理该用户消息", zap.Int64("useriD", msg.UserId), zap.Int64("u", msg.UserId%int64(conf.CloneNums())), zap.Int("CLONE", conf.CloneNums()), zap.Int("cloneId", conf.MQQueueID()))
		return
	}
	if isNotDealMsg(msg.Identifier) {
		log.Info("当前服务不处理该用户类型消息", zap.Any("data", msg))
		return
	}
	ClosingEngineHandler.Push(msg)
	//go dealClosingMsg(msg)
}

func checkAndDealingOrderMsg(orderId, lastSeq, curDealID int64) {
	next := lastSeq
	for cache.ExistOrderDealingMsg(orderId) {
		next++
		curMsg := cache.GetOrderDealingMsgBySeqId(orderId, next)
		if curMsg == nil {
			log.Info("当前订单下个序列数据为nil", zap.Any("workerId", curDealID))
			break
		}
		curMsg.DealId = curDealID
		switch curMsg.Topic {
		case define.MQTopicUserOrderTrade:
			MQMatchTrade(*curMsg)
			cache.SetOrderCurDealSeqId(orderId, curMsg.SeqId)
		case define.MQTopicUserOrderOver:
			MQMatchOver(*curMsg)
		}
		cache.DelOrderDealingMsgSeq(orderId, next)
	}
}

func MQMatchOver(message mq.MessagePack) {
	DealMqMatchOver(message, false)
}

func MQMatchTrade(message mq.MessagePack) {
	DealMqMatchTrade(message, false)
}

//处理订单成交消息
func DealMqMatchTrade(message mq.MessagePack, isRecover bool) {
	mr := new(proto.OrderTrade)
	err := json.Unmarshal(message.Data, mr)
	if err != nil {
		log.Errorf("解析成交消息出错：%+v,data；%+v", err, string(message.Data))
		return
	}
	if mr.Code == "" {
		log.Error("推送撮合消息没有附带合约信息", zap.Any("msg", mr))
		return
	}
	mr.MsgId = message.MsgId
	mr.IsRecover = isRecover
	mr.DealId = message.DealId
	mr.MsgSeqId = message.SeqId
	if isSupport(mr.Code) {
		//if !isRecover {
		//	cache.SetClosingMsg(message)
		//}
		//if IsUseEntryQueue() {
		//	ClosingEngineHandler.Push(UserEvent{
		//		UserId:  mr.Order.UserId,
		//		ThirdOrderId: mr.Order.ThirdOrderId,
		//		SeqId:   message.SeqId,
		//		Event:   cEventTrade,
		//		Data:    mr,
		//	})
		//} else {
		dealUserTrade(mr)
		//}
	}

}

//处理订单完成消息
func DealMqMatchOver(message mq.MessagePack, isRecover bool) {
	mr := new(proto.MatchOverMsg)
	err := json.Unmarshal(message.Data, mr)
	if err != nil {
		log.Errorf("解析成交消息出错：%+v,data；%+v", err, string(message.Data))
		return
	}
	if mr.Code == "" {
		log.Error("推送订单完成消息没有附带合约信息", zap.Any("msg", mr))
		return
	}
	mr.MsgId = message.MsgId
	mr.IsRecover = isRecover
	mr.DealID = message.DealId
	if isSupport(mr.Code) {

		//if !isRecover {
		//	cache.SetClosingMsg(message)
		//}
		//if IsUseEntryQueue() {
		//	ClosingEngineHandler.Push(UserEvent{
		//		UserId:  mr.UserId,
		//		ThirdOrderId: mr.ThirdOrderId,
		//		SeqId:   message.SeqId,
		//		Event:   cEventMatchOverMsg,
		//		Data:    mr,
		//	})
		//} else {
		dealMatchOver(mr)
		//}
	}
}
