package service

import (
	"errors"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/commonsrv"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
	"time"
)

type returnOrder struct {
	MatchID int64  `json:"match_id"`
	Code    string `json:"contract_code"`
	OrderId int64
	Amount  decimal.Decimal
	Price   decimal.Decimal
	IsMaker bool
	Order   *proto.EntrustOrder
}

func dealMatchOver(mr *proto.MatchOverMsg) {
	log.Info("收到订单完成消息", zap.Any("msg", mr))
	if mr.IsUserWithAccount() {
		dealMatchOverForUser(mr)
	} else {
		cache.DelOrderSeqId(mr.OrderId)
		dealMatchOverForMarketRobot(mr)
	}
}

func dealMatchOverForMarketRobot(mr *proto.MatchOverMsg) {
	switch mr.DealerState {
	case define.MatchOverDefault:
		dealMatchCancelForRobot(mr)
	case define.MatchOverForce:
		log.Errorf("error indentifer msg:%+v", mr)
	case define.MatchTradeOver:
		dealMatchTradeOverForRobot(mr)
	}
}

//处理用户订单结束
//为避免嵌套事务导致锁异常，在事务结束后处理协议成交，查看 defer database.CommitTx
func dealMatchOverForUser(mr *proto.MatchOverMsg) {
	log.Info("开始处理用户订单完成消息", zap.Any("data", mr))
	var (
		order           *proto.EntrustOrder
		ignoreDealOrder bool
	)
	tx, err := database.Begin()
	if err != nil {
		log.Error("dealMatchOverForUser get tx fail", zap.Error(err))
		return
	}
	defer database.CommitTx(tx, &err, 0, func(i int64, e error) {
		log.Info("处理订单完成状态", zap.Error(e), zap.Any("order", order), zap.Any("是否忽略处理", ignoreDealOrder))
		if e != nil {
			return
		}
		//解锁资源，修改订单
		if !ignoreDealOrder {
			mCancel := &proto.MCancel{
				OrderId:    order.ID,
				CancelMark: define.CancelOrderFinish,
				TradeMark:  define.TradeMarkWithOrderDefault,
			}
			err = commonsrv.DealContractAccountOrderCanceled(mCancel)
		}
	})

	//锁定委托订单
	order, err = database.GetUserEntrustOrderWithLock(tx, mr.OrderId)
	if err != nil {
		log.Error("查询订单信息报错 dealMatchOverForUser database.GetUserEntrustOrderWithLock fail", zap.Error(err))
		return
	}
	if order == nil {
		err = errors.New("无效订单")
		cache.DelOrderSeqId(mr.OrderId)
		log.Info("没有查询到订单", zap.Any("data", mr))
		return
	}

	//如果委托订单已经处理完毕，忽略
	if order.IsFinished() {
		ignoreDealOrder = true //忽略消息
		cache.DelOrderSeqId(mr.OrderId)
		log.Info("订单已完成,不在处理本次成交", zap.Any("订单", order), zap.Any("mr", mr))
		return
	}

}

//处理委托单子协议成交
func dealProtocalTrade(ro *returnOrder) {
	order := &proto.OrderTrade{
		MatchID:    database.NextID(),
		Code:       ro.Code,
		DealPrice:  ro.Price,
		DealVolume: ro.Amount,
		MatchTime:  time.Now(),
		Order: proto.MatchUser{
			UserId:     ro.Order.UserID,
			OrderId:    ro.Order.ID,
			Identifier: define.IdentifierUser,
		},
		IsProtocalTrade: true,
	}
	dealUserTrade(order)
}

func dealMatchTradeOverForRobot(mr *proto.MatchOverMsg) {

}

func dealMatchCancelForRobot(mr *proto.MatchOverMsg) {

}

func DealMatchOver(mr *proto.MatchOverMsg) {
	dealMatchOverForUser(mr)
}
