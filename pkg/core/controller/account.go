package controller

import (
	"context"
	"strings"

	"go.uber.org/zap"
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/proto"
	"spot/pkg/core/service"
)

// Login 登录
func (r *RPC) Login(ctx context.Context, arg *define.Arg, reply *define.Reply) error {
	request := new(proto.LoginArg)
	err := json.Unmarshal(arg.Data, request)
	if err != nil {
		log.Error("Login json unmarshal failed",
			zap.ByteString("arg.Data", arg.Data),
			zap.Error(err))
		r.epilog(ctx, define.ErrMsgParam, arg.ReqLang, arg, reply)
		return nil
	}
	request.Account = strings.TrimSpace(request.Account)

	reply.Data, err = service.LoginHandler(&arg.ReqArg, request)
	r.epilog(ctx, err, arg.ReqLang, arg, reply)
	return nil
}
