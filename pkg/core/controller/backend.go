package controller

import (
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/convert"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/messagequeue"
	"spot/libs/proto"
	"spot/pkg/core/service"
)

// BackendOrderCancel 管理后台进行合约撤单操作
func (r *RPC) BackendOrderCancel(arg *proto.BackendUserContractArg, reply *define.Reply) error {
	reqID := database.NextID()
	log.Info("BackendOrderCancel in", zap.Int64("reqID", reqID), zap.Any("arg", arg))
	defer log.Info("BackendOrderCancel out", zap.Int64("reqID", reqID), zap.Any("reply", reply))

	err := service.BackendOrderCancel(reqID, arg, reply)
	if err != nil {
		log.Error("BackedUserOrderCancel error", zap.Int64("reqID", reqID), zap.Any("arg", arg), zap.Error(err))
	}
	return nil
}

// BackendUserOrderCancel 管理后台进行用户订单撤销操作
func (r *RPC) BackendUserOrderCancel(arg *proto.ApiOrderCancelArg, reply *define.Reply) error {
	reqID := database.NextID()
	log.Info("BackendUserOrderCancel in", zap.Int64("reqID", reqID), zap.Any("arg", arg))
	defer log.Info("BackendUserOrderCancel out", zap.Int64("reqID", reqID), zap.Any("reply", reply))

	// 加redis锁
	if !cache.SetRedisLockStr("BackendUserOrderCancel", convert.Int64String(arg.UserID)) {
		reply.Ret = define.ErrCodeTradeFrequent
		reply.Msg = define.ErrMsgTradeFrequent.ErrMsg(define.ReqLangCN)
		return nil
	}
	defer cache.SetRedisUnLockStr("BackendUserOrderCancel", convert.Int64String(arg.UserID))

	// 条件单直接更新数据库状态
	list, err := database.CancelConditionOrder(arg.UserID, arg.OrderID, arg.ContractCode)
	if err != nil {
		log.Error("PlanCancelHandler CancelConditionOrder db error", zap.Int64("reqID", arg.ReqID), zap.Error(err))
		reply.Ret = define.ErrCodeBusy
		reply.Msg = define.ErrMsgBusy.ErrMsg(define.ReqLangCN)
		return nil
	}

	for i := range list {
		messagequeue.NotifyMqPlanOpenDel(&list[i])
		//service.GetPlanOrderWorker().RemoveOrder(list[i].ContractCode, decimal.NewFromFloat(list[i].TriggerPrice), list[i].Condition, list[i].PlanOrderId)
	}
	//notifyConditionOrderChange(order)

	return nil
}
