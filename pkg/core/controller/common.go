package controller

import (
	"context"
	"spot/libs/define"
	"spot/pkg/core/service"
)

// GetCoinList 币种列表
func (r *RPC) GetCoinList(ctx context.Context, arg *define.Arg, reply *define.Reply) error {
	var err error
	reply.Data, err = service.GetCoinListHandler(arg.ReqID)
	r.epilog(ctx, err, arg.ReqLang, arg, reply)
	return nil
}

// GetCoinLegalRate 获取币种法币汇率
func (r *RPC) GetCoinLegalRate(ctx context.Context, arg *define.Arg, reply *define.Reply) error {
	var err error
	reply.Data, err = service.GetCoinLegalRate(arg.ReqID)
	r.epilog(ctx, err, arg.ReqLang, arg, reply)
	return nil
}
