/*
	内部调用rpc接口
*/

package controller

import (
	"context"

	"go.uber.org/zap"
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/proto"
	"spot/pkg/core/service"
)

// EntrustOpen  去除前置校验的rpc开仓入口，供后台调用
func (r *RPC) EntrustOpen(ctx context.Context, arg *proto.OrderOpenArgs, reply *define.Reply) error {
	var err error
	if arg.PlanOrderId > 0 {
		service.PlanTrigger(arg)
		return nil
	}
	rsp, err := service.EntrustOpen(arg)
	r.epilog(ctx, err, arg.<PERSON>, arg, reply)
	reply.Data = rsp
	return nil
}

//后台委托撤销
func (r *RPC) InnerEntrustCancel(ctx context.Context, arg *proto.OrderCancelArgs, reply *define.Reply) error {
	err := service.EntrustCancel(arg)
	r.epilog(ctx, err, arg.<PERSON>, arg, reply)
	return nil
}

// EntrustOrderCancel 订单撤销委托 (接口包装）
func (r *RPC) EntrustOrderCancel(ctx context.Context, arg *define.Arg, reply *define.Reply) error {
	var err error
	request := new(proto.OrderCancelArgs)
	err = json.Unmarshal(arg.Data, request)
	if err != nil {
		log.Error("EntrustReverseOpen json unmarshal fail",
			zap.Int64("reqID", arg.ReqID), zap.Error(err))
		r.epilog(ctx, define.ErrMsgParam, arg.ReqLang, arg, reply)
		return nil
	}
	request.RequestId = arg.ReqID
	request.Lang = arg.ReqLang
	request.UserId = arg.UserID

	err = service.EntrustCancel(request)
	r.epilog(ctx, err, arg.ReqLang, arg, reply)
	return nil
}
