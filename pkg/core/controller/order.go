package controller

import (
	"context"
	"spot/pkg/core/model"

	"go.uber.org/zap"
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/proto"
	"spot/pkg/core/service"
)

// EntrustOrderCurrentList 当前委托
func (r *RPC) EntrustOrderCurrentList(ctx context.Context, arg *define.Arg, reply *define.Reply) error {
	request := new(proto.UserSymbol)
	err := json.Unmarshal(arg.Data, request)
	if err != nil {
		log.Error("EntrustOrderCurrentList json unmarshal error",
			zap.Int64("reqID", arg.ReqID), zap.ByteString("data", arg.Data), zap.Error(err))
		r.epilog(ctx, define.ErrMsgParam, arg.ReqLang, arg, reply)
		return nil
	}
	request.UserID = arg.UserID

	reply.Data, err = service.EntrustOrderCurrentList(ctx, arg.ReqArg, request)
	r.epilog(ctx, err, arg.ReqLang, arg, reply)
	return nil
}

// EntrustOrderCurrentPlanList 当前委托(计划单)
func (r *RPC) EntrustOrderCurrentPlanList(ctx context.Context, arg *define.Arg, reply *define.Reply) error {
	request := new(proto.UserSymbol)
	err := json.Unmarshal(arg.Data, request)
	if err != nil {
		log.Error("EntrustOrderCurrentPlanList json unmarshal error",
			zap.Int64("reqID", arg.ReqID), zap.ByteString("data", arg.Data), zap.Error(err))
		r.epilog(ctx, define.ErrMsgParam, arg.ReqLang, arg, reply)
		return nil
	}
	request.UserID = arg.UserID

	reply.Data, err = service.EntrustOrderCurrentPlanList(ctx, arg.ReqArg, request)
	r.epilog(ctx, err, arg.ReqLang, arg, reply)
	return nil
}

// EntrustOrderHistoryList 历史委托
func (r *RPC) EntrustOrderHistoryList(ctx context.Context, arg *define.Arg, reply *define.Reply) error {
	request := new(proto.UserSymbol)
	err := json.Unmarshal(arg.Data, request)
	if err != nil {
		log.Error("EntrustOrderHistoryList json unmarshal error",
			zap.Int64("reqID", arg.ReqID), zap.ByteString("data", arg.Data), zap.Error(err))
		r.epilog(ctx, define.ErrMsgParam, arg.ReqLang, arg, reply)
		return nil
	}
	request.UserID = arg.UserID

	reply.Data, err = service.EntrustOrderHistoryList(ctx, arg.ReqArg, request)
	r.epilog(ctx, err, arg.ReqLang, arg, reply)
	return nil
}

// EntrustOrderHistoryPlanList 历史委托(计划单)
func (r *RPC) EntrustOrderHistoryPlanList(ctx context.Context, arg *define.Arg, reply *define.Reply) error {
	request := new(proto.UserSymbol)
	err := json.Unmarshal(arg.Data, request)
	if err != nil {
		log.Error("EntrustOrderHistoryPlanList json unmarshal error",
			zap.Int64("reqID", arg.ReqID), zap.ByteString("data", arg.Data), zap.Error(err))
		r.epilog(ctx, define.ErrMsgParam, arg.ReqLang, arg, reply)
		return nil
	}
	request.UserID = arg.UserID

	reply.Data, err = service.EntrustOrderHistoryPlanList(ctx, arg.ReqArg, request)
	r.epilog(ctx, err, arg.ReqLang, arg, reply)
	return nil
}

// EntrustOrderTradeDetail 成交明细
func (r *RPC) EntrustOrderTradeDetail(ctx context.Context, arg *define.Arg, reply *define.Reply) error {
	var err error
	request := new(model.TradeDetailArg)
	if len(arg.Data) > 0 {
		err = json.Unmarshal(arg.Data, request)
		if err != nil {
			log.Error("EntrustOrderTradeDetail json unmarshal error",
				zap.Int64("reqID", arg.ReqID), zap.ByteString("data", arg.Data), zap.Error(err))
			r.epilog(ctx, define.ErrMsgParam, arg.ReqLang, arg, reply)
			return nil
		}
	}
	request.UserID = arg.UserID

	if request.LimitDays == 0 {
		request.LimitDays = define.ApiHistoryBillMaxDays
	}
	if request.LimitDays != define.ApiHistoryBillWeekDays && request.LimitDays != define.ApiHistoryBillMaxDays {
		log.Error("EntrustOrderTradeDetail nonsupport bill days",
			zap.Int64("reqID", arg.ReqID), zap.Int("days", request.LimitDays))
		r.epilog(ctx, define.ErrMsgParam, arg.ReqLang, arg, reply)
		return nil
	}

	reply.Data, err = service.EntrustOrderTradeDetail(ctx, arg.ReqArg, request)
	r.epilog(ctx, err, arg.ReqLang, arg, reply)
	return nil
}
