package controller

import (
	"context"
	"runtime"
	"time"

	"go.uber.org/zap"
	"spot/libs/conf"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/xplugins"
	"spot/libs/xrpc"
)

type RPC struct {
	s *xrpc.Server
}

var rpcServer *RPC

func InitRPC() (err error) {
	rpcServer = new(RPC)
	option := &xrpc.ServerOption{
		ServiceName:    define.ServerNameCore,
		NodeName:       conf.LocalName(),
		Rev:            rpcServer,
		Address:        conf.RPCAddr(),
		BasePath:       define.BasePath,
		Discovery:      conf.Discovery(),
		UpdateInterval: 10 * time.Second,
		Meta:           "",
		AuthFunc:       nil,
	}
	s, err := xrpc.NewXServer(option)
	if err != nil {
		log.Errorf("init Rpc server fail,%v", err)
		return
	}
	rpcServer.s = s
	s.RegisterOnShutdown(func() {})
	go s.Start()
	return
}

func ShutdownServers() {
	log.Info("core service begin stop")
	if rpcServer != nil {
		ctx, cancel := context.WithDeadline(context.Background(), time.Now().Add(time.Second))
		defer cancel()
		_ = rpcServer.s.Shutdown(ctx)
	}
	log.Info("core service stop")
}

func (r *RPC) epilog(ctx context.Context, err error, lang define.ReqLang, arg interface{}, reply *define.Reply) {
	if err != nil {
		switch e := err.(type) {
		case *define.ReplyError:
			if e != nil {
				reply.Ret = e.Code
				reply.Msg = e.LangErrMsg(lang)
			}
		default:
			reply.Ret = define.ErrCodeBusy
			reply.Msg = define.ErrMsgBusy.LangErrMsg(lang)
		}
	}

	sv := xplugins.GetSpanValues(ctx)
	pc, _, _, _ := runtime.Caller(1)
	fn := runtime.FuncForPC(pc).Name()

	switch args := arg.(type) {
	case *define.Arg:
		var data []byte
		if len(args.Data) > define.APILogMaxLength {
			data = args.Data[:define.APILogMaxLength]
		} else {
			data = args.Data
		}

		log.Info("epilog output",
			zap.String("method", fn),
			zap.Int64("reqID", args.ReqID),
			zap.Int64("userID", args.UserID),
			zap.String("inTime", sv.GetRequestTime()),
			zap.String("outTime", time.Now().Format(define.TimeFormatMillisecond)),
			zap.String("prevNode", sv.GetRequestNode()),
			zap.String("curNode", sv.GetResponseNode()),
			zap.ByteString("param", data),
			zap.Int("result", reply.Ret),
			zap.Error(err))
	default:
		log.Info("epilog output",
			zap.String("method", fn),
			zap.Any("param", arg),
			zap.String("inTime", sv.GetRequestTime()),
			zap.String("outTime", time.Now().Format(define.TimeFormatMillisecond)),
			zap.String("prevNode", sv.GetRequestNode()),
			zap.String("curNode", sv.GetResponseNode()),
			zap.Int("result", reply.Ret),
			zap.Error(err))
	}
}
