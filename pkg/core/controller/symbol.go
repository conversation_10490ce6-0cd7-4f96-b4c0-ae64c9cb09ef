package controller

import (
	"context"
	"strings"

	"go.uber.org/zap"
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/proto"
	"spot/pkg/core/service"
)

// SymbolList 交易对列表
func (r *RPC) SymbolList(ctx context.Context, arg *define.Arg, reply *define.Reply) error {
	var err error
	reply.Data, err = service.GetSymbolListHandler(arg.ReqID)
	r.epilog(ctx, err, arg.ReqLang, arg, reply)
	return nil
}

// SymbolDetail 交易对详情
func (r *RPC) SymbolDetail(ctx context.Context, arg *define.Arg, reply *define.Reply) error {
	request := new(proto.SymbolArg)
	err := json.Unmarshal(arg.Data, request)
	if err != nil {
		log.Error("SymbolDetail param error", zap.Int64("reqID", arg.ReqID), zap.Error(err))
		r.epilog(ctx, define.ErrMsgParam, arg.ReqLang, arg, reply)
		return nil
	}

	reply.Data, err = service.SymbolDetailHandler(arg.ReqID, &arg.TokenPayload, request)
	r.epilog(ctx, err, arg.ReqLang, arg, reply)
	return nil
}

// SymbolKline 交易对K线
func (r *RPC) SymbolKline(ctx context.Context, arg *define.Arg, reply *define.Reply) error {
	request := new(proto.ApiKLineArg)
	err := json.Unmarshal(arg.Data, request)
	if err != nil {
		log.Error("SymbolKline param error", zap.Int64("reqID", arg.ReqID), zap.Error(err))
		r.epilog(ctx, define.ErrMsgParam, arg.ReqLang, arg, reply)
		return nil
	}
	request.Duration = strings.ToLower(request.Duration)

	reply.Data, err = service.SymbolKlineHandler(arg.ReqID, request)
	r.epilog(ctx, err, arg.ReqLang, arg, reply)
	return nil
}

// GetSummary 获取成交摘要
func (r *RPC) GetSummary(ctx context.Context, arg *define.Arg, reply *define.Reply) error {
	var err error
	reply.Data, err = service.GetSummary(arg.ReqID)
	r.epilog(ctx, err, arg.ReqLang, arg, reply)
	return nil
}

// SymbolMarkets 全部交易对行情
func (r *RPC) SymbolMarkets(ctx context.Context, arg *define.Arg, reply *define.Reply) error {
	var err error
	reply.Data, err = service.GetSymbolMarkets(ctx, arg.ReqID)
	r.epilog(ctx, err, arg.ReqLang, arg, reply)
	return nil
}
