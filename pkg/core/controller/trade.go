package controller

import (
	"context"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/proto"
	"spot/pkg/core/model"
	"spot/pkg/core/service"
)

// Place 下单
func (r *RPC) Place(ctx context.Context, arg *define.Arg, reply *define.Reply) error {
	var err error
	args := new(proto.ApiPlaceArg)
	err = json.Unmarshal(arg.Data, args)
	if err != nil {
		log.Error("Place json unmarshal fail",
			zap.Int64("reqID", arg.ReqID), zap.Error(err))
		r.epilog(ctx, define.ErrMsgParam, arg.ReqLang, arg, reply)
		return nil
	}

	request := args.Convert()
	request.RequestId = arg.ReqID
	request.IpAddress = arg.ReqIP
	request.DeviceId = arg.DeviceID
	request.UserId = arg.UserID
	request.APPID = int(arg.ReqOs)
	request.OrderType = define.OrderTypeUserTrade
	request.Lang = arg.ReqLang
	log.Infof("开仓,arg:+v%+v", *request)

	// 判断用户是否可以下单
	if !arg.EnableTrade || arg.ForbidOpenClose == define.ForbidOpenCloseTypeOpenLimit {
		log.Error("Place can not to trade",
			zap.Int64("reqID", arg.ReqID), zap.Int64("userID", arg.UserID))
		r.epilog(ctx, define.ErrMsgTradeNotAllowed, arg.ReqLang, arg, reply)
		return nil
	}

	tc := cache.GetSystemTradeConf(arg.PlatformID)
	if tc.Opening {
		log.Error("Place system place closed", zap.Int64("reqID", arg.ReqID))
		r.epilog(ctx, define.ErrMsgBusy, arg.ReqLang, arg, reply)
		return nil
	}

	if cache.IsNcrExist(request.UserId, request.NToken) {
		log.Error("OrderOpenPosition resubmit", zap.Int64("reqID", arg.ReqID))
		r.epilog(ctx, define.ErrMsgResubmit, arg.ReqLang, arg, reply)
		return nil
	}

	//判断交易方向是否有效
	if request.Side != define.OrderBuy && request.Side != define.OrderSell {
		log.Error("Place side error", zap.Int64("reqID", arg.ReqID))
		r.epilog(ctx, define.ErrMsgParam, arg.ReqLang, arg, reply)
		return nil
	}

	//判断下单量是否合法
	if request.Amount.LessThanOrEqual(decimal.Zero) && request.Money.LessThanOrEqual(decimal.Zero) {
		log.Error("Place amount error", zap.Int64("reqID", arg.ReqID))
		r.epilog(ctx, define.ErrMsgParam, arg.ReqLang, arg, reply)
		return nil
	}

	//判断下单模式
	if request.Mode == 0 {
		request.Mode = define.EntrustModeBest5Level
	}
	if request.Mode != define.EntrustModeDefault && request.Mode != define.EntrustModeBest3Level && request.Mode != define.EntrustModeBest5Level {
		log.Error("Place mode error", zap.Int64("reqID", arg.ReqID))
		r.epilog(ctx, define.ErrMsgParam, arg.ReqLang, arg, reply)
		return nil
	}

	if request.NToken != "" {
		_ = cache.SetNcr(request.UserId, request.NToken)
	}

	_, err = service.EntrustOpen(request)
	if err != nil {
		log.Error("Place 开仓失败", zap.Int64("reqID", arg.ReqID), zap.Error(err))
		r.epilog(ctx, err, arg.ReqLang, arg, reply)
		if request.NToken != "" {
			cache.DelNcr(request.UserId, request.NToken)
		}
		return nil
	}
	r.epilog(ctx, nil, arg.ReqLang, arg, reply)
	return nil
}

// EntrustCancel 撤销
func (r *RPC) EntrustCancel(ctx context.Context, arg *define.Arg, reply *define.Reply) error {
	var request model.OrderIDArg
	err := json.Unmarshal(arg.Data, &request)
	if err != nil {
		log.Error("EntrustReverseOpen json unmarshal fail",
			zap.Int64("reqID", arg.ReqID), zap.Error(err))
		r.epilog(ctx, define.ErrMsgParam, arg.ReqLang, arg, reply)
		return nil
	}
	err = service.EntrustCancel(&proto.OrderCancelArgs{
		RequestId: arg.ReqID,
		Lang:      arg.ReqLang,
		UserId:    arg.UserID,
		ID:        request.OrderID,
	})
	r.epilog(ctx, err, arg.ReqLang, arg, reply)
	return nil
}

// EntrustOrderBatchCancel 批量撤销 (接口包装）
func (r *RPC) EntrustOrderBatchCancel(ctx context.Context, arg *define.Arg, reply *define.Reply) error {
	var err error
	request := new(proto.OrderBatchCancelArgs)
	err = json.Unmarshal(arg.Data, request)
	if err != nil {
		log.Error("EntrustOrderBatchCancel json unmarshal fail",
			zap.Int64("reqID", arg.ReqID), zap.Error(err))
		r.epilog(ctx, define.ErrMsgParam, arg.ReqLang, arg, reply)
		return nil
	}
	request.RequestId = arg.ReqID
	request.Lang = arg.ReqLang
	request.UserId = arg.UserID

	err = service.EntrustBatchCancel(request)
	r.epilog(ctx, err, arg.ReqLang, arg, reply)
	return nil
}

// PlanPlace 计划单下单
func (r *RPC) PlanPlace(ctx context.Context, arg *define.Arg, reply *define.Reply) error {
	request := new(model.OrderPlanArgs)
	err := json.Unmarshal(arg.Data, request)
	if err != nil {
		log.Error("PlanPlace json unmarshal error",
			zap.Int64("reqID", arg.ReqID), zap.ByteString("param", arg.Data), zap.Error(err))
		r.epilog(ctx, define.ErrMsgParam, arg.ReqLang, arg, reply)
		return nil
	}

	// 判断用户是否可以交易
	if !arg.EnableTrade || arg.ForbidOpenClose == define.ForbidOpenCloseTypeOpenLimit {
		log.Error("PlanPlace can not to trade", zap.Int64("reqID", arg.ReqID), zap.Int64("userID", arg.UserID))
		r.epilog(ctx, define.ErrMsgTradeNotAllowed, arg.ReqLang, arg, reply)
		return nil
	}

	tc := cache.GetSystemTradeConf(arg.PlatformID)
	if tc.Plans {
		log.Error("PlanPlace system plan place closed", zap.Int64("reqID", arg.ReqID), zap.Any("arg", arg))
		r.epilog(ctx, define.ErrMsgBusy, arg.ReqLang, arg, reply)
		return nil
	}

	//判断交易方向是否有效
	if request.Side != define.OrderBuy && request.Side != define.OrderSell {
		log.Error("PlanPlace param error", zap.Int64("reqID", arg.ReqID), zap.Any("request", request))
		r.epilog(ctx, define.ErrMsgParam, arg.ReqLang, arg, reply)
		return nil
	}

	// 只支持限价触发
	if request.EntrustType != define.EntrustTypeLimit {
		r.epilog(ctx, define.ErrMsgParam, arg.ReqLang, arg, reply)
		return nil
	}

	//判断下单模式,市价单判断委托模式
	//if request.EntrustType == define.EntrustTypeMarket {
	//	if request.Mode == 0 {
	//		request.Mode = define.EntrustModeBest5Level
	//	}
	//	if request.Mode != define.EntrustModeDefault && request.Mode != define.EntrustModeBest3Level && request.Mode != define.EntrustModeBest5Level {
	//		r.epilog(ctx, define.ErrMsgParam, arg.ReqLang, arg, reply)
	//		return nil
	//	}
	//}

	err = service.PlanPlaceHandler(&arg.ReqArg, &arg.TokenPayload, request)
	r.epilog(ctx, err, arg.ReqLang, arg, reply)
	return nil
}

// PlanCancel 计划单撤单
func (r *RPC) PlanCancel(ctx context.Context, arg *define.Arg, reply *define.Reply) error {
	request := new(model.OrderIDArg)
	err := json.Unmarshal(arg.Data, request)
	if err != nil {
		log.Error("PlanCancel json unmarshal error",
			zap.Int64("reqID", arg.ReqID), zap.ByteString("param", arg.Data), zap.Error(err))
		r.epilog(ctx, define.ErrMsgParam, arg.ReqLang, arg, reply)
		return nil
	}
	request.UserID = arg.UserID

	err = service.PlanCancelHandler(&arg.ReqArg, request)
	r.epilog(ctx, err, arg.ReqLang, arg, reply)
	return nil
}
