package db

import (
	"database/sql"

	"go.uber.org/zap"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
)

func GetKlineNodesForTradingView(request *proto.ApiKLineArg) ([]proto.KLine, error) {
	// 应该已经检查过Duration参数,防止sql注入
	dbName := define.KLineDbNamePrefix + request.Duration
	query := "select id, contract_code, start_time, end_time, open_price, close_price, high_price, low_price, volume, trade_value from " + dbName

	// 先查询第一个点的数据
	str := query + " where contract_code = ? and start_time >= ? and end_time <= ? order by start_time asc limit ?"
	rows, err := database.DefaultDB().Queryx(str, request.Symbol, request.StartTime, request.EndTime, define.ApiKLineMaxCount)
	if err != nil {
		log.Error("GetKlineNodesForTradingView db query error", zap.Any("request", request), zap.Error(err))
		return nil, err
	}

	list := make([]proto.KLine, 1, define.ApiKLineMaxCount)
	defer rows.Close()
	first, lack := true, true
	for rows.Next() {
		var node proto.KLine
		err = rows.StructScan(&node)
		if err != nil {
			log.Error("GetKlineNodesForTradingView db scan error", zap.Any("request", request), zap.Error(err))
			return nil, err
		}
		if first {
			if node.StartTime == request.StartTime {
				// 第一个点是请求的第一个点,不需要向前取点进行补全
				lack = false
			}
			first = false
		}
		list = append(list, node)
	}

	if lack {
		str = query + " where contract_code = ? and start_time < ? order by start_time desc limit 1"
		err = database.DefaultDB().Get(&list[0], str, request.Symbol, request.StartTime)
		if err != nil {
			if err == sql.ErrNoRows {
				return list[1:], nil
			}
			log.Error("GetKlineNodesForTradingView db scan", zap.Any("request", request), zap.Error(err))
			return nil, err
		}
		list[0].StartTime = request.StartTime
		list[0].EndTime = request.StartTime + define.DurationStr2Duration(request.Duration)
	} else {
		list = list[1:]
	}
	return list, nil
}
