package db

import (
	"context"
	"time"

	"spot/libs/database"
	"spot/libs/define"
	"spot/pkg/core/model"
)

const (
	_getEntrustOrderCurrentList            = "SELECT id AS order_id, contract_code AS 'symbol', base_coin_name AS 'coin_name', coin_name AS 'market_name', side, `mode`, state, entrust_type, price AS 'entrust_price', money AS 'entrust_value', volume AS 'entrust_volume', trade_volume, cost_asset AS 'trade_value', create_time, update_time FROM tb_order WHERE user_id=? AND IF(''=?, TRUE, contract_code=?) AND state IN(?, ?) AND entrust_type=? ORDER BY id DESC LIMIT ?"
	_getEntrustOrderCurrentPlanList        = "SELECT plan_order_id AS order_id, contract_code AS 'symbol', coin_name, market_name, side, `mode` AS 'entrust_mode', `status` AS 'state', `condition`, entrust_type, trigger_price, entrust_price, money AS 'entrust_value', amount AS 'entrust_volume', create_time FROM tb_plan_order WHERE user_id=? AND IF(''=?, TRUE, contract_code=?) AND `status` = ? ORDER BY plan_order_id DESC LIMIT ?"
	_getEntrustOrderHistoryList            = "SELECT id AS 'order_id', contract_code AS 'symbol', base_coin_name AS 'coin_name', coin_name AS 'market_name', side, `mode`, state, entrust_type, price AS entrust_price, trade_price, money AS 'entrust_value', volume AS 'entrust_volume', trade_volume, cost_asset AS 'trade_value', cost_fee, create_time, update_time FROM tb_order_history WHERE user_id=? AND IF(''=?, TRUE, contract_code=?) AND update_time BETWEEN ? AND ? ORDER BY update_time DESC LIMIT ?"
	_getEntrustOrderHistoryPlanListForPlan = "SELECT plan_order_id AS 'order_id', contract_code AS 'symbol', coin_name, market_name, side, `mode` AS 'entrust_mode', `status` AS state, `condition`, entrust_type, trigger_price, entrust_price, money AS 'entrust_value', amount AS 'entrust_volume', create_time, IF(`status`=?, cancel_time, order_time) AS 'update_time' FROM tb_plan_order WHERE user_id=? AND IF(''=?, TRUE, contract_code=?) AND `status` IN(?, ?, ?) AND create_time BETWEEN ? AND ? ORDER BY order_time DESC LIMIT ?"
	_getEntrustOrderTradeDetail            = "SELECT id AS 'trade_id', contract_code AS 'symbol', currency_name AS 'coin_name', price_currency_name AS 'market_name', side, price, volume AS 'trade_volume', trade_amount AS 'trade_value', commission, trade_time FROM tb_trade WHERE user_id=? AND IF(0=?, TRUE, entrust_order_id=?) AND IF(''=?, TRUE, contract_code=?) AND IF(''=?, TRUE, side=?) AND IF(0!=?, TRUE, trade_time BETWEEN ? AND ?) ORDER BY trade_time DESC LIMIT ?, ?"
)

func GetEntrustOrderCurrentList(ctx context.Context, userID int64, symbol string) ([]model.CurrentEntrustOrder, error) {
	list := make([]model.CurrentEntrustOrder, 0, define.MaxPageCount)
	err := database.DefaultDB().SelectContext(ctx, &list, _getEntrustOrderCurrentList, userID, symbol, symbol, define.OrderStatusDefault, define.OrderStatusPart, define.EntrustTypeLimit, define.MaxPageCount)
	return list, err
}

func GetEntrustOrderCurrentPlanList(ctx context.Context, userID int64, symbol string) ([]model.CurrentPlanEntrustOrder, error) {
	list := make([]model.CurrentPlanEntrustOrder, 0, define.MaxPageCount)
	err := database.DefaultDB().SelectContext(ctx, &list, _getEntrustOrderCurrentPlanList, userID, symbol, symbol, define.OrderConditionNotTrigger, define.MaxPageCount)
	return list, err
}

func GetEntrustOrderHistoryList(ctx context.Context, userID int64, symbol string, start, end time.Time) ([]model.HistoryEntrustOrder, error) {
	list := make([]model.HistoryEntrustOrder, 0, define.MaxPageCount)
	err := database.DefaultDB().SelectContext(ctx, &list, _getEntrustOrderHistoryList, userID, symbol, symbol, start, end, define.MaxPageCount)
	return list, err
}

func GetEntrustOrderHistoryPlanList(ctx context.Context, userID int64, symbol string, start, end time.Time) ([]model.HistoryPlanEntrustOrder, error) {
	list := make([]model.HistoryPlanEntrustOrder, 0, define.MaxPageCount)
	err := database.DefaultDB().SelectContext(ctx, &list, _getEntrustOrderHistoryPlanListForPlan, define.OrderCondCancel, userID, symbol, symbol, define.OrderCondCancel, define.OrderCondHadTrigger, define.OrderCondFailed, start, end, define.MaxPageCount)
	return list, err
}

func GetEntrustOrderTradeDetail(ctx context.Context, userID, orderID int64, symbol, side string, start, end time.Time, page define.Page) ([]model.TradeDetail, error) {
	list := make([]model.TradeDetail, 0, define.MaxPageCount)
	err := database.DefaultDB().SelectContext(ctx, &list, _getEntrustOrderTradeDetail, userID, orderID, orderID, symbol, symbol, side, side, orderID, start, end, page.Page, page.Count)
	return list, err
}
