package main

import (
	"context"
	"fmt"
	"os"
	"spot/libs/xrpcclient/order_rpc"
	"spot/libs/xrpcclient/wallet_rpc"

	"spot/libs/cache"
	"spot/libs/conf"
	"spot/libs/database"
	"spot/libs/log"
	"spot/libs/msg"
	"spot/libs/pprof"
	"spot/libs/xrpcclient/user_rpc"
	"spot/pkg/core/controller"
	"spot/pkg/core/server"
	"spot/pkg/core/service"
)

var (
	gitHash   string
	gitDigest string
	gitBranch string
	buildUser string
	buildTime string
	goVersion string
)

func main() {
	args := os.Args
	if len(args) == 2 && (args[1] == "--version" || args[1] == "-v") {
		fmt.Printf("Git Branch: %s \n", gitBranch)
		fmt.Printf("Git Commit Hash: %s \n", gitHash)
		fmt.Printf("Git Commit Digest: %s \n", gitDigest)
		fmt.Printf("Build User: %s \n", buildUser)
		fmt.Printf("Build TimeStamp: %s \n", buildTime)
		fmt.Printf("GoLang Version: %s \n", goVersion)
		return
	}

	// 初始化配置文件
	conf.Init()

	// 初始化日志
	log.InitLogger(conf.LogFile(), conf.LogLevel(), conf.LogOnlyFile())

	// 初始化短信
	msg.InitNewMsg(conf.DebugEnv(), conf.MsgServerURL(), conf.MsgServerAppID(), conf.MsgServerSecretKey())

	//初始化user
	user_rpc.InitUserClient(context.Background())
	defer user_rpc.Close()
	//初始化order服务
	order_rpc.InitClient(conf.Discovery(), conf.LocalName())

	// 初始化wallet
	wallet_rpc.InitWalletClient(context.Background())
	defer wallet_rpc.Close()

	// 初始化缓存
	cache.InitDefaultRedisConn()
	if conf.IsSimulate() {
		cache.InitExtraRedisConn()
	}

	// 初始化数据库
	database.InitDefaultDB()

	//int message queue
	service.InitMessageQueue()

	// 初始化rpc服务
	if err := controller.InitRPC(); err != nil {
		log.Fatalf("init rpc server failed, err:%v", err)
	}

	//开发环境下执行测试
	if conf.IsDev() {
		//time.Sleep(2*time.Second)
		//log.Infof("开始测试。。。。")
		////条件单触发
		//service.PlanTrigger(&proto.ConditionOrder{
		//	PlatformID:      1,
		//	PlanOrderId:     238138157831299072,
		//	TriggerPrice:    90000,
		//})
		//service.TestTriggeerLimit(21,2)
		//service.ForceTrigger(&proto.UserPosition{UserId: 164820419855511077, Id: 248045332921851905}, 0, 0,0)
		//handler.TestSetStop()
		//service.Test()
		//handler.TestSe
		//tStop()
		//handler.TestTradeList()
		//handler.TestHold()
		//service.TestUserContractTrigger(7146681, "EOSUSDT")
	}

	// 初始化服务监控
	pprof.Init(conf.PprofAddr())

	// 监听系统信号
	server.InitSignal()
}
