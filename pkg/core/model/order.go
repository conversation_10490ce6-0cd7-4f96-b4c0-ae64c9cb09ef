package model

import (
	"github.com/shopspring/decimal"
	"spot/libs/alias"
)

type CurrentEntrustOrderReply struct {
	List []CurrentEntrustOrder `json:"list"` // 列表
}

type CurrentEntrustOrder struct {
	OrderID       string          `db:"order_id" json:"order_id"`             // 订单id
	Symbol        string          `db:"symbol" json:"symbol"`                 // 交易对
	CoinName      string          `db:"coin_name" json:"coin_name"`           // 币种名
	MarketName    string          `db:"market_name" json:"market_name"`       // 市场名
	Side          string          `db:"side" json:"side"`                     // 买卖方向 B-买 S-卖
	Mode          int             `db:"mode" json:"mode"`                     // 委托模式 0-默认 1-对手价 2-最优3挡 3-最优5挡
	State         int             `db:"state" json:"state"`                   // 成交状态 1-等待成交 100-部分成交
	EntrustType   int             `db:"entrust_type" json:"entrust_type"`     // 委托类型 0-市价 1-限价
	EntrustPrice  decimal.Decimal `db:"entrust_price" json:"entrust_price"`   // 委托价格
	EntrustValue  decimal.Decimal `db:"entrust_value" json:"entrust_value"`   // 委托总额
	EntrustVolume decimal.Decimal `db:"entrust_volume" json:"entrust_volume"` // 委托数量
	TradeVolume   decimal.Decimal `db:"trade_volume" json:"trade_volume"`     // 成交数量
	TradeValue    decimal.Decimal `db:"trade_value" json:"trade_value"`       // 成交额
	CreateTime    alias.UnixTime  `db:"create_time" json:"create_time"`       // 委托时间
	UpdateTime    alias.UnixTime  `db:"update_time" json:"update_time"`       // 更新时间
}

type CurrentPlanEntrustOrderReply struct {
	List []CurrentPlanEntrustOrder `json:"list"` // 列表
}

type CurrentPlanEntrustOrder struct {
	OrderID       string          `db:"order_id" json:"order_id"`             // 订单id
	Symbol        string          `db:"symbol" json:"symbol"`                 // 交易对
	CoinName      string          `db:"coin_name" json:"coin_name"`           // 币种名
	MarketName    string          `db:"market_name" json:"market_name"`       // 市场名
	Side          string          `db:"side" json:"side"`                     // 买卖方向 B-买 S-卖
	State         int             `db:"state" json:"state"`                   // 状态 0-已撤销 1-未触发 2-已触发 3-触发失败
	Condition     int             `db:"condition" json:"condition"`           // 触发条件 1 >= 2 <=
	EntrustMode   int             `db:"entrust_mode" json:"entrust_mode"`     // 执行类型 0-默认 1-对手价 2-最优3挡 3-最优5挡
	EntrustType   int             `db:"entrust_type" json:"entrust_type"`     // 委托类型 0-市价 1-限价
	TriggerPrice  decimal.Decimal `db:"trigger_price" json:"trigger_price"`   // 触发价格
	EntrustPrice  decimal.Decimal `db:"entrust_price" json:"entrust_price"`   // 执行价格
	EntrustValue  decimal.Decimal `db:"entrust_value" json:"entrust_value"`   // 委托总额
	EntrustVolume decimal.Decimal `db:"entrust_volume" json:"entrust_volume"` // 委托数量
	CreateTime    alias.UnixTime  `db:"create_time" json:"create_time"`       // 委托时间
}

type HistoryEntrustOrderReply struct {
	List []HistoryEntrustOrder `json:"list"` // 列表
}

type HistoryEntrustOrder struct {
	OrderID       string          `db:"order_id" json:"order_id"`             // 订单id
	Symbol        string          `db:"symbol" json:"symbol"`                 // 交易对
	CoinName      string          `db:"coin_name" json:"coin_name"`           // 币种名
	MarketName    string          `db:"market_name" json:"market_name"`       // 市场名
	Side          string          `db:"side" json:"side"`                     // 买卖方向 B-买 S-卖
	Mode          int             `db:"mode" json:"mode"`                     // 委托模式 0-默认 1-对手价 2-最优3挡 3-最优5挡
	State         int             `db:"state" json:"state"`                   // 成交状态 200-全部成交 201-未成已撤 202-部分成交已撤
	EntrustType   int             `db:"entrust_type" json:"entrust_type"`     // 委托类型 0-市价 1-限价
	EntrustPrice  decimal.Decimal `db:"entrust_price" json:"entrust_price"`   // 委托价格
	TradePrice    decimal.Decimal `db:"trade_price" json:"trade_price"`       // 成交均价
	EntrustVolume decimal.Decimal `db:"entrust_volume" json:"entrust_volume"` // 委托数量
	EntrustValue  decimal.Decimal `db:"entrust_value" json:"entrust_value"`   // 委托总额
	TradeVolume   decimal.Decimal `db:"trade_volume" json:"trade_volume"`     // 成交数量
	TradeValue    decimal.Decimal `db:"trade_value" json:"trade_value"`       // 成交额
	CostFee       decimal.Decimal `db:"cost_fee" json:"cost_fee"`             // 交易手续费
	CreateTime    alias.UnixTime  `db:"create_time" json:"create_time"`       // 委托时间
	UpdateTime    alias.UnixTime  `db:"update_time" json:"update_time"`       // 更新时间
}

type HistoryPlanEntrustOrderReply struct {
	List []HistoryPlanEntrustOrder `json:"list"` // 列表
}

type HistoryPlanEntrustOrder struct {
	OrderID       string          `db:"order_id" json:"order_id"`             // 订单id
	Symbol        string          `db:"symbol" json:"symbol"`                 // 交易对
	CoinName      string          `db:"coin_name" json:"coin_name"`           // 币种名
	MarketName    string          `db:"market_name" json:"market_name"`       // 市场名
	Side          string          `db:"side" json:"side"`                     // 买卖方向 B-买 S-卖
	State         int             `db:"state" json:"state"`                   // 状态 0-已撤销 1-未触发 2-已触发 3-触发失败
	Condition     int             `db:"condition" json:"condition"`           // 触发条件 1 >= 2 <=
	EntrustMode   int             `db:"entrust_mode" json:"entrust_mode"`     // 执行类型 0-默认 1-对手价 2-最优3挡 3-最优5挡
	EntrustType   int             `db:"entrust_type" json:"entrust_type"`     // 委托类型 0-市价 1-限价
	TriggerPrice  decimal.Decimal `db:"trigger_price" json:"trigger_price"`   // 触发价格
	EntrustPrice  decimal.Decimal `db:"entrust_price" json:"entrust_price"`   // 执行价格
	EntrustValue  decimal.Decimal `db:"entrust_value" json:"entrust_value"`   // 委托总额
	EntrustVolume decimal.Decimal `db:"entrust_volume" json:"entrust_volume"` // 委托数量
	CreateTime    alias.UnixTime  `db:"create_time" json:"create_time"`       // 委托时间
	UpdateTime    alias.UnixTime  `db:"update_time" json:"update_time"`       // 更新时间
}

type TradeDetailArg struct {
	UserID    int64  `json:"user_id"`    // 用户id
	OrderID   string `json:"order_id"`   // 订单id
	Symbol    string `json:"symbol"`     // 交易对
	Side      string `json:"side"`       // 方向 B-买入 S-卖出
	LimitDays int    `json:"limit_days"` // 数据限制天数 当前仅支持7天或30天
}

type TradeDetailReply struct {
	List []TradeDetail `json:"list"` // 列表
}

type TradeDetail struct {
	TradeID     string          `db:"trade_id" json:"trade_id"`         // 成交id
	Symbol      string          `db:"symbol" json:"symbol"`             // 交易对
	CoinName    string          `db:"coin_name" json:"coin_name"`       // 币种名
	MarketName  string          `db:"market_name" json:"market_name"`   // 市场名
	Side        string          `db:"side" json:"side"`                 // 买卖方向 B-买 S-卖
	Price       decimal.Decimal `db:"price" json:"price"`               // 成交价
	TradeVolume decimal.Decimal `db:"trade_volume" json:"trade_volume"` // 成交数量
	TradeValue  decimal.Decimal `db:"trade_value" json:"trade_value"`   // 成交金额
	Commission  decimal.Decimal `db:"commission" json:"commission"`     // 手续费
	TradeTime   alias.UnixTime  `db:"trade_time" json:"trade_time"`     // 成交时间
}
