package model

import (
	"github.com/shopspring/decimal"
)

type OrderPlanArgs struct {
	Symbol          string          `json:"symbol"`           // 交易对
	Side            string          `json:"side"`             // 买卖方向 B-买 S-卖
	Price           decimal.Decimal `json:"price"`            // 触发价
	Amount          decimal.Decimal `json:"amount"`           // 委托币数量
	Money           decimal.Decimal `json:"money"`            // 委托价值
	EntrustPrice    decimal.Decimal `json:"entrust_price"`    // 委托执行价
	EntrustType     int             `json:"entrust_type"`     // 委托类型 0-市价 1-限价
	EntrustStrategy int             `json:"entrust_strategy"` // 委托策略 0-默认 1-fok 2-ioc 3-maker
	Mode            int             `json:"mode"`             // 下单模式 1-对手价 2-最优3挡 3-最优5挡
}

type OrderIDArg struct {
	OrderID string `json:"order_id"`
	UserID  int64  `json:"user_id"`
}

type PlanOrderInitData struct {
	PlanOrderID  int64           `json:"plan_order_id" db:"plan_order_id"`
	UserId       int64           `json:"user_id" db:"user_id"`
	TriggerPrice decimal.Decimal `json:"trigger_price" db:"trigger_price"`
}
