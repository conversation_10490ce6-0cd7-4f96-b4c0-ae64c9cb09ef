package redis

import (
	"strconv"

	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/convert"
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/proto"
	"spot/libs/utils"
)

func GetShareImageResourceFromCache(language define.ReqLang, shareType int, level int8) ([]proto.ShareImage, error) {
	key := utils.StrBuilderBySep(":", define.CacheKeyShareImages, strconv.Itoa(shareType), strconv.Itoa(int(language)), strconv.Itoa(int(level)))
	data, err := cache.DefaultRedisWithDB(define.CacheDBNumber2).LRange(key, 0, -1).Result()
	if err != nil {
		log.Error("GetShareImageResourceFromCache get date error", zap.Uint8("language", uint8(language)), zap.Error(err))
		return nil, err
	}

	list := make([]proto.ShareImage, len(data))
	for i := range data {
		err = json.Unmarshal(convert.Str2Bytes(data[i]), &list[i])
		if err != nil {
			log.Error("GetShareImageResourceFromCache json unmarshal error", zap.Uint8("language", uint8(language)), zap.Error(err))
			return nil, err
		}
	}
	return list, nil
}

func GetShareTextResourceFromCache(language define.ReqLang, shareType int, level int8) ([]proto.ShareText, error) {
	key := utils.StrBuilderBySep(":", define.CacheKeyShareTexts, strconv.Itoa(shareType), strconv.Itoa(int(language)), strconv.Itoa(int(level)))
	data, err := cache.DefaultRedisWithDB(define.CacheDBNumber2).LRange(key, 0, -1).Result()
	if err != nil {
		log.Error("GetShareTextResourceFromCache get date error", zap.Uint8("language", uint8(language)), zap.Error(err))
		return nil, err
	}

	list := make([]proto.ShareText, len(data))
	for i := range data {
		err = json.Unmarshal(convert.Str2Bytes(data[i]), &list[i])
		if err != nil {
			log.Error("GetShareTextResourceFromCache json unmarshal error", zap.Uint8("language", uint8(language)), zap.Error(err))
			return nil, err
		}
	}
	return list, nil
}
