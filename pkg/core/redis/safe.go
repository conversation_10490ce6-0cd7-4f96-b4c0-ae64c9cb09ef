package redis

import (
	"strconv"
	"time"

	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/define"
	"spot/libs/log"
)

func CacheUserNewTotpSecret(userID int64, secret string) error {
	return cache.DefaultRedis().Set(define.CacheKeySafeUserNewTotpSecret+strconv.FormatInt(userID, 10), secret, time.Hour*24).Err()
}

func GetUserNewTotpSecret(userID int64) (string, error) {
	return cache.DefaultRedis().Get(define.CacheKeySafeUserNewTotpSecret + strconv.FormatInt(userID, 10)).Result()
}

func DelUserNewTotpSecret(reqID, userID int64) {
	err := cache.DefaultRedis().Get(define.CacheKeySafeUserNewTotpSecret + strconv.FormatInt(userID, 10)).Err()
	if err != nil {
		log.Error("DelUserNewTotpSecret del failed", zap.Int64("reqID", reqID), zap.Error(err))
	}
}
