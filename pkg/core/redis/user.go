package redis

import (
	"github.com/dgrijalva/jwt-go"
	"spot/libs/convert"
	"spot/libs/proto"
	"strconv"
	"time"

	"github.com/go-redis/redis"
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/utils"
)

// 更新登录用户信息缓存
func UpdateTokenCache(user *define.TokenPayload) {
	key := define.CacheKeyLoginInfo + utils.StrBuilderByUnderCode(strconv.FormatInt(user.UserID, 10), "phone")
	updateTokenCache(key, user)

	key = define.CacheKeyLoginInfo + utils.StrBuilderByUnderCode(strconv.FormatInt(user.UserID, 10), "web")
	updateTokenCache(key, user)
}

func updateTokenCache(key string, user *define.TokenPayload) {
	data, err := cache.DefaultRedis().Get(key).Bytes()
	if err != nil {
		if err != redis.Nil {
			log.Error("updateTokenCache redis get error", zap.String("key", key), zap.Error(err))
		}
		return
	}
	payload := new(define.TokenPayload)
	err = json.Unmarshal(data, payload)
	if err != nil {
		log.Error("updateTokenCache json unmarshal error", zap.String("key", key), zap.Error(err))
		return
	}
	replaceTokenPayload(payload, user)

	data, err = json.Marshal(payload)
	if err != nil {
		log.Error("updateTokenCache json marshal error", zap.String("key", key), zap.Error(err))
		return
	}
	ttl := cache.DefaultRedis().TTL(key).Val()
	err = cache.DefaultRedis().SetXX(key, data, ttl).Err()
	if err != nil {
		log.Error("updateTokenCache redis set error", zap.String("key", key), zap.Error(err))
		return
	}
}

func replaceTokenPayload(old *define.TokenPayload, new *define.TokenPayload) {
	old.UserName = new.UserName
	old.Phone = new.Phone
	old.Email = new.Email
	old.SpareEmail = new.SpareEmail
	old.TotpSecret = new.TotpSecret
	old.EnableWithdraw = new.EnableWithdraw
	old.EnableTrade = new.EnableTrade
	old.ForbidOpenClose = new.ForbidOpenClose
	old.LoginPasswd = new.LoginPasswd
	old.FundPasswd = new.FundPasswd
	old.RestLoginTime = new.RestLoginTime
	old.RestFundTime = new.RestFundTime
	old.Verify = new.Verify
	old.RealName = new.RealName
	old.CardNo = new.CardNo
	old.StaticIdentity = new.StaticIdentity
	old.LastVerifyID = new.LastVerifyID
	old.AreaCode = new.AreaCode
	old.CountryCode = new.CountryCode
	old.Nickname = new.Nickname
	old.NicknameEn = new.NicknameEn
	old.Introduce = new.Introduce
	old.IntroduceEn = new.IntroduceEn
	old.Avatar = new.Avatar
	old.DealerState = new.DealerState
	old.FollowApproved = new.FollowApproved
	old.TradeApproved = new.TradeApproved
	old.LoginVerifyPhone = new.LoginVerifyPhone
	old.LoginVerifyEmail = new.LoginVerifyEmail
	old.LoginVerifyTotp = new.LoginVerifyTotp
	old.TradeVerifyFund = new.TradeVerifyFund
	old.LabelID = new.LabelID
	old.ProfitStyle = new.ProfitStyle
	old.ShowAgentRatio = new.ShowAgentRatio
	old.IsShowDealer = new.IsShowDealer
}

func GenToken(reqID int64, deviceId string, os define.OsType, user *proto.User) (string, error) {
	var (
		err   error
		jti   string
		token string
		exp   time.Duration
	)

	switch os {
	case define.OsAndroid, define.OsIos:
		exp = define.PhoneLoginExpireDate
		jti = utils.StrBuilderByUnderCode(strconv.FormatInt(user.UserID, 10), "phone")
	case define.OsWeb, define.OsH5:
		exp = define.WebLoginExpireDate
		jti = utils.StrBuilderByUnderCode(strconv.FormatInt(user.UserID, 10), "web")
	default:
		return token, define.ErrMsgParam
	}

	payload := ConvertUser2TokenPayload(user)
	payload.CacheID = jti
	payload.LastDevice = deviceId

	tokenArg := jwt.StandardClaims{
		Id:       jti,
		IssuedAt: time.Now().Unix(),
		Subject:  deviceId,
	}
	log.Info("GenToken with arg", zap.Int64("reqID", reqID), zap.String("jti", tokenArg.Id), zap.String("imei", deviceId), zap.Int64("iat", tokenArg.IssuedAt))
	token, err = jwt.NewWithClaims(jwt.SigningMethodHS256, tokenArg).SignedString(convert.Str2Bytes(define.TokenKey))
	if err != nil {
		log.Error("GenToken error", zap.Int64("reqID", reqID), zap.String("jti", tokenArg.Id), zap.String("imei", deviceId), zap.Int64("iat", tokenArg.IssuedAt), zap.Error(err))
		return token, define.ErrMsgBusy
	}

	data, err := json.Marshal(payload)
	if err != nil {
		log.Error("GenToken json marshal error", zap.Int64("reqID", reqID), zap.String("jti", tokenArg.Id), zap.String("imei", deviceId), zap.Int64("iat", tokenArg.IssuedAt), zap.Error(err))
		return token, define.ErrMsgBusy
	}

	err = cache.DefaultRedis().Set(define.CacheKeyLoginInfo+jti, data, exp).Err()
	if err != nil {
		log.Error("GenToken redis error", zap.Int64("reqID", reqID), zap.String("jti", tokenArg.Id), zap.String("imei", deviceId), zap.Int64("iat", tokenArg.IssuedAt), zap.Error(err))
		return token, define.ErrMsgBusy
	}
	return token, nil
}

func ConvertUser2TokenPayload(user *proto.User) *define.TokenPayload {
	return &define.TokenPayload{
		UserID:           user.UserID,
		UserName:         user.UserName,
		Phone:            user.Phone,
		Email:            user.Email,
		SpareEmail:       user.SpareEmail,
		TotpSecret:       user.TotpSecret,
		InviteCode:       user.InviteCode,
		EnableLogin:      user.EnableLogin,
		EnableWithdraw:   user.EnableWithdraw,
		EnableTrade:      user.EnableTrade,
		ForbidOpenClose:  user.ForbidOpenClose,
		LoginPasswd:      user.LoginPasswd,
		FundPasswd:       user.FundPasswd,
		InviteParent:     user.InviteParent,
		RestFundTime:     user.RestFundTime.Unix(),
		RestLoginTime:    user.RestLoginTime.Unix(),
		Verify:           user.Verify,
		RealName:         user.RealName,
		CardNo:           user.CardNo,
		StaticIdentity:   user.StaticIdentity,
		LastVerifyID:     user.LastVerifyID,
		AreaCode:         user.AreaCode,
		CountryCode:      user.CountryCode,
		Nickname:         user.Nickname,
		NicknameEn:       user.NicknameEn,
		Introduce:        user.Introduce,
		IntroduceEn:      user.IntroduceEn,
		Avatar:           user.Avatar,
		DealerState:      user.DealerState,
		FollowApproved:   user.FollowApproved,
		TradeApproved:    user.TradeApproved,
		PlatformID:       user.PlatformID,
		LoginVerifyPhone: user.LoginVerifyPhone,
		LoginVerifyEmail: user.LoginVerifyEmail,
		LoginVerifyTotp:  user.LoginVerifyTotp,
		TradeVerifyFund:  user.TradeVerifyFund,
		LabelID:          user.LabelID,
		ProfitStyle:      user.ProfitStyle,
		ShowAgentRatio:   user.ShowAgentRatio,
		IsShowDealer:     user.ISShowDealer,
		CreatedTime:      user.CreatedTime,
		IsAgent:          user.IsAgent,
	}
}
