package service

import (
	"database/sql"
	"strings"
	"time"

	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/check"
	"spot/libs/conf"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
	"spot/pkg/core/redis"
)

func Login<PERSON>andler(reqArg *define.ReqArg, request *proto.LoginArg) (res interface{}, err error) {
	request.Account = strings.ToLower(request.Account)
	isMail := check.EmailAddr(request.Account)
	if !isMail && !check.PhoneNumber(request.Account) && !check.AreaNumber(request.Account) {
		return nil, define.ErrMsgAccountFormat
	}
	if conf.SupportAccountType() != 0 {
		if !isMail && conf.SupportAccountType()&define.SupportAccountTypePhone != define.SupportAccountTypePhone {
			return nil, define.ErrMsgNonsupportPhoneNumber
		}
	}

	// 通过账号获取用户
	var user *proto.User
	if isMail {
		user, err = database.GetUserInfoByEmail(request.Account, nil)
	} else {
		user, err = database.GetUserInfoByPhone(request.Account, nil)
	}
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, define.ErrMsgUserNotExist
		}
		log.Error("LoginHandler GetUserByAccount failed", zap.Int64("reqID", reqArg.ReqID), zap.Error(err))
		return nil, define.ErrMsgBusy
	}

	if !user.EnableLogin {
		// 用户被禁止登录
		log.Error("LoginHandler user can not login", zap.Int64("reqID", reqArg.ReqID), zap.Any("user", user))
		return nil, define.ErrMsgBusy
	}
	if user.LimitState.Contains(define.UserLimitStateDeleteAccount) {
		// 已注销用户
		return nil, define.ErrMsgUserNotExist
	}

	if len(request.Password) == define.EmptyStrSize {
		// 短信验证码方式登陆
		log.Info("LoginHandler by code",
			zap.Int64("reqID", reqArg.ReqID),
			zap.String("account", request.Account),
			zap.String("code", request.Code),
			zap.String("ip", reqArg.ReqIP),
			zap.Uint8("os", uint8(reqArg.ReqOs)))

		//if !conf.DebugEnv() || request.Code != "XXXXXX" {
		//	// 验证短信验证码
		//	err = commonsrv.CheckAuthCode(reqArg.ReqID, request.Account, request.Code, define.NormalRequest, true)
		//	if err != nil {
		//		return nil, err
		//	}
		//}
	} else {
		// 密码方式登录
		log.Info("LoginHandler by password",
			zap.Int64("reqID", reqArg.ReqID),
			zap.String("account", request.Account),
			zap.String("ip", reqArg.ReqIP),
			zap.Uint8("os", uint8(reqArg.ReqOs)))

		// 判断登录密码
		//if !check.EncryptAndComparePassWord(request.Password, user.LoginPasswd) {
		//	log.Error("LoginHandler by password, password error",
		//		zap.Int64("reqID", reqArg.ReqID),
		//		zap.String("account", request.Account),
		//		zap.String("ip", reqArg.ReqIP),
		//		zap.Uint8("os", uint8(reqArg.ReqOs)))
		//	return nil, define.ErrMsgUserNotExist
		//}
	}

	// 登录
	user.LastLoginTime = time.Now()
	user.LastLoginIP = reqArg.ReqIP

	reply := new(proto.LoginReply)
	reply.Token, err = redis.GenToken(reqArg.ReqID, reqArg.DeviceID, reqArg.ReqOs, user)
	if err != nil {
		log.Error("LoginHandler generate token and sig error", zap.Int64("reqID", reqArg.ReqID), zap.Error(err))
		return nil, define.ErrMsgBusy
	}

	// 返回数据处理(密码等敏感数据已设置为不参与json序列化,不返回给前端)
	reply.HasPassword = len(user.LoginPasswd) > define.EmptyStrSize
	reply.HasFundPassword = len(user.FundPasswd) > define.EmptyStrSize
	reply.HasTotpBind = len(user.TotpSecret) > define.EmptyStrSize
	reply.LegalWithdrawLimit = cache.CheckLegalWithdrawLimit(user.UserID)
	reply.User = *user

	// 添加登录日志reqArg
	database.RecordOperateLog(user.UserID, request.Account, define.RecordTypeLogin, user.LastLoginTime, reqArg)
	return reply, nil
}
