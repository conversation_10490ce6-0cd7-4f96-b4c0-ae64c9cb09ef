package service

import (
	"math/rand"
	"time"

	"go.uber.org/zap"
	"spot/libs/conf"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
	"spot/pkg/core/redis"
)

var r = rand.New(rand.NewSource(time.Now().UnixNano()))

func ShareResourceHandler(reqArg *define.ReqArg, shareType int, level int8) (*proto.ShareResource, error) {
	reply := new(proto.ShareResource)
	language := reqArg.ReqLang
	if !conf.ShareLanguage() {
		language = define.ReqLangCN
	}

	// 获取分享图片资源 图片暂不包含语言,统一使用中文图
	images, err := redis.GetShareImageResourceFromCache(language, shareType, level)
	if err != nil {
		log.Error("ShareResourceHandler GetShareTextResourceFromDB error",
			zap.Int64("reqID", reqArg.ReqID), zap.Error(err))
		return reply, err
	}

	// 获取分享文案
	texts, err := redis.GetShareTextResourceFromCache(language, shareType, level)
	if err != nil {
		log.Error("ShareResourceHandler GetShareTextResourceFromCache error",
			zap.Int64("reqID", reqArg.ReqID), zap.Error(err))
		return reply, err
	}

	var index int
	if len(images) > 0 {
		// 移除不符合等级的数据
		images = filterShareImageByLevel(images, level)
		if len(images) > 0 {
			index = r.Intn(len(images))
			reply.ImageBackground = images[index].ImageBackground
			reply.TitleColor = images[index].TitleColor
			reply.TitleVertical = images[index].TitleVertical
			reply.ContentColor = images[index].ContentColor
		}
	}

	if len(texts) > 0 {
		// 移除不符合等级的数据
		texts = filterShareTextByLevel(texts, level)
		if len(texts) > 0 {
			index = r.Intn(len(texts))
			reply.TitleText = texts[index].TitleText
			reply.TitleSize = texts[index].TitleSize
		}
	}
	return reply, nil
}

func filterShareImageByLevel(src []proto.ShareImage, level int8) []proto.ShareImage {
	var dst []proto.ShareImage
	for i := range src {
		if src[i].LevelType == level {
			dst = append(dst, src[i])
		}
	}
	return dst
}

func filterShareTextByLevel(src []proto.ShareText, level int8) []proto.ShareText {
	var dst []proto.ShareText
	for i := range src {
		if src[i].LevelType == level {
			dst = append(dst, src[i])
		}
	}
	return dst
}
