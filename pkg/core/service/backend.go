package service

import (
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/commonsrv"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/messagequeue"
	"spot/libs/proto"
)

// 管理后台合约计划单撤单
func BackendOrderCancel(reqID int64, request *proto.BackendUserContractArg, reply *define.Reply) (err error) {
	log.Info("BackendOrderCancel", zap.Int64("reqID", reqID), zap.Any("request", request))

	// 加redis锁
	if !cache.SetRedisLockStr("BackendOrderCancel", request.ContractCode) {
		reply.Ret = define.ErrCodeResubmit
		reply.Msg = define.ErrMsgResubmit.Msg
		return
	}
	defer cache.SetRedisUnLockStr("BackendOrderCancel", request.ContractCode)

	// 判断合约是否存在
	_, err = commonsrv.GetContractDetail(reqID, request.ContractCode, nil)
	if err != nil {
		log.Error("PlanCancelHandler GetContractDetail error", zap.Int64("reqID", reqID), zap.Error(err))
		reply.Ret = define.ErrCodeBusy
		reply.Msg = define.NormalErrMsg
		return
	}

	// 条件单直接更新数据库状态
	list, err := database.CancelConditionOrder(0, 0, request.ContractCode)
	if err != nil {
		log.Error("PlanCancelHandler CancelConditionOrder db error", zap.Int64("reqID", reqID), zap.Error(err))
		reply.Ret = define.ErrCodeBusy
		reply.Msg = define.NormalErrMsg
		return
	}

	for i := range list {
		messagequeue.NotifyMqPlanOpenDel(&list[i])
	}
	return
}

// 管理后台合约平仓
func BackendOrderLiquidate(reqID int64, request *proto.BackendUserContractArg, reply *define.Reply) (err error) {
	log.Info("BackendOrderLiquidate", zap.Int64("reqID", reqID), zap.Any("request", request))
	//
	//// 加redis锁
	//if !cache.SetRedisLockStr("BackendOrderLiquidate", request.ContractCode) {
	//	return define.ErrMsgResubmit
	//}
	//defer cache.SetRedisUnLockStr("BackendOrderLiquidate", request.ContractCode)
	//
	//// 判断合约是否存在
	//_, err = commonsrv.GetContractDetail(reqID, request.ContractCode, nil)
	//if err != nil {
	//	log.Error("BackendOrderLiquidate GetContractDetail error", zap.Int64("reqID", reqID), zap.Error(err))
	//	return err
	//}
	//
	//// 获取当前合约所有持仓
	//list, err := database.GetContractAllPosition(request.ContractCode)
	//if err != nil {
	//	log.Error("BackendOrderLiquidate GetUserAllPosition error", zap.Int64("reqID", reqID), zap.Error(err))
	//	return define.ErrMsgBusy
	//}
	//
	//for i := range list {
	//	request := &proto.OrderCloseArgs{
	//		PositionId: list[i].OrderID,
	//		CloseType:  define.CloseOutTypeFull,
	//		OrderType:  define.OrderTypeUserTrade,
	//		RequestId:  database.NextID(),
	//		UserId:     list[i].UserID,
	//		APPID:      int(define.OsAutomatic),
	//		Mode:       define.EntrustModeBest5Level,
	//		IsAdminOp:  true,
	//	}
	//
	//	log.Info("BackendOrderLiquidate OrderClosePosition", zap.Int64("reqID", reqID), zap.Any("request", request))
	//	_, err = EntrustClose(request)
	//	if err != nil {
	//		log.Error("BackendOrderLiquidate OrderClosePosition fail", zap.Int64("reqID", reqID), zap.Any("request", request), zap.Error(err))
	//	}
	//}
	return
}
