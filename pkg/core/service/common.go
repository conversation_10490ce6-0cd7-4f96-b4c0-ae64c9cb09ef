package service

import (
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/commonsrv"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
	"spot/libs/singleflight"
)

func GetSummary(reqID int64) (*proto.Summary, error) {
	contractList, err := commonsrv.GetContractList(reqID, nil)
	if err != nil {
		return nil, define.ErrMsgBusy
	}
	if len(contractList) == 0 {
		log.Error("GetSummary contract list empty", zap.Int64("reqID", reqID))
		return nil, nil
	}

	// 获取全部合约指数价格
	index := cache.GetAllPriceIndex()

	// 获取全部涨跌幅
	changes := cache.GetAllContractApplies()

	var result proto.Summary
	for _, v := range contractList {
		p, ok := index[v.ContractCode]
		if !ok {
			continue
		}

		a, ok := changes[v.ContractCode]
		if !ok {
			continue
		}

		result.Trade24HValue = result.Trade24HValue.Add(a.TradeV24h.Mul(p.TradePrice))
	}
	return &result, nil
}

func GetCoinListHandler(reqID int64) ([]proto.Coin, error) {
	// 同一服务的同一时间内执行,合并查询
	list, err := singleflight.MergeDo[[]proto.Coin]("GetCoinListHandler", func() (any, error) {
		return commonsrv.GetCoinList()
	})
	if err != nil {
		log.Error("GetCoinListHandler MergeDo fail", zap.Int64("reqID", reqID), zap.Error(err))
		return nil, define.ErrMsgBusy
	}
	return list, nil
}

func GetCoinLegalRate(reqID int64) (map[string]proto.CoinLegalPrice, error) {
	res, err := cache.GetAllCoinLegalRate()
	if err != nil {
		log.Error("GetCoinLegalRate GetAllCoinLegalRate fail",
			zap.Int64("reqID", reqID), zap.Error(err))
		return nil, define.ErrMsgBusy
	}
	return res, nil
}
