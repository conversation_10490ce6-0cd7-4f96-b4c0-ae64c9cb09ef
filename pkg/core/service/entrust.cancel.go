package service

import (
	"spot/libs/convert"
	"spot/libs/messagequeue"
	"spot/libs/xrpcclient/order_rpc"
	"time"

	"github.com/jmoiron/sqlx"
	"go.uber.org/zap"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
)

// EntrustCancel 用户撤销委托
func EntrustCancel(order *proto.OrderCancelArgs) (err error) {
	//查询委托订单详情
	var (
		tx           *sqlx.Tx
		entrustOrder *proto.EntrustOrder
	)
	tx, err = database.Begin()
	if err != nil {
		log.Error("EntrustCancel open transaction fail",
			zap.Int64("reqID", order.RequestId), zap.Error(err))
		return
	}
	defer database.CommitTx(tx, &err, 0, nil)
	entrustOrder, err = database.GetUserEntrustOrder(tx, convert.String2Int64(order.ID))
	if err != nil {
		log.Error("EntrustCancel database.GetUserEntrustOrder fail",
			zap.Int64("reqID", order.RequestId), zap.Error(err))
		return
	}
	log.Info("查询订单信息", zap.Any("data", entrustOrder))
	if entrustOrder == nil {
		return
	}
	if entrustOrder.UserID != order.UserId {
		log.Error("EntrustCancel no auth fail", zap.Int64("reqID", order.RequestId))
		return define.ErrMsgParam
	}

	err = dealEntrustOrderCancel(tx, order.RequestId, entrustOrder)
	//log.Info("reply", zap.Any("reply", reply))
	return
}

//批量撤单
func EntrustBatchCancel(order *proto.OrderBatchCancelArgs) (err error) {
	if len(order.OrderIds) == 0 && order.Symbol == "" {
		return define.ErrMsgParam
	}
	//查询委托订单详情
	var (
		tx   *sqlx.Tx
		list []proto.EntrustOrder
	)
	tx, err = database.Begin()
	if err != nil {
		log.Error("EntrustCancel fail", zap.Error(err))
		return
	}
	defer database.CommitTx(tx, &err, 0, nil)
	list, err = database.ListEntrustUnFinishOrdersByIds(tx, order.UserId, order.OrderIds, order.Symbol, false)
	if err != nil {
		log.Error("EntrustCancel database.GetUserEntrustOrder fail", zap.Error(err))
		return
	}
	if len(list) == 0 {
		return
	}
	log.Info("开始用户批量撤单", zap.Any("arg", order))
	for _, entrustOrder := range list {
		o := entrustOrder
		e := dealEntrustOrderCancel(tx, order.RequestId, &o)
		if e != nil {
			log.Info("订单撤销中失败", zap.Error(e), zap.Any("订单信息", o))
			continue
		}
	}
	return
}

func dealEntrustOrderCancel(tx *sqlx.Tx, reqID int64, entrustOrder *proto.EntrustOrder) (err error) {
	//订单完成强平单禁止取消
	if entrustOrder.IsFinished() {
		log.Error("dealEntrustOrderCancel order is finished", zap.Int64("reqID", reqID))
		return define.ErrMsgOrderState
	}
	//if entrustOrder.IsEntrustMarket() {
	//	return define.ErrMsgParam
	//}

	//不是限价单不允许撤销
	if entrustOrder.EntrustType != define.EntrustTypeLimit {
		log.Error("dealEntrustOrderCancel order is not limit order", zap.Int64("reqID", reqID))
		return define.ErrMsgParam
	}

	err = database.UpdateEntrustOrderForCanceling(nil, entrustOrder.ID, define.CancelMarkUser)
	if err != nil {
		log.Error("database.UpdateEntrustOrderForCanceling fail", zap.Int64("reqID", reqID), zap.Error(err))
		return
	}

	cancelOrder(entrustOrder)

	return
}

//此处内部逻辑，市价限价均可撤销
func cancelOrder(entrustOrder *proto.EntrustOrder) {
	log.Info("开始准备撤销订单", zap.Any("order", entrustOrder))
	if entrustOrder == nil {
		return
	}

	if entrustOrder.IsFinished() {
		return
	}

	if entrustOrder.MarketSource != define.MarketSourceSourceDefault {
		if entrustOrder.ThirdOrderId > 0 {
			arg := &proto.ThirdOrderCancelArg{
				RequestId:    database.NextID(),
				ContractCode: entrustOrder.ContractCode,
				ThirdOrderId: entrustOrder.ThirdOrderId,
				OrderId:      entrustOrder.ID,
				MarketSource: entrustOrder.MarketSource,
			}
			reply := new(define.Reply)
			err := order_rpc.EntrustCancel(arg, reply)
			if err != nil {
				log.Error("rder_rpc.EntrustCancel fail", zap.Error(err))
				return
			}
			log.Info("third cancel", zap.Any("arg", arg), zap.Any("reply", reply))
			return
		}

		if time.Since(entrustOrder.CreateTime).Seconds() > 5 {
			//当前时间大于创建时间5s,直接撤销
			mo := &proto.MatchOverMsg{
				Code:        entrustOrder.ContractCode,
				OrderId:     entrustOrder.ID,
				UserId:      entrustOrder.UserID,
				Identifier:  define.IdentifierUser,
				DealerState: define.MatchOverDefault,
				Ts:          time.Now(),
			}
			log.Info("没有查询到可用的服务，将发送撤销消息", zap.Any("msg.consumer", mo))
			messagequeue.MQTopicOrderOverMsg(mo)
		}

		return
	}

	if entrustOrder.EntrustType == define.EntrustTypeLimit {
		messagequeue.NotifyMqEntrustLimitOrderCancel(entrustOrder)
		return
	}

	if entrustOrder.EntrustType == define.EntrustTypeMarket {
		mo := &proto.MatchOverMsg{
			Code:        entrustOrder.ContractCode,
			OrderId:     entrustOrder.ID,
			UserId:      entrustOrder.UserID,
			Identifier:  define.IdentifierUser,
			DealerState: define.MatchOverDefault,
			Ts:          time.Now(),
		}
		log.Info("没有查询到可用的服务，将发送撤销消息", zap.Any("msg.consumer", mo))
		messagequeue.MQTopicOrderOverMsg(mo)
		return
	}

}
