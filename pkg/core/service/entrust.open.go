package service

import (
	"spot/libs/messagequeue"
	"spot/libs/xrpcclient/order_rpc"
	"time"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/commonsrv"
	"spot/libs/conf"
	"spot/libs/convert"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/formul"
	"spot/libs/log"
	"spot/libs/nums"
	"spot/libs/proto"
	"spot/libs/xrpcclient/user_rpc"
)

// EntrustOpen 委托开仓
func EntrustOpen(order *proto.OrderOpenArgs) (rsp *proto.OrderOpenRsp, err error) {
	var (
		contractInfo *proto.Contract
		account      *proto.Account
		entrustOrder *proto.EntrustOrder
		dsInfo       *proto.DepthInfo
		outCoinName  string
		isThirdOrder bool
	)
	entrustId := database.NextID()
	rsp = new(proto.OrderOpenRsp)
	if order == nil {
		return nil, define.ErrMsgParam
	}
	log.Info("收到开仓委托", zap.Any("order", order), zap.Error(err))
	bTime := time.Now()
	defer func() {
		log.Info("处理完委托结果", zap.Any("order", order), zap.Any("reply", rsp), zap.Error(err), zap.String("cost", time.Since(bTime).String()))
	}()

	//判断是否重复请求
	if order.RequestId > 0 {
		isRepeatRequest := cache.SetRedisLockWithExp(define.RepeatRequestDuration, define.CacheRequest, define.ServerNameCore, convert.Int64String(order.RequestId))
		if !isRepeatRequest {
			log.Warn("重复请求", zap.Any("arg", order))
			return nil, define.ErrMsgTradeFrequent
		}
	}
	defer func() {
		if err != nil && order.RequestId > 0 {
			cache.SetRedisUnLockStr(define.CacheRequest, define.ServerNameCore, convert.Int64String(order.RequestId))
		}
	}()

	tx, err := database.Begin()
	if err != nil {
		log.Error("database get beginX fail", zap.Int64("reqID", order.RequestId), zap.Error(err))
		return nil, define.ErrMsgBusy
	}
	defer database.CommitTx(tx, &err, order.RequestId, func(i int64, e error) {
		//通知修改币种所有强评价
		if e == nil {
			//修改缓存中资金及持仓
			cTime := time.Now()
			defer func() {
				log.Info("开仓事务提交后耗时", zap.String("cost", time.Since(cTime).String()))
			}()
			messagequeue.NotifyMqEntrustOrderStatusUpdate(entrustOrder)
			if !isThirdOrder {
				//根据深度计算出成交价格
				DealEntrustOrder(entrustOrder, contractInfo)
			} else {
				//向order服务发送第三方订单处理
				err = DealEntrustOrderPlaceForThirdMarket(entrustOrder, contractInfo)
				if err != nil {
					log.Error("向第三方下单失败", zap.Error(err), zap.Any("order", order))
					rsp.Order = nil
					return
				}
			}
		}
	})

	//自定义订单id检查
	if order.ClientOrderId > 0 {
		var count int
		count, err = database.CountUserClientId(tx, order.UserId, order.ClientOrderId)
		if err != nil {
			log.Error("database.CountUserClientId fail", zap.Int64("reqID", order.RequestId), zap.Error(err))
			return
		}
		if count > 0 {
			err = define.ErrMsgOrderIdUserd
			return
		}

	}

	//查询合约详情
	contractInfo, err = commonsrv.GetContractDetail(order.RequestId, order.ContractCode, tx)
	if err != nil {
		log.Errorf("OrderOpenPosition GetContractDetail fail, reqID:%d, %v", order.RequestId, err)
		return nil, err
	}

	if contractInfo == nil {
		log.Error("OrderOpenPosition contract is nil fail, reqID", zap.String("code", order.ContractCode))
		return nil, define.ErrMsgParam
	}

	//判断是否上架
	//判断合约是否可以开仓
	if contractInfo.Delisted || !contractInfo.IsTradeEnable {
		return nil, define.ErrMsgSpotNotTrade
	}

	//系统维护
	if contractInfo.IsMaintenance {
		return nil, define.ErrMsgInMaintenance
	}

	//检测委托类型参数
	if order.EntrustType != define.EntrustTypeLimit && order.EntrustType != define.EntrustTypeMarket {
		log.Info("限价单委托类型有误", zap.Any("order", order))
		return nil, define.ErrMsgParam
	}

	//修正参数
	if order.EntrustType == define.EntrustTypeMarket && order.Mode == 0 {
		order.Mode = commonsrv.GetDefaultEntrustMode()
	}

	if order.EntrustType == define.EntrustTypeLimit {
		if order.Price.LessThanOrEqual(decimal.Zero) {
			log.Info("限价单委托价格有误", zap.Any("order", order))
			return nil, define.ErrMsgParam
		}
		//判断价格是否有效，是否在价格变动有效
		if order.Price.Mod(contractInfo.PriceStep).GreaterThan(decimal.Zero) {
			return nil, define.ErrMsgPriceInvalid
		}
		if !commonsrv.IsValidEntrustStrategy(order.EntrustStrategy) {
			log.Info("无效委托策略", zap.Any("order", order))
			return nil, define.ErrMsgParam
		}
	} else {
		//市价单传递委托策略强制重置
		if order.EntrustStrategy > 0 {
			order.EntrustStrategy = define.MatchStrategyDefault
		}
	}

	if order.EntrustStrategy > 0 {
		order.EntrustStrategy = define.MatchStrategyDefault
	}

	depth := cache.GetContractOriginDepth(order.ContractCode)
	if !conf.IsDev() {
		if depth == nil || time.Now().Unix()-depth.TS > define.ValidSeconds {
			log.Error("合约深度数据10s没有更新，暂停交易", zap.Int64("reqID", order.RequestId), zap.Any("depth", depth))
			return nil, define.ErrMsgBusy
		}
	}
	if depth == nil {
		if order.EntrustType == define.EntrustTypeMarket || order.EntrustStrategy == define.MatchStrategyIOC {
			log.Error("合约当前深度不足", zap.Int64("reqID", order.RequestId), zap.Any("code", order.ContractCode))
			return nil, define.ErrMsgDepthInvalid
		} else {
			depth = &proto.DepthContainer{ContractCode: contractInfo.ContractCode, Digit: contractInfo.Digit}
		}
	}
	dsInfo = depth.GetDepthSimpleInfo()
	log.Info("当前深度", zap.Int64("reqID", order.RequestId), zap.Any("depth", depth))
	log.Info("当前深度统计", zap.Int64("reqID", order.RequestId), zap.Any("dsInfo", dsInfo))

	//判断计划单是否已触发
	var coTriggerPrice decimal.Decimal
	if order.PlanOrderId > 0 {
		var o *proto.ConditionOrder
		//查询计划持仓记录
		o, err = database.GetConditionOrderByOrderId(tx, order.PlanOrderId)
		if err != nil {
			log.Errorf("database.GetConditionOrderBy, reqID:%d, planOrderId:%v fail,%v", order.RequestId, order.PlanOrderId, err)
			return
		}
		if o == nil {
			log.Info("没有查询到未触发的计划委托，退出", zap.Int64("id", order.PlanOrderId), zap.Any("order", order))
			return nil, define.ErrMsgParam
		}
		coTriggerPrice = o.TriggerPrice
		order.Mode = o.Mode

		//查看该计划单是否已委托开仓
		var count int64
		count, err = database.GetUserEntrustOrderCountForPlanOrder(tx, order.PlanOrderId, order.UserId)
		if err != nil {
			log.Error("database.GetUserEntrustOrderCountForPlanOrder fail", zap.Error(err), zap.Any("args", order))
			return
		}
		if count > 0 {
			log.Info("该条件单已委托,不在重复处理", zap.Int64("id", order.PlanOrderId), zap.Any("order", order))
			return nil, define.ErrMsgParam
		}

	}

	//判断用户是否可以下单
	var userInfo *proto.InnerUser
	if conf.IsDev() {
		userInfo = &proto.InnerUser{UserID: 180, EnableLogin: true, EnableTrade: true}
	} else {
		userInfo, err = user_rpc.GetInnerUserByID(order.RequestId, order.UserId, false)
		if err != nil {
			log.Errorf("user_rpc.GetInnerUserByID userid；%v，fail,%v", order.UserId, err)
			log.Info("没有查询到当前用户", zap.Any("order", order), zap.Error(err))
			return nil, define.ErrMsgParam
		}

	}
	if !userInfo.EnableTrade || userInfo.ForbidOpenClose == define.ForbidOpenCloseTypeOpenLimit {
		return nil, define.ErrMsgTradeNotAllowed
	}

	// 计算对应的下单参数
	if order.EntrustType == define.EntrustTypeMarket {
		// 市价单判断对手盘是否存在,获取最新成交价格
		var tradePrice decimal.Decimal
		if order.Side == define.OrderBuy {
			tradePrice = dsInfo.SellFirst
		} else {
			tradePrice = dsInfo.BuyFirst
		}
		if !tradePrice.IsPositive() {
			return nil, define.ErrMsgDepthInvalid
		}

		if order.Money.IsPositive() {
			// 市价单优先金额算数量
			order.Amount = order.Money.Div(tradePrice).Truncate(contractInfo.VolumeDigit)
		} else {
			// 数量算金额
			order.Money = order.Amount.Mul(tradePrice).Truncate(define.FloatPrecision)
		}
	} else {
		if !order.Price.IsPositive() || !order.Price.Mod(decimal.New(1, -contractInfo.Digit)).IsZero() {
			// 限价单价格小于等于0或价格精度与合约精度不一致
			log.Error("EntrustOpen 委托价格无效", zap.Any("order", order), zap.Int32("precision", contractInfo.Digit))
			return nil, define.ErrMsgParam
		}

		if order.Amount.IsPositive() {
			// 限价单优先数量算金额
			order.Money = order.Amount.Mul(order.Price).Truncate(define.FloatPrecision)
		} else {
			// 金额算数量
			order.Amount = order.Money.Div(order.Price).Truncate(contractInfo.VolumeDigit)
		}
	}

	// 判断条件单数量
	if !order.Amount.IsPositive() || !order.Amount.Mod(decimal.New(1, -contractInfo.VolumeDigit)).IsZero() {
		log.Error("EntrustOpen 委托数量精度无效", zap.Any("order", order), zap.Int32("precision", contractInfo.VolumeDigit))
		return nil, define.ErrMsgParam
	}
	// 判断用户最小下单量
	if contractInfo.MinOrderVolume.IsPositive() && order.Amount.LessThan(contractInfo.MinOrderVolume) {
		return nil, define.NewReplyErrorWithFormatData(define.ErrCodeEntrustBelowMinOpenVolume, contractInfo.MinOrderVolume.String(), contractInfo.BaseCoinName)
	}
	// 判断用户最大下单量
	if contractInfo.MaxOrderVolume.IsPositive() && order.Amount.GreaterThan(contractInfo.MaxOrderVolume) {
		return nil, define.NewReplyErrorWithFormatData(define.ErrCodeEntrustOverMaxOpenVolume, contractInfo.MaxOrderVolume.String(), contractInfo.BaseCoinName)
	}

	// 判断条件单金额
	if !order.Money.IsPositive() || !order.Money.Mod(decimal.New(1, -define.FloatPrecision)).IsZero() {
		log.Error("EntrustOpen 委托金额精度无效", zap.Any("order", order), zap.Int32("precision", define.FloatPrecision))
		return nil, define.ErrMsgParam
	}
	// 判断用户最小下单金额
	if contractInfo.MinOrderMoney.IsPositive() && order.Money.LessThan(contractInfo.MinOrderMoney) {
		return nil, define.NewReplyErrorWithFormatData(define.ErrCodeEntrustBelowMinOpenValue, contractInfo.MinOrderMoney.String(), contractInfo.CoinName)
	}
	// 判断用户最大下单金额
	if contractInfo.MaxOrderMoney.IsPositive() && order.Money.GreaterThan(contractInfo.MaxOrderMoney) {
		return nil, define.NewReplyErrorWithFormatData(define.ErrCodeEntrustOverMaxOpenValue, contractInfo.MaxOrderMoney.String(), contractInfo.CoinName)
	}

	if order.Side == define.OrderBuy {
		outCoinName = contractInfo.CoinName
	} else {
		outCoinName = contractInfo.BaseCoinName
	}

	account, err = database.GetUserAccountByCurrencyNameWithLock(tx, order.UserId, outCoinName)
	if err != nil {
		log.Error("db.GetUserAccount fail", zap.Int64("reqID", order.RequestId), zap.Int64("userId", order.UserId), zap.String("currencyName", outCoinName), zap.Error(err))
		return nil, define.ErrMsgBalanceInsufficient
	}

	//判断是否超出合约单边最大挂单量
	orders, err := database.ListEntrustUnFinishOrdersByUserIdAndContractSide(tx, account.UserId, order.ContractCode, order.Side)
	if err != nil {
		log.Error("database.ListEntrustUnFinishOrdersByUserIdAndContractSide", zap.Int64("reqID", order.RequestId), zap.Error(err))
		return
	}
	if contractInfo.MaxSideAmount > 0 && len(orders)+1 >= contractInfo.MaxSideAmount {
		//超出用户单合约最大下单量
		log.Info("超出单边下单限制", zap.Any("args", order), zap.Any("方向", order.Side), zap.Any("限制", contractInfo.MaxSideAmount), zap.Any("orders", orders))
		return nil, define.ErrMsgSpotOverMaxSideEntrust
	}

	// 查询当前未完成委托总笔数
	unFinishCount, err := database.GetUserUnfinishedEntrustCount(tx, order.UserId)
	if err != nil {
		log.Error("database.GetUserUnfinishedEntrustCount fail", zap.Int64("reqID", order.RequestId), zap.Error(err))
		return
	}
	if unFinishCount >= define.MaxEntrustOrderCount {
		return nil, define.ErrMsgUnfinishedEntrustOrderTooMany
	}

	//获取实时深度，根据委托类型
	//获取最新成交价格
	var lowSellPrice, maxBuyPrice decimal.Decimal
	last := cache.GetContractPriceIndex(contractInfo.ContractCode)
	if last == nil {
		//最高买价＝max(卖一价,最新成交价格)*（1+买价限制）
		maxBuyPrice = formul.LimitMax(dsInfo.SellFirst).Mul(nums.NewFromInt(1).Add(contractInfo.LimitBuyPrice))
		//最低卖价＝min(买一价,最新成交价格)*（1－卖价限制）
		lowSellPrice = formul.LimitMin(dsInfo.BuyFirst).Mul(nums.NewFromInt(1).Sub(contractInfo.LimitSellPrice))
	} else {
		//最高买价＝max(卖一价,最新成交价格)*（1+买价限制）
		maxBuyPrice = formul.LimitMax(dsInfo.SellFirst, last.TradePrice).Mul(nums.NewFromInt(1).Add(contractInfo.LimitBuyPrice))
		//最低卖价＝min(买一价,最新成交价格)*（1－卖价限制）
		lowSellPrice = formul.LimitMin(dsInfo.BuyFirst, last.TradePrice).Mul(nums.NewFromInt(1).Sub(contractInfo.LimitSellPrice))
	}

	if order.EntrustType == define.EntrustTypeLimit {
		//用户买入价格≤最高买价限制 用户卖出价格≥最低卖价限制
		if order.Price.GreaterThan(maxBuyPrice) || order.Price.LessThan(lowSellPrice) {
			log.Info("用户卖单，委托价格大于最高价或者低于最低价", zap.Int64("reqID", order.RequestId), zap.String("最高价", maxBuyPrice.String()), zap.String("最低价", lowSellPrice.String()), zap.String("最低价", lowSellPrice.String()), zap.Any("order", order), zap.Any("价格", map[string]string{"买一": dsInfo.BuyFirst.String(), "现货指数": last.TradePrice.String(), "最低卖价限制": contractInfo.LimitSellPrice.String()}))
			return nil, define.ErrMsgPriceOverLimit
		}
	}

	var cost decimal.Decimal
	if order.Side == define.OrderBuy {
		cost = order.Money
	} else {
		cost = order.Amount
	}

	available := account.Available
	//判断可用是否充足
	if cost.GreaterThan(available) {
		log.Infof("[place error]余额不足, reqID:%d，userId；%v,available；%v，payMoney；%v", order.RequestId, order.UserId, available.String(), order.Money.String())
		return nil, define.ErrMsgBalanceInsufficient
	}

	//修改资产冻结，从可用中扣除冻结
	err = database.UpdateUserAccountByAccountId(tx, account.AccountId, 0, -nums.Float(cost), nums.Float(cost), 0)
	if err != nil {
		log.Errorf("db.UpdateUserAccountByAccountId fail, reqID:%d,%v", order.RequestId, err)
		log.Info("db.UpdateUserAccountByAccountId fail", zap.Int64("reqID", order.RequestId), zap.Error(err))
		return
	}
	account.Available = available.Sub(cost)

	//向order服务发送第三方订单处理
	if contractInfo.IsThirdMarket && contractInfo.MarketSource != define.MarketSourceSourceDefault {
		isThirdOrder = true
	}

	//生成委托订单
	entrustOrder = &proto.EntrustOrder{
		ID:                 entrustId,
		UserID:             order.UserId,
		PlatformID:         account.PlatformID,
		ContractCode:       order.ContractCode,
		EntrustType:        order.EntrustType,
		EntrustStrategy:    order.EntrustStrategy,
		Mode:               order.Mode,
		Side:               order.Side,
		OrderType:          order.OrderType,
		Price:              order.Price,
		Volume:             order.Amount,
		Money:              order.Money,
		AssetLock:          cost,
		TradeVolume:        decimal.Zero,
		TradePrice:         decimal.Zero,
		TradeMoney:         decimal.Decimal{},
		TradeFee:           decimal.Decimal{},
		LastMatchPrice:     decimal.Decimal{},
		State:              define.OrderStatusDefault,
		CreateTime:         time.Now(),
		UpdateTime:         time.Now(),
		Mark:               0,
		ExtraID:            order.PlanOrderId,
		TriggerEntrustType: 0,
		PlanTriggerPrice:   coTriggerPrice,
		Imei:               order.DeviceId,
		IpAddress:          order.IpAddress,
		OrderClient:        order.APPID,
		ClientOrderId:      order.ClientOrderId,
		CoinName:           contractInfo.CoinName,
		BaseCoinName:       contractInfo.BaseCoinName,
		MarketSource:       contractInfo.MarketSource,
	}
	if entrustOrder.EntrustType == define.EntrustTypeMarket {
		// TODO 买委托量为0
		if entrustOrder.Side == define.OrderBuy {
			entrustOrder.Volume = decimal.Zero
		}
		entrustOrder.Price = decimal.Zero
	}

	if isThirdOrder {
		entrustOrder.State = define.OrderStatusTemp
	}

	err = database.InsertEntrustOrder(tx, entrustOrder)
	if err != nil {
		log.Error("开仓database.InsertEntrustOrder fail", zap.Error(err), zap.Any("order", order))
		return nil, define.ErrMsgBusy
	}

	entrustOrder.Volume = order.Amount
	rsp.Order = entrustOrder
	return
}

// DealEntrustOrderPlaceForThirdMarket 处理向第三方市场下单，如果失败撤销用户订单
func DealEntrustOrderPlaceForThirdMarket(order *proto.EntrustOrder, info *proto.Contract) (err error) {
	arg := &proto.ThirdOrderPlaceArg{
		RequestId:    database.NextID(),
		OrderId:      order.ID,
		UserId:       order.UserID,
		ContractCode: order.ContractCode,
		EntrustType:  order.EntrustType,
		Side:         order.Side,
		Price:        order.Price,
		Volume:       order.Volume,
		Money:        order.Money,
		MarketSource: order.MarketSource,
	}

	reply := &define.Reply{Data: &arg.ThirdOrderId}
	err = order_rpc.EntrustOpen(arg, reply)
	log.Info("向Order服务发送下单请求", zap.Error(err), zap.Any("arg", arg), zap.Any("reply", reply))
	if err != nil {
		log.Error("向Order服务发送下单请求失败", zap.Error(err), zap.Any("arg", arg), zap.Any("reply", reply))
		cancelEntrustOrderIfPlaceThirdFail(order, info, define.CancelOrderDrop)
		return
	}
	log.Info("向Order服务发送下单请求", zap.Error(err), zap.Any("arg", arg), zap.Any("reply", reply))
	if arg.ThirdOrderId <= 0 {
		var o *proto.EntrustOrder
		o, err = database.GetUserEntrustOrder(nil, order.ID)
		if err != nil {
			log.Error("cancelEntrustOrderIfPlaceThirdFail fail", zap.Error(err))
			return
		}
		if o.ThirdOrderId <= 0 {
			mCancel := &proto.MCancel{
				OrderId:    order.ID,
				CancelMark: define.CancelOrderDrop,
				TradeMark:  define.TradeMarkWithOrderDefault,
			}
			commonsrv.DealContractAccountOrderCanceled(mCancel)
		} else {
			arg.ThirdOrderId = o.ThirdOrderId
		}
	}
	if arg.ThirdOrderId <= 0 {
		err = define.ErrMsgSpotTradeLimit
	}
	return
}

func cancelEntrustOrderIfPlaceThirdFail(order *proto.EntrustOrder, info *proto.Contract, mark int) {

	o, err := database.GetUserEntrustOrder(nil, order.ID)
	if err != nil {
		log.Error("cancelEntrustOrderIfPlaceThirdFail fail", zap.Error(err))
		return
	}
	if o != nil && o.ThirdOrderId == 0 {
		mCancel := &proto.MCancel{
			OrderId:    order.ID,
			CancelMark: mark,
			TradeMark:  define.TradeMarkWithOrderDefault,
		}
		commonsrv.DealContractAccountOrderCanceled(mCancel)
	}
}
