package service

import (
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"spot/libs/commonsrv"
	"spot/libs/conf"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/messagequeue"
	"spot/libs/proto"
	"time"
)

//处理非第三方订单成交
func DealEntrustOrder(order *proto.EntrustOrder, c *proto.Contract) {
	if order == nil {
		return
	}

	if c == nil {
		c, _ = commonsrv.GetContractDetail(0, order.ContractCode, nil)
	}

	if c == nil {
		log.Error("获取合约信息出错", zap.Any("code", order.ContractCode))
		return
	}

	if order.EntrustType == define.EntrustTypeLimit {
		DealOrderForLimitEntrust(order, c)
	} else {
		dealOrderForMarketEntrust(order, c)
	}
}

func dealOrderForMarketEntrust(order *proto.EntrustOrder, c *proto.Contract) {
	log.Info("开始处理市价成交", zap.Any("order", order))
	var isUpdateTradePrice bool //是否修改了成交价
	mp, err := commonsrv.GenerateContractPrice(c, order, conf.IsDev())
	if err != nil || mp == nil {
		log.Errorf("code:%v,开仓撮合价格异常：%v", c.ContractCode, err)
		messagequeue.MQTopicOrderOverMsg(&proto.MatchOverMsg{
			MsgId:       database.NextID(),
			Code:        order.ContractCode,
			OrderId:     order.ID,
			UserId:      order.UserID,
			Identifier:  define.IdentifierUser,
			DealerState: define.MatchOverDefault,
			Ts:          time.Now(),
		})
		return
	}
	if mp.Status == define.OrderStatusNotDealCancel || mp.TradeVolume.LessThanOrEqual(decimal.Zero) {
		log.Errorf("当前订单未成交，开始发送订单完成指令", zap.Any("order", order), zap.Any("data", mp))
		messagequeue.MQTopicOrderOverMsg(&proto.MatchOverMsg{
			MsgId:       database.NextID(),
			Code:        order.ContractCode,
			OrderId:     order.ID,
			UserId:      order.UserID,
			Identifier:  define.IdentifierUser,
			DealerState: define.MatchOverDefault,
			Ts:          time.Now(),
		})
		return
	}

	//发送到清算
	if mp.TradeVolume.GreaterThan(decimal.Zero) {
		messagequeue.MQTopicOrderTradeMsg(&proto.OrderTrade{
			MsgId:      database.NextID(),
			Code:       order.ContractCode,
			DealPrice:  mp.TradePrice,
			DealVolume: mp.TradeVolume,
			MatchTime:  time.Now(),
			Order: proto.MatchUser{
				UserId:     order.UserID,
				OrderId:    order.ID,
				Identifier: define.IdentifierUser,
				IsMaker:    false,
			},
			IsProtocalTrade: false,
		})

		matchId := database.NextID()
		matchOrder := &proto.MatchOrder{
			Id:             matchId,
			EntrustOrderId: order.ID,
			Symbol:         order.ContractCode,
			Side:           order.Side,
			TradeVolume:    mp.TradeVolume.RoundBank(c.VolumeDigit),
			TradePrice:     mp.TradePrice,
			TradeTime:      time.Now(),
			Digit:          c.Digit,
			VolumeDigit:    c.VolumeDigit,
		}
		if mp != nil {
			matchOrder.MatchPrice = *mp
		}
		//推送成交记录,没有修改成交价的成交记录推送到kline
		if !isUpdateTradePrice {
			log.Info("开始推送成交到系统", zap.Any("order", order), zap.Any("matchOrder", matchOrder))
			go NotifyMqSelfNewTrade(matchOrder) //发送成交消息
		}

	}

	messagequeue.MQTopicOrderOverMsg(&proto.MatchOverMsg{
		MsgId:       database.NextID(),
		Code:        order.ContractCode,
		OrderId:     order.ID,
		UserId:      order.UserID,
		Identifier:  define.IdentifierUser,
		DealerState: define.MatchOverDefault,
		Ts:          time.Now(),
	})

	return
}

func DealOrderForLimitEntrust(order *proto.EntrustOrder, c *proto.Contract) {
	var tradePrice decimal.Decimal
	var rCount = 0
R:
	mp, _ := commonsrv.GenerateContractPriceForEntrustLimit(c, order.Side, order.Volume, conf.IsDev(), order.Price)
	if mp == nil {
		rCount++
		if rCount < 3 {
			time.Sleep(250 * time.Millisecond)
			goto R
		}
	}
	isTrade := false
	isUpdate := false

	if mp == nil {
		goto R
	} else {
		if mp.TradeVolume.GreaterThan(decimal.Zero) {
			isTrade = true
			isUpdate = true
			tradePrice = mp.TradePrice
		}
	}

	if isTrade && tradePrice.GreaterThan(decimal.Zero) {
		log.Info("限价单处理，部分成交发送到清算", zap.Any("成交数据", mp), zap.Any("订单", order))
		//发送到清算
		messagequeue.MQTopicOrderTradeMsg(&proto.OrderTrade{
			MsgId:      database.NextID(),
			Code:       order.ContractCode,
			DealPrice:  tradePrice,
			DealVolume: order.Volume,
			MatchTime:  time.Now(),
			Order: proto.MatchUser{
				UserId:     order.UserID,
				OrderId:    order.ID,
				Identifier: define.IdentifierUser,
				IsMaker:    false,
			},
			IsProtocalTrade: false,
		})
		messagequeue.MQTopicOrderOverMsg(&proto.MatchOverMsg{
			MsgId:       database.NextID(),
			Code:        order.ContractCode,
			OrderId:     order.ID,
			UserId:      order.UserID,
			Identifier:  define.IdentifierUser,
			DealerState: define.MatchTradeOver,
			Ts:          time.Now(),
		})

		matchId := database.NextID()
		matchOrder := &proto.MatchOrder{
			Id:             matchId,
			EntrustOrderId: order.ID,
			Symbol:         order.ContractCode,
			Side:           order.Side,
			TradeVolume:    order.Volume.RoundBank(c.VolumeDigit),
			TradePrice:     tradePrice,
			TradeTime:      time.Now(),
			Digit:          c.Digit,
			VolumeDigit:    c.VolumeDigit,
		}
		if mp != nil {
			matchOrder.MatchPrice = *mp
		}
		//推送成交记录
		if isUpdate {
			log.Info("开始推送成交到系统", zap.Any("order", order), zap.Any("matchOrder", matchOrder))
			go NotifyMqSelfNewTrade(matchOrder) //发送成交消息
		}
	} else {
		messagequeue.NotifyMqEntrustLimitOrderPlace(order)
	}
}
