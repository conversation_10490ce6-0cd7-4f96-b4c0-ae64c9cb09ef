package service

import (
	"github.com/jmoiron/sqlx"
	"go.uber.org/zap"
	"spot/libs/database"
	"spot/libs/log"
	"spot/libs/proto"
)

//用户当前委托
//普通委托，不包括强平，止盈止损
func ListUserEntrustOrderList(userId int64) (list []proto.EntrustOrder, err error) {
	//查询委托订单详情
	var (
		tx                 *sqlx.Tx
		code               string
		orderTypes, states []int
	)

	tx, err = database.Begin()
	if err != nil {
		log.Error("EntrustCancel fail", zap.Error(err))
		return
	}
	defer database.CommitTx(tx, &err, 0, nil)
	list, err = database.ListEntrustUnFinishOrdersByCustom(tx, userId, code, orderTypes, states)
	if err != nil {
		log.Error("EntrustCancel database.ListEntrustUnFinishOrdersByCustom fail", zap.Error(err))
		return
	}
	return
}

type ListOrdersArg struct {
	ContractCode string `json:"contract_code"`
	OrderType    int    `json:"order_type"` //查询订单类型 -1 全部 0-普通委托 1-条件单 2-止盈 3-止损
	State        int    `json:"state"`      //查询状态 -1 全部
}
