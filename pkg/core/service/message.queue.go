/*
@Time : 2019-12-31 10:53
<AUTHOR> mocha
@File : message.queue
*/
package service

import (
	"spot/libs/commonsrv"
	"spot/libs/conf"
	"spot/libs/messagequeue"
	"spot/libs/nums"
	"spot/libs/proto"
)

func InitMessageQueue() {
	messagequeue.NewMqProduct(conf.MQ(), nums.Int64String(conf.WorkerID()))
}

func StopMQProduct() {
	messagequeue.StopMQProduct()
}

func StopMQConsumer() {
	messagequeue.StopMQConsumer()
}

// NotifyMqSelfNewTrade /通知自有成交
func NotifyMqSelfNewTrade(trade *proto.MatchOrder) {
	//notifyMqSelfNewTrade(trade)           //发送成交消息
	index := commonsrv.GenerateIndexHistory(trade)
	if index == nil {
		return
	}
	//现货不推送成交到kline,只已第三方ticker为准
	//messagequeue.NotifyPriceIndex(index)

	messagequeue.NotifyMqNewTrade(trade)
}
