package service

import (
	"context"
	"spot/libs/convert"
	"spot/pkg/core/model"
	"time"

	"go.uber.org/zap"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
	"spot/pkg/core/db"
)

func EntrustOrderCurrentList(ctx context.Context, arg define.ReqArg, request *proto.UserSymbol) (*model.CurrentEntrustOrderReply, error) {
	var err error
	var reply model.CurrentEntrustOrderReply
	reply.List, err = db.GetEntrustOrderCurrentList(ctx, request.UserID, request.Symbol)
	if err != nil {
		log.Error("EntrustOrderCurrentList GetEntrustOrderCurrentList fail",
			zap.Int64("reqID", arg.ReqID), zap.Error(err))
		return nil, define.ErrMsgBusy
	}
	return &reply, nil
}

func EntrustOrderCurrentPlanList(ctx context.Context, reqArg define.ReqArg, request *proto.UserSymbol) (*model.CurrentPlanEntrustOrderReply, error) {
	var err error
	var reply model.CurrentPlanEntrustOrderReply
	reply.List, err = db.GetEntrustOrderCurrentPlanList(ctx, request.UserID, request.Symbol)
	if err != nil {
		log.Error("EntrustOrderCurrentPlanList GetEntrustOrderCurrentPlanList fail",
			zap.Int64("reqID", reqArg.ReqID), zap.Error(err))
		return nil, define.ErrMsgBusy
	}
	return &reply, nil
}

func EntrustOrderHistoryList(ctx context.Context, reqArg define.ReqArg, request *proto.UserSymbol) (*model.HistoryEntrustOrderReply, error) {
	end := time.Now()
	start := end.AddDate(0, 0, -define.ApiHistoryBillMaxDays)

	var err error
	var reply model.HistoryEntrustOrderReply
	reply.List, err = db.GetEntrustOrderHistoryList(ctx, request.UserID, request.Symbol, start, end)
	if err != nil {
		log.Error("EntrustOrderHistoryList GetEntrustOrderHistoryList fail",
			zap.Int64("reqID", reqArg.ReqID), zap.Error(err))
		return nil, define.ErrMsgBusy
	}
	return &reply, nil
}

func EntrustOrderHistoryPlanList(ctx context.Context, reqArg define.ReqArg, request *proto.UserSymbol) (*model.HistoryPlanEntrustOrderReply, error) {
	end := time.Now()
	start := end.AddDate(0, 0, -define.ApiHistoryBillMaxDays)

	var err error
	var reply model.HistoryPlanEntrustOrderReply
	reply.List, err = db.GetEntrustOrderHistoryPlanList(ctx, request.UserID, request.Symbol, start, end)
	if err != nil {
		log.Error("EntrustOrderHistoryPlanList GetEntrustOrderHistoryPlanList fail",
			zap.Int64("reqID", reqArg.ReqID), zap.Error(err))
		return nil, define.ErrMsgBusy
	}
	return &reply, nil
}

func EntrustOrderTradeDetail(ctx context.Context, reqArg define.ReqArg, request *model.TradeDetailArg) (*model.TradeDetailReply, error) {
	id := convert.String2Int64(request.OrderID)
	// web端不使用ID查询,查询全部
	if reqArg.ReqOs != define.OsWeb && id <= 0 {
		return nil, define.ErrMsgParam
	}

	end := time.Now()
	start := end.AddDate(0, 0, -request.LimitDays)

	var err error
	var reply model.TradeDetailReply
	reply.List, err = db.GetEntrustOrderTradeDetail(ctx, request.UserID, id, request.Symbol, request.Side, start, end, define.Page{Page:  0, Count: define.MaxPageCount})
	if err != nil {
		log.Error("EntrustOrderTradeDetail GetEntrustOrderTradeDetail fail",
			zap.Int64("reqID", reqArg.ReqID), zap.Error(err))
		return nil, define.ErrMsgBusy
	}
	return &reply, nil
}
