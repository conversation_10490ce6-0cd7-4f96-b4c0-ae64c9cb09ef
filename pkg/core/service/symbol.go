package service

import (
	"context"
	"sort"
	"time"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/commonsrv"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
	"spot/pkg/core/db"
	"spot/pkg/core/model"
)

func GetSymbolListHandler(reqID int64) (*model.SymbolListReply, *define.ReplyError) {
	symbols, err := commonsrv.GetContractList(reqID, nil)
	if err != nil {
		return nil, define.ErrMsgBusy
	}
	list := commonsrv.GetApiSymbolList(symbols)

	dict := make(map[string][]proto.ApiSymbolList)
	for _, symbol := range list {
		dict[symbol.MarketName] = append(dict[symbol.MarketName], symbol)
	}

	reply := model.SymbolListReply{
		List: make([]model.MarketSymbolList, len(dict)),
	}

	var idx int
	for key, val := range dict {
		reply.List[idx].MarketName = key
		reply.List[idx].Symbols = val
		idx++
	}

	coins, err := cache.GetCoinMarketConf()
	if err == nil {
		sort.SliceStable(reply.List, func(i, j int) bool {
			return coins[reply.List[i].MarketName].OrderBy > coins[reply.List[j].MarketName].OrderBy
		})
	}
	return &reply, nil
}

func SymbolDetailHandler(reqID int64, user *define.TokenPayload, request *proto.SymbolArg) (*proto.ApiSymbolDetail, error) {
	detail, err := commonsrv.GetContractDetail(reqID, request.Symbol, nil)
	if err != nil {
		log.Error("SymbolDetailHandler GetContractDetail error", zap.Int64("reqID", reqID), zap.Error(err))
		return nil, err
	}
	applies := cache.GetContractApplies(request.Symbol)

	return commonsrv.GetApiSymbolDetail(detail, applies), nil
}

func SymbolKlineHandler(reqID int64, request *proto.ApiKLineArg) (*proto.ApiKLineData, error) {
	data, myErr := GetTradingViewKLineData(reqID, request)
	if myErr != nil {
		return nil, myErr
	}

	reply := &proto.ApiKLineData{
		Symbol:     request.Symbol,
		Duration:   request.Duration,
		ServerTime: time.Now().Unix(),
		List:       make([]proto.KLine, 0, len(data)),
	}

	var ok bool
	filter := make(map[int64]bool)
	for i := range data {
		// 过滤重复数据
		if _, ok = filter[data[i].StartTime]; ok {
			continue
		}
		filter[data[i].StartTime] = true
		data[i].PVolume = decimal.Zero
		reply.List = append(reply.List, data[i])
	}
	return reply, nil
}

func GetTradingViewKLineData(reqID int64, request *proto.ApiKLineArg) ([]proto.KLine, *define.ReplyError) {
	// 应该检查该参数,防止sql注入
	duration := define.DurationStr2Duration(request.Duration)
	if duration <= 0 {
		return nil, define.ErrMsgParam
	}

	var (
		err  error
		list []proto.KLine
	)
	if request.StartTime == 0 {
		// 首次请求数据处理
		list, err = cache.GetKLineNodeASC(request.Symbol, request.Duration, -define.ApiKLineMaxCount, -1)
		if err != nil {
			log.Error("GetKLineForTradingView GetKLineNodeASC redis error", zap.Int64("reqID", reqID), zap.Any("request", request), zap.Error(err))
			return nil, define.ErrMsgBusy
		}
	} else {
		// 计算能获取的最早时间戳
		last := define.LastKlineStartTime(request.Duration)
		last -= duration * (define.ApiKLineMaxTotalCount - 1)
		if last > 0 && request.StartTime < last {
			request.StartTime = last
		}
		if request.StartTime >= request.EndTime {
			return list, nil
		}
		//log.Info("TEST LOG", zap.Int64("start", request.StartTime), zap.Int64("end", request.EndTime))

		// 其余从数据库获取
		list, err = db.GetKlineNodesForTradingView(request)
		if err != nil {
			log.Errorf("GetKLineForTradingView GetKlineNodesForTradingView db error", zap.Int64("reqID", reqID), zap.Any("request", request), zap.Error(err))
			return nil, define.ErrMsgBusy
		}
	}
	return list, nil
}

func GetSymbolMarkets(ctx context.Context, reqID int64) (*proto.APISymbolMarketReply, error) {
	symbols, err := commonsrv.GetContractList(reqID, nil)
	if err != nil {
		log.Error("GetSymbolMarkets GetContractList fail", zap.Int64("reqID", reqID), zap.Error(err))
		return nil, define.ErrMsgBusy
	}

	reply := proto.APISymbolMarketReply{
		List: make([]proto.APISymbolMarket, 0, len(symbols)),
	}

	// 获取全部涨跌幅
	changes := cache.GetAllContractApplies()

	// 获取全部价格
	index := cache.GetAllPriceIndex()

	for _, symbol := range symbols {
		if !symbol.Delisted && symbol.IsShow {
			chg := changes[symbol.ContractCode]

			reply.List = append(reply.List, proto.APISymbolMarket{
				Symbol:      symbol.ContractCode,
				Icon:        symbol.ContractIcon,
				Price:       index[symbol.ContractCode].TradePrice.Round(symbol.Digit),
				ChangeRatio: chg.ChangeDaily,
				HighPrice:   chg.HighPrice,
				LowPrice:    chg.LowPrice,
				TradeVolume: chg.TradeV24h,
			})
		}
	}

	return &reply, nil
}
