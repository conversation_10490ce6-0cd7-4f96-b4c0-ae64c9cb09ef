package service

import (
	"time"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/commonsrv"
	"spot/libs/convert"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/messagequeue"
	"spot/libs/notice"
	"spot/libs/proto"
	"spot/pkg/core/model"
)

// PlanTrigger 条件单触发调用
func PlanTrigger(arg *proto.OrderOpenArgs) {
	id := arg.PlanOrderId
	log.Infof("执行，条件单触发：%+v", id)
	order, err := database.GetConditionOrderByOrderId(nil, id)
	if err != nil {
		log.Errorf("database.GetConditionOrderByOrderId fail,%v,id：%v", err, id)
		return
	}
	if order == nil {
		log.Infof("没有查询到未触发的计划委托，退出，id；%v", id)
		return
	}
	tc := cache.GetSystemTradeConf(order.PlatformID)
	if tc.Plans {
		log.Error("PlanTrigger system plan trigger closed", zap.Any("arg", id))
		return
	}

	if commonsrv.IsMaintenance(order.ContractCode) {
		log.Infof("条件单触发,当前合约正在维护中", zap.Any("arg", order))
		return
	}

	key := define.CacheKeyPlanOpenLock
	s := cache.SetRedisLockStr(key, convert.Int64String(id))
	if !s {
		log.Errorf("当前条件单正在触发，请稍后，%+v", id)
		return
	}
	defer cache.SetRedisUnLockStr(key, convert.Int64String(id))
	defer func() {
		messagequeue.NotifyMqPlanOpenDel(order)
	}()

	log.Infof("计划单触发获取详情：%+v", *order)

	orderTime := time.Now()
	triggerState := notice.ContractTriggerNoticeStateSuccess
	_, err = EntrustOpen(arg)
	if err != nil {
		triggerState = notice.ContractTriggerNoticeStateFail
		log.Errorf("条件单触发失败:%+v,err:%v", *arg, err)
		err = database.UpdateConditionOrderState(nil, arg.PlanOrderId, define.OrderConditionNotTrigger, define.OrderCondFailed, orderTime)
		if err != nil {
			log.Errorf("database.UpdateConditionOrderState fail,%v", err)
			return
		}
	} else {
		err = database.UpdateConditionOrderState(nil, arg.PlanOrderId, define.OrderConditionNotTrigger, define.OrderCondHadTrigger, orderTime)
		if err != nil {
			log.Error("conditionOrderHandler UpdateConditionOrderState error", zap.Int64("reqID", 0), zap.Error(err))
			return
		}
	}
	go notice.SendContractTriggerNotice(arg.PlanOrderId, arg.UserId, arg.ContractCode, arg.Side, notice.ContractTriggerNoticeTypePlanCN, order.TriggerPrice.String(), triggerState, orderTime)
}

// PlanPlaceHandler 条件单下单
func PlanPlaceHandler(reqArg *define.ReqArg, user *define.TokenPayload, request *model.OrderPlanArgs) error {
	// 加redis锁
	if !cache.SetRedisLockStr(convert.Int64String(user.UserID), request.Symbol) {
		return define.ErrMsgTradeFrequent
	}
	defer cache.SetRedisUnLockStr(convert.Int64String(user.UserID), request.Symbol)

	// 获取合约信息
	contract, err := commonsrv.GetContractDetail(reqArg.ReqID, request.Symbol, nil)
	if err != nil {
		log.Error("PlanPlaceHandler GetContractDetail error", zap.Int64("reqID", reqArg.ReqID), zap.Error(err))
		return err
	}

	//判断合约是否可以开仓
	if contract.Delisted || !contract.IsTradeEnable {
		log.Error("PlanPlaceHandler contract status error", zap.Int64("reqID", reqArg.ReqID), zap.Any("contract", contract))
		return define.ErrMsgSpotNotTrade
	}

	if !request.Price.IsPositive() || !request.Price.Mod(decimal.New(1, -contract.Digit)).IsZero() {
		// 触发价格小于等于0或触发价格精度与合约精度不一致
		log.Error("PlanPlaceHandler 委托触发价格无效", zap.Int64("reqID", reqArg.ReqID), zap.Any("request", request), zap.Int32("precision", contract.Digit))
		return define.ErrMsgParam
	}

	// 计算对应的下单参数
	if request.EntrustType == define.EntrustTypeMarket {
		if request.Money.IsPositive() {
			// 市价单优先金额算数量
			request.Amount = request.Money.Div(request.Price).Truncate(contract.VolumeDigit)
		} else {
			// 数量算金额
			request.Money = request.Amount.Mul(request.Price).Truncate(define.FloatPrecision)
		}
	} else {
		if !request.EntrustPrice.IsPositive() || !request.EntrustPrice.Mod(decimal.New(1, -contract.Digit)).IsZero() {
			// 限价单委托价格小于等于0或执行价格精度与合约精度不一致
			log.Error("PlanPlaceHandler 委托执行价格无效", zap.Int64("reqID", reqArg.ReqID), zap.Any("request", request), zap.Int32("precision", contract.Digit))
			return define.ErrMsgParam
		}

		if request.Amount.IsPositive() {
			// 限价单优先数量算金额
			request.Money = request.Amount.Mul(request.EntrustPrice).Truncate(define.FloatPrecision)
		} else {
			// 金额算数量
			request.Amount = request.Money.Div(request.EntrustPrice).Truncate(contract.VolumeDigit)
		}
	}

	// 判断条件单数量
	if !request.Amount.IsPositive() || !request.Amount.Mod(decimal.New(1, -contract.VolumeDigit)).IsZero() {
		log.Error("PlanPlaceHandler 委托数量精度无效", zap.Int64("reqID", reqArg.ReqID), zap.Any("request", request), zap.Int32("precision", contract.VolumeDigit))
		return define.ErrMsgParam
	}
	// 判断用户最小下单量
	if contract.MinOrderVolume.IsPositive() && request.Amount.LessThan(contract.MinOrderVolume) {
		return define.NewReplyErrorWithFormatData(define.ErrCodeEntrustBelowMinOpenVolume, contract.MinOrderVolume.String(), contract.BaseCoinName)
	}
	// 判断用户最大下单量
	if contract.MaxOrderVolume.IsPositive() && request.Amount.GreaterThan(contract.MaxOrderVolume) {
		return define.NewReplyErrorWithFormatData(define.ErrCodeEntrustOverMaxOpenVolume, contract.MaxOrderVolume.String(), contract.BaseCoinName)
	}

	// 判断条件单金额
	if !request.Money.IsPositive() || !request.Money.Mod(decimal.New(1, -define.FloatPrecision)).IsZero() {
		log.Error("PlanPlaceHandler 委托金额精度无效", zap.Int64("reqID", reqArg.ReqID), zap.Any("request", request), zap.Int32("precision", define.FloatPrecision))
		return define.ErrMsgParam
	}
	// 判断用户最小下单金额
	if contract.MinOrderMoney.IsPositive() && request.Money.LessThan(contract.MinOrderMoney) {
		return define.NewReplyErrorWithFormatData(define.ErrCodeEntrustBelowMinOpenValue, contract.MinOrderMoney.String(), contract.CoinName)
	}
	// 判断用户最大下单金额
	if contract.MaxOrderMoney.IsPositive() && request.Money.GreaterThan(contract.MaxOrderMoney) {
		return define.NewReplyErrorWithFormatData(define.ErrCodeEntrustOverMaxOpenValue, contract.MaxOrderMoney.String(), contract.CoinName)
	}

	// 获取当前指数价格
	index := cache.GetContractPriceIndex(request.Symbol)
	if index == nil {
		index = new(proto.IndexHistory)
	}
	log.Info("PlanPlaceHandler GetContractPriceIndexIgnoreErr",
		zap.Int64("reqID", reqArg.ReqID), zap.String("code", request.Symbol), zap.Any("index", index))
	if !index.TradePrice.IsPositive() {
		// 当前价格异常,返回设置失败
		log.Error("PlanPlaceHandler tradePrice fail", zap.Int64("reqID", reqArg.ReqID))
		return define.ErrMsgBusy
	}

	order := &proto.ConditionOrder{
		PlatformID:      user.PlatformID,
		Condition:       0,
		ContractCode:    request.Symbol,
		CoinName:        contract.BaseCoinName,
		MarketName:      contract.CoinName,
		Imei:            reqArg.DeviceID,
		IpAddress:       reqArg.ReqIP,
		Amount:          request.Amount,
		Money:           request.Money,
		OrderClient:     int(reqArg.ReqOs),
		CreateTime:      time.Now(),
		OrderTime:       time.Now(),
		CancelTime:      time.Now(),
		PlanOrderId:     reqArg.ReqID,
		Side:            request.Side,
		Status:          define.OrderConditionNotTrigger,
		TriggerPrice:    request.Price,
		UserId:          user.UserID,
		EntrustType:     request.EntrustType,
		EntrustStrategy: request.EntrustStrategy,
		EntrustPrice:    request.EntrustPrice,
		Mode:            request.Mode,
	}

	// 获取当前合约指数
	if index.TradePrice.LessThan(request.Price) {
		order.Condition = define.OrderConditionGreaterOrEqual
	} else {
		order.Condition = define.OrderConditionLessOrEqual
	}

	// 入库订单
	err = insertPlanOrder(reqArg.ReqID, order)
	if err != nil {
		log.Error("PlanPlaceHandler InsertPlanOrder error", zap.Int64("reqID", reqArg.ReqID), zap.Any("request", request), zap.Error(err))
		return err
	}

	messagequeue.NotifyMqPlanOpenAdd(order)
	//go GetPlanOrderWorker().JoinOrder(request.ContractCode, request.Price, order.Condition, order.PlanOrderId)

	return nil
}

func insertPlanOrder(reqID int64, order *proto.ConditionOrder) (err error) {
	tx, err := database.Begin()
	if err != nil {
		log.Error("insertPlanOrder open transaction failed", zap.Int64("reqID", reqID), zap.Any("order", order), zap.Error(err))
		return define.ErrMsgBusy
	}
	defer database.CommitTx(tx, &err, reqID, nil)

	//var _cl, _ll, _sl int

	// 判断用户总计划单张数(计划单最多开10张)
	count, err := database.GetPlanOrderCount(tx, order.UserId)
	if err != nil {
		log.Error("insertPlanOrder GetPlanOrderCount error",
			zap.Int64("reqID", reqID),
			zap.Int64("userID", order.UserId),
			zap.String("contract", order.ContractCode),
			zap.Error(err))
		return define.ErrMsgBusy
	}
	if count >= define.MaxPlanOrderCount {
		log.Error("insertPlanOrder The number of currently untriggered planned orders reaches the upper limit",
			zap.Int64("reqID", reqID),
			zap.Int64("userID", order.UserId),
			zap.String("contract", order.ContractCode),
			zap.Int("max", define.MaxPlanOrderCount),
			zap.Int("current", count))
		return define.ErrMsgPlanOrderLimit
	}

	// 入库订单
	err = database.InsertPlanOrder(tx, order)
	if err != nil {
		log.Error("insertPlanOrder db.InsertPlanOrder error", zap.Int64("reqID", reqID), zap.Any("order", order), zap.Error(err))
		return
	}

	//// 更新持仓统计
	//err = db.UpdateUserContract(tx, order.UserId, order.CurrencyName, order.ContractCode, _cl, _ll, _sl)
	//if err != nil {
	//	log.Error("insertPlanOrder db.UpdateUserContract error", zap.Int64("reqID", reqID), zap.Any("order", order), zap.Error(err))
	//}
	return
}

// PlanCancelHandler 条件单撤单
func PlanCancelHandler(reqArg *define.ReqArg, request *model.OrderIDArg) error {
	// 加redis锁
	if !cache.SetRedisLockStr(convert.Int64String(request.UserID), request.OrderID) {
		return define.ErrMsgTradeFrequent
	}
	defer cache.SetRedisUnLockStr(convert.Int64String(request.UserID), request.OrderID)

	// 条件单直接更新数据库状态
	list, err := database.CancelConditionOrder(request.UserID, convert.String2Int64(request.OrderID), "")
	if err != nil {
		log.Error("PlanCancelHandler CancelConditionOrder db error", zap.Int64("reqID", reqArg.ReqID), zap.Error(err))
		return define.ErrMsgBusy
	}

	for i := range list {
		messagequeue.NotifyMqPlanOpenDel(&list[i])
	}

	return nil
}
