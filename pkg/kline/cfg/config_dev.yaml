identifier: "Spot-kline_01" #服务唯一标识
run_env: "test" #运行环境，prod-生产,test-测试，dev-开发
dubug: true # 是否启用调试模式
zip_http: false # 是否启用zip压缩
local_name: "Panda Spot Test KLine Server" # 服务名称
rpc_addr: "************:18190" # rpc服务监听地址
pprof_addr: "************:18191" # 服务运行状态监听地址
symbols: [ "BTC/USDT","BCH/USDT","ETH/USDT","ETC/USDT","LINK/USDT","EOS/USDT","FIL/USDT","LTC/USDT","UNI/USDT","AAVE/USDT","DOT/USDT","XRP/USDT","SOL/USDT","LUNA/USDT","ATOM/USDT","ETH/BTC" ] #服务支持合约
mq: "amqp://base:v3OEpqHwaaQlmq@************:5672" #rabbit mq config
default_db: "root:v3OEpqHwaaQlfbx@tcp(************:3306)/futures_basecoin?charset=utf8mb4&parseTime=true&loc=Local" # 数据库连接
log_file: "/data/logs/spot/kline" # 日志文件位置
log_level: "info" # 日志等级
log_only_file: true # 是否仅打印日志到文件

mail_prefix: "【Panda】" # 发送邮件前缀(报警邮件用)
caution_receiver: [] # 警告邮件通知列表
sensitive_conf: "" # 敏感信息配置文件位置
msg_server_url: "http://************:10110" # 新短信邮件服务地址
msg_server_app_id: "025633e8-c0ea-463e-bc27-04c50e8b05b8" # 新短信邮件服务appID
msg_server_secret_key: "954146C06A993123F376A0F3DD7C8076F480B4C837AEB426746410F493800871" # 新短信邮件服务secret

default_redis_conf: { # 默认redis连接
  "address": "************:6379", # 连接地址
  "password": "v3OEpqHwaaQlrds", # 连接密码
  "use_tls": false, # 是否使用tls连接
  "default_db": 5, # 使用的db号
  "pool_size": 20, # 连接池数量
  "db_nums": { # 使用的redis库号,仅会初始化这里定义了的db号 key(db号):value(描述)
    #    0: "db 0",
    #    1: "db 1",
    2: "公共数据",
    #    3: "db 3",
#    4: "USD合约",
    5: "现货",
    #    6: "db 6",
    #    7: "db 7",
    #    8: "db 8",
    #    9: "db 9",
    #    10: "db 10",
    #    11: "db 11",
    #    12: "db 12",
    #    13: "db 13",
    #    14: "db 14",
    #    15: "db 15",
  },
}
