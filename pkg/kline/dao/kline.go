/*
@Time : 2019-12-30 17:07
<AUTHOR> mocha
@File : kline
*/
package dao

import (
	"fmt"
	"spot/libs/database"
	"spot/libs/log"
	"spot/libs/proto"
)

func InsertKline(v *proto.KLine, klineQuntun string) (err error) {
	strf := "insert into tb_kline_%s(contract_code, start_time, end_time, open_price, close_price, high_price, low_price, volume, trade_value)  values (?,?,?,?,?,?,?,?,?)"
	str := fmt.Sprintf(strf, klineQuntun)
	_, err = database.DefaultDB().Exec(str, v.Symbol, v.StartTime, v.EndTime, v.OpenPrice, v.ClosePrice, v.HighPrice, v.LowPrice, v.Volume, v.TradeValue)
	if err != nil {
		log.Errorf("入库kline失败,err;%v", err)
	}
	return
}

func InsertKlines(list []*proto.KLine, klineQuntun string) (err error) {
	if len(list) == 0 {
		return nil
	}
	strf := "insert into tb_kline_%s(contract_code, start_time, end_time, open_price, close_price, high_price, low_price, volume, trade_value)  values (?,?,?,?,?,?,?,?,?)"
	str := fmt.Sprintf(strf, klineQuntun)
	smt, err := database.DefaultDB().Preparex(str)
	if err != nil {
		log.Errorf("InsertKlines get prepare db error,%v", err)
		return
	}
	for _, v := range list {
		_, err = smt.Exec(v.Symbol, v.StartTime, v.EndTime, v.OpenPrice, v.ClosePrice, v.HighPrice, v.LowPrice, v.Volume, v.TradeValue)
		if err != nil {
			log.Errorf("InsertKlines 入库kline失败,err;%v", err)
		}
	}
	return
}

func GetKlineList(klineQuntun string) (list []proto.KLine, err error) {
	strf := "select id, contract_code, start_time, end_time, open_price, close_price, high_price, low_price, volume from tb_kline_%s order by start_time asc"
	str := fmt.Sprintf(strf, klineQuntun)
	err = database.DefaultDB().Select(&list, str)
	if err != nil {
		log.Errorf("GetKlineList,err;%v", err)
	}
	return
}
