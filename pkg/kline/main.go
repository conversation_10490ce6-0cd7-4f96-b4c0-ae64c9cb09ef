package main

import (
	"fmt"
	"os"

	"spot/libs/cache"
	"spot/libs/conf"
	"spot/libs/database"
	"spot/libs/log"
	"spot/libs/pprof"
	"spot/pkg/kline/server"
	"spot/pkg/kline/service"
)

var (
	gitHash   string
	gitDigest string
	gitBranch string
	buildUser string
	buildTime string
	goVersion string
)

func main() {
	args := os.Args
	if len(args) == 2 && (args[1] == "--version" || args[1] == "-v") {
		fmt.Printf("Git Branch: %s \n", gitBranch)
		fmt.Printf("Git Commit Hash: %s \n", gitHash)
		fmt.Printf("Git Commit Digest: %s \n", gitDigest)
		fmt.Printf("Build User: %s \n", buildUser)
		fmt.Printf("Build TimeStamp: %s \n", buildTime)
		fmt.Printf("GoLang Version: %s \n", goVersion)
		return
	}

	// 初始化配置文件
	conf.Init()

	// 初始化日志
	log.InitLogger(conf.LogFile(), conf.LogLevel(), conf.LogOnlyFile())

	// 初始化缓存
	cache.InitDefaultRedisConn()

	// 初始化数据库
	database.InitDefaultDB()

	//// 初始化rpc服务
	//if err := server.InitRPC(); err != nil {
	//	log.Fatalf("init rpc server failed, err:%v", err)
	//}

	//recover kline data
	//service.RecoverKline()

	service.RegisterSymbolsConsumer()
	defer service.UnregisterSymbolsConsumer()

	//启动kline处理程序
	service.InitKlineCal()

	//启动队列kline预处理机制
	service.StartSymbolTickerHandler()

	//开始启动基础价格分钟线生成程序
	log.Info("开始启动基础价格分钟线生成程序")
	service.InitBasePriceKlineHandler()

	//int message queue
	service.InitMessageQueue()

	// 初始化服务监控
	pprof.Init(conf.PprofAddr())

	// 监听系统信号
	server.InitSignal()
}
