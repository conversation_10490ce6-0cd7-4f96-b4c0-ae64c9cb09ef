/*
@Time : 2019-12-30 17:15
<AUTHOR> mocha
@File : rpc.server
*/
package server

//func (r *RPC) NotifySymbolChange(arg *proto.SymbolArg, reply *define.Reply) error {
//	symbol, err := database.GetContractByCode(arg.ContractCode)
//	if err != nil {
//		log.Errorf("查询指定交易对信息失败:%v,contractCode;%v", err, arg.ContractCode)
//		reply.Ret = define.ErrCodeActionInvalid
//		reply.Msg = define.ErrMsgActionInvalid.Msg
//		return nil
//	}
//
//	return nil
//}
