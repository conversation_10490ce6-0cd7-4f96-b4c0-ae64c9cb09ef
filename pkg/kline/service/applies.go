/*
@Time : 3/11/20 4:11 下午
<AUTHOR> mocha
@File : applies
*/
package service

import (
	"github.com/shopspring/decimal"
	"spot/libs/cache"
	"spot/libs/commonsrv"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
)

func dealSymbolApplies(tn *TradeNode) {
	if tn == nil || tn.Price.LessThanOrEqual(decimal.Zero) {
		return
	}
	a := &proto.Applies{ContractCode: tn.ContractCode}
	//获取日k
	var h, l decimal.Decimal
	dailyKline := cache.GetkLineNodeByIndex(tn.ContractCode, define.Kline1D, -1)
	if dailyKline == nil {
		log.Errorf("获取到kline最新一条数据为空")
		return
	}
	if tn.Price.GreaterThanOrEqual(dailyKline.HighPrice) {
		h = tn.Price
	} else {
		h = dailyKline.HighPrice
	}
	if tn.Price.LessThanOrEqual(dailyKline.LowPrice) {
		l = tn.Price
	} else {
		l = dailyKline.LowPrice
	}
	c, err := commonsrv.GetContractDetail(0, a.ContractCode, nil)
	if err != nil {
		log.Errorf("commonsrv.GetContractDetai,err:%v", err)
		return
	}
	a.HighPrice = h.Truncate(c.Digit)
	a.LowPrice = l.Truncate(c.Digit)
	if tn.Price.IsZero() {
		log.Errorf("成交价为0")
		return
	}
	a.ChangeDaily, a.Change = calApplies(dailyKline.OpenPrice, tn.Price)
	a.Change = a.Change.Round(c.Digit)
	a.Change10m = getDurationApplies(define.AppliesChange10m, tn, 10)
	a.Change30m = getDurationApplies(define.AppliesChange30m, tn, 30)
	a.Change1h = getDurationApplies(define.AppliesChange1h, tn, 1*60)
	a.Change2h = getDurationApplies(define.AppliesChange2h, tn, 2*60)
	a.Change4h = getDurationApplies(define.AppliesChange4h, tn, 4*60)
	a.Change8h = getDurationApplies(define.AppliesChange8h, tn, 8*60)
	a.Change24h = getDurationApplies(define.AppliesChange24h, tn, 24*60)
	a.TradeV24h, a.TradeM24h = Get24hTradeVolume(tn.ContractCode, define.Kline2H)
	cache.SetContractAppliesChange(a)
	SendAppliesMsg(a)
	log.Infof("%v涨跌幅:%+v", a.ContractCode, a)
}

//取最近一根kline
func Get24hTradeVolume(code, timeQuan string) (decimal.Decimal, decimal.Decimal) {
	var start, end int64
	start, end = -1, -1
	switch timeQuan {
	case define.Kline1D:
		start, end = -1, -1
	case define.Kline12H:
		start = -2
	case define.Kline6H:
		start = -4
	case define.Kline2H:
		start = -12
	case define.Kline1H:
		start = -24
	}
	list, err := cache.GetKLineNodeASC(code, timeQuan, start, end)
	if err != nil {
		log.Errorf("Get24hTradeVolume ->cache.GetKLineNodeASC fail,%v", err)
		return decimal.Zero, decimal.Zero
	}
	var total, totalV decimal.Decimal
	for _, line := range list {
		if line.Volume.LessThanOrEqual(decimal.Zero) {
			continue
		}
		total = total.Add(line.Volume)
		totalV = totalV.Add(line.TradeValue)
	}
	return total, totalV
}

func getDurationApplies(name string, tn *TradeNode, mintus int64) decimal.Decimal {
	var a decimal.Decimal
	size := cache.GetkLineSize(tn.ContractCode, define.Kline1M)
	if mintus > size {
		mintus = size
	}
	//获取kline -minutus根
	last := cache.GetkLineNodeByIndex(tn.ContractCode, define.Kline1M, -mintus)
	if last != nil {
		log.Debugf("合约:%v,%v之前kline:%+v", tn.ContractCode, name, last)
		a, _ = calApplies(last.OpenPrice, tn.Price)
	} else {
		log.Debugf("合约:%v,%v获取kline失败", tn.ContractCode, name)
	}
	return a
}

func calApplies(start, end decimal.Decimal) (decimal.Decimal, decimal.Decimal) {
	d := end.Sub(start)
	return d.Div(end).Mul(define.DecimalHundred).Round(2), d
}
