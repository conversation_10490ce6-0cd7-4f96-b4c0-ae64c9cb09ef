/*
@Time : 3/11/20 4:23 下午
<AUTHOR> mocha
@File : calkine
*/
package service

import (
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
	"spot/pkg/kline/dao"
	"time"
)

type TradeNode struct {
	Id           int64
	ContractCode string
	Price        decimal.Decimal
	PAmount      decimal.Decimal //平台成交量
	Volume       decimal.Decimal
	Ts           int64
}

// 计算分时K线
func doCalcKlineByDuration(order *TradeNode, duration time.Duration) {
	if order == nil {
		return
	}
	//log.Infof("计算分时:%v,order:%+v", duration, *order)
	if order == nil {
		return
	}
	var (
		err         error
		timeQuantum string
	)
	switch duration {
	case define.Kline1MValue:
		timeQuantum = define.Kline1M
		break
	case define.Kline5MValue:
		timeQuantum = define.Kline5M
		break
	case define.Kline15MValue:
		timeQuantum = define.Kline15M
		break
	case define.Kline30MValue:
		timeQuantum = define.Kline30M
		break
	case define.Kline1HValue:
		timeQuantum = define.Kline1H
		break
	case define.Kline2HValue:
		timeQuantum = define.Kline2H
		break
	case define.Kline4HValue:
		timeQuantum = define.Kline4H
		break
	case define.Kline6HValue:
		timeQuantum = define.Kline6H
		break
	case define.Kline12HValue:
		timeQuantum = define.Kline12H
		break
	case define.Kline1DValue:
		timeQuantum = define.Kline1D
		break
	case define.Kline1WValue:
		timeQuantum = define.Kline1W
		break
	default:
		log.Errorf("calcKlineByDuration not support duration: %d", duration)
		break
	}
	if len(timeQuantum) > 0 {
		err = updateKLineNode(order.ContractCode, timeQuantum, int64(duration), order)
		if err != nil {
			log.Errorf("calcKlineOneWeek redis UpdateKLineNode error: %v", err)
			return
		}

	}
}

func updateKLineNode(contractCode string, timeQuantum string, timeQuantumValue int64, order *TradeNode) (err error) {
	log.Debug("开始处理分时线", zap.Any("code", contractCode), zap.String("quan", timeQuantum), zap.Any("data", order))
	currentNode, err := cache.GetKLineNodeByIndex(contractCode, timeQuantum, -1)
	if err != nil {
		log.Errorf("updateKLineNode cache.GetKLineNodeByIndex fail,%v,contractCode;%v，timeQuantum：%v,order:%+v", err, contractCode, timeQuantum, *order)
		return
	}
	if currentNode == nil {
		//直接插入节点
		createFirstNode(timeQuantum, timeQuantumValue, order)
		return
	}

	//检查并修订最近节点错误数据
	if currentNode.OpenPrice.GreaterThan(decimal.Zero) {
		if currentNode.ClosePrice.LessThanOrEqual(decimal.Zero) || currentNode.LowPrice.LessThanOrEqual(decimal.Zero) {
			if currentNode.ClosePrice.LessThanOrEqual(decimal.Zero) {
				currentNode.ClosePrice = currentNode.OpenPrice
			}
			if currentNode.OpenPrice.LessThan(currentNode.LowPrice) {
				currentNode.LowPrice = currentNode.OpenPrice
			}
			if currentNode.ClosePrice.LessThan(currentNode.LowPrice) {
				currentNode.LowPrice = currentNode.ClosePrice
			}
			_ = cache.UpdateKlineNodeByIndex(timeQuantum, -1, currentNode)
		}
	}

	//有节点,交易时间小于上个节点结束时间
	if order.Ts < currentNode.EndTime {
		//修改节点
		updateCurrentKLineNode(timeQuantum, timeQuantumValue, currentNode, order)
		return
	}

	if order.Ts >= currentNode.EndTime {
		//入库本节点
		err = dao.InsertKline(currentNode, timeQuantum)
		if err != nil {
			log.Errorf("插入数据库%v,失败,err:%v", timeQuantum, err)
		}
		var nodes []*proto.KLine
		for {
			currentNode = createNextKLineNode(timeQuantum, timeQuantumValue, currentNode)
			if order.Ts < currentNode.EndTime {
				//修改节点
				updateCurrentKLineNode(timeQuantum, timeQuantumValue, currentNode, order)
				break
			} else {
				nodes = append(nodes, currentNode)
			}
		}
		err = dao.InsertKlines(nodes, timeQuantum)
		if err != nil {
			log.Errorf("插入数据库%v,失败,err:%v", timeQuantum, err)
		}
		return
	}

	return
}

func createFirstNode(name string, duration int64, order *TradeNode) (node *proto.KLine) {
	//nodeStartTime := (order.Ts / duration) * duration
	var nodeStartTime int64
	switch name {
	case define.Kline1M:
		nodeStartTime = createMin(order.Ts, 1).Unix()
	case define.Kline5M:
		nodeStartTime = createMin(order.Ts, 5).Unix()
	case define.Kline15M:
		nodeStartTime = createMin(order.Ts, 15).Unix()
	case define.Kline30M:
		nodeStartTime = createMin(order.Ts, 30).Unix()
	case define.Kline1H:
		nodeStartTime = createHour(order.Ts, 1).Unix()
	case define.Kline2H:
		nodeStartTime = createHour(order.Ts, 2).Unix()
	case define.Kline4H:
		nodeStartTime = createHour(order.Ts, 4).Unix()
	case define.Kline6H:
		nodeStartTime = createHour(order.Ts, 6).Unix()
	case define.Kline12H:
		nodeStartTime = createHour(order.Ts, 12).Unix()
	case define.Kline1D:
		nodeStartTime = createHour(order.Ts, 24).Unix()
	case define.Kline1W:
		nodeStartTime = createWeek(order.Ts).Unix()
		//nodeStartTime = GetFirstDateOfWeekForChina(order.Ts).Unix()
	}
	node = &proto.KLine{
		ID:         database.NextID(),
		Symbol:     order.ContractCode,
		StartTime:  nodeStartTime,
		EndTime:    nodeStartTime + duration,
		OpenPrice:  order.Price,
		ClosePrice: order.Price,
		HighPrice:  order.Price,
		LowPrice:   order.Price,
		Volume:     order.Volume,
		PVolume:    order.PAmount,
		Ts:         time.Now().Unix(),
	}
	cache.PushKLineNode(node, name)
	return
}

//old
func GetFirstDateOfWeekForChina(thisTime int64) (t time.Time) {
	now := time.Unix(thisTime, 0)
	offset := int(time.Monday - now.Weekday())
	if offset > 0 {
		offset = -6
	}

	t = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.UTC).AddDate(0, 0, offset)
	return
}

func createNextKLineNode(name string, duration int64, preNode *proto.KLine) (node *proto.KLine) {
	//ctime := preNode.EndTime + 1
	//nodeStartTime := (ctime / duration) * duration
	nodeStartTime := preNode.EndTime
	node = &proto.KLine{
		ID:         database.NextID(),
		Symbol:     preNode.Symbol,
		StartTime:  nodeStartTime,
		EndTime:    nodeStartTime + duration,
		OpenPrice:  preNode.ClosePrice,
		ClosePrice: preNode.ClosePrice,
		HighPrice:  preNode.ClosePrice,
		LowPrice:   preNode.ClosePrice,
		Volume:     decimal.Zero,
		PVolume:    decimal.Zero,
		Ts:         time.Now().Unix(),
	}
	cache.PushKLineNode(node, name)
	return
}

func updateCurrentKLineNode(name string, duration int64, node *proto.KLine, order *TradeNode) (err error) {
	if order.Price.LessThanOrEqual(decimal.Zero) {
		log.Info("修改最近节点，获取到价格为0，不处理本次更新节点", zap.Any("order", order))
		return
	}
	if node.HighPrice.LessThan(order.Price) {
		node.HighPrice = order.Price
	}
	if node.LowPrice.GreaterThan(order.Price) {
		node.LowPrice = order.Price
	}
	node.ClosePrice = order.Price
	node.Volume = node.Volume.Add(order.Volume)
	node.TradeValue = node.TradeValue.Add(order.Volume.Mul(order.Price).Truncate(define.FullPrecision))
	node.PVolume = node.PVolume.Add(order.PAmount)
	_ = cache.UpdateKlineNodeByIndex(name, -1, node)
	return
}

func createMin(d int64, gasM int) time.Time {
	s := time.Unix(d, 0)
	m := (s.Minute() / gasM) * gasM
	return time.Date(s.Year(), s.Month(), s.Day(), s.Hour(), m, 0, 0, time.Local)
}

func createHour(d int64, gap int) time.Time {
	s := time.Unix(d, 0)
	h := (s.Hour() / gap) * gap
	return time.Date(s.Year(), s.Month(), s.Day(), h, 0, 0, 0, time.Local)
}

func createWeek(thisTime int64) (t time.Time) {
	now := time.Unix(thisTime, 0)
	offset := int(time.Monday - now.Weekday())
	if offset > 0 {
		offset = -6
	}

	t = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local).AddDate(0, 0, offset)
	return
}
