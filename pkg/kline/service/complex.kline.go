package service

import (
	"github.com/shopspring/decimal"
	"spot/libs/cache"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
	"time"
)

func InitBasePriceKlineHandler() {
	DepthKlineHandler = futures_basecoinPriceKlineHandler()
	DepthKlineHandler.Run()
}

// DepthKlineHandler 处理现货基准价格、标记价格、铺单基准价格分钟线
var DepthKlineHandler *BasePriceKlineHandler

type BasePriceKlineHandler struct {
	SpotChannel      chan proto.ComplexPrice      //现货channel
	MarketChannel    chan proto.ComplexPrice      //标记价格及现货channel
	DepthBaseChannel chan proto.ComplexPrice      //铺单基准价
	CodeHandlerMap   map[string]*BaseKlineHandler //合约处理器
}

func futures_basecoinPriceKlineHandler() *BasePriceKlineHandler {
	return &BasePriceKlineHandler{
		SpotChannel:      make(chan proto.ComplexPrice, 10240),
		MarketChannel:    make(chan proto.ComplexPrice, 10240),
		DepthBaseChannel: make(chan proto.ComplexPrice, 10240),
		CodeHandlerMap:   make(map[string]*BaseKlineHandler),
	}
}

func (bh *BasePriceKlineHandler) IsDepthChannelFull() bool {
	return len(bh.DepthBaseChannel) == cap(bh.DepthBaseChannel)
}

func (bh *BasePriceKlineHandler) IsMarketChannelFull() bool {
	return len(bh.MarketChannel) == cap(bh.MarketChannel)
}

func (bh *BasePriceKlineHandler) Run() {
	go func() {
		for {
			select {
			case spot := <-bh.SpotChannel:
				if h, ok := bh.CodeHandlerMap[spot.ContractCode]; ok {
					h.market <- spot
				} else {
					h = futures_basecoinKlineHandler(spot.ContractCode)
					bh.CodeHandlerMap[spot.ContractCode] = h
					h.Run()
				}
			case market := <-bh.MarketChannel:
				if h, ok := bh.CodeHandlerMap[market.ContractCode]; ok {
					h.market <- market
				} else {
					h = futures_basecoinKlineHandler(market.ContractCode)
					bh.CodeHandlerMap[market.ContractCode] = h
					h.Run()
				}
			case depth := <-bh.DepthBaseChannel:
				if h, ok := bh.CodeHandlerMap[depth.ContractCode]; ok {
					h.depth <- depth
				} else {
					h = futures_basecoinKlineHandler(depth.ContractCode)
					bh.CodeHandlerMap[depth.ContractCode] = h
					h.Run()
				}
			}
		}
	}()
}

type BaseKlineHandler struct {
	code        string
	spot        chan proto.ComplexPrice //标记价格及现货channel
	market      chan proto.ComplexPrice //标记价格及现货channel
	depth       chan proto.ComplexPrice //铺单基准价
	durationMap map[string]time.Duration
}

func futures_basecoinKlineHandler(code string) *BaseKlineHandler {
	return &BaseKlineHandler{
		code:        code,
		spot:        make(chan proto.ComplexPrice, 10240),
		market:      make(chan proto.ComplexPrice, 10240),
		depth:       make(chan proto.ComplexPrice, 10240),
		durationMap: make(map[string]time.Duration),
	}
}

func (h *BaseKlineHandler) Run() {
	h.init()
	go func() {
		tick := time.Tick(1 * time.Minute)
		for {
			select {
			case market := <-h.spot:
				h.dealKline(define.BasePriceSpot, &KNode{Price: market.Price, Ts: market.TS})
			case market := <-h.market:
				h.dealKline(define.BasePriceMarket, &KNode{Price: market.Price, Ts: market.TS})
			case depth := <-h.depth:
				h.dealKline(define.BasePriceDepth, &KNode{Price: depth.Price, Ts: depth.TS})
			case <-tick:
				h.spot <- proto.ComplexPrice{
					ContractCode: h.code,
					Price:        decimal.Zero,
				}
				h.market <- proto.ComplexPrice{
					ContractCode: h.code,
					Price:        decimal.Zero,
				}
				h.depth <- proto.ComplexPrice{
					ContractCode: h.code,
					Price:        decimal.Zero,
				}
			}
		}

	}()
}

func (h *BaseKlineHandler) init() {
	h.durationMap[define.Kline1M] = define.Kline1MValue
}

func (h *BaseKlineHandler) dealKline(priceType string, k *KNode) {
	for name, duration := range h.durationMap {
		h.handle(name, duration, priceType, k)
	}
}

func (h *BaseKlineHandler) handle(name string, duration time.Duration, priceType string, order *KNode) {
	dur := int64(duration)
	cur := cache.GetBasePriceKLineNodeByIndex(priceType, h.code, name, -1)
	if order == nil {
		return
	}
	if cur == nil {
		if order.Price.LessThanOrEqual(decimal.Zero) {
			return
		}
		cur = h.createFirstNode(name, dur, order)
		err := cache.PushBasePriceKLineNode(priceType, name, cur)
		if err != nil {
			log.Errorf("cache.PushBasePriceKLineNode fail,%v", err)
		}
		return
	}
	//有节点,交易时间小于上个节点结束时间
	if order.Ts < cur.EndTime {
		//修改节点
		cur = h.updateCurrentKLineNode(cur, order)
		err := cache.UpdateBasePriceKlineNodeByIndex(priceType, name, -1, cur)
		if err != nil {
			log.Errorf("cache.UpdateBasePriceKlineNodeByIndex fail,%v", err)
		}
		return
	}

	if order.Ts >= cur.EndTime {
		for {
			cur = h.createNextKLineNode(dur, cur)
			cache.PushBasePriceKLineNode(priceType, name, cur)
			if order.Ts < cur.EndTime {
				cur = h.updateCurrentKLineNode(cur, order)
				err := cache.UpdateBasePriceKlineNodeByIndex(priceType, name, -1, cur)
				if err != nil {
					log.Errorf("cache.UpdateBasePriceKlineNodeByIndex fail,%v", err)
				}
				break
			}
		}
		return
	}
}

func (h *BaseKlineHandler) createNextKLineNode(duration int64, preNode *proto.KLine) (node *proto.KLine) {
	nodeStartTime := preNode.EndTime
	node = &proto.KLine{
		ID:         database.NextID(),
		Symbol:     preNode.Symbol,
		StartTime:  nodeStartTime,
		EndTime:    nodeStartTime + duration,
		OpenPrice:  preNode.ClosePrice,
		ClosePrice: preNode.ClosePrice,
		HighPrice:  preNode.ClosePrice,
		LowPrice:   preNode.ClosePrice,
	}
	return
}

func (h *BaseKlineHandler) updateCurrentKLineNode(node *proto.KLine, order *KNode) *proto.KLine {
	price := order.Price
	if !price.IsPositive() {
		price = node.ClosePrice
	}
	if node.HighPrice.LessThan(price) {
		node.HighPrice = price
	}
	if node.LowPrice.GreaterThan(price) {
		node.LowPrice = price
	}
	node.ClosePrice = price
	return node
}

type KNode struct {
	Price decimal.Decimal
	Ts    int64
}

func (h *BaseKlineHandler) createFirstNode(name string, duration int64, order *KNode) (node *proto.KLine) {
	var nodeStartTime int64
	switch name {
	case define.Kline1M:
		nodeStartTime = createMin(order.Ts, 1).Unix()
	case define.Kline5M:
		nodeStartTime = createMin(order.Ts, 5).Unix()
	case define.Kline15M:
		nodeStartTime = createMin(order.Ts, 15).Unix()
	case define.Kline30M:
		nodeStartTime = createMin(order.Ts, 30).Unix()
	case define.Kline1H:
		nodeStartTime = createHour(order.Ts, 1).Unix()
	case define.Kline2H:
		nodeStartTime = createHour(order.Ts, 2).Unix()
	case define.Kline4H:
		nodeStartTime = createHour(order.Ts, 4).Unix()
	case define.Kline6H:
		nodeStartTime = createHour(order.Ts, 6).Unix()
	case define.Kline12H:
		nodeStartTime = createHour(order.Ts, 12).Unix()
	case define.Kline1D:
		nodeStartTime = createHour(order.Ts, 24).Unix()
	case define.Kline1W:
		nodeStartTime = createWeek(order.Ts).Unix()
	}
	node = &proto.KLine{
		ID:         database.NextID(),
		Symbol:     h.code,
		StartTime:  nodeStartTime,
		EndTime:    nodeStartTime + duration,
		OpenPrice:  order.Price,
		ClosePrice: order.Price,
		HighPrice:  order.Price,
		LowPrice:   order.Price,
	}
	return
}

//func createMin(d int64, gasM int) time.Time {
//	s := time.Unix(d, 0)
//	m := (s.Minute() / gasM) * gasM
//	return time.Date(s.Year(), s.Month(), s.Day(), s.Hour(), m, 0, 0, time.Local)
//}
//
//func createHour(d int64, gap int) time.Time {
//	s := time.Unix(d, 0)
//	h := (s.Hour() / gap) * gap
//	return time.Date(s.Year(), s.Month(), s.Day(), h, 0, 0, 0, time.Local)
//}
//
//func createWeek(thisTime int64) (t time.Time) {
//	now := time.Unix(thisTime, 0)
//	offset := int(time.Monday - now.Weekday())
//	if offset > 0 {
//		offset = -6
//	}
//
//	t = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local).AddDate(0, 0, offset)
//	return
//}
