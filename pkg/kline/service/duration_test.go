/*
@Time : 3/12/20 8:19 上午
<AUTHOR> mocha
@File : duration_test
*/
package service

import (
	"spot/libs/define"
	"testing"
	"time"
)

func TestDuration(t *testing.T) {
	otime := time.Date(2020, 3, 13, 7, 20, 20, 00, time.Local).Unix()
	t.Logf("o:%v", otime)
	d := define.Kline1DValue
	nodeStartTime := otime / int64(d) * int64(d)
	t.Logf("n:%v", nodeStartTime)
	ti := time.Unix(nodeStartTime, 0)
	t.Logf("t:%v", ti)
	t.Logf("t:%v", ti.Unix())

	nodeStartTime2 := (otime/int64(define.Kline1MValue))*int64(define.Kline1MValue) - int64(8*time.Hour/time.Second)
	ti1 := time.Unix(nodeStartTime2, 0)
	t.Logf("t:%v", ti1)

}

func TestHalftTime(t *testing.T) {
	otime := time.Date(2020, 3, 12, 12, 30, 20, 00, time.Local).Unix()
	nodeStartTime := (otime/int64(define.Kline12HValue))*int64(define.Kline12HValue) + int64(4*time.Hour/time.Second)
	t.Log(time.Unix(nodeStartTime, 0))
	nodeStartTime2 := (otime/int64(define.Kline12HValue))*int64(define.Kline12HValue) - int64(8*time.Hour/time.Second)
	t.Log(time.Unix(nodeStartTime2, 0))
}

func TestTime2(t *testing.T) {
	otime := time.Date(2020, 12, 4, 1, 3, 20, 00, time.Local).Unix()
	nodeStartTime := (otime / int64(define.Kline6HValue)) * int64(define.Kline6HValue)
	t.Log(time.Unix(nodeStartTime, 0))
}

func TestFirstWeek(t *testing.T) {
	t.Log(GetFirstDateOfWeekForChina(time.Now().Unix()))
	u := GetFirstDateOfWeekForChina(time.Now().Unix()).Unix()
	d := time.Unix(u, 0)
	t.Log(d)
	t.Log(5 / 2 * 2)
}

func TestBegin(t *testing.T) {
	//t.Log(createWeek(time.Now().Unix()))
	t.Log(createHour(time.Date(2020, 10, 20, 9, 50, 20, 30, time.Local).Unix(), 24))
	//t.Log(createMin(time.Date(2020, 10, 20, 5, 50, 20, 30, time.Local).Unix(), 60))
}
