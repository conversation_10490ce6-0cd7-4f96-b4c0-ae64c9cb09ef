/*
@Time : 3/11/20 3:31 下午
<AUTHOR> mocha
@File : init
*/
package service

import (
	"go.uber.org/zap"
	"math/rand"
	"spot/libs/container/safemap"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/nums"
	"sync"
	"time"
)

func TestKline() {
	for {
		n := rand.Intn(60)
		price := nums.NewFromInt(n).Mul(nums.NewFromInt(200))
		amount := nums.NewFromInt(n * 100)
		t := &TradeNode{Id: database.NextID(), ContractCode: "ETHUSD", Price: price, Volume: amount, Ts: time.Now().Unix()}
		DefaultKlineHandler.handleKlineCalc(t)
		i := time.Duration(n) * time.Second
		time.Sleep(time.Duration(i))
	}

}

func InitKlineCal() {
	DefaultKlineHandler = newKlineHandler(klineQueueSize, klineWarnSize)
	go DefaultKlineHandler.startKlineMonitor()
}

var (
	DefaultKlineHandler *KlineHandler
	klineQueueSize      = 10000
	klineWarnSize       = 8000
	onceWarn            sync.Once
)

type TradeQueue struct {
	contractCode  string
	tradeQueueMap map[time.Duration]chan *TradeNode
}

func (t *TradeQueue) Loop() {
	for duration, orderChan := range t.tradeQueueMap {
		go doCalcKline(orderChan, duration)
	}
}

type KlineHandler struct {
	queueSize        int
	queueWarnSize    int
	contractQueueMap *safemap.Map //map[string]*TradeQueue
}

func newKlineHandler(queueSize, warnSize int) *KlineHandler {
	return &KlineHandler{
		queueSize:        queueSize,
		queueWarnSize:    queueSize,
		contractQueueMap: safemap.New(),
	}
}

func (h *KlineHandler) startKlineMonitor() {
	for {
		time.Sleep(1 * time.Minute)
		h.contractQueueMap.Each(func(c interface{}, v interface{}) {
			tradeQueue, ok := v.(*TradeQueue)
			if !ok {
				log.Errorf("获取队列出错,非TradeQueue类型")
				return
			}
			for duration, orderChan := range tradeQueue.tradeQueueMap {
				chanLength := len(orderChan)
				if chanLength > h.queueWarnSize {
					log.Infof("kline order chan is too long , tradePair: %s, duration:%d, chanLength:%d", tradeQueue.contractCode, duration, chanLength)
					onceWarn.Do(func() {
						warnManager(tradeQueue.contractCode, chanLength)
					})
				}
			}
		})
	}
}

func (h *KlineHandler) handleKlineCalc(order *TradeNode) {
	log.Infof("收到新的成交:%+v", *order)
	if !order.Price.IsPositive() {
		log.Info("新的成交价格异常，请注意查看来源", zap.Any("data", order))
	}

	var tq *TradeQueue
	tqv, ok := h.contractQueueMap.Get(order.ContractCode)
	if ok {
		var isMatch bool
		tq, isMatch = tqv.(*TradeQueue)
		if !isMatch {
			log.Errorf("handleKlineCalc not match *TradeQueue type")
			return
		}
	} else {
		tq = &TradeQueue{contractCode: order.ContractCode, tradeQueueMap: h.crateTradeQueueMap()}
		tq.Loop()
		h.contractQueueMap.Set(order.ContractCode, tq)
	}

	for _, orderChan := range tq.tradeQueueMap {
		orderChan <- order
	}

}

func (h *KlineHandler) crateTradeQueueMap() map[time.Duration]chan *TradeNode {
	orderChanMap := make(map[time.Duration]chan *TradeNode)
	orderChanMap[define.Kline1MValue] = make(chan *TradeNode, h.queueSize)
	orderChanMap[define.Kline5MValue] = make(chan *TradeNode, h.queueSize)
	orderChanMap[define.Kline15MValue] = make(chan *TradeNode, h.queueSize)
	orderChanMap[define.Kline30MValue] = make(chan *TradeNode, h.queueSize)
	orderChanMap[define.Kline1HValue] = make(chan *TradeNode, h.queueSize)
	orderChanMap[define.Kline2HValue] = make(chan *TradeNode, h.queueSize)
	orderChanMap[define.Kline4HValue] = make(chan *TradeNode, h.queueSize)
	orderChanMap[define.Kline6HValue] = make(chan *TradeNode, h.queueSize)
	orderChanMap[define.Kline12HValue] = make(chan *TradeNode, h.queueSize)
	orderChanMap[define.Kline1DValue] = make(chan *TradeNode, h.queueSize)
	orderChanMap[define.Kline1WValue] = make(chan *TradeNode, h.queueSize)
	return orderChanMap
}

func doCalcKline(orderChan chan *TradeNode, duration time.Duration) {
	for {
		order := <-orderChan
		if duration == define.Kline1MValue {
			dealSymbolApplies(order)
		}
		//计算kline
		doCalcKlineByDuration(order, duration) //计算k先分时线
	}
}

func warnManager(code string, length int) {
	//ip, err := utils.GetLocalIp()
	//if err != nil {
	//	log.Errorf("warnManager GetLocalIp err %v", err)
	//}
	//title := fmt.Sprintf("IP:%s k线计算压力过大, 请及时处理", ip)
	//content := fmt.Sprintf("tradePairId:%d, 当前队列长度:%d, 缓存区设计长度:%d, 时间:%s", pairId, length, KLINE_LIST_LEN, time.Now().String())
}

//
//func initContractLastLine1m(list []proto.Contract) {
//
//	for _, contract := range list {
//		//获取合约分钟线最后node
//		node := cache.GetkLineNodeByIndex(contract.ContractCode, define.Kline1M, -1)
//		if node != nil {
//			contractLastNode.Set(contract.ContractCode, *node)
//		}
//	}
//}
//
//func InitContractKlineQueue(symbols []proto.Contract) {
//	SymbolKlineContainer = NewSymbolKlineQueue()
//	for _, v := range symbols {
//		q := SymbolKlineContainer.Get(v.ContractCode)
//		if q == nil {
//			SymbolKlineContainer.Put(v.ContractCode, NewKlineQueue())
//		}
//	}
//}
