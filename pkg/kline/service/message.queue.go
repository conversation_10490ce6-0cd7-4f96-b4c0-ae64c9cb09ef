/*
@Time : 2019-12-31 10:53
<AUTHOR> mocha
@File : message.queue
*/
package service

import (
	"encoding/json"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/conf"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/messagequeue"
	"spot/libs/mq"
	"spot/libs/nums"
	"spot/libs/proto"
)

var (
	mqProduct      *mq.MessageQueue
	mqConsumer     *mq.MessageQueue
	supportSymbols = make(map[string]bool)
)

func InitMessageQueue() {
	//生产者
	messagequeue.NewMqProduct(conf.MQ(), nums.Int64String(conf.WorkerID()))

	//消费者
	handler := make(map[string]mq.MsgHandleFunc)
	handler[define.MQTopicPriceIndex] = MQDealNewTradeMsg   //处理最新成交
	handler[define.MQTopicComplexPrice] = MQDealMarkPrice   //处理标记价格
	handler[define.MQTopicSpotIndexPrice] = MQDealSpotPrice //处理现货指数
	//handler[define.MQTopicDepthIndexPrice] = MQDealDepthPrice //处理铺单基准价格
	messagequeue.NewMqConsumer(define.MQDefaultExchangeName, define.MQConsumerKlineQueueName, define.MQDefaultMQBindKey, nums.Int2String(conf.MQQueueID()), handler)
}

func StopMQProduct() {
	messagequeue.StopMQProduct()
}

func StopMQConsumer() {
	messagequeue.StopMQConsumer()
}

//最新成交消息
func MQDealNewTradeMsg(mp mq.MessagePack) {
	d := new(proto.IndexHistory)
	err := json.Unmarshal(mp.Data, d)
	if err != nil {
		log.Errorf("MQDealNewTradeMsg json unMarsha fail,%v", err)
		return
	}
	if !supportSymbols[d.ContractCode] {
		// 不需要处理的交易对
		return
	}
	if d.TradePrice.LessThanOrEqual(decimal.Zero) {
		log.Errorf("传递价格异常：%+v", *d)
		return
	}

	tn := &TradeNode{Id: d.IndexId, ContractCode: d.ContractCode, Price: d.TradePrice, Volume: d.DealAmount, Ts: d.CreateBy.Unix()}
	if d.IsMain {
		tn.PAmount = tn.Volume
	}
	SymbolTickerHandler.Push(*tn)
	//DefaultKlineHandler.handleKlineCalc(tn)
}

func MQDealDepthPrice(mp mq.MessagePack) {
	d := new(proto.ComplexPrice)
	err := json.Unmarshal(mp.Data, d)
	if err != nil {
		log.Errorf("MQDealDepthPrice json unMarsha fail,%v", err)
		return
	}
	if !supportSymbols[d.ContractCode] {
		// 不需要处理的交易对
		return
	}

	log.Info("MQDealDepthPrice 本次处理交易对", zap.Any("d", d))
	if d.Price.LessThanOrEqual(decimal.Zero) {
		log.Errorf("传递价格异常：%+v", *d)
		return
	}

	if DepthKlineHandler.IsDepthChannelFull() {
		log.Errorf("合约：%v，kline铺单基准价格队列已满", d.ContractCode)
		return
	}
	DepthKlineHandler.DepthBaseChannel <- *d

}

func MQDealSpotPrice(mp mq.MessagePack) {
	d := new(proto.ComplexPrice)
	err := json.Unmarshal(mp.Data, d)
	if err != nil {
		log.Errorf("MQDealSpotPrice json unMarsha fail,%v", err)
		return
	}
	if !supportSymbols[d.ContractCode] {
		// 不需要处理的交易对
		return
	}

	log.Info("MQDealSpotPrice 本次处理交易对", zap.Any("d", d))
	if d.Price.LessThanOrEqual(decimal.Zero) {
		log.Errorf("传递价格异常：%+v", *d)
		return
	}
}

func MQDealMarkPrice(mp mq.MessagePack) {
	d := new(proto.ComplexPrice)
	err := json.Unmarshal(mp.Data, d)
	if err != nil {
		log.Errorf("MQDealMarkPrice json unMarsha fail,%v", err)
		return
	}
	if !supportSymbols[d.ContractCode] {
		// 不需要处理的交易对
		return
	}
	log.Info("MQDealMarkPrice 本次处理交易对", zap.Any("d", d))

	if d.Price.LessThanOrEqual(decimal.Zero) {
		log.Errorf("传递价格异常：%+v", *d)
		return
	}
	DepthKlineHandler.MarketChannel <- *d
}

func RegisterSymbolsConsumer() {
	symbols := conf.Symbols()
	if len(symbols) == 0 {
		panic("not register anyone symbol")
	}
	for _, symbol := range symbols {
		if cache.InitServerSymbolInit(define.ServerNameKline, symbol, conf.Identifier()) {
			supportSymbols[symbol] = true
		} else {
			if cache.IsEqualSrvIdentity(define.ServerNameKline, symbol, conf.Identifier()) {
				supportSymbols[symbol] = true
			} else {
				log.Infof("RegisterSymbolsConsumer %v交易对已被其他服务注册，请查看，或手动删除该交易对", symbol)
			}
		}
	}
}

func UnregisterSymbolsConsumer() {
	for symbol := range supportSymbols {
		cache.DelServerSymbol(define.ServerNameKline, symbol)
	}
}
