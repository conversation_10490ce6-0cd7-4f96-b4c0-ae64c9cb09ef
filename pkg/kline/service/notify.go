/*
@Time : 3/9/20 4:02 下午
<AUTHOR> mocha
@File : notify 通知push模块
*/
package service

import (
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/messagequeue"
	"spot/libs/mq"
	"spot/libs/proto"
)

//发送涨跌幅消息
func SendAppliesMsg(market *proto.Applies) {
	apply := &proto.ContractApply{Symbol: market.ContractCode, ChangeRatio: market.ChangeDaily, Change: market.Change, HighPrice: market.HighPrice, LowPrice: market.LowPrice, TradeV24h: market.TradeV24h, TradeM24h: market.TradeM24h}

	b, err := json.Marshal(apply)
	if err != nil {
		log.Errorf("notifyMarketPrice json marshal fail,%v", err)
		return
	}
	msg := mq.MessagePack{Topic: define.MQTopicContractApplies, Data: b}
	messagequeue.SendMessage(msg)
}
