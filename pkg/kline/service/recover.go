package service

import (
	"spot/libs/cache"
	"spot/libs/conf"
	"spot/libs/define"
	"spot/libs/log"
	"spot/pkg/kline/dao"
)

//是否需要恢复数据
//慎重，不会检测redis是否有数据，直接rpush
func IsNeedRecover() bool {
	return conf.KlineRecover()
}

//默认周Kline使用7日间隔
//true 则使用周一作为第一天
func IsWeekNature() bool {
	return conf.KlineWeekNature()
}

//从数据库恢复kline到redis
func RecoverKline() {
	if !IsNeedRecover() {
		return
	}
	log.Infof("开始从数据库中恢复kline......")
	defer log.Infof("恢复完成")
	duration := define.GetKlineDuration()
	for _, s := range duration {
		list, err := dao.GetKlineList(s)
		if err != nil {
			log.Errorf("dao getKlines fail,qun:%v,err:%v", s, err)
			continue
		}
		for _, line := range list {
			k := line
			cache.PushKLineNode(&k, s)
		}

	}
}
