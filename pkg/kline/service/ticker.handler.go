package service

import (
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"spot/libs/conf"
	"spot/libs/log"
	"time"
)

var SymbolTickerHandler *TickHandler

// StartSymbolTickerHandler kline ticker预处理机制
func StartSymbolTickerHandler() {
	SymbolTickerHandler = NewTickerHandler()
	SymbolTickerHandler.Run()
}

type TickHandler struct {
	entry       chan TradeNode
	symbolEntry map[string]*SymbolEntry
}

func NewTickerHandler() *TickHandler {
	return &TickHandler{
		entry:       make(chan TradeNode, 10240),
		symbolEntry: make(map[string]*SymbolEntry),
	}
}

func (th *TickHandler) Push(ticker TradeNode) {
	ts := time.Unix(ticker.Ts, 0).Truncate(time.Minute).Unix()
	ticker.Ts = ts
	th.entry <- ticker
}
func (th *TickHandler) Run() {
	go func() {
		for t := range th.entry {
			log.Info("kline handler entry", zap.Int("队列长度", len(th.entry)), zap.Int("cap", cap(th.entry)))
			if item, ok := th.symbolEntry[t.ContractCode]; ok {
				item.entry <- t
			} else {
				item = NewSymbolEntry(t.ContractCode)
				item.Run()
				th.symbolEntry[t.ContractCode] = item
				item.entry <- t
			}
		}

	}()
}

type SymbolEntry struct {
	low, high TradeNode
	curTs     int64
	entry     chan TradeNode
	symbol    string
}

func NewSymbolEntry(symbol string) *SymbolEntry {
	return &SymbolEntry{
		entry:  make(chan TradeNode, 128),
		symbol: symbol,
	}
}

func (se *SymbolEntry) Run() {
	du := conf.TickerDuration()
	if du == 0 {
		du = 100
	}

	tick := time.Tick(time.Duration(du) * time.Millisecond)
	go func() {
		for {
			select {
			case <-tick:
				se.notifyAndReset()
			case node := <-se.entry:
				se.handleTick(&node)
			}
		}

	}()
}

func (se *SymbolEntry) handleTick(node *TradeNode) {
	log.Info("kline ticker handler", zap.String("symbol", se.symbol), zap.Int("队列长度", len(se.entry)), zap.Int("cap", cap(se.entry)))
	if se.curTs != node.Ts {
		se.notifyAndReset()
	}
	se.dealEntryTicker(*node)
}

func (se *SymbolEntry) dealEntryTicker(t TradeNode) {
	if t.Price.LessThan(decimal.Zero) {
		return
	}

	se.curTs = t.Ts

	if se.low.Price.Equal(decimal.Zero) && se.high.Price.Equal(decimal.Zero) {
		se.low.Price = t.Price
		se.high.Price = t.Price
		se.low.Volume, se.low.PAmount = t.Volume, t.PAmount
		se.low.Ts, se.high.Ts = t.Ts, t.Ts
		return
	}

	if t.Price.LessThan(se.low.Price) {
		se.low.Price = t.Price
		se.low.Volume = se.low.Volume.Add(t.Volume)
		se.low.PAmount = se.low.PAmount.Add(t.PAmount)
	} else {
		se.high.Price = t.Price
		se.high.Volume = se.high.Volume.Add(t.Volume)
		se.high.PAmount = se.high.PAmount.Add(t.PAmount)
	}
}

func (se *SymbolEntry) notifyAndReset() {
	log.Info("kline 开始交易对tikcers kline处理并重置", zap.String("symbol", se.symbol), zap.Any("low", se.low), zap.Any("high", se.high))
	//处理旧数据并推送
	low, high := se.low, se.high
	if low.Price.GreaterThan(decimal.Zero) && low.Volume.GreaterThan(decimal.Zero) {
		log.Info("开始推送到kline处理", zap.Any("low", low))
		DefaultKlineHandler.handleKlineCalc(&low)
	}

	if high.Price.GreaterThan(decimal.Zero) && high.Volume.GreaterThan(decimal.Zero) {
		log.Info("开始推送到kline处理", zap.Any("high", high))
		DefaultKlineHandler.handleKlineCalc(&high)
	}

	//重置
	se.low = TradeNode{ContractCode: se.symbol, Ts: time.Now().Unix()}
	se.high = TradeNode{ContractCode: se.symbol, Ts: time.Now().Unix()}
}
