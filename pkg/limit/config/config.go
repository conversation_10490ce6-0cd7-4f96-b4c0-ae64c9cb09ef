package config

import (
	"github.com/modern-go/concurrent"
	"go.uber.org/atomic"
)

var EntrustOrders = concurrent.NewMap()

var BatchEntrustOrders = concurrent.NewMap()

var SupportSymbols []string

var IsSrvInitialization atomic.Bool

var SupportSymbol = concurrent.NewMap()

func GetSuppports() (list []string) {
	SupportSymbol.Range(func(key, value any) bool {
		code := key.(string)
		list = append(list, code)
		return true
	})
	return
}
