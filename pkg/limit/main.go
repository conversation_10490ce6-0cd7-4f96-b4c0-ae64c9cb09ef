package main

import (
	"fmt"
	"os"
	"spot/libs/xrpcclient/order_rpc"
	biz_service "spot/pkg/limit/service/biz.service"
	msg_consumer "spot/pkg/limit/service/msg.consumer"

	"spot/libs/cache"
	"spot/libs/conf"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/pprof"
	"spot/pkg/limit/ilog"
	"spot/pkg/limit/server"
	"spot/pkg/limit/service"
)

var (
	gitHash   string
	gitDigest string
	gitBranch string
	buildUser string
	buildTime string
	goVersion string
)

func main() {
	args := os.Args
	if len(args) == 2 && (args[1] == "--version" || args[1] == "-v") {
		fmt.Printf("Git Branch: %s \n", gitBranch)
		fmt.Printf("Git Commit Hash: %s \n", gitHash)
		fmt.Printf("Git Commit Digest: %s \n", gitDigest)
		fmt.Printf("Build User: %s \n", buildUser)
		fmt.Printf("Build TimeStamp: %s \n", buildTime)
		fmt.Printf("GoLang Version: %s \n", goVersion)
		return
	}

	// 初始化配置文件
	conf.Init()

	// 初始化日志
	initLogger()

	//初始化缓存
	cache.InitDefaultRedisConn()

	// 初始化数据库
	database.InitDefaultDB()

	//初始化order服务
	order_rpc.InitClient(conf.Discovery(), conf.LocalName())

	//初始化本服务支持的交易对
	biz_service.GetSupportSymbol()

	//int message queue
	biz_service.InitMessageQueue()

	//恢复限价挂单
	service.StartLimitMatchEngine()

	msg_consumer.InitMessageQueue()

	// 初始化服务监控
	pprof.Init(conf.PprofAddr())

	// 监听系统信号
	server.InitSignal()
}

func initLogger() {
	log.InitLogger(conf.LogFile(), conf.LogLevel(), conf.LogOnlyFile(), define.LoggerOrder, define.LoggerDepth)
	ilog.Order = log.GetLogger(define.LoggerOrder)
	ilog.Depth = log.GetLogger(define.LoggerDepth)
}
