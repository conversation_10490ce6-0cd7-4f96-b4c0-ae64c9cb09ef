/*
@Time : 3/6/20 10:22 上午
<AUTHOR> mocha
@File : model
*/
package model

import (
	"time"
)

type CPrice struct {
	Symbol string
	Price  float64
	Time   time.Time
	Source string
}

//type CPriceRate struct {
//	ContractCode   string
//	FundingRate      float64
//	DailyPriceRate float64
//	Time           time.Time
//}

type ContractCPrice struct {
}

type SourceContent struct {
	Name   string
	Weight float64
	Id     int
}
