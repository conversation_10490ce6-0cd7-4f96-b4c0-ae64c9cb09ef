package server

import (
	"go.uber.org/zap"
	"os"
	"os/signal"
	biz "spot/pkg/limit/service/biz.service"
	intellbook "spot/pkg/limit/service/engine"
	"strconv"
	"syscall"

	"spot/libs/conf"
	"spot/libs/log"
	"spot/libs/msg"
	"spot/libs/pprof"
	msgMq "spot/pkg/limit/service/msg.consumer"
)

// InitSignal register signals handler.
func InitSignal() {
	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGHUP, syscall.SIGQUIT, syscall.SIGTERM, syscall.SIGINT)
	srvName := conf.LocalName() + strconv.Itoa(conf.CloneID())
	msg.SendCautionEmail("服务重启提醒", srvName+"服务已重新启动完成!")
	log.Info("服务启动通知", zap.String("服务", conf.LocalName()), zap.String("服务标识", conf.Identifier()), zap.Int("cloneId", conf.CloneID()))
	for {
		s := <-c
		log.Info("服务收到停止信号", zap.String("服务", conf.LocalName()), zap.String("服务标识", conf.Identifier()), zap.Int("cloneId", conf.CloneID()), zap.String("信号", s.String()))
		//msg.SendCautionEmail("服务停止提醒", fmt.Sprintf("%s服务接收到系统信号[%s],正在关闭!", srvName, s.String()))
		switch s {
		case syscall.SIGQUIT, syscall.SIGTERM, syscall.SIGINT:
			biz.Shutdown()
			msgMq.StopMQConsumer()
			biz.StopMQProduct()
			intellbook.Shutdown()
			pprof.Stop()
			return
		case syscall.SIGHUP:
			return
		default:
			return
		}
	}
}
