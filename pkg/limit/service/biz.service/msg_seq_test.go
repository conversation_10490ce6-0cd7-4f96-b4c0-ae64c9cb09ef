package biz_service

import (
	"spot/libs/cache"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"testing"
)

func TestOrderSeqIncre(t *testing.T) {
	log.InitLogger("logs/panda/closing", "info", false)
	log.Info("hello")
	define.RedisCommonDb = 2
	cache.InitDefaultRedisDBCon("127.0.0.1:6379", "", 2, false)
	database.InitDefaultDataBase("root:123456@tcp(127.0.0.1:3306)/new_basecoin?charset=utf8&parseTime=true&loc=Local")
	t.Log(cache.IncrementOrderSeqId(101))
	t.Log(cache.IncrementOrderSeqId(101))
	t.Log(cache.IncrementOrderSeqId(101))
}
