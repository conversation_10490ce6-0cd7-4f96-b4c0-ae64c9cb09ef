package biz_service

import (
	"spot/libs/commonsrv"
	"spot/libs/messagequeue"
	"spot/libs/proto"
)

// NotifyMqSelfNewTrade /通知自有成交
func NotifyMqSelfNewTrade(trade *proto.MatchOrder) {
	if !trade.TradePrice.IsPositive() {
		return
	}
	//notifyMqSelfNewTrade(trade)           //发送成交消息
	index := commonsrv.GenerateIndexHistory(trade)
	if index == nil {
		return
	}
	messagequeue.NotifyPriceIndex(index)

	messagequeue.NotifyMqNewTrade(trade)
}
