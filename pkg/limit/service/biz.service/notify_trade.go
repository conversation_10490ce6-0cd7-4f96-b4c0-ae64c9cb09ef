package biz_service

import (
	"errors"
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/commonsrv"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/messagequeue"
	"spot/libs/mq"
	"spot/libs/proto"
	"spot/libs/xrpcclient/order_rpc"
)

//发送订单完成消息
func MQTopicOrderOverMsg(d *proto.MatchOverMsg) {
	b, err := json.Marshal(d)
	if err != nil {
		log.Errorf("MQTopicOrderOverMsg json marshal fail,%v", err)
		return
	}
	message := mq.MessagePack{Topic: define.MQTopicUserOrderOver, Code: d.Code, Data: b, Identifier: d.Identifier, UserId: d.UserId}
	message.SetExtra(d.OrderId, cache.IncrementOrderSeqId(d.OrderId))
	log.Infof("开始发送mq消息：%+v,topic:%v", string(b), message.Topic)
	messagequeue.SendMessageForClosing(message)
}

//发送订单成交消息
func MQTopicOrderTradeMsg(d *proto.OrderTrade) {
	b, err := json.Marshal(d)
	if err != nil {
		log.Errorf("MQTopicOrderTradeMsg json marshal fail,%v", err)
		return
	}
	message := mq.MessagePack{Topic: define.MQTopicUserOrderTrade, Code: d.Code, Data: b, Identifier: d.Order.Identifier, UserId: d.Order.UserId}
	id := d.Order.OrderId
	message.SetExtra(id, cache.IncrementOrderSeqId(id))
	log.Infof("开始发送mq消息：%+v,topic:%v", string(b), message.Topic)
	messagequeue.SendMessageForClosing(message)
}

//向第三方下单，如果失败将撤销订单
func DealEntrustOrderPlaceForThirdMarket(order *proto.EntrustOrder, info *proto.Contract) (err error) {
	arg := &proto.ThirdOrderPlaceArg{
		RequestId:    database.NextID(),
		OrderId:      order.ID,
		UserId:       order.UserID,
		ContractCode: order.ContractCode,
		EntrustType:  order.EntrustType,
		Side:         order.Side,
		Price:        order.Price,
		Volume:       order.Volume,
		Money:        order.Money,
		MarketSource: order.MarketSource,
	}

	reply := &define.Reply{Data: &arg.ThirdOrderId}
	err = order_rpc.EntrustOpen(arg, reply)
	log.Info("向Order服务发送下单请求", zap.Error(err), zap.Any("arg", arg), zap.Any("reply", reply))
	if err != nil {
		log.Error("向Order服务发送下单请求失败", zap.Error(err), zap.Any("arg", arg), zap.Any("reply", reply))
		cancelEntrustOrderIfPlaceThirdFail(order, info, define.CancelOrderFinish)
		return
	}
	log.Info("向Order服务发送下单请求", zap.Error(err), zap.Any("arg", arg), zap.Any("reply", reply))
	if arg.ThirdOrderId <= 0 {
		var o *proto.EntrustOrder
		o, err = database.GetUserEntrustOrder(nil, order.ID)
		if err != nil {
			log.Error("cancelEntrustOrderIfPlaceThirdFail fail", zap.Error(err))
			return
		}
		if o.ThirdOrderId <= 0 {
			mCancel := &proto.MCancel{
				OrderId:    order.ID,
				CancelMark: define.CancelOrderFinish,
				TradeMark:  define.TradeMarkWithOrderDefault,
			}
			commonsrv.DealContractAccountOrderCanceled(mCancel)
		} else {
			arg.ThirdOrderId = o.ThirdOrderId
		}
	}
	if arg.ThirdOrderId <= 0 {
		err = errors.New("向第三放下单失败")
	}
	return
}

func cancelEntrustOrderIfPlaceThirdFail(order *proto.EntrustOrder, info *proto.Contract, mark int) {

	o, err := database.GetUserEntrustOrder(nil, order.ID)
	if err != nil {
		log.Error("cancelEntrustOrderIfPlaceThirdFail fail", zap.Error(err))
		return
	}
	if o != nil && o.ThirdOrderId == 0 {
		mCancel := &proto.MCancel{
			OrderId:    order.ID,
			CancelMark: mark,
			TradeMark:  define.TradeMarkWithOrderDefault,
		}
		commonsrv.DealContractAccountOrderCanceled(mCancel)
	}
}
