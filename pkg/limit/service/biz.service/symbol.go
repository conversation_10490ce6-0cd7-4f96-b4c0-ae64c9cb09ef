package biz_service

import (
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/conf"
	"spot/libs/define"
	"spot/libs/log"
	"spot/pkg/limit/config"
)

func GetSupportSymbol() {
	list := conf.Symbols()
	if len(list) == 0 {
		panic("从配置文件中获取支持的交易对列表数量为0，退出")
	}
	//for _, s := range list {
	//	cache.DelServerSymbol(define.ServerNameLimit, s)
	//}

	listCanSet := InitSymbolMatch(list)
	if len(list) != len(listCanSet) {
		log.Warn("部分配置的交易对无法设置成功，可能有其他服务已设置", zap.Any("配置交易对啊", list), zap.Any("设置成功的", listCanSet))
	}
	log.Info("本服务支持的交易对", zap.Any("data", listCanSet))
	if len(listCanSet) == 0 {
		panic("系统可设置提供服务的交易对个数为0，退出")
	}
	config.SupportSymbols = listCanSet

	for _, s := range listCanSet {
		config.SupportSymbol.Store(s, s)
	}
}

func Shutdown() {
	DeleteServerSymbol()
}

// InitSymbolMatch 根据配置的交易对列表，查询可支持的列表
func InitSymbolMatch(symbols []string) (list []string) {
	for _, symbol := range symbols {
		if cache.InitServerSymbolInit(define.ServerNameLimit, symbol, conf.Identifier()) {
			list = append(list, symbol)
		} else {
			if cache.IsEqualSrvIdentity(define.ServerNameLimit, symbol, conf.Identifier()) {
				list = append(list, symbol)
			}
			log.Infof("InitSymbolMatch %v交易对已被其他服务注册，请查看，或手动删除该交易对", symbol)
		}
	}
	return
}

// DeleteServerSymbol 移除服务支持的交易对列表
func DeleteServerSymbol() {
	config.SupportSymbol.Range(func(key, value interface{}) bool {
		s := key.(string)
		log.Info("开始准备移除本服务支持的交易对", zap.String("s", s))
		cache.DelServerSymbol(define.ServerNameLimit, s)
		return true
	})
}
