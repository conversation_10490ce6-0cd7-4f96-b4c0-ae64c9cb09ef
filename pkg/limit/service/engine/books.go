package intellbook

import (
	"errors"
	"github.com/modern-go/concurrent"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
	"strings"
	"time"
)

var _books *concurrent.Map

func init() {
	_books = concurrent.NewMap()
}

// Shutdown 服务重启，处理队列中撤单请求
func Shutdown() {
	log.Info("准备恢复limit服务")
	_books.Range(func(key, value interface{}) bool {
		code := key.(string)
		book := getOrderBook(code)
		book.Shutdown()
		return true
	})
}

//获取books指定合约的orderBook
func getOrderBook(name string) *OrderBook {
	var book *OrderBook
	actual, ok := _books.Load(name)
	if !ok {
		contract := getContractInfo(name)
		if contract == nil {
			contract = &proto.Contract{}
		}
		book = NewOrderBook(name, *contract)
		actual, ok = _books.LoadOrStore(name, book)
		if !ok {
			return book
		}
	}
	if book, ok := actual.(*OrderBook); ok {
		return book
	}
	return nil
}

func getContractInfo(name string) *proto.Contract {
	//return &proto.Contract{Digit: 2}
	c, err := cache.GetContractInfo(name)
	if err != nil {
		log.Errorf("getContractInfo cache.GetContractInfo code；%v,err；%v", name, err)
		return nil
	}
	return c
}

// PlaceOrder 下单时及撤单控制盘同时只处理一种操作
//市价单,传递要要买的money
func PlaceOrder(arg *proto.EntrustOrder) (err error) {
	if arg == nil {
		return errors.New("参数为空")
	}

	code := arg.ContractCode
	book := getOrderBook(code)

	if arg.CreateTime.IsZero() {
		arg.CreateTime = time.Now()
	}

	order := proto.MOrder{
		Code:            code,
		UserId:          arg.UserID,
		OrderId:         arg.ID,
		Price:           arg.Price,
		Volume:          arg.Volume,
		Side:            arg.Side,
		EntrustType:     arg.EntrustType,
		Mode:            arg.Mode,
		MatchType:       arg.MatchType(),
		EntrustIdentity: define.IdentifierUser,
		CreateTime:      arg.CreateTime,
		EnQueueTime:     time.Now(),
		MarketSource:    arg.MarketSource,
	}
	log.Info("开始下单", zap.Any("arg", order))
	book.PlaceOrder(&order)
	return nil
}

// CancelOrder 下单时及撤单控制盘同时只处理一种操作
//撤销订单
func CancelOrder(arg *proto.EntrustOrder) (err error) {
	code := arg.ContractCode
	book := getOrderBook(code)
	order := proto.MOrder{
		Code:            code,
		UserId:          arg.UserID,
		OrderId:         arg.ID,
		Side:            arg.Side,
		Price:           arg.Price,
		EntrustIdentity: define.IdentifierUser,
		CreateTime:      time.Now(),
		EnQueueTime:     time.Now(),
		MarketSource:    arg.MarketSource,
	}
	log.Info("开始撤单", zap.Any("arg", order))
	book.CancelOrder(&order)
	return
}

func NotifyPriceFirstChange(code string, buy, sell decimal.Decimal) {
	if strings.Trim(code, " ") == "" {
		return
	}
	if buy.LessThanOrEqual(decimal.Zero) && sell.LessThanOrEqual(decimal.Zero) {
		log.Error("推送的买一买一异常", zap.String("code", code), zap.String("buyFirst", buy.String()), zap.String("sellFirst", sell.String()))
		return
	}

	book := getOrderBook(code)
	book.NotifyPriceChange(buy, sell)
}
