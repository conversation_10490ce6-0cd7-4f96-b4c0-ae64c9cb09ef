package intellbook

import (
	"bytes"
	"fmt"
	"github.com/emirpasic/gods/lists/singlylinkedlist"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"spot/libs/log"
	"spot/libs/proto"
)

type PriceList struct {
	*singlylinkedlist.List
	total decimal.Decimal
}

func NewPriceList(orders ...*proto.MOrder) *PriceList {
	pl := &PriceList{
		List:  singlylinkedlist.New(),
		total: decimal.Decimal{},
	}
	pl.Put(orders...)
	return pl
}

func (p *PriceList) UpdateTradeVolume(v decimal.Decimal) {
	p.total = p.total.Sub(v)
}
func (p *PriceList) Put(orders ...*proto.MOrder) {
	if len(orders) == 0 {
		return
	}
	for _, order := range orders {
		p.total = p.total.Add(order.Volume)
		p.Add(order)
	}
}

func (p *PriceList) Del(order *proto.MOrder) {
	//var list []*proto.MOrder
	for i := p.Size() - 1; i >= 0; i-- {
		v, ok := p.Get(i)
		if ok {
			item, _ := v.(*proto.MOrder)
			if item.OrderId == order.OrderId {
				p.Remove(i)
				p.total = p.total.Sub(item.Volume)
			}
		}
	}
	//for it.Next() {
	//	_, value := it.Index(), it.Value()
	//	item, _ := value.(*proto.MOrder)
	//	if item.ThirdOrderId == order.ThirdOrderId {
	//		list = append(list, item)
	//	}
	//}

	//for _, i := range list {
	//	p.Remove(p.IndexOf(i))
	//	p.total = p.total.Sub(i.Volume)
	//
	//}

	p.Each(func(index int, value interface{}) {
		item, _ := value.(*proto.MOrder)
		if item.OrderId == order.OrderId {
			log.Error("订单移除失败", zap.Any("订单", order), zap.Any("orderBookorder", item))
		}
	})
	//p.List.Each(func(index int, value interface{}) {
	//	item, _ := value.(*proto.MOrder)
	//	if item.ThirdOrderId == order.ThirdOrderId {
	//		p.total = p.total.Sub(item.Volume)
	//		fmt.Println("zui", index, "order", item.ThirdOrderId)
	//		p.List.Remove(p.IndexOf(value))
	//	}
	//})
}

func (p *PriceList) Total() decimal.Decimal {
	if p.total.LessThanOrEqual(decimal.Zero) {
		fmt.Errorf("总数小于0，请查证")
	}
	return p.total
}

func (p *PriceList) String() string {
	var bf bytes.Buffer
	bf.WriteString("list:{ total:")
	bf.WriteString(p.total.String() + ", orders:[")
	p.Each(func(index int, value interface{}) {
		item, _ := value.(*proto.MOrder)
		s := fmt.Sprintf("{id:%+v,volume:%+v,bsId:%+v,user_id:%+v,offset:%+v,entrust_identity:%+v},", item.OrderId, item.Volume, item.BusinessId, item.UserId, item.Offset, item.EntrustIdentity)
		bf.WriteString(s)
	})
	bf.WriteString("]")
	return bf.String()
}
