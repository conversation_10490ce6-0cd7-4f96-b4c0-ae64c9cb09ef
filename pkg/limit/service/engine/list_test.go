package intellbook

import (
	"github.com/emirpasic/gods/lists/singlylinkedlist"
	"spot/libs/nums"
	"spot/libs/proto"
	"sync"
	"testing"
)

func TestSlice(t *testing.T) {
	l := singlylinkedlist.New()
	l.Add(1, 2, 3, 3, 4, 4, 4, 5, 6, 7, 8, 9)
	a := []int{4, 5, 6, 3, 7, 8, 9, 1}
	for _, i := range a {
		l.Each(func(index int, value interface{}) {
			a := value.(int)
			if a == i {
				//		t.Log("将删除", index, "值", a)
				l.Remove(l.IndexOf(value))
			}
		})
		//t.Log("处理", i)
		//it := l.Iterator()
		//for it.Next() {
		//	index, value := it.Index(), it.Value()
		//	a := value.(int)
		//	if a == i {
		//		s := l.IndexOf(value)
		//		t.Log("将删除", index, "值", a)
		//		l.Remove(s)
		//	}
		//}
	}

	//for _, i := range r {
	//	l.Remove(i)
	//}
	//it := l.Iterator()
	//for it.Next() {
	//	index, value := it.Index(), it.Value()
	//	i := value.(int)
	//	if i == 3 {
	//		l.Remove(index)
	//	}
	//}
	t.Log(l)
}

func TestList(t *testing.T) {
	l := NewPriceList()
	l.Put(&proto.MOrder{OrderId: 1, Volume: nums.NewFromFloat(10)},
		&proto.MOrder{OrderId: 2, Volume: nums.NewFromFloat(10)},
		&proto.MOrder{OrderId: 3, Volume: nums.NewFromFloat(10)},
		&proto.MOrder{OrderId: 3, Volume: nums.NewFromFloat(10)},
		&proto.MOrder{OrderId: 4, Volume: nums.NewFromFloat(10)},
		&proto.MOrder{OrderId: 4, Volume: nums.NewFromFloat(10)},
		&proto.MOrder{OrderId: 5, Volume: nums.NewFromFloat(10)},
		&proto.MOrder{OrderId: 5, Volume: nums.NewFromFloat(10)},
		&proto.MOrder{OrderId: 5, Volume: nums.NewFromFloat(10)},
		&proto.MOrder{OrderId: 5, Volume: nums.NewFromFloat(10)},
		&proto.MOrder{OrderId: 5, Volume: nums.NewFromFloat(10)},
	)

	t.Log("----------")
	t.Log(l.Total())
	l.Del(&proto.MOrder{OrderId: 4})
	t.Log(l.Total())
	l.Del(&proto.MOrder{OrderId: 5})
	l.Del(&proto.MOrder{OrderId: 5})
	t.Log(l.Total())
	t.Log(l.String())
}

func TestWaitGroup(t *testing.T) {
	for i := 0; i < 1000; i++ {

		var wg sync.WaitGroup
		wg.Add(2)
		go func() {
			defer wg.Done()
			t.Log("a i", i)
		}()
		go func() {
			defer wg.Done()
			t.Log("b i", i)
		}()
		wg.Wait()
	}
}
