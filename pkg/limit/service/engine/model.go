package intellbook

import (
	"spot/libs/proto"
	"time"
)

type ActionBatchOrder struct {
	ReqID           int64
	EntrustIdentity int
	Action          string
	MatchStrategy   int //撮合成交策略
	List            []proto.MOrder

	ISFillAndCancel  bool           //是否在撤销之前铺单,false-先撤后挂 true-先挂后撤
	CancelList       []proto.MOrder //如果下单时携带撤销订单，挂单完成同时执行撤单请求
	EnQueueTime      time.Time
	depthOrderConfig proto.DepthOrderConfig
	BDepthReplyChan  chan proto.BDepthReply `json:"-"`
}

type Symbol struct {
	Name     string
	SymbolId int
}
