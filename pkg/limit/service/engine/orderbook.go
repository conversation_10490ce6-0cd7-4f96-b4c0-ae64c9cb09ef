package intellbook

import (
	"bytes"
	"fmt"
	"github.com/emirpasic/gods/maps/treemap"
	"github.com/shopspring/decimal"
	"go.uber.org/atomic"
	"go.uber.org/zap"
	"runtime/debug"
	"spot/libs/commonsrv"
	"spot/libs/conf"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
	biz_service "spot/pkg/limit/service/biz.service"
	"sync"
	"time"
)

type Price struct {
	Buy, Sell decimal.Decimal
}

type OrderBook struct {
	ok   *atomic.Bool
	name string
	sync.RWMutex
	buys      *treemap.Map
	sells     *treemap.Map
	contract  proto.Contract
	isUseBsRC bool       //是否启动铺单市价最高价格限制
	entrance  chan Price //普通订单队列
	lastDeal  int        //0-上次先处理买盘 1-上次先处理卖盘
}

func NewOrderBook(name string, contract proto.Contract) *OrderBook {
	book := &OrderBook{
		name:     name,
		contract: contract,
		buys:     treemap.NewWith(decimalComparator),
		sells:    treemap.NewWith(decimalComparator),
		entrance: make(chan Price, 512),
		ok:       atomic.NewBool(true),
		lastDeal: 1,
	}
	book.run()
	log.Info("合约初始化完毕", zap.String("code", book.name))
	return book
}

func (book *OrderBook) Shutdown() {
	log.Error("开始通知orderbook进行关闭操作", zap.String("code", book.name))
	log.Info("开始通知orderbook进行关闭操作", zap.String("code", book.name))
	book.ok.Store(false)
	book.Lock()
	defer book.Unlock()
	close(book.entrance)

}

func (book *OrderBook) run() {
	go func() {
		defer func() {
			if e := recover(); e != nil {
				log.Errorf("OrderBook run fail,err：%v,stack:%v", e, string(debug.Stack()))
			}
		}()
		for {
			if len(book.entrance) == cap(book.entrance) {
				var minSellFirst, maxBuyFirst decimal.Decimal
				for i := 0; i < len(book.entrance); i++ {
					price, ok := <-book.entrance
					if !ok {
						log.Info("队列关闭,停止执行")
						return
					}
					if minSellFirst.Equal(decimal.Zero) {
						minSellFirst = price.Sell
					}
					if maxBuyFirst.Equal(decimal.Zero) {
						maxBuyFirst = price.Buy
					}
					if price.Sell.LessThan(minSellFirst) {
						minSellFirst = price.Sell
					}
					if price.Buy.GreaterThan(maxBuyFirst) {
						maxBuyFirst = price.Buy
					}
				}
				log.Info("当前队列满，最高买价格及最低卖价格进行触发", zap.String("code", book.name), zap.String("maxBuy", maxBuyFirst.String()), zap.String("minSell", minSellFirst.String()))
				book.DelMatch(maxBuyFirst, minSellFirst)

			} else {
				price, ok := <-book.entrance
				if !ok {
					log.Info("队列关闭,停止执行")
					return
				}
				log.Info("合约按最新价格进行触发", zap.String("code", book.name), zap.String("buy", price.Buy.String()), zap.String("sell", price.Sell.String()))
				book.DelMatch(price.Buy, price.Sell)
			}

		}
	}()
}

func (book *OrderBook) String() string {
	return fmt.Sprintf("buy:%+v,size：%v\n,sell:%+v,size；%v\n", book.ToString(book.buys), book.buys.Size(), book.ToString(book.sells), book.sells.Size())
}

func (book *OrderBook) ToString(p *treemap.Map) string {
	var bf bytes.Buffer
	bf.WriteString("[")
	p.Each(func(key interface{}, value interface{}) {
		bf.WriteString("{price:")
		bf.WriteString(fmt.Sprintf("%+v ,", key))
		l := value.(*PriceList)
		bf.WriteString(l.String())
		bf.WriteString("},")
	})

	bf.WriteString("]")
	return bf.String()
}

//获取同方向order book
func (book *OrderBook) getSideBook(side string) *treemap.Map {
	switch side {
	case SideBuy:
		return book.buys
	case SideSell:
		return book.sells
	}
	return nil
}

func (book *OrderBook) put(order *proto.MOrder) {
	iBook := book.getSideBook(order.Side)
	if iBook == nil {
		return
	}
	var sList *PriceList
	oList, ok := iBook.Get(order.Price)
	if !ok {
		sList = NewPriceList(order)
		iBook.Put(order.Price, sList)
		sList.Sort(orderComparator)
		return
	}
	sList, ok = oList.(*PriceList)
	if !ok {
		return
	}
	sList.Put(order)
}

//方向、id,委托价格为必须
func (book *OrderBook) remove(order proto.MOrder) (old *proto.MOrder) {
	defer func() {
		if e := recover(); e != nil {
			log.Errorf("OrderBook remove order fail,err：%v,stack:%v", e, string(debug.Stack()))
		}
	}()
	if (order.Side != SideBuy && order.Side != SideSell) || order.OrderId == 0 {
		log.Error("order book remove 移除的订单缺少方向及订单id", zap.Any("order", order))
		return
		//panic("移除的订单缺少方向及订单id")
	}
	iBook := book.getSideBook(order.Side)
	if iBook == nil {
		log.Info("获取指定方向的orderbook无效", zap.Any("order", order))
		return
	}
	var sList *PriceList
	oList, ok := iBook.Get(order.Price)
	if !ok {
		log.Info("没有查询到价格节点", zap.Any("order", order))
		return
	}

	sList, ok = oList.(*PriceList)
	if !ok {
		log.Error("当前价格结构体不是*PriceList")
		return
	}
	sList.Del(&order)
	if sList.Size() == 0 {
		iBook.Remove(order.Price)
	}
	return
}

func (book *OrderBook) getBuySellFirst() (buyFirst, sellFirst decimal.Decimal) {
	if book.sells.Size() == 0 {
		sellFirst = decimal.Zero
	} else {
		s, _ := book.sells.Min()
		sellFirst = s.(decimal.Decimal)
	}
	if book.buys.Size() == 0 {
		buyFirst = decimal.Zero
	} else {
		b, _ := book.buys.Max()
		buyFirst = b.(decimal.Decimal)
	}
	return
}

func (book *OrderBook) getPriceList(side string) (price decimal.Decimal, list *PriceList, m *treemap.Map) {
	var k, v interface{}
	if side == SideBuy {
		m = book.buys
		if m.Size() == 0 {
			return
		}
		k, v = m.Max()
	} else {
		m = book.sells
		if m.Size() == 0 {
			return
		}
		k, v = m.Min()
	}
	if m.Size() == 0 {
		return
	}
	price = k.(decimal.Decimal)
	list = v.(*PriceList)
	return
}

//获取匹配的对手盘价格等信息
func (book *OrderBook) getRivalLevel(side string) (price decimal.Decimal, list *PriceList, m *treemap.Map) {
	var k, v interface{}
	if side == SideBuy {
		m = book.sells
		if m.Size() == 0 {
			return
		}
		k, v = m.Min()
	} else {
		m = book.buys
		if m.Size() == 0 {
			return
		}
		k, v = m.Max()
	}
	if m.Size() == 0 {
		return
	}
	price = k.(decimal.Decimal)
	list = v.(*PriceList)
	return
}

func getCancelOverMsg(order *proto.MOrder) proto.MatchOverMsg {
	ds := proto.MatchOverMsg{
		Code:        order.Code,
		OrderId:     order.OrderId,
		UserId:      order.UserId,
		Identifier:  order.EntrustIdentity,
		DealerState: define.MatchOverDefault,
		Ts:          time.Now(),
	}
	if order.MatchType == define.MatchTypeForce {
		ds.DealerState = define.MatchOverForce
	}
	return ds
}

// PlaceOrder 下单时及撤单控制盘同时只处理一种操作
func (book *OrderBook) PlaceOrder(order *proto.MOrder) {
	book.Lock()
	defer book.Unlock()
	book.put(order)
	log.Debug("place after", zap.Any("data", book.String()))
	return
}

// CancelOrder 下单时及撤单控制盘同时只处理一种操作
//撤销订单
func (book *OrderBook) CancelOrder(order *proto.MOrder) {
	book.Lock()
	defer book.Unlock()
	_ = book.remove(*order)
	m := getCancelOverMsg(order)
	//发送撤销指令到清算
	biz_service.MQTopicOrderOverMsg(&m)
	log.Debug("cancel after", zap.Any("data", book.String()))
	return
}

func (book *OrderBook) dealOrderCancel(wg *sync.WaitGroup, o proto.MOrder) {
	book.Lock()
	defer book.Unlock()
	if wg != nil {
		defer wg.Done()
	}
	_ = book.remove(o)
}

//卖方限价成交区间：卖方委托价格≤做市商本轮铺单委托买1价
//买方限价成交区间：买方委托价格≥做市商本轮铺单委托卖1价
func (book *OrderBook) isCanTrade(isBuyHub bool, price, rivalPrice decimal.Decimal) bool {
	if rivalPrice.LessThanOrEqual(decimal.Zero) {
		return false
	}
	if isBuyHub {
		return price.GreaterThanOrEqual(rivalPrice)
	} else {
		return price.LessThanOrEqual(rivalPrice)
	}
	return false
}

func (book *OrderBook) NotifyPriceChange(buyFirst, sellFirst decimal.Decimal) {
	book.entrance <- Price{
		Buy:  buyFirst,
		Sell: sellFirst,
	}
}

func (book *OrderBook) DelMatch(buyFirst, sellFirst decimal.Decimal) {
	defer func() {
		if e := recover(); e != nil {
			log.Errorf("OrderBook DelMatch fail,err：%v,stack:%v", e, string(debug.Stack()))
		}
	}()
	book.Lock()
	defer book.Unlock()
	rcAmount := getRcAmountByCode(book.name)
	log.Info("通知合约价格变化", zap.String("合约", book.name), zap.String("买", buyFirst.String()), zap.String("卖", sellFirst.String()), zap.Int64("最大处理个数", rcAmount))
	var buy, sell, hasDealAmount int64
	var wg sync.WaitGroup
	wg.Add(2)
	go func() {
		defer wg.Done()
		buy = book.dealMatchHub(true, sellFirst, rcAmount)

	}()
	go func() {
		defer wg.Done()
		sell = book.dealMatchHub(false, buyFirst, rcAmount)
	}()
	wg.Wait()
	hasDealAmount = buy + sell
	log.Info("本次处理的成交记录数目", zap.String("code", book.name), zap.String("买1", buyFirst.String()), zap.String("卖1", sellFirst.String()), zap.Int64("买", buy), zap.Int64("卖", sell), zap.Int64("size", hasDealAmount))
	log.Info("after notify", zap.String("code", book.name), zap.String("买1", buyFirst.String()), zap.String("卖1", sellFirst.String()), zap.String("data", book.String()))
}

func getRcAmountByCode(name string) int64 {
	nums := conf.GetRcDealNums()
	if nums <= 0 {
		return 100
	}
	return int64(nums)
}

func (book *OrderBook) dealMatchHub(isBuyHub bool, rivalPrice decimal.Decimal, amount int64) (count int64) {
	if rivalPrice.LessThanOrEqual(decimal.Zero) {
		log.Info("合约对手价格无效", zap.String("code", book.name), zap.Any("price", rivalPrice.String()))
		return
	}
	for {
		canTrade, volume := book.dealMatch(isBuyHub, rivalPrice, amount)
		if !canTrade {
			break
		}
		count += volume
	}
	return
}

func (book *OrderBook) dealMatch(isBuyHub bool, rivalPrice decimal.Decimal, amount int64) (isCanMatch bool, count int64) {
	var (
		shouldDelOrders []proto.MOrder //买卖盘将移除订单列表
	)
	side := define.OrderBuy
	if !isBuyHub {
		side = define.OrderSell
	}

	//获取买盘
	price, priceList, m := book.getPriceList(side)
	if priceList == nil || priceList.Size() == 0 || m.Size() == 0 || price.LessThanOrEqual(decimal.Zero) {
		return
	}

	isCanMatch = book.isCanTrade(isBuyHub, price, rivalPrice)
	if !isCanMatch {
		return
	}

	defer func() {
		log.Info("当前要移除的订单", zap.Any("code", book.name), zap.String("side", side), zap.Any("orders", shouldDelOrders))
		for _, o := range shouldDelOrders {
			book.remove(o)
		}
		if priceList.Empty() {
			log.Info("对手盘价格为空，准备删除价格节点", zap.String("委托方向", side), zap.String("合约", book.name), zap.String("price", price.String()))
			m.Remove(price)
		}
	}()
	li := priceList.Iterator()
	for li.Next() {
		bookOrder := li.Value().(*proto.MOrder)
		//发送成交消息
		v := bookOrder.Volume.IntPart()
		//if v+count > amount {
		//	break
		//}
		count += v
		book.sendTradeMsg(bookOrder, bookOrder.Price)
		shouldDelOrders = append(shouldDelOrders, *bookOrder)
	}

	return
}

func (book *OrderBook) sendTradeMsg(order *proto.MOrder, tradePrice decimal.Decimal) {
	//获取合约指定配置
	contract, err := commonsrv.GetContractDetail(0, order.Code, nil)
	if err != nil {
		log.Error("获取合约配置信息出错", zap.Error(err), zap.String("code", order.Code))
		return
	}
	if contract.MarketSource == define.MarketSourceSourceDefault {
		tradePrice = order.Price
		//发送到清算
		biz_service.MQTopicOrderTradeMsg(&proto.OrderTrade{
			MsgId:      database.NextID(),
			Code:       book.name,
			DealPrice:  tradePrice,
			DealVolume: order.Volume,
			MatchTime:  time.Now(),
			Order: proto.MatchUser{
				UserId:     order.UserId,
				OrderId:    order.OrderId,
				Identifier: order.EntrustIdentity,
				IsMaker:    true,
			},
		})

		biz_service.MQTopicOrderOverMsg(&proto.MatchOverMsg{
			MsgId:       database.NextID(),
			Code:        book.name,
			OrderId:     order.OrderId,
			UserId:      order.UserId,
			Identifier:  order.EntrustIdentity,
			DealerState: define.MatchTradeOver,
			Ts:          time.Now(),
		})

		matchId := database.NextID()
		matchOrder := &proto.MatchOrder{
			Id:             matchId,
			EntrustOrderId: order.OrderId,
			Symbol:         book.name,
			Side:           order.Side,
			TradeVolume:    order.Volume,
			TradePrice:     tradePrice,
			TradeTime:      time.Now(),
			Digit:          book.contract.Digit,
			VolumeDigit:    book.contract.VolumeDigit,
			//Slippage:       0,
		}
		//推送成交记录
		biz_service.NotifyMqSelfNewTrade(matchOrder) //发送成交消息
		return
	}

	//向order发送限价委托
	//查询订单详情
	eOrder, err := database.GetUserEntrustOrder(nil, order.OrderId)
	if err != nil {
		return
	}
	biz_service.DealEntrustOrderPlaceForThirdMarket(eOrder, contract)

}

func orderComparator(a, b interface{}) int {
	m1 := a.(*proto.MOrder)
	m2 := b.(*proto.MOrder)

	if m1.Offset == OffsetClose && m2.Offset == OffsetOpen {
		return -1
	}
	if m1.Offset == OffsetOpen && m2.Offset == OffsetClose {
		return 1
	}

	if m1.Offset == m2.Offset {
		if m1.OrderId > m2.OrderId {
			return 1
		}
		if m1.OrderId < m2.OrderId {
			return -1
		}
	}
	return 0
}

func decimalComparator(a, b interface{}) int {
	aAsserted := a.(decimal.Decimal)
	bAsserted := b.(decimal.Decimal)
	switch {
	case aAsserted.GreaterThan(bAsserted):
		return 1
	case aAsserted.LessThan(bAsserted):
		return -1
	default:
		return 0
	}
}
