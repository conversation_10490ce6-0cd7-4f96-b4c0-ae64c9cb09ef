package service

//import (
//	"go.uber.org/zap"
//	"spot/libs/database"
//	"spot/libs/define"
//	"spot/libs/log"
//	"spot/libs/nums"
//	"spot/libs/proto"
//	"spot/libs/xrpc_client"
//	"testing"
//	"time"
//)
//
//func TestRpcClient(t *testing.T) {
//	log.InitLogger("testMatch", "info", false)
//	database.InitIdWorker(10)
//	xrpc_client.InitMatchClient("127.0.0.1:8500", "engine")
//	ID := database.NextID()
//	reply := new(define.Reply)
//	arg := &proto.EntrustPlaceMatchArg{
//		RequestId: 0,
//		EntrustBase: proto.EntrustBase{
//			EntrustSymbol: proto.EntrustSymbol{
//				Code: "BTCUSDT",
//			},
//			UserID:          0,
//			OrderID:         ID,
//			EntrustIdentity: define.IdentifierMarketRobot,
//		},
//		EntrustDetail: proto.EntrustDetail{
//			EntrustType: define.EntrustTypeLimit,
//			//Side:           "S",
//			Side:            "B",
//			Price:           nums.NewFromFloat(48020),
//			Volume:          nums.NewFromInt(100),
//			Offset:          define.OffsetOpen,
//			Mode:            define.EntrustModeBest5Level,
//			EntrustStrategy: define.MatchStrategyDefault,
//			CreateTime:      time.Now(),
//			CancelDuration:  0,
//		},
//	}
//
//	err := xrpc_client.EntrustMatch(arg, reply)
//	if err != nil {
//		log.Error("requst error", zap.Error(err))
//		return
//	}
//
//	//cancelRequest:=&proto.EntrustCancelArg{
//	//	EntrustBase: proto.EntrustBase{
//	//		EntrustSymbol: proto.EntrustSymbol{
//	//			Code: "BTCUSDT",
//	//		},
//	//		UserID:          0,
//	//		OrderID:         ID,
//	//		EntrustIdentity: define.IdentifierMarketRobot,
//	//	},
//	//}
//	//e:=xrpc_client.EntrustCancelMatch(cancelRequest,reply)
//	//if e != nil {
//	//	t.Log("cancel fail",zap.Error(e))
//	//	return
//	//}
//	//log.Info("reply",zap.Any("reply",reply))
//}
//
//func TestRpcClientForSell(t *testing.T) {
//	log.InitLogger("testMatch", "info", false)
//	database.InitIdWorker(10)
//	xrpc_client.InitMatchClient("127.0.0.1:8500", "engine")
//	ID := database.NextID()
//	reply := new(define.Reply)
//	arg := &proto.EntrustPlaceMatchArg{
//		RequestId: 0,
//		EntrustBase: proto.EntrustBase{
//			EntrustSymbol: proto.EntrustSymbol{
//				Code: "BTCUSDT",
//			},
//			UserID:          0,
//			OrderID:         ID,
//			EntrustIdentity: define.IdentifierMarketRobot,
//		},
//		EntrustDetail: proto.EntrustDetail{
//			EntrustType:     define.EntrustTypeLimit,
//			Side:            "S",
//			Price:           nums.NewFromFloat(48030),
//			Volume:          nums.NewFromInt(1),
//			Offset:          define.OffsetOpen,
//			Mode:            define.EntrustModeBest5Level,
//			EntrustStrategy: define.MatchStrategyDefault,
//			CreateTime:      time.Now(),
//			CancelDuration:  0,
//		},
//	}
//
//	err := xrpc_client.EntrustMatch(arg, reply)
//	if err != nil {
//		log.Error("requst error", zap.Error(err))
//		return
//	}
//
//	//cancelRequest:=&proto.EntrustCancelArg{
//	//	EntrustBase: proto.EntrustBase{
//	//		EntrustSymbol: proto.EntrustSymbol{
//	//			Code: "BTCUSDT",
//	//		},
//	//		UserID:          0,
//	//		OrderID:         ID,
//	//		EntrustIdentity: define.IdentifierMarketRobot,
//	//	},
//	//}
//	//e:=xrpc_client.EntrustCancelMatch(cancelRequest,reply)
//	//if e != nil {
//	//	t.Log("cancel fail",zap.Error(e))
//	//	return
//	//}
//	//log.Info("reply",zap.Any("reply",reply))
//}
