/*
@Time : 2019-12-30 15:37
<AUTHOR> mocha
@File : notify
*/
package msg_consumer

import (
	"go.uber.org/zap"
	"spot/libs/conf"
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/messagequeue"
	"spot/libs/mq"
	"spot/libs/nums"
	"spot/libs/proto"
	"spot/pkg/limit/config"
	intellbook "spot/pkg/limit/service/engine"
)

var (
	mqConsumer *mq.MessageQueue
)

func NewMqConsumer() {
	handler := make(map[string]mq.MsgHandleFunc)
	handler[define.MQTopicDepth] = MQDepthMsg                    //处理深度
	handler[define.MQTopicLimitOrderAdd] = MqLimitOrderPlace     //处理下单
	handler[define.MQTopicLimitOrderRemove] = MqLimitOrderCancel //处理撤销
	messagequeue.NewMqProduct(conf.MQ(), nums.Int64String(conf.WorkerID()))
	messagequeue.NewMqConsumer(define.MQDefaultExchangeName, define.ServerNameLimit, define.MQDefaultMQBindKey, nums.Int2String(conf.MQQueueID()), handler)
}

func MqLimitOrderCancel(message mq.MessagePack) {
	order := new(proto.EntrustOrder)
	err := json.Unmarshal(message.Data, order)
	if err != nil {
		log.Error("MqLimitOrderCancel json unmarshal fail", zap.Error(err), zap.Any("data", message))
		return
	}
	if !isSupport(order.ContractCode) {
		log.Info("服务不支持该合约", zap.String("NAME", order.ContractCode), zap.Any("order", order), zap.Any("data", string(message.Data)), zap.Any("support", config.GetSuppports()))
		return
	}
	intellbook.CancelOrder(order)
}

func MqLimitOrderPlace(message mq.MessagePack) {
	order := new(proto.EntrustOrder)
	err := json.Unmarshal(message.Data, order)
	if err != nil {
		log.Error("MqLimitOrderCancel json unmarshal fail", zap.Error(err), zap.Any("data", message))
		return
	}
	if !isSupport(order.ContractCode) {
		log.Info("服务不支持该合约", zap.String("NAME", order.ContractCode), zap.Any("order", order), zap.Any("data", string(message.Data)), zap.Any("support", config.GetSuppports()))
		return
	}
	intellbook.PlaceOrder(order)
}

func InitMessageQueue() {
	NewMqConsumer()
}

func StopMQConsumer() {
	messagequeue.StopMQConsumer()
}

func MQDepthMsg(mp mq.MessagePack) {
	d := new(proto.DepthContainerPub)
	err := json.Unmarshal(mp.Data, d)
	if err != nil {
		log.Errorf("MQDepthMsg json unMarsha fail,%v", err)
		return
	}

	if !isSupport(d.Symbol) {
		log.Info("服务不支持该合约", zap.Any("data", string(mp.Data)), zap.Any("support", config.GetSuppports()))
		return
	}

	b, s := d.GetFirstPrice()
	intellbook.NotifyPriceFirstChange(d.Symbol, b, s)
}

func isSupport(code string) bool {
	log.Debug("return", zap.String("code", code), zap.Any("s", config.GetSuppports()))
	if len(conf.Symbols()) == 0 {
		log.Error("当前没有配置支持的交易对")
		return false
	}
	_, ok := config.SupportSymbol.Load(code)
	return ok
}
