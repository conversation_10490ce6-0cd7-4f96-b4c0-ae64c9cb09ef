// Package service /*
package service

import (
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
	"spot/pkg/limit/config"
	biz_service "spot/pkg/limit/service/biz.service"
	intellbook "spot/pkg/limit/service/engine"
	"time"
)

// StartLimitMatchEngine  启动撮合引擎
func StartLimitMatchEngine() {
	config.IsSrvInitialization.Store(true)
	defer func() {
		config.IsSrvInitialization.Store(false)
	}()

	recoverMatchFromDatabase()
}

//从数据库中恢复撮合订单
func recoverMatchFromDatabase() {
	//log.Info("开始恢复支持的交易对委托订单", zap.Any("data", config.SupportSymbols))
	//list, err := database.ListEntrustUnFinishLimitOrdersForMatch(nil, config.SupportSymbols)
	//if err != nil {
	//	log.Errorf("recoverMatchOrder database.ListEntrustUnFinishOrders symbols:%+v,err:%v", config.SupportSymbols, err)
	//	return
	//}
	//log.Info("要恢复的订单数目", zap.Int("数量", len(list)))
	//for _, order := range list {
	//	o := order
	//	dealOrder(&o)
	//}
}

func dealOrder(order *proto.EntrustOrder) {
	log.Info("开始恢复委托订单", zap.Any("data", order))

	var tradePrice decimal.Decimal
	var isTrade bool
	depth := cache.GetContractOriginDepth(order.ContractCode)
	if depth != nil {
		buy, sell := depth.GetFirstPrice()
		if order.Side == define.OrderBuy {
			if order.Price.GreaterThanOrEqual(sell) {
				tradePrice = sell
				isTrade = true
			}
		} else {
			if order.Price.LessThanOrEqual(buy) {
				tradePrice = buy
				isTrade = true
			}
		}
	}

	if isTrade && tradePrice.GreaterThan(decimal.Zero) {
		log.Info("开始恢复支持的交易对委托订单,当前直接可以成交", zap.Any("data", order), zap.Any("成交价", tradePrice.String()))
		//发送到清算
		biz_service.MQTopicOrderTradeMsg(&proto.OrderTrade{
			MsgId:      database.NextID(),
			Code:       order.ContractCode,
			DealPrice:  tradePrice,
			DealVolume: order.Volume,
			MatchTime:  time.Now(),
			Order: proto.MatchUser{
				UserId:     order.UserID,
				OrderId:    order.ID,
				Identifier: define.IdentifierUser,
				IsMaker:    true,
			},
			IsProtocalTrade: false,
		})
		biz_service.MQTopicOrderOverMsg(&proto.MatchOverMsg{
			MsgId:       database.NextID(),
			Code:        order.ContractCode,
			OrderId:     order.ID,
			UserId:      order.UserID,
			Identifier:  define.IdentifierUser,
			DealerState: define.MatchTradeOver,
			Ts:          time.Now(),
		})

		matchId := database.NextID()
		matchOrder := &proto.MatchOrder{
			Id:             matchId,
			EntrustOrderId: order.ID,
			Symbol:         order.ContractCode,
			Side:           order.Side,
			TradeVolume:    order.Volume,
			TradePrice:     tradePrice,
			TradeTime:      time.Now(),
			Digit:          8,
			VolumeDigit:    8,
			//Digit:          book.contract.Digit,
			//VolumeDigit:    book.contract.VolumeDigit,
		}
		go biz_service.NotifyMqSelfNewTrade(matchOrder) //发送成交消息
	} else {
		log.Info("开始恢复支持的交易对委托订单,不能成交，放入orderBook", zap.Any("data", order))
		intellbook.PlaceOrder(order)
	}
}
