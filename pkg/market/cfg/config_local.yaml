identifier: "cf-market-001" #服务唯一标识
run_env: "prod" #运行环境，prod-生产,test-测试，dev-开发
dubug: true # 是否启用调试模式
zip_http: false # 是否启用zip压缩
local_name: "158 BaseCoin Market Server" # 服务名称
rpc_addr: "0.0.0.0:19903" # rpc服务监听地址
#symbols: [ "BTC/USDT","BCH/USDT","ETH/USDT","ETC/USDT","LINK/USDT","EOS/USDT","FIL/USDT","LTC/USDT","UNI/USDT","AAVE/USDT","DOT/USDT","XRP/USDT","SOL/USDT","LUNA/USDT","ATOM/USDT","ETH/BTC" ] #服务支持合约
symbols: [ "BTC/USDT" ] #服务支持合约
pprof_addr: "0.0.0.0:21006" # 服务运行状态监听地址
sms_server: "http://127.0.0.1:8085" # 短信服务Url
mq: "amqp://user:password@localhost:5672" #rabbit mq config
#default_db: "root:123456@tcp(127.0.0.1:3306)/futures_basecoin?charset=utf8&parseTime=true&loc=Local" # 数据库连接
default_db: "root:123456@tcp(127.0.0.1:3306)/futures_basecoin?charset=utf8&parseTime=true&loc=Local" # 数据库连接
log_file: "market" # 日志文件位置
log_level: "info" # 日志等级
sms_prefix: "【Base】" # 发送短信前缀
mail_prefix: "【Base】" # 发送邮件前缀
caution_receiver: [ ] # 警告邮件通知列表
market_receiver: [ "<EMAIL>" ] # 行情报警邮件列表
sensitive_conf: "" # 敏感信息配置文件位置
depth_height: 10  #深度高度
#主从服务
is_slave: false
clone_nums: 1

read_timeout: 30 # 读超时时间
write_timeout: 40 # 写超时时间

app_config: {
  "huobi": {
    "ws_uri": "wss://api.huobi.pro/ws",
  },
#  "binance": {
#    "ws_uri": "wss://stream.binance.com:9443/ws/stream",
#  },
#  "bitfinex": {
#    "ws_uri": "wss://api-pub.bitfinex.com/ws/2",
#  },
#  "kraken": {
#    "ws_uri": "wss://ws.kraken.com",
#  },
#  "coinBase": {
#    "ws_uri": "wss://ws-feed.pro.coinbase.com",
#  },
#  "hitBtc": {
#    "ws_uri": "wss://api.hitbtc.com/api/2/ws",
#  },
#  "okex": {
#    "weight": 0.05,"ws_uri": "wss://ws.okx.com:8443/ws/v5/public",
#  },
}

msg_server_url: "http://127.0.0.1:10110" # 新短信邮件服务地址
msg_server_app_id: "025633e8-c0ea-463e-bc27-04c50e8b05b8" # 新短信邮件服务appID
msg_server_secret_key: "954146C06A993123F376A0F3DD7C8076F480B4C837AEB426746410F493800871" # 新短信邮件服务secret

default_redis_conf: { # 默认redis连接
  "address": "127.0.0.1:6379", # 连接地址
  "password": "", # 连接密码
  "use_tls": false, # 是否使用tls连接
  "pool_size": 20, # 连接池数量
  "default_db": 5, # 默认使用的db号
  "db_nums": { # 使用的redis库号,仅会初始化这里定义了的db号 key(db号):value(描述)
    #    0: "db 0",
    #    1: "db 1",
    2: "公共数据",
    #    3: "db 3",
        4: "db 4",
    5: "现货",
    #    6: "db 6",
    #    7: "db 7",
    #    8: "db 8",
    #    9: "db 9",
    #    10: "db 10",
    #    11: "db 11",
    #    12: "db 12",
    #    13: "db 13",
    #    14: "db 14",
    #    15: "db 15",
  },
}
