package config

import (
	"github.com/modern-go/concurrent"
	"spot/libs/container/safemap"
	"spot/libs/proto"
)

var SupportSymbol *concurrent.Map
var MarketMaps map[string]MarketEngine
var MarketSymbolMap = make(map[int][]proto.Contract) //支持的交易对根据mrketId分组数据
var ContractMap = safemap.New()

type MarketEngine interface {
	IsForever() bool
	Init()
	Sub([]proto.Contract)
	UnSub([]proto.Contract)
}

func GetSupportSymbolArray() (list []string) {
	if SupportSymbol == nil {
		return
	}
	SupportSymbol.Range(func(key, value any) bool {
		s := key.(string)
		list = append(list, s)
		return true
	})
	return
}
