/*
@Time : 2019-04-26 15:25
<AUTHOR> mocha
@File : huobi
*/
package config

const (
	HuobiAccountInfo   = "account:huobi"
	HuobiSymbols       = "huobi:symbols"
	HuobiAccountNotice = "coin:notice:%s:%s"
)

const (
	HttpPost = "POST"
	HttpGet  = "GET"
)
const (
	GetKLine       = "/market/history/kline"
	GetMergeTicker = "/market/detail/merged" //获取聚合行情
	GetTickers     = "/market/tickers"
	GetMarketDepth = "/market/depth" //获取深度
	GetRecentTrade = "/market/trade"
	GetTrade       = "/market/detail" //获取Market Detail 24小时成交量数据

)
const (
	GetSymbols   = "/v1/common/symbols"   //查询系统支持的所有交易及精度
	GetCurrencys = "/v1/common/currencys" //查询系统支持的所有币种
	GetTimestamp = "/v1/common/timestamp" //系统时间
)

const (
	GetAccounts       = "/v1/account/accounts"
	GetAccountBalance = "/v1/account/accounts/%s/balance"
)

const (
	PlaceOrder              = "/v1/order/orders/place"                   //下单
	SubmitCancelOrder       = "/v1/order/orders/%s/submitcancel"         //撤销订单
	CancelOrderWithClientId = "/v1/order/orders/submitCancelClientOrder" //撤销订单基于clientid
	OpenOrders              = "/v1/order/openOrders"                     //查询当前未成交订单
	OpenOrdersBatch         = "/v1/orders/batchCancelOpenOrders"         //批量撤单
	CancelOrderByIds        = "/v1/order/orders/batchcancel"             //基于订单Id的批量撤单
)

const (
	OrderDetail           = "/v1/order/orders/%s"              //查询订单详情
	OrderDetailByClientId = "/v1/order/orders/getClientOrder"  //查询订单详情
	TradeDealDeatail      = "/v1/order/orders/%s/matchresults" //指定订单成交明细
	OrderSearch           = "/v1/order/orders"                 //按条件查询历史订单
	OrderMatchResults     = "/v1/order/matchresults"           //当前和历史成交记录
)

const (
	//symol以逗号分隔,执行通配符*
	//WSOrderState    = "orders.%s"
	WSOrderState    = "orders.%s.update" //订单更新,2019.8月新增,替代旧
	WSMarketTickers = "market.tickers"
	WSTradeDetail   = "market.%s.trade.detail"
	WSDepth         = "market.%s.depth.%s"
	WSKline         = "market.%s.kline.%s"
	WsAskBid        = "market.%s.bbo"
)

const (
	HuobiAccountTypeSpot   = "spot"   //现货
	HuobiAccountTypeMargin = "margin" //杠杆
	HuobiAccountTypeOtc    = "otc"    //场外
	HuobiAccountTypePoint  = "point"  //点卡
)

const (
	OrderSideBuy         = 0
	OrderSideSell        = 1
	OrderPricetypeLimit  = 0
	OrderPricetypeMarket = 1
)

const (
	OrderMarket = "market"
	OrderLimit  = "limit"
	SideBuyKey  = "buy"
	SideSellKey = "sell"
	//buy-market, sell-market, buy-limit, sell-limit
	OrderBuyMarket  = "buy-market"
	OrderSellMarket = "sell-market"
	OrderBuyLimit   = "buy-limit"
	OrderSellLimit  = "sell-limit"
)

const (
	SucessHuobi = "ok"
	ErrorHuobi  = "error"
)

const (
	//submitting , submitted, partial-filled 部分成交, partial-canceled 部分成交撤销, filled 完全成交, canceled 已撤销
	OrderStatusSubmitting      = 0 //提交中
	OrderStatusSubmitted       = 1 //已提交
	OrderStatusPartialFilled   = 2 //部分成交
	OrderStatusPartialCanceled = 3 //部分成交,撤销
	OrderStatusFilled          = 4 //完全成交
	OrderStatusCanceled        = 5 //已撤销

)

var OrderState map[string]int

const (
	HbOrderStatusSubmitting      = "submitting"       //提交中
	HbOrderStatusSubmitted       = "submitted"        //已提交
	HbOrderStatusPartialFilled   = "partial-filled"   //部分成交
	HbOrderStatusPartialCanceled = "partial-canceled" //部分成交,撤销
	HbOrderStatusFilled          = "filled"           //完全成交
	HbOrderStatusCanceled        = "canceled"         //已撤销
)
