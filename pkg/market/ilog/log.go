package ilog

import (
	"path/filepath"
	log "spot/libs/zlog"
)

var (
	BucketLogger log.Logger
	DepthLogger  log.Logger
	PriceLogger  log.Logger
)

func InitLogger(file, level string) {
	// 创建日志文件夹
	fPath := filepath.Dir(file)
	fName := filepath.Base(file)
	option := &log.LoggerOption{
		IsSingleLevel: false,
		FilePath:      fPath,
		FileName:      fName,
		MaxSize:       500,
		MaxBackups:    100,
		MaxBackupDays: 30,
		Compress:      true,
		ServiceName:   fName,
		LogLevel:      level,
	}
	log.InitLoggerManager(option)
	BucketLogger = log.GetLogger("bucket")
	DepthLogger = log.GetLogger("depth")
	PriceLogger = log.GetLogger("price")
}
