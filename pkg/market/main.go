package main

import (
	"fmt"
	"os"

	"spot/libs/cache"
	"spot/libs/conf"
	"spot/libs/database"
	"spot/libs/log"
	"spot/libs/pprof"
	"spot/libs/xsignal"
	"spot/pkg/market/ilog"
	"spot/pkg/market/server"
	"spot/pkg/market/service"
	bizservice "spot/pkg/market/service/biz.service"
)

var isDepthTest = false

//var isDepthTest = false

var (
	gitHash   string
	gitDigest string
	gitBranch string
	buildUser string
	buildTime string
	goVersion string
)

func main() {
	args := os.Args
	if len(args) == 2 && (args[1] == "--version" || args[1] == "-v") {
		fmt.Printf("Git Branch: %s \n", gitBranch)
		fmt.Printf("Git Commit Hash: %s \n", gitHash)
		fmt.Printf("Git Commit Digest: %s \n", gitDigest)
		fmt.Printf("Build User: %s \n", buildUser)
		fmt.Printf("Build TimeStamp: %s \n", buildTime)
		fmt.Printf("GoLang Version: %s \n", goVersion)
		return
	}

	// 初始化配置文件
	conf.Init()

	// 初始化日志
	initLogger()

	// 初始化服务监控
	pprof.Init(conf.PprofAddr())

	//初始化服务支持的交易对
	bizservice.InitSupportSymbol()

	//初始化缓存
	cache.InitDefaultRedisConn()

	// 初始化数据库
	database.InitDefaultDB()

	// 初始化rpc服务
	if conf.IsMaster() {
		if err := server.InitRPC(); err != nil {
			log.Fatalf("init rpc server failed, err:%v", err)
		}
	}

	//int message queue
	bizservice.InitMessageQueue()

	service.Init()

	//启动ticker handler
	bizservice.StartSymbolTickerHandler()

	//初始化多行情处理引擎
	if !isDepthTest {
		service.StartBizEngine()
	}

	//运行定时任务
	server.RunCronTask()

	// 监听系统信号
	xsignal.NewSignal().AppendCloseFunc(
		server.ShutdownServers,
		pprof.Stop).SignalMonitor()
}

func initLogger() {
	log.InitLogger(conf.LogFile(), conf.LogLevel(), conf.LogOnlyFile())
	ilog.InitLogger(conf.LogFile(), conf.LogLevel())

	//path := filepath.Dir(conf.LogFile())
	//name := filepath.Base(conf.LogFile())
	//option := &zlog.LoggerOption{FilePath: path, FileName: name, LogLevel: conf.LogLevel(), MaxSize: 500, MaxBackups: 30, MaxAge: 1, Compress: true, ServiceName: name}
	//log.InitLoggerManager(option, true)
}
