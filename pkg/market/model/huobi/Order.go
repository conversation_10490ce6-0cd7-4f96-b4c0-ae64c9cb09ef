/*
@Time : 2019-04-29 21:07
<AUTHOR> mocha
@File : Order
*/
package models

//old 火币订单详情
type HuoBiOrder struct {
	Id              int64  `json:"id"`
	Symbol          string `json:"symbol"`
	AccountId       int64  `json:"account-id"`
	Amount          string `json:"amount"`
	Price           string `json:"price"`
	CreateTime      int64  `json:"created-at"`
	Type            string `json:"type"`
	FiledAmount     string `json:"field-amount"`
	FiledCashAmount string `json:"field-cash-amount"`
	FiledFee        string `json:"field-fees"`
	FinishTime      int64  `json:"finished-at"`
	Api             string `json:"api"`
	CanceledTime    int64  `json:"canceled-at"`
	Exchange        string `json:"exchange"`
	Batch           string `json:"batch"`
	State           string `json:"state"`
}

//{
//"op": "notify",
//"ts": *************,
//"topic": "orders.htusdt.update",
//"data": {
//"unfilled-amount": "0.000000000000000000",
//"filled-amount": "5000.000000000000000000",
//"price": "1.662100000000000000",
//"order-id": **********,
//"symbol": "htusdt",
//"match-id": 94984,
//"filled-cash-amount": "8301.357280000000000000",
//"role": "taker|maker",
//"order-state": "filled",
//"client-order-id": "a0001",
//"order-type": "buy-limit"
//}

type HBOrderDetail struct {
	ClientOrderId    string `json:"client-order-id"`    //自定义订单id
	Id               int64  `json:"order-id"`           //火币订单id
	MatchId          int64  `json:"match-id"`           //如果成交,则为撮合id,否则为消息id
	Symbol           string `json:"symbol"`             //交易对名称
	UnFilledAmount   string `json:"unfilled-amount"`    //订单未成交数量  最近未成交数量（当order-state = submitted 时，unfilled-amount 为原始订单量；当order-state = canceled OR partial-canceled 时，unfilled-amount 为未成交数量；当order-state = filled 时，如果 order-type = buy-market，unfilled-amount 可能为一极小值；如果order-type <> buy-market 时，unfilled-amount 为零；当order-state = partial-filled AND role = taker 时，unfilled-amount 为未成交数量；当order-state = partial-filled AND role = maker 时，unfilled-amount 为未成交数量。）
	FilledAmount     string `json:"filled-amount"`      //最近成交量
	Price            string `json:"price"`              //成交价
	FilledCashAmount string `json:"filled-cash-amount"` //最近成交数额
	Role             string `json:"role"`               //角色,take|maker
	OrderState       string `json:"order-state"`        //订单类型，包括buy-market, sell-market, buy-limit, sell-limit, buy-ioc, sell-ioc, buy-limit-maker, sell-limit-maker,buy-stop-limit,sell-stop-limit
	OrderType        string `json:"order-type"`

	TotalFilledAmount     string `json:"total-filled-amount"`
	TotalFilledCashAmount string `json:"total-filled-cash-amount"`
	Source                int    `json:"source"` //来源,0-ws,1-api
}

type HBOrderDataStatus struct {
	OP    string        `json:"op"`
	TS    int64         `json:"ts"`
	Topic string        `json:"topic"`
	Data  HBOrderDetail `json:"data"`
}

type MarketOrder struct {
	HBOrderDetail
}

//"id": 29553,
////"order-id": 59378,
////"match-id": 59335,
////"trade-id": 100282808529,
////"symbol": "ethusdt",
////"type": "buy-limit",
////"source": "api",
////"price": "100.1000000000",
////"filled-amount": "9.1155000000",
////"filled-fees": "0.0182310000",
////"created-at": 1494901400435,
////"role": "maker",
////"filled-points": "0.0",
////"fee-deduct-currency": ""

type MatchDetail struct {
	Data []MatchData `json:"data"`
}

type MatchData struct {
	Id                int64  `json:"id"`
	OrderId           int64  `json:"order-id"`
	Symbol            string `json:"symbol"` //交易对名称
	MatchId           int64  `json:"match-id"`
	TradeId           int64  `json:"trade-id"`
	Type              string `json:"type"`
	Source            string `json:"source"`
	Price             string `json:"price"`
	FilledAmount      string `json:"filled-amount"`
	FilledFees        string `json:"filled-fees"`
	CreateAt          int64  `json:"create-at"`
	Role              string `json:"role"`
	FilledPoints      string `json:"filled-points"`
	FeeDeductCurrency string `json:"fee-deduct-currency"`
}
