package server

import (
	"github.com/robfig/cron"
	"spot/libs/log"
)

const (
	everyM     = "0 * * * * *"   // 秒,分,时,日,月,周 每分钟
	everyS     = "*/1 * * * * *" // 秒,分,时,日,月,周 每分钟
	everyFiveS = "*/1 * * * * *" // 秒,分,时,日,月,周 每分钟
)

func RunCronTask() {
	//biz_service.SpotContractHttpGet()
	//biz_service.SpotContractHttpGetForAux() //辅助

	c := cron.New()
	//err := c.AddFunc(everyS, func() {
	//	bizservice.SpotContractHttpGet()
	//	bizservice.SpotContractHttpGetForCoin()
	//})
	//if err != nil {
	//	log.Errorf("添加每秒钟获取第三方成交价失败,%v", err)
	//}
	//
	//err = c.AddFunc(everyFiveS, func() {
	//	bizservice.SpotContractHttpGetForAux() //辅助
	//})
	//if err != nil {
	//	log.Errorf("添加每秒钟获取第三方成交价失败,%v", err)
	//}

	c.Start()
	log.Infof("定时任务启动完毕")
}
