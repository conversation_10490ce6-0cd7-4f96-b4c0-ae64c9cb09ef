/*
@Time : 2019-12-30 17:15
<AUTHOR> mocha
@File : rpc.server
*/
package server

import (
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
	"spot/pkg/market/service"
)

func (r *RPC) NotifySymbolChange(arg *proto.SymbolArg, reply *define.Reply) error {
	log.Infof("收到合约更新通知")
	//_, err := database.GetContractByCode(arg.ContractCode)
	//if err != nil {
	//	log.Errorf("查询指定信息信息失败:%v,symbolId;%v", err, arg.ContractCode)
	//	reply.Ret = define.ErrCodeActionInvalid
	//	reply.Msg = define.ErrMsgActionInvalid.Msg
	//	return nil
	//}
	go service.StartSymbolsBusiness()

	return nil
}

func (r *RPC) TestContractWarn(arg *proto.SymbololWarnArg, reply *define.Reply) error {
	log.Warnf("TestContractWarn,arg；%+v", *arg)
	//okex_impl.DealMarketWarn(arg.Gap, arg.ContractCode)
	return nil
}

func (r *RPC) TestWsClose(arg string, reply *define.Reply) error {
	//okex_impl.SendWsApiStatusEmail(arg)
	return nil
}
