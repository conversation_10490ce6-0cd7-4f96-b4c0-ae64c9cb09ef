package biance_spot

import (
	"go.uber.org/zap"
	"spot/libs/biance"
	"spot/libs/conf"
	"spot/libs/convert"
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/nums"
	"spot/libs/proto"
	bizservice "spot/pkg/market/service/biz.service"
	"spot/pkg/market/service/wshander"
	"strings"
	"time"
)

var apiService *biance.MarketWsClient
var dataHandler *wshander.WsDataHandler

func Init() {
	//初始化ws处理协成
	dataHandler = wshander.NewWsDataHandler(dealTrade, dealDepth)

	//初始化websocket
	appKey, ok := conf.AppKeyConfig()[_name]
	if !ok {
		log.Errorf("%v,没有查询到app配置信息")
		return
	}
	log.Infof("获取到%v app config:%+v", _name, appKey)

	wsUrl := appKey.GlobalWSURI
	dataChannel := make(chan []byte, 10240)
	go func() {
		for bytes := range dataChannel {
			dealMarketWsData(bytes)
		}
	}()
	go func() {
		for range time.Tick(5 * time.Second) {
			log.Info("币安websocket入口检测", zap.Int("当前队列长度", len(dataChannel)))
		}
	}()
	apiService = biance.NewMarketWsClient(&biance.Config{WsPoint: wsUrl}, func(bytes []byte) {
		log.Infof("binance spot:%v", string(bytes))
		dataChannel <- bytes
		//dealMarketWsData(bytes)
	})
}

func dealMarketWsData(b []byte) {
	msgType := &struct {
		EventType string `json:"e"` // EventType represents the update type
		Time      uint64 `json:"E"` // Time represents the event time
		Symbol    string `json:"s"`
	}{}
	//log.Infof("binace msg:%+v", string(b))
	if err := json.Unmarshal(b, msgType); err != nil {
		return
	}

	data := &wshander.WsData{
		Source: _name,
		Name:   msgType.Symbol,
		Data:   b,
	}
	if msgType.EventType == "trade" || msgType.EventType == "aggTrade" {
		data.DataType = wshander.WsTrade
	} else if msgType.EventType == "depthUpdate" {
		data.DataType = wshander.WsDepth
	}
	dataHandler.Notify(data)

}

func dealTrade(b []byte) {
	log.Info("开始处理最新成交", zap.String("Data", string(b)))
	update := &TradesUpdate{}
	err := json.Unmarshal(b, update)
	if err != nil {
		log.Errorf("%v unmarshal order Data fail,%v", err)
		return
	}
	side := define.OrderBuy
	if !update.Maker {
		side = define.OrderSell
	}
	//log.Info("开始处理最新成交 TEST LOG", zap.String("side", side), zap.Any("update", update))

	price := nums.NewFromString(update.Price)
	amount := convert.String2Float(update.Quantity)
	name := getContractCode(update.Symbol)
	if name == "" {
		return
	}
	marketTrade := proto.MarketTrade{Symbol: name, DealTime: time.Now().Unix(), Ts: time.Now(), Price: price, Volume: nums.NewFromFloat(amount), Side: side, Source: _name}
	log.Info("trade", zap.Any("Data", marketTrade))
	bizservice.DealThirdSpotTrade(marketTrade)

	return
}

func getContractCode(symbol string) string {
	data, ok := convertMap.Load(strings.ToLower(symbol))
	if !ok {
		return ""
	}
	name := data.(string)
	if name == "" {
		log.Infof("%v最新成交，交易对名称有误", name)
		return ""
	}
	return name
}

func dealDepth(b []byte) {
	log.Info("开始处理深度数据", zap.String("Data", string(b)))
	//log.Infof("binance depth origin:%v", string(b))
	update := &DepthUpdate{}
	err := json.Unmarshal(b, update)
	if err != nil {
		log.Errorf("%v unmarshal depth Data fail,%v", err)
		return
	}
	//log.Info("11111111111", zap.Any("Data", update), zap.String("s", update.Symbol), zap.Any("s", convertMap))
	name := getContractCode(update.Symbol)
	if name == "" {
		return
	}

	log.Infof("depth update:%+v", *update)
	//ask, bid := symbolFullDepth.update(update)
	asks := make([]proto.DepthData, 0) //当前的所有卖单 [price, quote volume
	bids := make([]proto.DepthData, 0) //火币买盘

	for _, v := range update.Asks {
		price := nums.NewFromString(v[0])
		amount := nums.NewFromString(v[1])
		gsData := proto.DepthData{Price: price, Volume: amount}
		asks = append(asks, gsData)
	}
	for _, v := range update.Bids {
		price := nums.NewFromString(v[0])
		amount := nums.NewFromString(v[1])
		gsData := proto.DepthData{Price: price, Volume: amount}
		bids = append(bids, gsData)
	}
	depth := proto.DepthHolder{Symbol: name, Asks: asks, Bids: bids, Source: _name, Ts: time.Now(), LastUpdate: int64(update.UpdateID)}
	log.Infof("depth binance:%+v", depth)

	dealPartDepth(&depth, false)
	//biz_service.DealDepth(depth)
	return
}

type DepthUpdate struct {
	EventType string     `json:"e"` // EventType represents the update type
	Time      uint64     `json:"E"` // Time represents the event time
	Symbol    string     `json:"s"` // Symbol represents the symbol related to the update
	UpdateID  int        `json:"u"` // UpdateID to sync up with updateid in /api/v1/depth
	LUpdateID int        `json:"U"` // UpdateID to sync up with updateid in /api/v1/depth
	Bids      [][]string `json:"b"` // Bids is a list of bids for symbol
	Asks      [][]string `json:"a"` // Asks is a list of asks for symbol
}

//{
//"e": "aggTrade",  // 事件类型
//"E": 123456789,   // 事件时间
//"s": "BNBBTC",    // 交易对
//"a": 12345,       // 归集交易ID
//"p": "0.001",     // 成交价格
//"q": "100",       // 成交数量
//"f": 100,         // 被归集的首个交易ID
//"l": 105,         // 被归集的末次交易ID
//"T": 123456785,   // 成交时间
//"m": true,        // 买方是否是做市方。如true，则此次成交是一个主动卖出单，否则是一个主动买入单。
//"M": true         // 请忽略该字段
//}
type TradesUpdate struct {
	EventType             string `json:"e"` // EventType represents the update type
	Time                  uint64 `json:"E"` // Time represents the event time
	Symbol                string `json:"s"` // Symbol represents the symbol related to the update
	TradeID               int    `json:"a"` // TradeID is the aggregated trade ID
	Price                 string `json:"p"` // Price is the trade price
	Quantity              string `json:"q"` // Quantity is the trade quantity
	FirstBreakDownTradeID int    `json:"f"` // FirstBreakDownTradeID is the first breakdown trade ID
	LastBreakDownTradeID  int    `json:"l"` // LastBreakDownTradeID is the last breakdown trade ID
	TradeTime             uint64 `json:"T"` // Time is the trade time
	Maker                 bool   `json:"m"` // Maker indicates whether buyer is a maker
}
