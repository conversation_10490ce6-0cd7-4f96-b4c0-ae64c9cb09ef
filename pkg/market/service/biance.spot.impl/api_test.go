package biance_spot

import (
	"math/rand"
	"spot/libs/biance"
	"spot/libs/log"
	"spot/libs/nums"
	"spot/libs/proto"
	"testing"
)

func TestWs(t *testing.T) {
	log.InitLogger("info", "info", false)
	apiService = biance.NewMarketWsClient(&biance.Config{WsPoint: "wss://stream.binance.com:9443/ws/stream"}, func(bytes []byte) {
		log.Infof("binance spot:%v", string(bytes))
		dealMarketWsData(bytes)
	})
	e := &Engineer{}
	var list []proto.Contract
	info := proto.Contract{ContractCode: "BTC/USDT", CoinName: "USDT", BaseCoinName: "BTC"}
	list = append(list, info)
	e.Sub(list)
	e.GetDepth()
	select {}
}

func TestA(t *testing.T) {
	S := nums.NewFromInt64(rand.Int63n(4)).Add(nums.NewFromInt(1)).Shift(-5)
	t.Log(S)
}
