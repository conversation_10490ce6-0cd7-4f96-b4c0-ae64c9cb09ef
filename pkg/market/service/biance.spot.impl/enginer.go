package biance_spot

import (
	"github.com/modern-go/concurrent"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"net/http"
	"spot/libs/biance"
	"spot/libs/define"
	"spot/libs/nums"
	"spot/libs/utils"
	"spot/pkg/market/model"
	"sync"
	"time"
)

import (
	"fmt"
	"spot/libs/log"
	"spot/libs/proto"
	"strings"
)

const _name = define.MarketNameSpotBinance

var convertMap = concurrent.NewMap()

type Engineer struct {
	Lock    sync.Mutex
	LastRev time.Time
	model.EngineDetail
	Codes []proto.Contract
}

func (e *Engineer) IsForever() bool {
	return false
}

func (e *Engineer) SupportRContract() bool {
	return false
}

func (e *Engineer) Name() string {
	return _name
}

func (e *Engineer) Init() {
	Init()
	go func() {
		time.Sleep(10 * time.Second)
		e.GetDepth()
	}()
}

func (e *Engineer) GetDepth() {
	tick := time.Tick(1 * time.Minute)
	go func() {
		for range tick {
			e.Lock.Lock()
			codes := e.Codes
			e.Lock.Unlock()
			log.Info("eCode", zap.Any("data", codes))
			if len(e.Codes) > 0 {
				dealContractDepth(codes)
			} else {
				log.Info("币安ecodes is nil")
			}

		}
	}()
}

func (e *Engineer) Sub(s []proto.Contract) {
	e.Lock.Lock()
	e.Codes = s
	e.Lock.Unlock()
	var symbols []string
	for _, entry := range s {
		name := utils.StrBuilderBySep("", entry.BaseCoinName, entry.CoinName)
		symbol := strings.ToLower(name)
		symbols = append(symbols, strings.ToLower(name))
		convertMap.Store(symbol, entry.ContractCode)
	}
	SubscriptTradePairs(symbols)
	log.Info("eCode", zap.Any("Data", e.Codes))
}

func (e *Engineer) UnSub(s []proto.Contract) {
	var symbols []string
	for _, entry := range s {
		name := utils.StrBuilderBySep("", entry.BaseCoinName, entry.CoinName)
		symbol := strings.ToLower(name)
		symbols = append(symbols, symbol)
	}
	//UnSubscribe(symbols)
}

func NewEngineer() *Engineer {
	return &Engineer{
		EngineDetail: model.EngineDetail{Name: _name},
	}
}

func SubscriptTradePairs(list []string) {
	log.Infof("%v订阅的交易对：%v", _name, list)
	topics := createSubTopics(list)
	if apiService != nil {
		apiService.Subscript(topics)
	}
}

//var topics = []string{"%s@depth"}

var topics = []string{"%s@aggTrade", "%s@depth"}

//var topics = []string{"%s@trade", "%s@depth"}

func createSubTopics(list []string) []string {
	var l []string
	for _, topic := range topics {
		for _, s := range list {
			l = append(l, fmt.Sprintf(topic, s))
		}
	}
	return l
}

func GetSpotDepth(client *http.Client, code string) (dh *proto.DepthHolder) {
	count := 100
	asks := make([]proto.DepthData, 0) //当前的所有卖单 [price, quote volume]
	bids := make([]proto.DepthData, 0) //买盘
	data := biance.GetSpotDepth(client, code, count)
	if data == nil {
		log.Info("获取biance现货合约深度数据为空", zap.String("code", code))
		return
	}
	var wg sync.WaitGroup
	wg.Add(2)
	go func() {
		defer wg.Done()
		for _, v := range data.Asks {
			price := nums.NewFromString(v[0])
			if price.LessThanOrEqual(decimal.Zero) {
				continue
			}
			amount := nums.NewFromString(v[1])
			gsData := proto.DepthData{Price: price, Volume: amount}
			asks = append(asks, gsData)
		}
	}()

	go func() {
		defer wg.Done()
		for _, v := range data.Bids {
			price := nums.NewFromString(v[0])
			if price.LessThanOrEqual(decimal.Zero) {
				continue
			}
			amount := nums.NewFromString(v[1])
			gsData := proto.DepthData{Price: price, Volume: amount}
			bids = append(bids, gsData)
		}
	}()
	wg.Wait()
	dh = &proto.DepthHolder{Symbol: code, Asks: asks, Bids: bids, Source: _name, Ts: time.Now(), LastUpdate: data.LastUpdateID}
	return
}

var client = utils.NewHttpClient()

func dealContractDepth(codes []proto.Contract) {
	log.Info("开始binance现货深度获取", zap.Any("codes", codes))
	var wg sync.WaitGroup
	for _, v := range codes {
		wg.Add(1)
		go func(w *sync.WaitGroup, code string) {
			GetRest(code)
			w.Done()
		}(&wg, v.ContractCode)
	}
	wg.Wait()
}

func GetRest(code string) {
	dh := GetSpotDepth(client, code)
	if dh != nil {
		dealPartDepth(dh, true)
	}
}
