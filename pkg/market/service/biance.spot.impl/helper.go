package biance_spot

import (
	"spot/libs/commonsrv"
	"spot/libs/proto"
)

func converSystemSymbol(entry proto.Contract) string {
	return commonsrv.DealSourceNeedConvert(_name, entry.ContractCode)
	//return entry.BaseCoinName + entry.CoinName
}

type BiMap struct {
	keyMap   map[interface{}]interface{}
	valueMap map[interface{}]interface{}
}

func NewBiMap() *BiMap {
	return &BiMap{
		keyMap:   make(map[interface{}]interface{}),
		valueMap: make(map[interface{}]interface{}),
	}
}

func (b *BiMap) Put(key, value interface{}) {
	b.keyMap[key] = value
	b.valueMap[value] = key
}

func (b *BiMap) Del(key interface{}) {
	v, ok := b.keyMap[key]
	if ok {
		delete(b.valueMap, v)
		delete(b.keyMap, key)
	}
}

func (b *BiMap) GetByKey(key interface{}) (interface{}, bool) {
	a, ok := b.keyMap[key]
	return a, ok
}

func (b *BiMap) GetByValue(value interface{}) (interface{}, bool) {
	a, ok := b.valueMap[value]
	return a, ok
}
