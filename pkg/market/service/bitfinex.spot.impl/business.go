/*
@Time : 2/7/20 5:32 下午
<AUTHOR> mocha
@File : binance.init
*/
package bitfinex_spot_impl

import (
	"bytes"
	"encoding/json"
	"fmt"
	"spot/libs/bitfinex"
	"spot/libs/commonsrv"
	"spot/libs/container/safemap"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/nums"
	"spot/libs/proto"
	"spot/libs/utils"
	"spot/pkg/market/service/biz.service"
	"strings"
	"time"
)

var convertMap = safemap.New()
var channelMap = make(map[int64]string) //记录订阅
func StartClient(config *define.AppKey) {
	t := time.Tick(1 * time.Hour)
	convertMap = safemap.New()
	client = bitfinex.NewMarketWsClient(&bitfinex.Config{WsPoint: config.GlobalWSURI}, wsMsgHandler)
	client.CloseHook(ReportWsClose)
	client.Start()
	client.Loop()
	go func() {
		for range t {
			channelMap = make(map[int64]string) //重新初始化
			client.Restart()
		}
	}()

}

func ReportWsClose(code int, msg string) {
	log.Errorf("rev ws close report code:%v,msg:%v", code, msg)
	//biz_service.ReportMarketStop(code, msg, "")
	//
	////发送api断开告警
	////SendWsApiStatusEmail(msg)
	////修改websocke状态
	//err := database.UpdateContractMarketConfig(0)
	//if err != nil {
	//	log.Errorf("database.UpdateContractMarketConfig fail,%v", err)
	//	return
	//}
}

func subscript(list []proto.Contract) {
	var symbols []string
	for _, entry := range list {
		contractCode := utils.StrBuilderBySep("", entry.BaseCoinName, entry.CoinName)
		code := fmt.Sprintf("t%v", contractCode)
		code = strings.ReplaceAll(code, "USDT", "USD")
		symbols = append(symbols, code)
	}
	if client == nil {
		Init()
	}
	client.Subscript([]string{"Trades"}, symbols)
}

func unSubscribe() {
	//client.UnSubscript(topics)
}

func wsMsgHandler(data []byte) {
	if bytes.Contains(data, []byte("subscribed")) {
		sub := new(bitfinex.Sub)
		e := json.Unmarshal(data, sub)
		if e != nil {
			log.Errorf("json unmarshal fail,%v", e)
			return
		}
		if sub.ChannelId > 0 {
			key := sub.ChannelId
			code := strings.ReplaceAll(sub.Pair, "USD", "USDT")
			channelMap[key] = code
		}
		return
	}
	dealTicker(data)
}

func dealTicker(data []byte) {
	var list []interface{}
	e := json.Unmarshal(data, &list)
	if e != nil {
		return
	}
	id := list[0].(float64)
	cId := int64(id)
	code, ok := channelMap[cId]
	if !ok {
		return
	}
	typeS, ok := list[1].(string)
	if !ok || (typeS != "tu" && typeS != "te") {
		return
	}

	d := list[2].([]interface{})
	if len(d) < 3 {
		return
	}
	//date := d[1].(float64)
	price := d[3].(float64)

	log.Infof("code:%v,price:%v", code, price)
	if price <= 0 {
		return
	}

	code, Price := commonsrv.ConvertTradePriceAndCode(code, nums.NewFromFloat(price))
	td := &proto.MarketTrade{
		Symbol:   code,
		DealTime: time.Now().Unix(),
		Price:    Price,
		Ts:       time.Now(),
		Source:   _name,
	}
	biz_service.DealThirdSpotTrade(*td)
}
