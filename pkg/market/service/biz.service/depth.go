package biz_service

import (
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/commonsrv"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
	"strings"
	"time"
)

func DealDepth(holder proto.DepthHolder) {
	log.Infof("收到depth:%+v", holder)
	c, err := commonsrv.GetContractDetail(0, holder.Symbol, nil)
	if err != nil {
		log.Errorf("commonsrv.GetContractDetail fail,%v", err)
		return
	}
	data := commonsrv.DealDepth(holder, c)
	s := IsSourcePush(holder.Source, c)
	log.Info("开始判断是深度否推送成交", zap.Any("数据来源", holder.Source), zap.Any("下单市场", c.MarketSource.GetHedgeSource()), zap.Any("铺单来源", c.<PERSON>etingSource), zap.Bool("是否推送", s))
	if s {
		NotifyDepth(c, data)
	}
}
func NotifyDepth(p *proto.Contract, holder *proto.DepthHolder) {
	var buy, sell []proto.Depth
	for i, item := range holder.Bids {
		iDepth := proto.Depth{Price: item.Price, Amount: item.Volume, TotalAmount: item.Volume}
		if i == 0 {
			buy = append(buy, iDepth)
			continue
		}
		l := buy[i-1]
		iDepth.TotalAmount = iDepth.TotalAmount.Add(l.TotalAmount)
		buy = append(buy, iDepth)
	}

	for i, item := range holder.Asks {
		iDepth := proto.Depth{Price: item.Price, Amount: item.Volume, TotalAmount: item.Volume}
		if i == 0 {
			sell = append(sell, iDepth)
			continue
		}
		l := sell[i-1]
		iDepth.TotalAmount = iDepth.TotalAmount.Add(l.TotalAmount)
		sell = append(sell, iDepth)
	}

	depthFinal := &proto.DepthContainer{
		Id:           database.NextID(),
		TS:           time.Now().Unix(),
		Level:        0,
		Digit:        p.Digit,
		PriceLimit:   p.Digit,
		VolumeDigit:  p.VolumeDigit,
		ContractCode: p.ContractCode,
		Buy:          buy,
		Sell:         sell,
	}
	//缓存合约指数
	cache.SetContractOriginDepth(depthFinal) //缓存深度数据

	cache.SetContractDepthInfo(&proto.DepthSimpleInfo{
		ContractCode: p.ContractCode,
		BuyFirst:     buy[0].Price,
		SellFirst:    sell[0].Price,
		TS:           time.Now(),
	})
	//NotifyMsgDepthToMsgHandler(*depthFinal)
	notifyMqDepthChange(depthFinal.GetDepthContainerPub())

	//生成成交价指数数据推送并入缓存
	h := cache.GetContractPriceIndex(p.ContractCode)
	if h == nil {
		h = &proto.IndexHistory{ContractCode: p.ContractCode}

	}
	if h.TradePrice.GreaterThan(decimal.Zero) {
		h.TradePrice = h.TradePrice
		//美元折合
		usd, _ := cache.GetCoinRate(define.LegalCoinNameUSD, define.CacheDBNumber2)
		if strings.ToUpper(p.CoinName) == "USDT" {
			h.TradePriceCn = h.TradePrice.Mul(usd.PriceCNY).Round(define.CNYPrecision)
		} else {
			//折合成保证金币种量，换算成美元，折合人民币
			u, _ := cache.GetCoinRate(p.CoinName, define.CacheDBNumber2)
			h.TradePriceCn = h.TradePrice.Mul(u.PriceUSDT).Mul(usd.PriceCNY).Round(define.CNYPrecision)
		}
	}
	h.IndexId = database.NextID()
	h.CreateBy = time.Now()
	h.BuyFirst = buy[0].Price
	h.SellFirst = sell[0].Price

	if h.BuyFirst.GreaterThanOrEqual(h.SellFirst) {
		log.Warnf("深度生成：计算出指数价价格 卖出价大于买入价，退出，index:%+v", *h)
		log.Infof("[warning]计算出指数价价格 卖出价大于买入价，退出，index:%+v", *h)
		return
	}

	cache.SetContractPriceIndex(h)

}

//生成最新成交信息
func notifyNewTrade(h *proto.IndexHistory, contract *proto.Contract) {
	order := &proto.MatchOrder{
		Id:           h.IndexId,
		Symbol:       h.ContractCode,
		Side:         h.Side,
		TradeVolume:  h.DealAmount.RoundBank(contract.VolumeDigit),
		TradePrice:   h.TradePrice,
		TradePriceCn: h.TradePriceCn,
		TradeTime:    h.CreateBy,
		Digit:        contract.Digit,
		VolumeDigit:  contract.VolumeDigit,
	}
	if h.DealAmount.LessThanOrEqual(decimal.Zero) {
		log.Errorf("生成的成交量小于0,%+v", *order)
		return
	}
	notifyMqNewTrade(order)
}

func IsSourcePush(source string, c *proto.Contract) (isPush bool) {
	if c.Delisted {
		log.Info("当前合约下架中", zap.String("code", c.ContractCode))
		return
	}
	if c.IsMaintenance {
		log.Info("当前合约维护中", zap.String("code", c.ContractCode))
		return
	}
	if c.BucketingSource.GetHedgeSource() == source {
		isPush = true
	}
	//log.Infof("s:%v,s1:%v", c.BucketingSource.GetHedgeSource(), source)
	return
}
