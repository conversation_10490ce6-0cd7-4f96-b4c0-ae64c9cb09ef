package biz_service

import (
	"github.com/modern-go/concurrent"
	"go.uber.org/zap"
	"spot/libs/log"
	"spot/libs/proto"
	"time"
)

var _depthHandler = concurrent.NewMap()

func getDepthHandler(code string) *DepthMsgHandler {
	var handler *DepthMsgHandler
	accrual, ok := _depthHandler.Load(code)
	if !ok {
		handler = NewDepthMsgHandler(code)
		accrual, ok = _depthHandler.LoadOrStore(code, handler)
		if !ok {
			handler.Run()
			return handler
		}
	}
	handler, ok = accrual.(*DepthMsgHandler)
	if ok {
		return handler
	}
	return nil
}

type DepthMsgHandler struct {
	code           string
	depthChan      chan proto.DepthContainer
	dc             *proto.DepthContainer
	ti             *proto.DealInfo
	lastPushDcId   int64
	lastDealInfoId int64
}

func NewDepthMsgHandler(code string) *DepthMsgHandler {
	return &DepthMsgHandler{
		code:      code,
		depthChan: make(chan proto.DepthContainer, 1),
	}
}

func NotifyMsgDepthToMsgHandler(dc proto.DepthContainer) {
	log.Info("开始通知深度变化", zap.Any("data", dc))
	handler := getDepthHandler(dc.ContractCode)
	if handler != nil {
		handler.depthChan <- dc
	}
}

func (h *DepthMsgHandler) Run() {
	go func() {
		tick := time.Tick(250 * time.Millisecond)
		for {
			select {
			case depth := <-h.depthChan:
				h.dc = &depth
			case <-tick:
				h.dealDepth(h.dc)
			}
		}
	}()
}

func (h *DepthMsgHandler) dealDepth(depth *proto.DepthContainer) {
	if depth != nil && depth.Id != h.lastPushDcId {
		//处理深度的推送
		notifyMqDepthChange(depth.GetDepthContainerPub())
		h.lastPushDcId = depth.Id
	}
}
