/*
@Time : 2019-12-30 15:37
<AUTHOR> mocha
@File : notify
*/
package biz_service

import (
	"encoding/json"
	"go.uber.org/zap"
	"spot/libs/conf"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/mq"
	"spot/libs/proto"
)

var (
	mqProduct  *mq.MessageQueue
	mqConsumer *mq.MessageQueue
)

func NewMqProduct() {
	mqProduct = mq.NewMessageQueue(conf.MQ())
	mqProduct.Ping()
}

func NewMqConsumer() {
	mqConsumer = mq.NewMessageQueue(conf.MQ())
	queueName := conf.MQQueueName()
	if queueName == "" {
		queueName = define.MQConsumerMarketQueueName
	}
	if conf.IsSimulate() {
		queueName = "sim:" + queueName
	}
	err := mqConsumer.Consumer(define.MQDefaultExchangeName, mq.ExchangeTypeDirect, queueName, define.MQDefaultMQBindKey)
	if err != nil {
		log.Errorf("创建mq消费者失败,%v", err)
	}
	//mq.DefaultHandleMap[define.MQTopicPriceIndex] = MQIndexPriceChange //处理标记价格
	//mq.DefaultHandleMap[define.MQTopicPlatFormTrade] = MQSelftNewTrade //处理自有成交

	mqConsumer.Ping()
}

func InitMessageQueue() {
	NewMqProduct()
	//NewMqConsumer()
}

func StopMQProduct() {
	mqProduct.ShutDown()
}

func StopMQConsumer() {
	mqConsumer.ShutDown()
}

func sendMessage(msg mq.MessagePack) {
	err := mqProduct.Publish(define.MQDefaultExchangeName, mq.ExchangeTypeDirect, define.MQDefaultMQBindKey, msg.Marsha(), define.MQReliable)
	if err != nil {
		log.Errorf("发送mq消息失败,err:%v", err)
		return
	}
}

//通知成交价格及买一卖一
func notifyPriceIndex(market *proto.IndexHistory) {
	b, err := json.Marshal(market)
	if err != nil {
		log.Errorf("notifyMarketPrice json marshal fail,%v", err)
		return
	}
	msg := mq.MessagePack{Topic: define.MQTopicPriceIndex, Data: b}
	sendMessage(msg)
}

//通知综合指数价格
func notifyMarketComplexPrice(market *proto.ComplexPrice) {
	b, err := json.Marshal(market)
	if err != nil {
		log.Errorf("notifyMarketPrice json marshal fail,%v", err)
		return
	}
	msg := mq.MessagePack{Topic: define.MQTopicComplexPrice, Data: b}
	sendMessage(msg)
}

//
////通知标记价格
//func notifyMarketPrice(market *proto.MarketPrice) {
//	b, err := json.Marshal(market)
//	if err != nil {
//		log.Errorf("notifyMarketPrice json marshal fail,%v", err)
//		return
//	}
//	msg := mq.MessagePack{Topic: define.MQTopicMarketPrice, Data: b}
//	sendMessage(msg)
//}

//通知价格指数变化
func MQIndexPriceChange(mp mq.MessagePack) {
	d := new(proto.IndexHistory)
	err := json.Unmarshal(mp.Data, d)
	if err != nil {
		log.Errorf("MqOrderMatch json unMarsha fail,%v", err)
		return
	}
	//log.Warnf("收到指数价格变化，准备检测指数价格")
	//checkPriceIndex(d)
}

//通知深度数据
func notifyMqDepthChange(trade *proto.DepthContainerPub) {
	log.Info("开始推送深度数据", zap.Any("data", trade))
	b, err := json.Marshal(*trade)
	if err != nil {
		log.Errorf("notifyMqTradeMatchMsg json marshal fail,%v", err)
		return
	}
	msg := mq.MessagePack{Topic: define.MQTopicDepth, Data: b}
	sendMessage(msg)
}

//通知最新成交
func notifyMqNewTrade(trade *proto.MatchOrder) {
	b, err := json.Marshal(*trade)
	if err != nil {
		log.Errorf("notifyMqTradeMatchMsg json marshal fail,%v", err)
		return
	}
	msg := mq.MessagePack{Topic: define.MQTopicNewTrade, Data: b}
	sendMessage(msg)
}

//通知自有成交
func notifyMqSelfNewTrade(trade *proto.MatchOrder) {
	b, err := json.Marshal(*trade)
	if err != nil {
		log.Errorf("notifyMqTradeMatchMsg json marshal fail,%v", err)
		return
	}
	msg := mq.MessagePack{Topic: define.MQTopicPlatFormTrade, Data: b}
	sendMessage(msg)
}
