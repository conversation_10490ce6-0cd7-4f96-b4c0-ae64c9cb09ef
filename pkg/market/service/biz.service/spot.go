package biz_service

import (
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"spot/libs/biance"
	"spot/libs/bitfinex"
	"spot/libs/bitstamp"
	"spot/libs/bittrex"
	"spot/libs/cache"
	"spot/libs/coinbase"
	"spot/libs/commonsrv"
	"spot/libs/define"
	"spot/libs/ftx_spot"
	"spot/libs/hitbtc"
	"spot/libs/huobi"
	"spot/libs/huobi_spot"
	"spot/libs/log"
	"spot/libs/okex"
	"spot/libs/poloniex"
	"spot/libs/proto"
	"spot/libs/zb"
	"time"
)

//获取指数转换后的合约map
func ConvertSpotSource(source string, cMap map[string]proto.Contract) (result map[string]struct{}) {
	result = make(map[string]struct{})
	if cMap == nil {
		return nil
	}
	for _, contract := range cMap {
		code := contract.ContractCode
		code = commonsrv.DealSourceNeedConvert(source, code)
		result[code] = struct{}{}
	}
	return
}

func SpotContractHttpGet() {
	c, err := commonsrv.GetContractMapWithNoSetCache(0, nil)
	if err != nil {
		return
	}
	//cMap := make(map[string]struct{})
	//for _, code := range c {
	//	cMap[code.ContractCode] = struct{}{}
	//}
	dealBittrexSpot(ConvertSpotSource(define.MarketNameSpotBittrex, c))
	dealPoloniex(ConvertSpotSource(define.MarketNameSpotPoloniex, c))
	dealBitstamp(ConvertSpotSource(define.MarketNameSpotBitstamp, c))
	//dealZB(cMap)
	dealFrx(ConvertSpotSource(define.MarketNameSpotFrx, c))

}

func SpotContractHttpGetForCoin() {
	c, err := commonsrv.GetContractMapWithNoSetCache(0, nil)
	if err != nil {
		return
	}
	cMap := make(map[string]struct{})
	for _, code := range c {
		cMap[code.ContractCode] = struct{}{}
	}

	list := commonsrv.GetExchangeCoinSymbols()
	for code := range list {
		if _, ok := cMap[code]; !ok {
			cMap[code] = struct{}{}
		}
	}

	dealOkexSpot(ConvertSpotSource(define.MarketNameSpotOkex, c))
	dealHuobiSpot(ConvertSpotSource(define.MarketNameSpotHuobi, c))
	dealBinanceSpot(ConvertSpotSource(define.MarketNameSpotBinance, c))
}

func SpotContractHttpGetForAux() {
	c, err := commonsrv.GetContractMapWithNoSetCache(0, nil)
	if err != nil {
		return
	}
	cMap := make(map[string]struct{})
	for _, code := range c {
		cMap[code.ContractCode] = struct{}{}
	}

	dealBitfinex(ConvertSpotSource(define.MarketNameSpotBitfinex, c))
	dealHitBtc(ConvertSpotSource(define.MarketNameSpotHitbtc, c))
	dealCoinBase(ConvertSpotSource(define.MarketNameSpotCoinbase, c))
}

func dealCoinBase(cMap map[string]struct{}) {
	list := coinbase.GetTickers(cMap)
	cacheSpotContract(list, define.MarketNameSpotCoinbase)
}

func dealHitBtc(cMap map[string]struct{}) {
	list := hitbtc.GetTickers(cMap)
	cacheSpotContract(list, define.MarketNameSpotHitbtc)
}

func dealBitfinex(cMap map[string]struct{}) {
	list := bitfinex.GetTickers(cMap)
	cacheSpotContract(list, define.MarketNameSpotBitfinex)
}

func dealPoloniex(cMap map[string]struct{}) {
	list := poloniex.GetTickers(cMap)
	cacheSpotContract(list, define.MarketNameSpotPoloniex)
}

func dealBitstamp(cMap map[string]struct{}) {
	list := bitstamp.GetTickers(cMap)
	cacheSpotContract(list, define.MarketNameSpotBitstamp)
}

func dealZB(cMap map[string]struct{}) {
	list := zb.GetTickers(cMap)
	cacheSpotContract(list, define.MarketNameSpotZB)
}

func dealFrx(cMap map[string]struct{}) {
	list := ftx_spot.GetTickers(cMap)
	cacheSpotContract(list, define.MarketNameSpotFrx)
}

func dealBittrexSpot(cMap map[string]struct{}) {
	list := bittrex.GetTickers(cMap)
	cacheSpotContract(list, define.MarketNameSpotBittrex)
}

func dealHuobiSpot(cMap map[string]struct{}) {
	list := huobi_spot.GetTickers(cMap)
	cacheSpotContract(list, define.MarketNameSpotHuobi)
}

func dealBinanceSpot(cMap map[string]struct{}) {
	list := biance.GetTickers(cMap)
	cacheSpotContract(list, define.MarketNameSpotBinance)
}

func dealOkexSpot(cMap map[string]struct{}) {
	list := okex.GetTickersForSpot(cMap)
	cacheSpotContract(list, define.MarketNameOkex)
}

func dealHuobi(cMap map[string]struct{}) {
	list := huobi.GetTickers(cMap)
	cacheContractTradePrice(list, define.MarketNameHuoBi)
}

func cacheSpotContract(list []proto.MarketTrade, source string) {
	log.Infof("收到合约现货交易：%+v", list)
	for _, v := range list {
		v.Source = source
		v.IsRestAPI = true
		v.Symbol, v.Price = commonsrv.ConvertTradePriceAndCode(v.Symbol, v.Price)
		if IsUpdateByRestAPI(v.Symbol, v.Source, v.Price, v.Ts) {
			DealThirdSpotTrade(v)
		}
	}
}

//处理交易对第三方合约成交价格
func cacheContractTradePrice(list []proto.MarketTrade, source string) {
	log.Info("收到合约成交价格", zap.String("来源", source), zap.Any("data", list))
	for _, v := range list {
		v.Source = source
		v.IsRestAPI = true
		if IsUpdateByRestAPIByContract(v.Symbol, v.Source, v.Price, v.Ts) {
			DealThirdTrade(v)
		}
	}
}

func IsUpdateByRestAPIByContract(code string, source string, price decimal.Decimal, ts time.Time) (s bool) {
	mk := cache.GeContractMarketPriceBySource(code, source)
	if mk == nil {
		s = true
		return
	}
	if ts.Before(mk.Ts) {
		return
	}
	if mk.Price.Equal(price) {
		log.Info("合约最后价格与合约缓存价格一致，不进行更新", zap.String("code", code), zap.String("价格", price.String()), zap.Any("last", mk))
		return
	}
	if mk.IsRestAPI {
		return true
	}
	if time.Since(mk.Ts).Seconds() > 10 {
		return true
	}

	return s
}

func IsUpdateByRestAPI(code string, source string, price decimal.Decimal, ts time.Time) (s bool) {
	mk := cache.GeContractSpotPriceBySource(code, source)
	if mk == nil {
		s = true
		return
	}
	if ts.Before(mk.Ts) {
		return
	}
	if mk.Price.Equal(price) {
		log.Info("最后价格与合约缓存价格一致，不进行更新", zap.String("code", code), zap.String("价格", price.String()), zap.Any("last", mk))
		return
	}
	if mk.IsRestAPI {
		return true
	}
	if time.Since(mk.Ts).Seconds() > 10 {
		return true
	}

	return s
}

//获取某合约当前基准价历史
func GetContractSpotIndexBaseHistory(code string) (indexBaseHistory *proto.IndexBaseHistory) {
	spotIndex := cache.GetContractSpotIndexPrice(code)
	trade := cache.GetContractPriceIndex(code)
	if spotIndex == nil || trade == nil {
		return
	}
	depthBase := cache.GetContractDepthBasePrice(code)
	indexBaseHistory = &proto.IndexBaseHistory{
		Code:           code,
		TradePrice:     trade.TradePrice,
		SpotIndexPrice: spotIndex.Price,
		DepthBase:      depthBase, //暂时没用
		//BuyFirst:       trade.SellPrice,
		//SellFirst:      trade.BuyPrice,
		TS: time.Now(),
	}
	indexBaseHistory.CalBaseDiff()
	return
}

//将当前合约基准价历史放入单次缓存
func pushDepthContractSpotIndexBaseHistory(code string) {
	h := GetContractSpotIndexBaseHistory(code)
	if h != nil {
		cache.PushContractDepthSpotIndexBaseHistory(h)
	}
}

//生成合约指数基准差值历史
func GenerateContractSpotIndexBaseHistory() {
	spotIndex := cache.GetAllContractSpotIndexPrice()
	tradeMap := cache.GetAllPriceIndex()
	for code, price := range spotIndex {
		if price.Price.LessThanOrEqual(decimal.Zero) {
			continue
		}
		trade, ok := tradeMap[code]
		if !ok {
			continue
		}
		indexBaseHistory := proto.IndexBaseHistory{
			Code:           code,
			SpotIndexPrice: price.Price,
			TradePrice:     trade.TradePrice,
			//BuyFirst:       trade.SellPrice,
			//SellFirst:      trade.BuyPrice,
			TS: time.Now(),
		}
		indexBaseHistory.CalBaseDiff()
		cache.PushContractSpotIndexBaseHistory(&indexBaseHistory)
	}
}
