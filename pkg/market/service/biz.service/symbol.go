package biz_service

import (
	"github.com/modern-go/concurrent"
	"go.uber.org/zap"
	"spot/libs/conf"
	"spot/libs/log"
	"spot/pkg/market/config"
)

func InitSupportSymbol() {
	config.SupportSymbol = concurrent.NewMap()
	list := conf.Symbols()
	log.Info("当前系统配置支持交易对", zap.Any("list", list))
	if len(conf.Symbols()) == 0 {
		log.Error("当前没有配置支持的交易对")
		panic("请检查配置文件")
	}
	log.Info("当前服务支持的交易对", zap.Any("data", list))
	for _, s := range list {
		config.SupportSymbol.Store(s, s)
	}
}
