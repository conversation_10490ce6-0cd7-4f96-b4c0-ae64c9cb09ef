package biz_service

import (
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/commonsrv"
	"spot/libs/conf"
	"spot/libs/log"
	"spot/libs/nums"
	"spot/libs/proto"
	"time"
)

//处理第三方推送的成交记录
func DealThird24HVolume(trade proto.Market24Volume) {
	log.Infof("收到%v24h成交量,data:%+v", trade.Source, trade)
	if trade.Volume24Symbol.LessThanOrEqual(decimal.Zero) {
		return
	}
	err := cache.SetContractMarket24Volume(&trade)
	if err != nil {
		log.Errorf("SetContractMarket24Volume fail,%v", err)
		return
	}
}

//处理第三方推送的成交记录
func DealThirdTrade(trade proto.MarketTrade) {
	log.Infof("收到%v合约最新成交记录,data:%+v", trade.Source, trade)
	if trade.Price.LessThanOrEqual(decimal.Zero) {
		log.Errorf("收到%v合约最新成交记录无效,data:%+v", trade.Source, trade)
		return
	}
	var isUpdate bool
	//获取缓存中最新数据
	mk := cache.GeContractMarketPriceBySource(trade.Symbol, trade.Source)
	if mk == nil {
		isUpdate = true
	} else {
		if !mk.Price.Equal(trade.Price) {
			isUpdate = true
		} else {
			log.Error("合约来源价格与缓存中数据价格一直，不更新本次价格", zap.Any("来源价格", trade), zap.Any("缓存最新价格", mk))
		}
	}
	if isUpdate {
		err := cache.SetContractMarketPrice(&trade)
		if err != nil {
			log.Errorf("DealThirdTrade cache.SetContractMarketPrice fail,%v", err)
			return
		}
	}
}

func covertDepthForContract(holder proto.DepthHolder) (dh *proto.DepthHolder) {
	c, err := commonsrv.GetContractDetail(0, holder.Symbol, nil)
	if err != nil {
		log.Errorf("commonsrv.GetContractDetail fail,%v", err)
		return
	}

	//log.Infof("code；%v,精度：%v,priceStep；%v",c.ContractCode,c.Digit,c.PriceStep.String())

	digit := c.Digit
	var bidDepth, askDepth []proto.DepthData
	for _, v := range holder.Bids {
		price := nums.Floor(v.Price, c.PriceStep).Truncate(digit)
		if len(bidDepth) == 0 {
			bidDepth = append(bidDepth, proto.DepthData{Price: price, Volume: v.Volume})
			continue
		}
		lastIndex := len(bidDepth) - 1
		last := bidDepth[lastIndex]
		if price.Equal(last.Price) {
			last.Volume = last.Volume.Add(v.Volume)
			bidDepth[lastIndex] = last
		} else {
			bidDepth = append(bidDepth, proto.DepthData{Price: price, Volume: v.Volume})
		}
	}
	for _, v := range holder.Asks {
		price := nums.Ceiling(v.Price, c.PriceStep).Truncate(digit)
		if len(askDepth) == 0 {
			askDepth = append(askDepth, proto.DepthData{Price: price, Volume: v.Volume})
			continue
		}
		lastIndex := len(askDepth) - 1
		last := askDepth[lastIndex]
		if price.Equal(last.Price) {
			last.Volume = last.Volume.Add(v.Volume)
			askDepth[lastIndex] = last
		} else {
			askDepth = append(askDepth, proto.DepthData{Price: price, Volume: v.Volume})
		}
	}
	var minSize = len(bidDepth)
	if minSize > len(askDepth) {
		minSize = len(askDepth)
	}
	if minSize == 0 {
		return nil
	}

	return &proto.DepthHolder{
		Symbol: holder.Symbol,
		Source: holder.Source,
		Asks:   askDepth[:minSize],
		Bids:   bidDepth[:minSize],
		Ts:     time.Now(),
	}
}

func DealThirdIndexPrice(ip *proto.IndexPrice) {
	log.Infof("收到%v指数价格：%+v", ip.Source, *ip)
	err := cache.SetContractMarketIndexPrice(ip)
	if err != nil {
		log.Errorf("DealThirdIndexPrice cache.SetContractMarketIndexPrice fail,%v", err)
		return
	}
}

func DealThirdMarketPrice(mp *proto.MarkPrice) {
	log.Infof("收到%v标记价格：%+v", mp.Source, *mp)

}

func depthHeight() int {
	h := conf.DepthHeight()
	if h == 0 {
		h = 15
	}
	return h
}
