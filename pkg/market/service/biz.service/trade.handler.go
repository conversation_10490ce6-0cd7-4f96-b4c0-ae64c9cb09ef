package biz_service

import (
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"spot/libs/commonsrv"
	"spot/libs/conf"
	"spot/libs/log"
	"spot/libs/proto"
	"time"
)

var SymbolTickerHandler *TickHandler

// StartSymbolTickerHandler 启动ticker handler
func StartSymbolTickerHandler() {
	SymbolTickerHandler = NewTickerHandler()
	SymbolTickerHandler.Run()
}

func DealThirdSpotTrade(trade proto.MarketTrade) {
	commonsrv.DealThirdSpotTrade(trade)

	//获取配置，并推送到消息系统
	c, err := commonsrv.GetContractDetail(0, trade.Symbol, nil)
	if err != nil {
		log.Errorf("commonsrv.GetContractDetail fail,%v", err)
		return
	}
	log.Info("开始判断是否推送成交", zap.Any("data", trade), zap.Any("source", c.BucketingSource.GetHedgeSource()), zap.Any("marketSource", c.MarketSource), zap.Any("bucketiingSource", c.BucketingSource))
	if IsSourcePush(trade.Source, c) {
		//交由ticker处理器处理
		if SymbolTickerHandler != nil {
			SymbolTickerHandler.Push(trade)
		}
	}
}

type TickHandler struct {
	entry       chan proto.MarketTrade
	symbolEntry map[string]*SymbolEntry
}

func NewTickerHandler() *TickHandler {
	return &TickHandler{
		entry:       make(chan proto.MarketTrade, 10240),
		symbolEntry: make(map[string]*SymbolEntry),
	}
}

func (th *TickHandler) Push(ticker proto.MarketTrade) {
	th.entry <- ticker
}
func (th *TickHandler) Run() {
	go func() {
		for t := range th.entry {
			log.Info("ticker handler entry", zap.Int("队列长度", len(th.entry)), zap.Int("cap", cap(th.entry)))
			if item, ok := th.symbolEntry[t.Symbol]; ok {
				item.entry <- t
			} else {
				item = NewSymbolEntry(t.Symbol)
				item.Run()
				th.symbolEntry[t.Symbol] = item
				item.entry <- t
			}
		}

	}()
}

type SymbolEntry struct {
	low, high proto.MarketTrade
	entry     chan proto.MarketTrade
	symbol    string
}

func NewSymbolEntry(symbol string) *SymbolEntry {
	return &SymbolEntry{
		entry:  make(chan proto.MarketTrade, 128),
		symbol: symbol,
	}
}

func (se *SymbolEntry) Run() {
	du := conf.TickerDuration()
	if du == 0 {
		du = 100
	}

	tick := time.Tick(time.Duration(du) * time.Millisecond)
	go func() {
		for {
			select {
			case <-tick:
				se.notifyAndReset()
			case t := <-se.entry:
				log.Info("ticker handler", zap.String("symbol", se.symbol), zap.Int("队列长度", len(se.entry)), zap.Int("cap", cap(se.entry)))
				se.dealEntryTicker(t)
				if len(se.entry) == cap(se.entry) {
					for i := 0; i < len(se.entry); i++ {
						t = <-se.entry
						se.dealEntryTicker(t)
					}
				}
			}
		}

	}()

}

func (se *SymbolEntry) dealEntryTicker(t proto.MarketTrade) {
	if se.low.Price.LessThanOrEqual(decimal.Zero) {
		se.low.Price = t.Price
		se.low.Volume = t.Volume
		se.low.Side = t.Side
		se.low.Source = t.Source
	} else {
		if t.Price.LessThan(se.low.Price) {
			se.low.Price = t.Price
		}
		if t.Volume.GreaterThan(se.low.Volume) {
			se.low.Volume = t.Volume
		}
		se.low.Ts = t.Ts
	}
	if se.high.Price.LessThanOrEqual(decimal.Zero) {
		se.high.Price = t.Price
		se.high.Volume = t.Volume
		se.high.Side = t.Side
		se.high.Source = t.Source
	} else {
		if t.Price.GreaterThan(se.high.Price) {
			se.high.Price = t.Price
		}
		if t.Volume.GreaterThan(se.high.Volume) {
			se.high.Volume = t.Volume
		}
		se.high.Ts = t.Ts
	}
}

func (se *SymbolEntry) notifyAndReset() {
	log.Info("开始交易对ticker推送并重置", zap.String("symbol", se.symbol), zap.Any("low", se.low), zap.Any("high", se.high))
	//处理旧数据并推送
	if se.low.Price.GreaterThan(decimal.Zero) {
		//判断两者量是否相等
		if se.low.Price.Equal(se.high.Price) && se.low.Volume.Equal(se.high.Volume) {
			//处理low
			dealAndNotifyTicker(se.low)
		} else {
			dealAndNotifyTicker(se.low)
			dealAndNotifyTicker(se.high)
		}
	}
	//重置
	se.low = proto.MarketTrade{Symbol: se.symbol, Ts: time.Now()}
	se.high = proto.MarketTrade{Symbol: se.symbol, Ts: time.Now()}
}

//处理ticker并推送
func dealAndNotifyTicker(trade proto.MarketTrade) {
	log.Info("开始处理并推送ticker数据", zap.Any("data", trade))
	if trade.Price.LessThanOrEqual(decimal.Zero) || trade.Volume.LessThanOrEqual(decimal.Zero) {
		return
	}
	//推送到消息系统
	c, err := commonsrv.GetContractDetail(0, trade.Symbol, nil)
	if err != nil {
		log.Errorf("commonsrv.GetContractDetail fail,%v", err)
		return
	}
	index := commonsrv.GenerateIndexHistoryWithMarkTrade(&trade)
	if index == nil {
		return
	}
	notifyPriceIndex(index)
	notifyNewTrade(index, c)
}
