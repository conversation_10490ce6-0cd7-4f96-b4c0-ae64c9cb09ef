package biz_service

import (
	"github.com/shopspring/decimal"
	"spot/libs/commonsrv"
	"spot/libs/conf"
	"spot/libs/container/safemap"
	"spot/libs/define"
	"spot/libs/log"
	"strings"
)

var lastIndexMap = safemap.New()

var testIndexMap = safemap.New()

func calVolatility(this decimal.Decimal, last decimal.Decimal) decimal.Decimal {
	return last.Sub(this).Abs().Div(last)
}

func SendMarketDataEmail(code, content string, marketSubType string) {
	if conf.IsSimulate() {
		return
	}
	if strings.Contains(conf.LocalName(), "Sim") {
		content += "(模拟盘）"
	}
}

func ReportMarketStop(code int, errMsg, contractCode string) {
	if contractCode != "" {
		log.Warnf("收到合约行情中断，code:%v", contractCode)
	} else {
		contractCode = "ALL"
		log.Warnf("WS行情中断")
	}
	go commonsrv.SaveSysErrorRecord(nil, define.SysErrorTypeTickClose, errMsg, contractCode, 0)
}
