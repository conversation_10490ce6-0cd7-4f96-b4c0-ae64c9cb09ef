/*
@Time : 2/7/20 5:32 下午
<AUTHOR> mocha
@File : binance.init
*/
package coinbase_spot_impl

import (
	"bytes"
	"encoding/json"
	"spot/libs/coinbase"
	"spot/libs/commonsrv"
	"spot/libs/container/safemap"
	"spot/libs/convert"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
	"spot/libs/utils"
	"spot/pkg/market/service/biz.service"
	"strings"
	"time"
)

var convertMap = safemap.New()

func StartClient(config *define.AppKey) {
	t := time.Tick(1 * time.Hour)
	convertMap = safemap.New()
	client = coinbase.NewMarketWsClient(&coinbase.Config{WsPoint: config.GlobalWSURI}, wsMsgHandler)
	client.CloseHook(ReportWsClose)
	client.Start()
	client.Loop()
	go func() {
		for range t {
			client.Restart()
		}
	}()

}

func ReportWsClose(code int, msg string) {
	log.Errorf("rev ws close report code:%v,msg:%v", code, msg)
	//biz_service.ReportMarketStop(code, msg, "")
	//
	////发送api断开告警
	////SendWsApiStatusEmail(msg)
	////修改websocke状态
	//err := database.UpdateContractMarketConfig(0)
	//if err != nil {
	//	log.Errorf("database.UpdateContractMarketConfig fail,%v", err)
	//	return
	//}
}

func subscript(list []proto.Contract) {
	var topics []string
	for _, entry := range list {
		contractCode := utils.StrBuilderBySep("", entry.BaseCoinName, entry.CoinName)
		code := commonsrv.DealSourceNeedConvert(_name, contractCode)
		if strings.HasSuffix(code, "BTC") {
			code = strings.ReplaceAll(code, "BTC", "-BTC")
		}
		topics = append(topics, code)
	}
	if client == nil {
		Init()
	}
	client.Subscript(topics)
}

func unSubscribe() {
	//client.UnSubscript(topics)
}

func wsMsgHandler(data []byte) {
	log.Infof("coinbase:%v", convert.Bytes2Str(data))
	if bytes.Contains(data, []byte("heartbeat")) {
		return
	}
	dealTicker(data)
}

func dealTicker(data []byte) {
	var ticker coinbase.Ticker
	err := json.Unmarshal(data, &ticker)
	if err != nil {
		return
	}
	var code string
	if strings.HasSuffix(ticker.Code, "-USD") {
		code = strings.ReplaceAll(ticker.Code, "-USD", "USDT")
	}
	if strings.HasSuffix(ticker.Code, "-BTC") {
		code = strings.ReplaceAll(ticker.Code, "-BTC", "BTC")
	}
	code, Price := commonsrv.ConvertTradePriceAndCode(code, ticker.Price)
	side := define.OrderBuy
	if ticker.Side == "sell" {
		side = define.OrderSell
	}
	ticker.Code = code
	if code == "" {
		return
	}
	td := &proto.MarketTrade{
		Symbol:   code,
		DealTime: time.Now().Unix(),
		Price:    Price,
		Side:     side,
		Ts:       time.Now(),
		Source:   _name,
	}
	//log.Infof("ticker liupeng :%+v,td:%+v",ticker,*td)
	biz_service.DealThirdSpotTrade(*td)
}
