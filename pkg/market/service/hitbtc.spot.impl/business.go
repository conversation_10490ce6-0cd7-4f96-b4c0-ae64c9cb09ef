/*
@Time : 2/7/20 5:32 下午
<AUTHOR> mocha
@File : binance.init
*/
package hitbtc_spot_impl

import (
	"bytes"
	"encoding/json"
	"spot/libs/commonsrv"
	"spot/libs/convert"
	"spot/libs/define"
	"spot/libs/hitbtc"
	"spot/libs/log"
	"spot/libs/proto"
	"spot/pkg/market/service/biz.service"
	"strings"
	"time"
)

func StartClient(config *define.AppKey) {
	t := time.Tick(1 * time.Hour)

	client = hitbtc.NewMarketWsClient(&hitbtc.Config{WsPoint: config.GlobalWSURI}, wsMsgHandler)
	client.CloseHook(ReportWsClose)
	client.Start()
	client.Loop()
	go func() {
		for range t {
			client.Restart()
		}
	}()

}

func ReportWsClose(code int, msg string) {
	log.Errorf("rev ws close report code:%v,msg:%v", code, msg)
	//biz_service.ReportMarketStop(code, msg, "")
	//
	////发送api断开告警
	////SendWsApiStatusEmail(msg)
	////修改websocke状态
	//err := database.UpdateContractMarketConfig(0)
	//if err != nil {
	//	log.Errorf("database.UpdateContractMarketConfig fail,%v", err)
	//	return
	//}
}

func subscript(list []proto.Contract) {
	var topics []string
	for _, contract := range list {
		cCode := commonsrv.DealSourceNeedConvert(_name, contract.ContractCode)
		code := strings.ReplaceAll(cCode, "USDT", "USD")
		if strings.HasSuffix(cCode, "BTC") {
			code = strings.ReplaceAll(cCode, "BTC", "BTC")
		}

		topics = append(topics, code)
		//topics = append(topics, strings.ReplaceAll(contract.ContractCode, "USDT", "USD"))
	}
	if client == nil {
		Init()
	}
	client.Subscript([]string{"subscribeTrades"}, topics)
}

func unSubscribe() {
	//client.UnSubscript(topics)
}

func wsMsgHandler(data []byte) {
	log.Infof("hitbtc:%v", convert.Bytes2Str(data))
	if bytes.Contains(data, []byte("updateTrades")) {
		dealTicker(data)
	}
}

func dealTicker(data []byte) {
	log.Infof("huobi spot ticker：%v", string(data))
	tr := new(hitbtc.TickerRsp)
	e := json.Unmarshal(data, tr)
	if e != nil {
		return
	}
	code := strings.ReplaceAll(tr.Params.Symbol, "USD", "USDT")
	//if strings.HasSuffix(code, "BTC") {
	//	code = strings.ReplaceAll(tr.Params.Symbol, "BTC", "BTC")
	//}
	s := len(tr.Params.Data)
	if s == 0 {
		return
	}
	trade := tr.Params.Data[0]
	side := define.OrderBuy
	if trade.Side != "buy" {
		side = define.OrderSell
	}
	code, Price := commonsrv.ConvertTradePriceAndCode(code, trade.Price)
	td := &proto.MarketTrade{
		Symbol:   code,
		DealTime: time.Now().Unix(),
		Price:    Price,
		Volume:   trade.Quantity,
		Side:     side,
		Ts:       time.Now(),
		Source:   _name,
	}
	biz_service.DealThirdSpotTrade(*td)
}
