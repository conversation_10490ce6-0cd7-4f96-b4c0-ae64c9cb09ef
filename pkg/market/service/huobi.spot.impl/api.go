package huobi_spot_impl

import (
	"spot/libs/conf"
	"spot/libs/huobi_spot"
	"spot/libs/log"
	"spot/pkg/market/service/wshander"
)

var client *huobi_spot.MarketWsClient
var dataHandler *wshander.WsDataHandler

func Init() {
	appKey, ok := conf.AppKeyConfig()[_name]
	if !ok {
		log.Errorf("%v,没有查询到app配置信息", _name)
		return
	}
	//初始化ws处理协成
	dataHandler = wshander.NewWsDataHandler(dealTicker, dealDepth)
	log.Infof("获取到%v app config:%+v", _name, appKey)
	go StartClient(&appKey)
}
