// Package huobi_spot_impl /*
package huobi_spot_impl

import (
	"fmt"
	"go.uber.org/zap"
	"spot/libs/commonsrv"
	"spot/libs/container/safemap"
	"spot/libs/convert"
	"spot/libs/define"
	"spot/libs/huobi_spot"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/nums"
	"spot/libs/proto"
	"spot/pkg/market/service/biz.service"
	"spot/pkg/market/service/wshander"
	"strings"
	"time"
)

var convertMap = safemap.New()

const (
	depthTopic  = "market.%s.depth.step0"
	marketTopic = "market.%s.trade.detail"
)

func StartClient(config *define.AppKey) {
	t := time.Tick(1 * time.Hour)
	convertMap = safemap.New()
	client = huobi_spot.NewMarketWsClient(&huobi_spot.Config{WsPoint: config.GlobalWSURI}, wsMsgHandler)
	client.CloseHook(ReportWsClose)
	client.Start()
	client.Loop()
	go func() {
		for range t {
			client.Restart()
		}
	}()

}

func ReportWsClose(code int, msg string) {
	log.Errorf("rev ws close report code:%v,msg:%v", code, msg)
}

func subscript(list []proto.Contract) {
	var topics []string
	for _, contract := range list {
		topic := fmt.Sprintf(depthTopic, convertSystemSymbol(contract))
		mDetail := fmt.Sprintf(marketTopic, convertSystemSymbol(contract))
		convertMap.Set(topic, contract.ContractCode)
		convertMap.Set(mDetail, contract.ContractCode)
		topics = append(topics, topic, mDetail)
	}
	if client == nil {
		Init()
	}
	client.Subscript(topics)
}

func unSubscribe() {
	//client.UnSubscript(topics)
}

func wsMsgHandler(data []byte) {
	log.Debugf("huobiSpot websocket:%v", convert.Bytes2Str(data))
	r := new(huobi_spot.RspData)
	err := json.Unmarshal(data, r)
	if err != nil {
		log.Error("huobi receive data json unmarshal fail", zap.Error(err))
		return
	}
	code, ok := convertMap.Get(r.Channel)
	if !ok {
		log.Debugf("不支持该交易对，%v", r.Channel)
		return
	}
	symbol := code.(string)
	wsData := &wshander.WsData{
		Source: _name,
		Name:   symbol,
		Data:   data,
	}
	if strings.Contains(r.Channel, "depth.step") {
		wsData.DataType = wshander.WsDepth
		dataHandler.Notify(wsData)
		//dealDepth(data)
		return
	} else if strings.Contains(r.Channel, "trade.detail") {
		wsData.DataType = wshander.WsTrade
		dataHandler.Notify(wsData)
		//dealTicker(data)
	}

}

func dealTicker(data []byte) {
	log.Infof("huobiSpot websocket ticker：%v", string(data))
	p := new(huobi_spot.TradeTicker)
	err := json.Unmarshal(data, p)
	if err != nil {
		log.Errorf("MsgHandler unMarshal fail,%v", err)
		return
	}
	log.Infof("p：%+v", *p)
	channel := p.Channel
	code, ok := convertMap.Get(channel)
	if !ok {
		log.Errorf("不支持该交易对，%v", p.Channel)
		return
	}
	symbol := code.(string)
	if len(p.Ticker.List) == 0 {
		return
	}
	first := p.Ticker.List[0]
	side := define.OrderBuy
	if first.Direction != "buy" {
		side = define.OrderSell
	}
	symbol = strings.ToUpper(symbol)
	symbol, Price := commonsrv.ConvertTradePriceAndCode(symbol, nums.NewFromFloat(first.Price))
	td := &proto.MarketTrade{
		Symbol:   symbol,
		DealTime: time.Now().Unix(),
		Price:    Price,
		Volume:   nums.NewFromFloat(first.Amount),
		Side:     side,
		Ts:       time.Now(),
		Source:   _name,
	}
	biz_service.DealThirdSpotTrade(*td)
}

func dealDepth(data []byte) {
	p := new(huobi_spot.Ticker)
	err := json.Unmarshal(data, p)
	if err != nil {
		log.Errorf("MsgHandler unMarshal fail,%v", err)
		return
	}
	channel := p.Channel
	code, ok := convertMap.Get(channel)
	if !ok {
		log.Debugf("不支持该交易对，%v", p.Channel)
		return
	}
	symbol := code.(string)

	bids := p.Tick.Bids
	asks := p.Tick.Asks
	var bidDepth, askDepth []proto.DepthData
	for _, v := range bids {
		d := proto.DepthData{
			Price:  nums.NewFromFloat(v[0]),
			Volume: nums.NewFromFloat(v[1]),
		}
		bidDepth = append(bidDepth, d)
	}
	for _, v := range asks {
		d := proto.DepthData{
			Price:  nums.NewFromFloat(v[0]),
			Volume: nums.NewFromFloat(v[1]),
		}
		askDepth = append(askDepth, d)
	}
	holder := proto.DepthHolder{
		Symbol: symbol,
		Source: _name,
		Asks:   askDepth,
		Bids:   bidDepth,
		Ts:     time.Now(),
	}

	log.Debugf("huobiSpot websocket %+v", holder)
	biz_service.DealDepth(holder)
	return
}
