/*
@Time : 2/7/20 5:32 下午
<AUTHOR> mocha
@File : binance.init
*/
package kraken_spot_impl

import (
	"encoding/json"
	"spot/libs/commonsrv"
	"spot/libs/container/safemap"
	"spot/libs/define"
	"spot/libs/kraken"
	"spot/libs/log"
	"spot/libs/nums"
	"spot/libs/proto"
	"spot/pkg/market/service/biz.service"
	"strings"
	"time"
)

var convertMap = safemap.New()

func StartClient(config *define.AppKey) {
	t := time.Tick(1 * time.Hour)
	convertMap = safemap.New()
	client = kraken.NewMarketWsClient(&kraken.Config{WsPoint: config.GlobalWSURI}, wsMsgHandler)
	client.CloseHook(ReportWsClose)
	client.Start()
	client.Loop()
	go func() {
		for range t {
			client.Restart()
		}
	}()

}

func ReportWsClose(code int, msg string) {
	log.Errorf("rev ws close report code:%v,msg:%v", code, msg)
	//biz_service.ReportMarketStop(code, msg, "")
	//
	////发送api断开告警
	////SendWsApiStatusEmail(msg)
	////修改websocke状态
	//err := database.UpdateContractMarketConfig(0)
	//if err != nil {
	//	log.Errorf("database.UpdateContractMarketConfig fail,%v", err)
	//	return
	//}
}

func subscript(list []proto.Contract) {
	var topics []string
	for _, contract := range list {
		code := commonsrv.DealSourceNeedConvert(_name, contract.ContractCode)
		cCode := strings.ReplaceAll(contract.ContractCode, "USDT", "/USD")
		if strings.HasSuffix(code, "BTC") {
			cCode = strings.ReplaceAll(contract.ContractCode, "BTC", "/XBT")
		}
		topics = append(topics, cCode)
	}
	if client == nil {
		Init()
	}
	client.Subscript(topics)
	//client.Subscript([]string{"BTC/USD"})
}

func unSubscribe() {
	//client.UnSubscript(topics)
}

func wsMsgHandler(data []byte) {
	dealTicker(data)
}

//TestBClient: ws.client_test.go:97: topic:trade,code；BTCUSDT,price:33531.80000,size；0.00403930,date；1610518163.097419,side；b
func dealTicker(data []byte) {
	log.Infof("kraken spot ticker：%v", string(data))
	var l []interface{}
	err := json.Unmarshal(data, &l)
	if err != nil {
		return
	}
	//1-数据 2-topic 3-合约名
	//1-数组 0-价格 1-量 2-时间 3-方向s-b
	topic := l[2].(string)
	if topic != "trade" {
		return
	}
	code := l[3].(string)
	code = strings.Replace(code, "/BTC", "XBT", -1)
	code = strings.ReplaceAll(code, "XBT", "BTC")
	code = strings.Replace(code, "/USD", "USDT", -1)
	d := l[1].([]interface{})
	f := d[0].([]interface{})
	price, size, _, s := f[0].(string), f[1].(string), f[2].(string), f[3].(string)
	//t.Logf("topic:%v,code；%v,price:%v,size；%v,date；%v,side；%v", topic, code, price, size, date, side)
	side := define.OrderBuy
	if s != "b" {
		side = define.OrderSell
	}
	code, Price := commonsrv.ConvertTradePriceAndCode(code, nums.NewFromString(price))
	td := &proto.MarketTrade{
		Symbol:   code,
		DealTime: time.Now().Unix(),
		Price:    Price,
		Volume:   nums.NewFromString(size),
		Side:     side,
		Ts:       time.Now(),
		Source:   _name,
	}
	biz_service.DealThirdSpotTrade(*td)
}
