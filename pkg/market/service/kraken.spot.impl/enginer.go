package kraken_spot_impl

import (
	"spot/libs/define"
	"spot/libs/proto"
	"spot/pkg/market/model"
	"time"
)

const _name = define.MarketNameSpotKraken

type Engineer struct {
	LastRev time.Time
	model.EngineDetail
}

func (e *Engineer) IsForever() bool {
	return true
}

func (e *Engineer) Name() string {
	return _name
}

func (e *Engineer) Init() {
	Init()
}

func (e *Engineer) Sub(s []proto.Contract) {
	subscript(s)
}

func (e *Engineer) UnSub(s []proto.Contract) {
	unSubscribe()
}

func NewEngineer() *Engineer {
	return &Engineer{
		EngineDetail: model.EngineDetail{Name: _name},
	}
}
