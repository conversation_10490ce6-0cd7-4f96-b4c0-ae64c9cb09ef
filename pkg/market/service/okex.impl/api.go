package okexc_impl

import (
	"spot/libs/conf"
	"spot/libs/container/safemap"
	"spot/libs/log"
	"spot/libs/okex"
	"spot/pkg/market/service/wshander"
)

var okExClient *okex.MarketWsClient
var dataHandler *wshander.WsDataHandler

var priceRateMap = safemap.New() //合约资金费率map

func Init() {
	//初始化ws处理协成
	dataHandler = wshander.NewWsDataHandler(dealTicker, dealDepth)

	appKey, ok := conf.AppKeyConfig()[_name]
	if !ok {
		log.Errorf("%v,没有查询到app配置信息")
		return
	}
	log.Infof("获取到%v app config:%+v", _name, appKey)
	StartOkExClient(&appKey)
}
