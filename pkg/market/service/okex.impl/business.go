package okexc_impl

import (
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"runtime"
	"runtime/debug"
	"spot/libs/commonsrv"
	"spot/pkg/market/service/biz.service"
	"spot/pkg/market/service/wshander"
	"strings"
	"time"

	"spot/libs/conf"
	"spot/libs/container/safemap"
	"spot/libs/convert"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/nums"
	"spot/libs/okex"
	"spot/libs/proto"
)

var okConvertCode = safemap.New() //okex instrumentId 与平台contract_code对应map

func StartOkExClient(config *define.AppKey) {
	okExClient = okex.NewMarketWsClient(&okex.Config{WsPoint: config.GlobalWSURI}, okexMsgHandler)
	okExClient.CloseHook(ReportWsClose)
	okExClient.Start()
	okExClient.Loop()

	go func() {
		t := time.Tick(5 * time.Minute)
		for range t {
			okExClient.ReSubDepth()
		}
	}()
}

func ReportWsClose(code int, msg string) {
	log.Errorf("rev ws close report code:%v,msg:%v", code, msg)
	biz_service.ReportMarketStop(code, msg, "")
}

func subForOkEx(symbols []proto.Contract) {
	log.Infof("开始订阅okex交易对,%v", symbols)
	var topics []okex.Arg
	for _, v := range symbols {
		codeSpot := fmt.Sprintf(okex.SpotContract, v.BaseCoinName, v.CoinName)
		okConvertCode.Set(codeSpot, v.ContractCode)
		tickerTopic := okex.Arg{
			Channel: okex.ChannelTicker,
			InstId:  codeSpot,
		}

		depthTopic := okex.Arg{
			Channel: okex.ChannelDepth,
			InstId:  codeSpot,
		}

		topics = append(topics, tickerTopic, depthTopic)
	}
	if len(topics) == 0 {
		return
	}
	log.Infof("OkEx设置订阅主题:%+v", topics)
	if okExClient == nil {
		Init()
	}
	okExClient.Subscript(topics)
}

func okexMsgHandler(data []byte) {
	log.Info("okex", zap.Any("data", string(data)))
	p := new(okex.OkexBasicRsp)
	err := json.Unmarshal(data, p)
	if err != nil {
		log.Errorf("okex MsgHandler unMarshal fail,%v", err)
		return
	}
	wsData := &wshander.WsData{
		Source: _name,
		Name:   p.Arg.InstId,
		Data:   data,
	}

	switch p.Arg.Channel {
	case okex.ChannelDepth:
		wsData.DataType = wshander.WsDepth
		dataHandler.Notify(wsData)
		//dealDepth(data)
	case okex.ChannelTicker:
		wsData.DataType = wshander.WsTrade
		dataHandler.Notify(wsData)
		//dealTicker(data)
	default:
		log.Infof(convert.Bytes2Str(data))

	}
}

func dealTicker(tickerData []byte) {
	rsp := new(okex.OkexTickerRsp)
	err := json.Unmarshal(tickerData, rsp)
	if err != nil {
		log.Infof("dealTicker json unmarshal:%+v，err:%v", string(tickerData), err)
	}
	tickers := rsp.Data

	if len(tickers) == 0 {
		return
	}
	ticker := tickers[0]

	id := ticker.InstId
	code, ok := GetContractCode(id)
	if !ok {
		log.Errorf("不支持该交易对，%v", id)
		return
	}
	trade := proto.MarketTrade{
		Symbol:   code,
		DealTime: time.Now().Unix(),
		Price:    nums.NewFromString(ticker.Last),
		Volume:   nums.NewFromString(ticker.LastSz),
		Side:     define.OrderBuy,
		Ts:       time.Now(),
		Source:   _name,
	}
	mp := proto.Market24Volume{
		Symbol:         code,
		Volume24:       trade.Trade24,
		Volume24Symbol: trade.Trade24M,
		Source:         _name,
		Ts:             trade.Ts,
	}

	if ticker.InstType == "SWAP" {
		biz_service.DealThirdTrade(trade)
		biz_service.DealThird24HVolume(mp)
	} else {
		code, price := commonsrv.ConvertTradePriceAndCode(code, nums.NewFromString(ticker.Last))
		trade.Symbol = code
		trade.Price = price
		biz_service.DealThirdSpotTrade(trade)
	}
	return
}

func dealDepth(data []byte) {
	defer func() {
		if e := recover(); e != nil {
			_, file, line, _ := runtime.Caller(3)
			log.Errorf("market revover,file:%v,line:%v,%v", file, line, e)
			log.Errorf("market stack err:%v", string(debug.Stack()))
		}
	}()

	okxRsp := new(okex.OkexDepthRsp)
	err := json.Unmarshal(data, &okxRsp)
	if err != nil {
		log.Errorf("dealDepth Unmarshal fail,%v", err)
		return
	}
	instId := okxRsp.Arg.InstId
	ticker := okxRsp.Data
	if len(ticker) == 0 {
		log.Infof("u depth:%+v 长度为0", ticker)
		return
	}
	depth := ticker[0]
	log.Debugf("update depth:%+v", depth)
	code, ok := GetContractCode(instId)
	if !ok {
		log.Infof("不支持交易对：%v", instId)
		return
	}

	depthBoard := GetSpotDepthBoard(code)
	switch okxRsp.Action {
	case okex.ActionPartial:
		log.Debugf("okex处理深度Full：%+v", okxRsp)
		depthBoard.Notify(&depth, true)
	case okex.ActionUpdate:
		log.Debugf("okex处理深度Part：%+v", okxRsp)
		depthBoard.Notify(&depth, false)
	}
}

//
//var depthMap, lockMap *safemap.Map
//
//func init() {
//	depthMap, lockMap = safemap.New(), safemap.New()
//}
//
//type okDepth struct {
//	lock  sync.RWMutex
//	code  string
//	buys  *treemap.Map
//	sells *treemap.Map
//}
//

//
////设置ok depth
//func setOkDepth(code string, depth *okex.OkDepth) {
//	d := &okDepth{code: code, buys: treemap.NewWith(container.FloatDescCompartor), sells: treemap.NewWith(container.FloatAscCompartor)}
//	for _, v := range depth.Asks {
//		price, amount := convert.String2Float(v[0]), convert.String2Int(v[1])
//		d.sells.Put(price, amount)
//	}
//	for _, v := range depth.Bids {
//		price, amount := convert.String2Float(v[0]), convert.String2Int(v[1])
//		d.buys.Put(price, amount)
//	}
//	depthMap.Set(code, d)
//	log.Debugf("orginal full，code:%v,buy；%v", d.code, d.buys.String())
//	log.Debugf("orignal full，code:%v，sell；%v", d.code, d.sells.String())
//}
//
////修改深度
//func updateOkDepth(code string, depth *okex.OkDepth) (buys, sells treemap.Map) {
//	log.Debugf("开始修改深度：code:%v,%+v", code, *depth)
//	//获取原始深度
//	dOrgnal, o := depthMap.Get(code)
//	if !o {
//		log.Errorf("获取指定深度盘失败，%v", code)
//		//setOkDepth(code, depth)
//		return
//	}
//	d, ok := dOrgnal.(*okDepth)
//	if !ok {
//		log.Errorf("updateOkDepth dOrgnal not *okDepth")
//		return
//	}
//	//d.lock.Lock()
//	//defer d.lock.Unlock()
//	//log.Infof("old；buy:%+v", d.buys.String())
//	//log.Infof("old；sell:%+v", d.sells.String())
//	var delAsks, delBids []float64 //待删除切片
//	for _, v := range depth.Asks { //卖盘
//		price, amount := convert.String2Float(v[0]), convert.String2Int(v[1])
//		_, e := d.sells.Get(price)
//		if !e {
//			d.sells.Put(price, amount)
//			continue
//		}
//		if amount == 0 {
//			delAsks = append(delAsks, price)
//			//d.sells.RemovePos(price)
//		} else {
//			d.sells.Put(price, amount)
//		}
//
//	}
//	for _, v := range depth.Bids { //买盘
//		price, amount := convert.String2Float(v[0]), convert.String2Int(v[1])
//		_, e := d.buys.Get(price)
//		if !e {
//			d.buys.Put(price, amount)
//			continue
//		}
//		if amount == 0 {
//			delBids = append(delBids, price)
//			//d.buys.RemovePos(price)
//		} else {
//			d.buys.Put(price, amount)
//		}
//	}
//	for _, price := range delAsks {
//		if d.sells == nil {
//			continue
//		}
//		_, ok := d.sells.Get(price)
//		if ok {
//			d.sells.Remove(price)
//		} else {
//			log.Errorf("移除sell价格：%v，节点已被移除", price)
//			log.Infof("移除sell价格：%v，节点已被移除", price)
//		}
//		//log.Debugf("remove price node ask:%v", price)
//	}
//	for _, price := range delBids {
//		if d.buys == nil {
//			continue
//		}
//		_, ok := d.buys.Get(price)
//		if ok {
//			d.buys.Remove(price)
//		} else {
//			log.Errorf("移除buy价格：%v，节点已被移除", price)
//			log.Infof("移除buy价格：%v，节点已被移除", price)
//		}
//		log.Debugf("remove price node bid:%v", price)
//	}
//	log.Debugf("code:%v,update buy after:%v", d.code, d.buys.String())
//	log.Debugf("code；%v,update sell after:%v", d.code, d.sells.String())
//	buys = *d.buys
//	sells = *d.sells
//	return
//}
//
func GetContractCode(instrument string) (code string, ok bool) {
	c, ok := okConvertCode.Get(instrument)
	if !ok {
		return
	}
	code, o := c.(string)
	if o {
		return
	}
	return
}

//
//var CRecTimeMap = safemap.New()
//
//type ContractStatus struct {
//	Status  bool
//	RevTime time.Time
//}
//
//func setCodeRecvData(instrumentId string) {
//	c := convertSymbol2ThisSystem(instrumentId)
//	last, ok := CRecTimeMap.Get(c)
//	if !ok {
//		return //不存在则返回false
//	}
//	cs, ok := last.(*ContractStatus)
//	if !ok {
//		CRecTimeMap.Set(c, &ContractStatus{Status: true, RevTime: time.Now()})
//	} else {
//		cs.RevTime = time.Now()
//	}
//}
//
//func IsCodeRevOverTime(code string, gapMinutes int) (s bool) {
//	newTime := time.Now()
//	last, ok := CRecTimeMap.Get(code)
//	if !ok {
//		return //不存在则返回false
//	}
//	cs, ok := last.(*ContractStatus)
//	if !ok {
//		log.Errorf("IsCodeRevOverTime lastTime is not time.Time")
//		return
//	}
//	if cs == nil {
//		log.Errorf("获取合约状态失败，为nil")
//		return
//	}
//
//	gap := newTime.Sub(cs.RevTime).Minutes()
//	log.Debugf("code:%v,lastTime:%v,newTime；%v,gap；%v", code, cs.RevTime, newTime, int(gap))
//	gapInt := int(gap)
//	if gapInt >= gapMinutes {
//		log.Debugf("code:%v,超过1分钟没有收到数据，lastTime:%v,newTime；%v,gap；%v", code, cs.RevTime, newTime, int(gap))
//		s = true
//		if cache.SetRedisLockTime(10, "MarketCacheEmail", code) { //10分钟内不重复报警
//			msg.SendCautionEmail("market合约异常报警", fmt.Sprintf("合约:%v，%v分钟没有收到okex数据,请注意查看", code, gapInt))
//		}
//		if cs.Status {
//			//设置为断开一次
//			biz_service.ReportMarketStop(0, define.GetSysErrorTypeName(define.SysErrorTypeTickClose), code)
//		}
//		cs.Status = false
//	}
//
//	return s
//}

//处理api状态预警
func SendWsApiStatusEmail(msg string) {
	content := fmt.Sprintf("WS盘口断开;%v;", msg)
	if strings.Contains(conf.LocalName(), "Sim") {
		content += "(模拟盘）"
	}
	//commonsrv.SendWarningEmailWithMarket(define.WaringTypeAPIStatus, content, "", "", define.NoUse)
}

func Test() {
	//if !conf.IsProduct() {
	//	log.Infof("测试邮件发送")
	//	dealMarketWarn(0, "BTCUSDT")
	//	log.Infof("测试邮件发送")
	//	dealMarketWarn(5, "BTCUSDT")
	//}
	SendWsApiStatusEmail("ws close")
	//TestCheckPriceIndex(&proto.IndexHistory{BuyPrice: 9220.2, SellPrice: 9181.9, ContractCode: "BTCUSDT"})
	//TestCheckPriceIndex(&proto.IndexHistory{BuyPrice: 9239.2, SellPrice: 9211.9, ContractCode: "BTCUSDT"})
}
