package okexc_impl

import (
	"spot/libs/define"
	"spot/libs/proto"
	"spot/pkg/market/model"
	"time"
)

const _name = define.MarketNameOkex

var symbolMap = make(map[string]string)

type Engineer struct {
	LastRev time.Time
	model.EngineDetail
}

func (e *Engineer) IsForever() bool {
	return true
}

func (e *Engineer) Name() string {
	return _name
}

func (e *Engineer) Init() {
	Init()
}

func (e *Engineer) Sub(s []proto.Contract) {
	subForOkEx(s)
}

func (e *Engineer) UnSub(s []proto.Contract) {
	var symbols []string
	for _, entry := range s {
		name := convertSystemSymbol(entry)
		symbols = append(symbols, name)
	}
	UnSubscribe(symbols)
}

func NewEngineer() *Engineer {
	return &Engineer{
		EngineDetail: model.EngineDetail{Name: _name},
	}
}

func UnSubscribe(list []string) {
	//topics := createSubTopics(list)
	okExClient.UnSubscript()
}
