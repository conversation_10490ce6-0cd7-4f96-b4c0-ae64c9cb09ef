package okexc_impl

import (
	"github.com/emirpasic/gods/maps/treemap"
	"github.com/modern-go/concurrent"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"spot/libs/commonsrv"
	"spot/libs/container"
	"spot/libs/log"
	"spot/libs/nums"
	"spot/libs/okex"
	"spot/libs/proto"
	bizservice "spot/pkg/market/service/biz.service"
	"sync"
	"time"
)

var SpotDepth = concurrent.NewMap()

func GetSpotDepthBoard(code string) *DepthBoard {
	var db *DepthBoard
	v, ok := SpotDepth.Load(code)
	if !ok {
		db = NewDepthBoard(code, true)
		SpotDepth.Store(code, db)
		return db
	}
	db, ok = v.(*DepthBoard)
	if ok {
		return db
	}
	return db
}

type DepthBoard struct {
	Code string

	buys           *treemap.Map
	sells          *treemap.Map
	event          chan DepthEvent
	push           chan proto.DepthHolder
	rw             sync.RWMutex
	isSpot         bool
	LastFullUpdate int64
}

type DepthEvent struct {
	IsInit bool
	depth  okex.OkDepth
}

func NewDepthBoard(code string, isSpot bool) *DepthBoard {
	d := &DepthBoard{
		Code:   code,
		buys:   treemap.NewWith(container.DecimalDescCompartor),
		sells:  treemap.NewWith(container.DecimalAscCompartor),
		event:  make(chan DepthEvent, 128),
		push:   make(chan proto.DepthHolder, 128),
		isSpot: isSpot,
	}
	d.run()
	return d
}

func (d *DepthBoard) Init(depth okex.OkDepth) {
	d.clear()
	for _, v := range depth.Asks {
		price, amount := nums.NewFromString(v[0]), nums.NewFromString(v[1])
		d.sells.Put(price, amount)
	}
	for _, v := range depth.Bids {
		price, amount := nums.NewFromString(v[0]), nums.NewFromString(v[1])
		d.buys.Put(price, amount)
	}
	log.Info("获取合约全量", zap.Any("buy", d.buys), zap.Any("sell", d.sells))
}

func (d *DepthBoard) Update(depth okex.OkDepth) {

	for _, v := range depth.Asks { //卖盘
		price, amount := nums.NewFromString(v[0]), nums.NewFromString(v[1])
		_, e := d.sells.Get(price)
		if !e && amount.GreaterThan(decimal.Zero) {
			d.sells.Put(price, amount)
			continue
		}
		if amount.Equal(decimal.Zero) {
			d.sells.Remove(price)
		} else {
			d.sells.Put(price, amount)
		}

	}
	for _, v := range depth.Bids { //买盘
		price, amount := nums.NewFromString(v[0]), nums.NewFromString(v[1])
		_, e := d.buys.Get(price)
		if !e && amount.GreaterThan(decimal.Zero) {
			d.buys.Put(price, amount)
			continue
		}
		if amount.Equal(decimal.Zero) {
			d.buys.Remove(price)
		} else {
			d.buys.Put(price, amount)
		}
	}

}

func (d *DepthBoard) clear() {
	d.buys.Clear()
	d.sells.Clear()
}

func (d *DepthBoard) run() {
	go func() {
		for ev := range d.event {
			log.Info("okx depthBoard 当前队列长度", zap.String("code", d.Code), zap.Int("size", len(d.event)))
			if ev.IsInit {
				d.Init(ev.depth)
			} else {
				//if ev.depth.LastUpdate > 0 && ev.depth.LastUpdate < d.LastFullUpdate {
				//	continue
				//}
				d.Update(ev.depth)
			}
			d.rw.Lock()
			d.rw.Unlock()
			d.calBuySell(d.Code, *d.buys, *d.sells)
		}
	}()

	go func() {
		for item := range d.push {
			log.Info("okx 深度处理 当前队列长度", zap.String("code", d.Code), zap.Int("size", len(d.push)))
			if len(d.push) == cap(d.push) {
				for i := 0; i < cap(d.push); i++ {
					item = <-d.push
				}
			}
			bizservice.DealDepth(item)
		}
	}()
}

func (d *DepthBoard) calBuySell(code string, buyData, sellData treemap.Map) {
	//bF, _ := buyData.Max()
	sF, _ := sellData.Min()
	//buyFist := bF.(decimal.Decimal)
	sellFist := sF.(decimal.Decimal)
	var buy, sell []proto.DepthData
	buyIndex := 0
	buys := buyData.Select(func(key interface{}, value interface{}) bool {
		p := key.(decimal.Decimal)
		s := false
		if buyIndex < depthHeight() && p.LessThan(sellFist) {
			s = true
			buyIndex++
		}
		return s
	})

	sellIndex := 0
	sells := sellData.Select(func(key interface{}, value interface{}) bool {
		s := false
		if sellIndex < depthHeight() {
			s = true
			sellIndex++
		}
		return s
	})

	var wg sync.WaitGroup
	wg.Add(2)
	go func() {
		defer wg.Done()
		buys.Each(func(key interface{}, value interface{}) {
			price := key.(decimal.Decimal)
			amount := value.(decimal.Decimal)
			d := proto.DepthData{
				Price:  price,
				Volume: amount,
			}
			buy = append(buy, d)
		})
	}()

	go func() {
		defer wg.Done()
		sells.Each(func(key interface{}, value interface{}) {
			price := key.(decimal.Decimal)
			amount := value.(decimal.Decimal)
			sell = append(sell, proto.DepthData{
				Price:  price,
				Volume: amount,
			})
		})
	}()
	wg.Wait()
	holder := proto.DepthHolder{
		Symbol: code,
		Asks:   sell,
		Bids:   buy,
		Source: _name,
		Ts:     time.Now(),
	}
	d.push <- holder
	log.Info("spot depth final", zap.Any("depth", holder))
}

func (d *DepthBoard) Notify(dh *okex.OkDepth, IsInit bool) {
	d.event <- DepthEvent{IsInit: IsInit, depth: *dh}
}

func depthHeight() int {
	return commonsrv.GetFetchDepthCommonHeight()
}
