// Package service /*
package service

import (
	"go.uber.org/zap"
	"spot/libs/conf"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
	tsconfig "spot/pkg/market/config"
	biancespot "spot/pkg/market/service/biance.spot.impl"
	huobi_spot_impl "spot/pkg/market/service/huobi.spot.impl"
	okexc_impl "spot/pkg/market/service/okex.impl"
	"strings"
	"time"
)

func Init() {
	//初始化行情引擎
	apps := conf.AppKeyConfig()
	log.Infof("tsconfig；%+v", apps)
	tsconfig.MarketMaps = make(map[string]tsconfig.MarketEngine)
	for s := range apps {
		switch s {
		case define.MarketNameSpotHuobi:
			tsconfig.MarketMaps[s] = huobi_spot_impl.NewEngineer() //火币现货
		case define.MarketNameSpotBinance:
			tsconfig.MarketMaps[s] = biancespot.NewEngineer() //binance现货
		case define.MarketNameOkex:
			tsconfig.MarketMaps[s] = okexc_impl.NewEngineer() //okex
		}
	}
}

// StartBizEngine 启动多行情处理引擎
func StartBizEngine() {

	//启动行情业务引擎
	StartMarketEngine()

	//启动交易对初始化业务逻辑
	StartSymbolsBusiness()

	//处理遍历任务
	//StartCronTask()

}

func StartCronTask() {
	//每分钟从数据库中获取最新的数据并订阅
	sub := time.Tick(10 * time.Second)
	//sub := time.Tick(2 * time.Minute)
	//cal := time.Tick(TradePriceDuration * time.Millisecond)
	go func() {
		for {
			select {
			case <-sub:
				go StartSymbolsBusiness()
			}
		}
	}()
}

func InitSupportContract() (dbSymbols []proto.Contract, old map[string]proto.Contract) {
	old = make(map[string]proto.Contract)
	tsconfig.ContractMap.Each(func(i interface{}, i2 interface{}) {
		if c, ok := i2.(proto.Contract); ok {
			old[c.ContractCode] = c
		}
	})
	dbSymbols, err := database.GetSupportContractsTx(nil)
	if err != nil {
		log.Errorf("获取在线的交易对失败,%v", err)
		return
	}
	log.Info("数据库查询到交易对", zap.Any("data", dbSymbols))
	if len(dbSymbols) == 0 {
		log.Infof("查询到支持合约数量:%v", len(dbSymbols))
		return
	}

	for i, v := range dbSymbols {
		v.ContractCode = strings.ToUpper(v.ContractCode)
		dbSymbols[i] = v
	}
	for _, v := range dbSymbols {
		tsconfig.ContractMap.Set(v.ContractCode, v)
	}
	return
}

func StartSymbolsBusiness() {
	//获取支持的交易对列表
	current, _ := InitSupportContract()
	log.Info("数据库支持交易对", zap.Any("data", len(current)), zap.Any("程序配置", tsconfig.GetSupportSymbolArray()))
	var list []proto.Contract
	for _, contract := range current {
		//根据系统支持的交易对，生成服务支持的合约信息
		_, ok := tsconfig.SupportSymbol.Load(contract.ContractCode)
		if ok {
			list = append(list, contract)
		}
	}
	log.Info("本次配置支持的合约", zap.Any("cnfMap", tsconfig.GetSupportSymbolArray()), zap.Any("list", list))
	//订阅行情
	ReSubSymbol(list)
}

func ReSubSymbol(symbolMap []proto.Contract) {
	log.Info("系统检测要订阅的交易对信息：%+v", zap.Any("data", symbolMap))
	for _, engine := range tsconfig.MarketMaps {
		if len(symbolMap) > 0 {
			go engine.Sub(symbolMap)
		}
	}
}
