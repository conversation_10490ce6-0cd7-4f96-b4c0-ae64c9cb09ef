package wshander

import (
	"go.uber.org/zap"
	"spot/libs/log"
	"sync"
)

type WsDataHandler struct {
	lock       sync.Mutex
	data       map[string]*SymbolHandler
	tickerFunc func([]byte)
	depthFunc  func([]byte)
}

func NewWsDataHandler(tickFunc, depthFunc func([]byte)) *WsDataHandler {
	return &WsDataHandler{
		data:       make(map[string]*SymbolHandler),
		tickerFunc: tickFunc,
		depthFunc:  depthFunc,
	}
}

func (h *WsDataHandler) Notify(data *WsData) {
	h.lock.Lock()
	defer h.lock.Unlock()
	handler, ok := h.data[data.Name]
	if !ok {
		handler = NewSymbolHandler(data.Source, data.Name, h)
		h.data[data.Name] = handler
	}
	handler.Notify(data)
}

type DataType int

const (
	WsTrade DataType = 1
	WsDepth DataType = 2
)

type WsData struct {
	DataType
	Name   string
	Source string
	Data   []byte
}

type SymbolHandler struct {
	Source    string
	Name      string
	data      map[DataType]chan WsData
	WsHandler *WsDataHandler
}

func (h *SymbolHandler) Notify(data *WsData) {
	chanData, ok := h.data[data.DataType]
	if ok {
		chanData <- *data
	}
}

func (h *SymbolHandler) Run() {
	go func() {
		r := h.data[WsTrade]
		for item := range r {
			if h.WsHandler != nil {
				if len(r) == cap(r) {
					for i := 0; i < cap(r); i++ {
						item = <-r
					}
				}
				h.WsHandler.tickerFunc(item.Data)
				log.Info("成交处理", zap.Any("source", h.Source), zap.Any("code", item.Name), zap.Any("当前队列长度", len(r)))
			}
		}
	}()
	go func() {
		r := h.data[WsDepth]
		for data := range r {
			if h.WsHandler != nil {
				h.WsHandler.depthFunc(data.Data)
				log.Info("深度处理", zap.Any("source", h.Source), zap.Any("code", data.Name), zap.Any("当前队列长度", len(r)))
			}
		}
	}()

}

func NewSymbolHandler(source, name string, wh *WsDataHandler) *SymbolHandler {
	s := &SymbolHandler{
		Source:    source,
		Name:      name,
		data:      make(map[DataType]chan WsData),
		WsHandler: wh,
	}
	s.data[WsTrade] = make(chan WsData, 128)
	s.data[WsDepth] = make(chan WsData, 1024)
	s.Run()
	return s
}
