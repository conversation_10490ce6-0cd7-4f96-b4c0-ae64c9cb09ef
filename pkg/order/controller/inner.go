/*
	内部调用rpc接口
*/

package controller

import (
	"context"
	"errors"
	"go.uber.org/zap"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
	"spot/pkg/order/engine"
)

// Entrust  去除前置校验的rpc开仓入口，供后台调用
func (r *RPC) EntrustOpen(ctx context.Context, arg *proto.ThirdOrderPlaceArg, reply *define.Reply) error {
	var err error
	var id int64
	if arg.MarketSource == define.MarketSourceSourceDefault {
		err = errors.New("无权访问")
		return err
	} else {
		//向第三方下单
		id, err = engine.Entrust(arg)
		if err != nil {
			log.Error("EntrustOpen 向第三方下单出错", zap.Error(err), zap.Any("下单源", arg.MarketSource), zap.Any("order", arg))
		} else {
			arg.ThirdOrderId = id
		}
	}
	r.epilog(ctx, err, arg.ReqLang, arg, reply)
	reply.Data = id
	return nil
}

// EntrustCancel 后台委托撤销
func (r *RPC) EntrustCancel(ctx context.Context, arg *proto.ThirdOrderCancelArg, reply *define.Reply) error {
	var err error
	if arg.MarketSource == define.MarketSourceSourceDefault {
		err = errors.New("无权访问")
		return err
	}
	err = engine.Cancel(arg)
	r.epilog(ctx, err, arg.ReqLang, arg, reply)
	return nil
}
