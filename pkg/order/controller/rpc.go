package controller

import (
	"context"
	"runtime"
	"spot/pkg/order/config"
	"spot/pkg/order/service"
	"time"

	"go.uber.org/zap"
	"spot/libs/conf"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/xplugins"
	"spot/libs/xrpc"
)

type RPC struct {
	s    *xrpc.Server
	IsOk bool
}

var rpcServer *RPC

func InitRPC() (err error) {
	//listCanSet := config.SupportSymbols
	//if len(listCanSet) == 0 {
	//	panic("获取本服务可设置交易对数量为0，退出")
	//}
	//extra := strings.Join(listCanSet, ",")
	log.Info("开始初始化rpc服务", zap.String("address", conf.RPCAddr()), zap.String("服务名", define.ServerNameOrder))
	rpcServer = new(RPC)
	option := &xrpc.ServerOption{
		ServiceName:    define.ServerNameOrder,
		NodeName:       conf.LocalName(),
		Rev:            rpcServer,
		Address:        conf.RPCAddr(),
		BasePath:       define.BasePath,
		Discovery:      conf.Discovery(),
		UpdateInterval: 10 * time.Second,
		//Meta:           "symbols=" + extra,
		//Extra:          listCanSet,
		AuthFunc: nil,
	}
	s, err := xrpc.NewXServer(option)
	if err != nil {
		log.Errorf("init Rpc server fail,%v", err)
		//service.DeleteServerSymbol(listCanSet)
		return
	}
	rpcServer.s = s
	rpcServer.IsOk = true
	rpcServer.s = s
	s.RegisterOnShutdown(func() {})
	//s.RegisterOnShutdown(func() {
	//	l := s.GetExtra()
	//	if l != nil {
	//		s := l.([]string)
	//		service.DeleteServerSymbol(s)
	//	}
	//})
	go s.Start()
	//for _, name := range listCanSet {
	//	config.SupportSymbolMap[name] = struct{}{}
	//}
	log.Info("启动rpc server")
	return
}

func ShutdownServers() {
	rpcServer.IsOk = false
	service.DeleteServerSymbol(config.SupportSymbols)
	if rpcServer != nil {
		ctx, _ := context.WithDeadline(context.Background(), time.Now().Add(time.Second))
		_ = rpcServer.s.Shutdown(ctx)
	}
	log.Info("rc service begin stop")
}

func (r *RPC) epilog(ctx context.Context, err error, lang define.ReqLang, arg interface{}, reply *define.Reply) {
	if err != nil {
		switch e := err.(type) {
		case *define.ReplyError:
			if e != nil {
				reply.Ret = e.Code
				reply.Msg = e.LangErrMsg(lang)
			}
		default:
			reply.Ret = define.ErrCodeBusy
			//reply.Msg = define.ErrMsgBusy.LangErrMsg(lang)
			reply.Msg = e.Error()
		}
	}

	sv := xplugins.GetSpanValues(ctx)
	pc, _, _, _ := runtime.Caller(1)
	fn := runtime.FuncForPC(pc).Name()

	switch args := arg.(type) {
	case *define.Arg:
		var data []byte
		if len(args.Data) > define.APILogMaxLength {
			data = args.Data[:define.APILogMaxLength]
		} else {
			data = args.Data
		}

		log.Info("epilog output",
			zap.String("method", fn),
			zap.Int64("reqID", args.ReqID),
			zap.Int64("userID", args.UserID),
			zap.String("inTime", sv.GetRequestTime()),
			zap.String("outTime", time.Now().Format(define.TimeFormatMillisecond)),
			zap.String("prevNode", sv.GetRequestNode()),
			zap.String("curNode", sv.GetResponseNode()),
			zap.ByteString("param", data),
			zap.Int("result", reply.Ret),
			zap.Error(err))
	default:
		log.Info("epilog output",
			zap.String("method", fn),
			zap.Any("param", arg),
			zap.String("inTime", sv.GetRequestTime()),
			zap.String("outTime", time.Now().Format(define.TimeFormatMillisecond)),
			zap.String("prevNode", sv.GetRequestNode()),
			zap.String("curNode", sv.GetResponseNode()),
			zap.Int("result", reply.Ret),
			zap.Error(err))
	}
}
