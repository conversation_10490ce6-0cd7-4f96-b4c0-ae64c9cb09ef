package binance_impl

import (
	"context"
	"errors"
	"fmt"
	"github.com/adshao/go-binance/v2"
	"github.com/adshao/go-binance/v2/common"
	"github.com/shopspring/decimal"
	"go.uber.org/atomic"
	"go.uber.org/zap"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/nums"
	"spot/libs/proto"
	"spot/pkg/order/model"
	"spot/pkg/order/service"
	"strings"
	"time"
)

const revWin = 10000

const qtyNotSupport = "Quote order qty market orders are not supported for this symbol."

type Client struct {
	id      define.MarketSource
	account *proto.ThirdMarketConfig
	client  *binance.Client
	lock    atomic.Bool
	isLimit atomic.Int64
	ws      *WsClient
}

type WsClient struct {
	listenKey    string
	client       *Client
	doneC, doneS chan struct{}
	isOk         bool
}

func NewWsClient(client *Client) *WsClient {
	return &WsClient{client: client}
}

func (w *WsClient) InitListenKey() {
	var err error
Retry:
	w.listenKey, err = w.client.GetListenKey()
	if err != nil {
		log.Error("binance user ws GetListenKey fail", zap.Error(err))
		time.Sleep(200 * time.Millisecond)
		goto Retry
		return
	}
}

func (w *WsClient) Start() {
	w.InitListenKey()
	w.KeepAive()
	w.CreteWs()
	go func() {
		for range w.doneS {
			log.Info("收到ws退出指令")
			w.isOk = false
			w.CreteWs()
		}
	}()
}

func (w *WsClient) reloadConfig() {
	if w.isOk {
		w.doneS <- struct{}{}
	}
}

func (w *WsClient) CreteWs() {
	log.Info("开始启动binance 用户ws", zap.String("listenKey", w.listenKey))
	var err error
	wsHandler := func(event *binance.WsUserDataEvent) {
		log.Info(" 币安获取到用户数据", zap.Any("data", event))
		switch event.Event {
		case binance.UserDataEventTypeExecutionReport:
			w.dealOrderTrade(event.OrderUpdate)
		case binance.UserDataEventTypeOutboundAccountPosition:
			w.dealAccountUpdate(event.AccountUpdate)
		}
	}
	errHandler := func(err error) {
		log.Error("binance user ws error", zap.Error(err))
		w.doneS <- struct{}{}
		return
	}
	if w.listenKey == "" {
		w.InitListenKey()
	}
	w.doneC, w.doneS, err = binance.WsUserDataServe(w.listenKey, wsHandler, errHandler)
	if err != nil {
		fmt.Println(err)
		return
	}
	w.isOk = true
	log.Info("binance 用户ws连接成功")
}

func (w *WsClient) KeepAive() {
	t := time.Tick(20 * time.Minute)
	go func() {
		for range t {
			err := w.client.KeepAliveListenKey(w.listenKey)
			if err != nil {
				log.Error("binance user ws KeepAliveListenKey fail", zap.Error(err))
			}
		}
	}()
}

func (c *Client) GetAccount() (list []model.ThirdAccountBalance, err error) {
	defer func() {
		c.dealError(err)
	}()
	if c.isLimit.Load() > 0 {
		err = model.ErrApiLimit
		return
	}
	res, err := c.GetApi().NewGetAccountService().Do(context.Background(), binance.WithRecvWindow(revWin))
	if err != nil {
		log.Error("Binance GetAccount fail", zap.Error(err))
		return
	}
	for _, balance := range res.Balances {
		hb := model.ThirdAccountBalance{
			SourceId: c.account.SourceId,
			Coin:     balance.Asset,
			Balance:  nums.NewFromString(balance.Free),
			Lock:     nums.NewFromString(balance.Locked),
			CanTrade: res.CanTrade,
		}
		list = append(list, hb)
	}
	return
}

func (c *Client) ReloadConfig() {
	if c != nil {
		c.reloadConfig()
	}
}

func (c *Client) GetApi() *binance.Client {
Retry:
	if c.lock.Load() {
		time.Sleep(50 * time.Millisecond)
		goto Retry
	}
	return c.client
}

func (c *Client) ListOpenOrders(arg interface{}) (list []model.ClientOrder, err error) {
	defer func() {
		c.dealError(err)
	}()
	if c.isLimit.Load() > 0 {
		err = model.ErrApiLimit
		return
	}
	openOrders, err := c.GetApi().NewListOpenOrdersService().
		Do(context.Background(), binance.WithRecvWindow(revWin))
	if err != nil {
		log.Error("Binance ListOpenOrders fail", zap.Error(err))
		return
	}
	log.Info("币安查询订单返回", zap.Any("arg", arg), zap.Any("data", openOrders))
	for _, o := range openOrders {
		if o != nil {
			co := getClientOrder(o)
			if co != nil {
				list = append(list, *co)
			}
		}
	}
	return
}

func (c *Client) ListOrderHistory(data interface{}) (list []model.ClientOrder, err error) {
	defer func() {
		c.dealError(err)
	}()
	if c.isLimit.Load() > 0 {
		err = model.ErrApiLimit
		return
	}
	arg, ok := data.(model.OrderHistoryArg)
	if !ok {
		log.Error("Binance 查询订单 参数出错 not model.OrderHistoryArg")
		err = errors.New("请求参数无效")
		return
	}
	code := strings.ReplaceAll(arg.Code, "/", "")
	orders, err := c.GetApi().NewListOrdersService().Symbol(code).OrderID(arg.LastOrderId).
		Do(context.Background(), binance.WithRecvWindow(revWin))
	if err != nil {
		log.Error("ListOrderHistory fail", zap.Error(err))
		return
	}
	log.Info("币安查询全部订单", zap.Any("arg", arg), zap.Any("data", orders))
	for _, o := range orders {
		if o != nil {
			//fmt.Printf("%+v",*o)
			co := getClientOrder(o)
			if co != nil {
				list = append(list, *co)
			}
		}
	}
	return
}

func GetConvertForBinance(o *proto.ThirdOrderPlaceArg) (code string, side binance.SideType, ot binance.OrderType) {
	code = strings.ReplaceAll(o.ContractCode, "/", "")
	if o.Side == define.OrderBuy {
		side = binance.SideTypeBuy
	} else {
		side = binance.SideTypeSell
	}
	if o.EntrustType == define.EntrustTypeLimit {
		ot = binance.OrderTypeLimit
	} else {
		ot = binance.OrderTypeMarket
	}
	return
}

func (c *Client) Place(order *proto.ThirdOrderPlaceArg) (reply model.ClientPlaceReply, err error) {
	defer func() {
		c.dealError(err)
	}()
	if c.isLimit.Load() > 0 {
		log.Info("币安当前请求地址受限，本次不再强求", zap.Any("order", order))
		err = model.ErrApiLimit
		return
	}
	code, side, ot := GetConvertForBinance(order)
	srv := c.GetApi().NewCreateOrderService().Symbol(code).
		Side(side).Type(ot).NewClientOrderID(nums.Int64String(order.OrderId))
	if ot == binance.OrderTypeLimit {
		srv = srv.Quantity(order.Volume.String()).Price(order.Price.String()).TimeInForce(binance.TimeInForceTypeGTC)
	} else {
		if order.Side == define.OrderBuy {
			srv = srv.QuoteOrderQty(order.Money.String())
		} else {
			if order.Volume.GreaterThan(decimal.Zero) {
				srv = srv.Quantity(order.Volume.String())
			} else {
				srv = srv.QuoteOrderQty(order.Money.String())
			}
		}
	}
	rOrder, err := srv.Do(context.Background(), binance.WithRecvWindow(revWin))
	if err != nil {
		log.Error("Binance Place fail", zap.Error(err))
		apiErr, ok := err.(*common.APIError)
		if ok && (apiErr.Code == -3041 || apiErr.Code == -2010) {
			//处理下单余额不足报警
			service.SendPlateBalanceLackMsg(order)
		}
		if ok && apiErr.Message == qtyNotSupport {
			log.Error("向币安下单失败，交易对不支持传递下单金额", zap.Error(err), zap.Any("order", order))
		}
		return
	}
	log.Info("下单返回", zap.Any("data", rOrder))
	reply.Id = rOrder.OrderID
	reply.Status = convertStatus(rOrder.Status)
	return
}

func convertStatus(s binance.OrderStatusType) (status define.OrderState) {
	switch s {
	case binance.OrderStatusTypeNew:
		status = define.OrderStatusDefault
	case binance.OrderStatusTypePartiallyFilled:
		status = define.OrderStatusPart
	case binance.OrderStatusTypeFilled:
		status = define.OrderStatusFull
	case binance.OrderStatusTypeCanceled:
		status = define.OrderStatusPartCancel
	case binance.OrderStatusTypePendingCancel:
	case binance.OrderStatusTypeRejected:
		status = define.OrderStatusDrop
	case binance.OrderStatusTypeExpired:
		status = define.OrderStatusDrop
	}
	return
}

func (c *Client) Cancel(code string, orderId int64) (err error) {
	log.Info("币安开始撤销第三方订单", zap.String("code", code), zap.Int64("orderId", orderId))
	defer func() {
		c.dealError(err)
	}()
	if c.isLimit.Load() > 0 {
		err = model.ErrApiLimit
		return
	}
	code = strings.ReplaceAll(code, "/", "")
	_, err = c.GetApi().NewCancelOrderService().Symbol(code).
		OrderID(orderId).Do(context.Background(), binance.WithRecvWindow(revWin))
	if err != nil {
		log.Error("Binance Cancel fail", zap.Error(err), zap.String("code", code), zap.Int64("orderId", orderId))
		return
	}
	return
}

func (c *Client) GetExchangeInfo() (list []model.PriceLimit, err error) {
	defer func() {
		c.dealError(err)
	}()
	if c.isLimit.Load() > 0 {
		err = model.ErrApiLimit
		return
	}
	rsp, err := c.GetApi().NewExchangeInfoService().Do(context.Background())
	if err != nil {
		log.Error("Binance GetExchangeInfo fail", zap.Error(err))
		return
	}

	//fmt.Println(rsp)
	//if rsp == nil {
	//
	//	fmt.Println(rsp)
	//} else {
	//	fmt.Println("nil")
	//}
	if rsp != nil {
		list = CachePriceLimitForBinance(rsp.Symbols)
	}
	return
}

func (c *Client) GetAvgPrice(name string) (price decimal.Decimal, err error) {
	defer func() {
		c.dealError(err)
	}()
	if c.isLimit.Load() > 0 {
		err = model.ErrApiLimit
		return
	}
	rsp, err := c.GetApi().NewAveragePriceService().Symbol(name).Do(context.Background())
	if err != nil {
		log.Error("Binance GetExchangeInfo fail", zap.Error(err))
		return
	}
	price = nums.NewFromString(rsp.Price)
	return
}

func (c *Client) GetNewPrice(name string) (price decimal.Decimal, err error) {
	defer func() {
		c.dealError(err)
	}()
	if c.isLimit.Load() > 0 {
		err = model.ErrApiLimit
		return
	}
	rsp, err := c.GetApi().NewRecentTradesService().Symbol(name).Do(context.Background())
	if err != nil {
		log.Error("Binance GetExchangeInfo fail", zap.Error(err))
		return
	}
	if len(rsp) > 0 {
		price = nums.NewFromString(rsp[0].Price)
	}
	return
}

func (c *Client) GetHegeConfig() *proto.ThirdMarketConfig {
	return c.account
}

func (c *Client) reloadConfig() {
	cfg := service.GetMarketSourceConfig(define.MarketSourceBinance)
	if cfg == nil {
		return
	}
	if cfg.PrivateKey == c.account.PrivateKey && cfg.PublicKey == cfg.PublicKey {
		return
	}
	c.lock.Store(true)
	defer c.lock.Store(false)
	c.account = cfg
	client := binance.NewClient(cfg.PublicKey, cfg.PrivateKey)
	c.client = client
	if cfg.IsTest {
		binance.UseTestnet = true
	} else {
		binance.UseTestnet = false
	}
}

var _ model.Client = (*Client)(nil)

func NewClient(id define.MarketSource, cfg *proto.ThirdMarketConfig) *Client {
	if cfg == nil {
		panic("对冲配置为空")
	}

	if cfg.IsTest {
		binance.UseTestnet = true
	}
	api := binance.NewClient(cfg.PublicKey, cfg.PrivateKey)
	client := &Client{id: id, account: cfg, client: api}
	go func() {
		t := time.Tick(30 * time.Second)
		for range t {
			last := client.isLimit.Load()
			if client.isLimit.Load() > 0 {
				if time.Now().Unix()-last > 20 {
					client.isLimit.Store(0)
				}
			}
		}
	}()
	return client
}

func NewClientWithWs(id define.MarketSource, cfg *proto.ThirdMarketConfig) *Client {
	if cfg == nil {
		panic("对冲配置为空")
	}

	if cfg.IsTest {
		binance.UseTestnet = true
	}
	api := binance.NewClient(cfg.PublicKey, cfg.PrivateKey)
	client := &Client{id: id, account: cfg, client: api}
	client.ws = NewWsClient(client)
	client.ws.Start()
	go func() {
		t := time.Tick(30 * time.Second)
		for range t {
			last := client.isLimit.Load()
			if client.isLimit.Load() > 0 {
				if time.Now().Unix()-last > 20 {
					client.isLimit.Store(0)
				}
			}
		}
	}()
	return client
}

func getClientOrder(o *binance.Order) *model.ClientOrder {
	if o == nil {
		return nil
	}
	order := model.ClientOrder{
		ID:          o.OrderID,
		Code:        o.Symbol,
		TradeAmount: nums.NewFromString(o.ExecutedQuantity),
		TradeMoney:  nums.NewFromString(o.CummulativeQuoteQuantity),
		UpdateTime:  time.Unix(o.UpdateTime/1000, 0),
		CreateTime:  time.Unix(o.Time/1000, 0),
	}
	if order.TradeMoney.LessThan(decimal.Zero) {
		return nil
	}

	switch o.Status {
	case binance.OrderStatusTypeNew:
		order.State = define.OrderStatusDefault
	case binance.OrderStatusTypePartiallyFilled:
		order.State = define.OrderStatusPart
	case binance.OrderStatusTypeFilled:
		if order.TradeAmount.GreaterThan(decimal.Zero) {
			order.TradePrice = order.TradeMoney.Div(order.TradeAmount)
		}
		order.State = define.OrderStatusFull
	case binance.OrderStatusTypeCanceled:
		if order.TradeAmount.GreaterThan(decimal.Zero) {
			order.TradePrice = order.TradeMoney.Div(order.TradeAmount)
			order.State = define.OrderStatusPartCancel
		} else {
			order.State = define.OrderStatusNotDealCancel
		}
	case binance.OrderStatusTypePendingCancel:
	case binance.OrderStatusTypeRejected:
		order.State = define.OrderStatusDrop
	case binance.OrderStatusTypeExpired:
		order.State = define.OrderStatusDrop
	}
	return &order
}

func (c *Client) dealError(err error) {
	apiErr, ok := err.(*common.APIError)
	if !ok {
		return
	}

	log.Error("Binance api error", zap.Any("error", apiErr))
	//-3022 账户无权交易
	switch apiErr.Code {
	//case 418, 429, -1003, -4005:
	//	c.isLimit.Store(time.Now().Unix())
	//	log.Error("binance api请求限制", zap.Error(err))
	//	service.SendPlateAccountExceptionMsg(define.ExchangeHedgeSourceBinance)
	//case -2014, -2015, -1022:
	//	log.Error("binance 无效的api", zap.Error(err))
	//	service.SendPlateAccountExceptionMsg(define.ExchangeHedgeSourceBinance)
	//case -3022:
	//	log.Error("binance 账户无权交易", zap.Error(err))
	//	service.SendPlateAccountExceptionMsg(define.ExchangeHedgeSourceBinance)
	}
}

func (c *Client) GetMyTrades(code string, orderId int64) (result []model.ClientTrade, err error) {
	defer func() {
		c.dealError(err)
	}()
	symbol := strings.ReplaceAll(code, "/", "")
	list, err := c.GetApi().NewListTradesService().Symbol(symbol).OrderId(orderId).Do(context.Background())
	if err != nil {
		log.Error("Binance GetMyTrades fail", zap.Error(err))
		return
	}
	for _, item := range list {
		ct := model.ClientTrade{
			TradeId:      item.ID,
			ThirdOrderId: item.OrderID,
			Code:         item.Symbol,
			TradePrice:   nums.NewFromString(item.Price),
			TradeAmount:  nums.NewFromString(item.Quantity),
			TradeMoney:   nums.NewFromString(item.QuoteQuantity),
			IsMaker:      item.IsMaker,
			Fee:          nums.NewFromString(item.Commission),
			FeeAsset:     nums.NewFromString(item.CommissionAsset),
			CreateTime:   time.UnixMilli(item.Time),
			MarketSource: define.MarketSourceBinance,
		}
		result = append(result, ct)
	}
	return
}

func (c *Client) GetListenKey() (listenKey string, err error) {
	defer func() {
		c.dealError(err)
	}()
	listenKey, err = c.GetApi().NewStartUserStreamService().Do(context.Background())
	if err != nil {
		log.Error("Binance GetListenKey fail", zap.Error(err))
		return
	}
	return
}

func (c *Client) KeepAliveListenKey(listenKey string) (err error) {
	defer func() {
		c.dealError(err)
	}()
	err = c.GetApi().NewKeepaliveUserStreamService().ListenKey(listenKey).Do(context.Background())
	if err != nil {
		log.Error("Binance KeepAliveListenKey fail", zap.Error(err))
		return
	}
	return
}

func (c *Client) CloseListenKey(listenKey string) (err error) {
	defer func() {
		c.dealError(err)
	}()
	err = c.GetApi().NewCloseUserStreamService().ListenKey(listenKey).Do(context.Background())
	if err != nil {
		log.Error("Binance CloseListenKey fail", zap.Error(err))
		return
	}
	return
}

//websocket 订单状态
func (w *WsClient) dealOrderTrade(update binance.WsOrderUpdate) {
	log.Info("币安ws收到订单状态", zap.Any("data", fmt.Sprintf("%+v", update)))
	thisTradeVolume := nums.NewFromString(update.LatestVolume)
	thisTradePrice := nums.NewFromString(update.LatestPrice)
	thisTradeMoney := nums.NewFromString(update.LatestQuoteVolume)
	thisTradeId := update.TradeId
	order := model.ClientTrade{
		TradeId:       thisTradeId,
		ClientOrderId: nums.String2Int64(update.OrigCustomOrderId),
		ThirdOrderId:  update.Id,
		Code:          update.Symbol,
		TradePrice:    thisTradePrice,
		TradeAmount:   thisTradeVolume,
		TradeMoney:    thisTradeMoney,
		IsMaker:       update.IsMaker,
		Fee:           nums.NewFromString(update.FeeCost),
		FeeAsset:      nums.NewFromString(update.FeeAsset),
		CreateTime:    time.UnixMilli(update.CreateTime),
		IsWs:          true,
		MarketSource:  define.MarketSourceBinance,
	}
	var os define.OrderState
	s := binance.OrderStatusType(update.Status)
	switch s {
	case binance.OrderStatusTypeNew:
		os = define.OrderStatusDefault
	case binance.OrderStatusTypePartiallyFilled:
		os = define.OrderStatusPart
	case binance.OrderStatusTypeFilled:
		os = define.OrderStatusFull
	case binance.OrderStatusTypeCanceled:
		if nums.NewFromString(update.FilledVolume).GreaterThan(decimal.Zero) {
			os = define.OrderStatusPartCancel
		} else {
			os = define.OrderStatusNotDealCancel
		}
	case binance.OrderStatusTypePendingCancel:
	case binance.OrderStatusTypeRejected:
		os = define.OrderStatusDrop
	case binance.OrderStatusTypeExpired:
		os = define.OrderStatusDrop
	}

	if os.IsHasTrade() {
		//处理本次成交
		service.DealOrderThisTrade(&order)
	}
	if os.IsFinished() {
		w.dealOrderFinish(&order)
	}
}

func (w *WsClient) dealOrderFinish(order *model.ClientTrade) {
	result, err := w.client.GetMyTrades(order.Code, order.ThirdOrderId)
	if err != nil {
		log.Error("dealOrderFinish GetMyTrades fail", zap.Error(err))
		return
	}
	for _, trade := range result {
		t := trade
		service.DealOrderThisTrade(&t)
	}
	service.DealOrderFinish(order.ThirdOrderId, define.TradeMarkWithTradeFinish)
}

func (w *WsClient) dealAccountUpdate(update []binance.WsAccountUpdate) {
	log.Info("币安收到变动账户", zap.Any("data", fmt.Sprintf("%+v", update)))
}
