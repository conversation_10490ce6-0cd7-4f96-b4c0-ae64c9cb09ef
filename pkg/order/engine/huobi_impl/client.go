package huobi_impl

import (
	"errors"
	"fmt"
	"github.com/huobirdcenter/huobi_golang/logging/applogger"
	"github.com/huobirdcenter/huobi_golang/pkg/client"
	"github.com/huobirdcenter/huobi_golang/pkg/client/orderwebsocketclient"
	hModel "github.com/huobirdcenter/huobi_golang/pkg/model"
	"github.com/huobirdcenter/huobi_golang/pkg/model/auth"
	hOrder "github.com/huobirdcenter/huobi_golang/pkg/model/order"

	"github.com/shopspring/decimal"
	"go.uber.org/atomic"
	"go.uber.org/zap"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/nums"
	"spot/libs/proto"
	"spot/pkg/order/model"
	"spot/pkg/order/service"
	"strings"
	"time"
)

const revWin = 10000

type Client struct {
	id        define.MarketSource
	account   *proto.ThirdMarketConfig
	lock      atomic.Bool
	isLimit   atomic.Int64
	accountId int64
}

type WsClient struct {
	listenKey    string
	client       *Client
	doneC, doneS chan struct{}
	isOk         bool
}

func NewWsClient(client *Client) *WsClient {
	return &WsClient{client: client}
}

func (w *WsClient) Start() {
	go func() {
		client := new(orderwebsocketclient.SubscribeOrderWebSocketV2Client).Init(w.client.account.PublicKey, w.client.account.PrivateKey, host)

		// Set the callback handlers
		client.SetHandler(
			// Connected handler
			func(resp *auth.WebSocketV2AuthenticationResponse) {
				if resp.IsSuccess() {
					// Subscribe if authentication passed
					client.Subscribe("orders#*", "1149")
				} else {
					applogger.Info("Authentication error, code: %d, message:%s", resp.Code, resp.Message)
				}
			},
			// Response handler
			func(resp interface{}) {
				subResponse, ok := resp.(hOrder.SubscribeOrderV2Response)
				if ok {
					if subResponse.Action == "sub" {
						if subResponse.IsSuccess() {
							applogger.Info("Subscription topic %s successfully", subResponse.Ch)
						} else {
							applogger.Error("Subscription topic %s error, code: %d, message: %s", subResponse.Ch, subResponse.Code, subResponse.Message)
						}
					} else if subResponse.Action == "push" {
						if subResponse.Data != nil {
							o := subResponse.Data
							applogger.Info("Order update, event: %s, symbol: %s, type: %s, status: %s",
								o.EventType, o.Symbol, o.Type, o.OrderStatus)
							w.dealOrderUpdate(o.OrderId, o.Symbol, o.OrderStatus)
						}
					}
				} else {
					applogger.Warn("Received unknown response: %v", resp)
				}
			})

		// Connect to the server and wait for the handler to handle the response
		client.Connect(true)
	}()
	go func() {
		for range w.doneS {
			log.Info("收到ws退出指令")
			w.isOk = false
			w.CreteWs()
		}
	}()
}

func (w *WsClient) dealOrderUpdate(id int64, code, status string) {
	log.Info("币安ws收到订单状态", zap.Int64("orderid", id), zap.Any("data", fmt.Sprintf("%+v", status)))
	order := model.ClientOrder{
		ID:         id,
		Code:       code,
		CreateTime: time.Now(),
	}
	switch status {
	case OrderFullTrade:
		order.State = define.OrderStatusFull
	case OrderCancel, OrderPartCancel:
		order.State = define.OrderStatusPartCancel
	}
	if order.State.IsFinished() {
		w.dealOrderFinish(&order)
	}
}

func (w *WsClient) reloadConfig() {
	if w.isOk {
		w.doneS <- struct{}{}
	}
}

func (w *WsClient) CreteWs() {
	log.Info("开始启动binance 用户ws", zap.String("listenKey", w.listenKey))

	log.Info("binance 用户ws连接成功")
}

func (c *Client) getAndSetAccountId() (err error) {
	defer func() {
		c.dealError(err)
	}()
	if c.isLimit.Load() > 0 {
		err = model.ErrApiLimit
		return
	}

	res, err := new(client.AccountClient).Init(c.account.PublicKey, c.account.PrivateKey, host).GetAccountInfo()
	if err != nil {
		log.Error("Binance GetAccount fail", zap.Error(err))
		return
	}

	for _, balance := range res {
		if balance.Type == "spot" {
			c.accountId = balance.Id
		}
	}
	return
}

func (c *Client) GetAccount() (list []model.ThirdAccountBalance, err error) {
	defer func() {
		c.dealError(err)
	}()
	if c.isLimit.Load() > 0 {
		err = model.ErrApiLimit
		return
	}
	if c.accountId == 0 {
		c.getAndSetAccountId()
	}
	res, err := new(client.AccountClient).Init(c.account.PublicKey, c.account.PrivateKey, host).GetAccountBalance(nums.Int64String(c.accountId))
	if err != nil {
		log.Error("Binance GetAccount fail", zap.Error(err))
		return
	}
	for _, balance := range res.List {
		hb := model.ThirdAccountBalance{
			SourceId: c.account.SourceId,
			Coin:     balance.Currency,
			Balance:  nums.NewFromString(balance.Balance),
		}
		list = append(list, hb)
	}
	return
}

func (c *Client) ReloadConfig() {
	if c != nil {
		c.reloadConfig()
	}
}

const (
	OrderFullTrade  = "filled"
	OrderPartCancel = "partial-canceled"
	OrderCancel     = "canceled"
	//filled（已成交），partial-canceled（部分成交撤销），canceled（已撤销）
)

func (c *Client) ListOrderHistory(data interface{}) (list []model.ClientOrder, err error) {
	defer func() {
		c.dealError(err)
	}()
	if c.isLimit.Load() > 0 {
		err = model.ErrApiLimit
		return
	}
	arg, ok := data.(model.OrderHistoryArg)
	if !ok {
		log.Error("Binance 查询订单 参数出错 not model.OrderHistoryArg")
		err = errors.New("请求参数无效")
		return
	}
	request := &hModel.GetRequest{}
	code := strings.ToLower(strings.ReplaceAll(arg.Code, "/", ""))
	request.AddParam("symbol", code)
	request.AddParam("direct", "next")                          //next prev
	request.AddParam("from", nums.Int64String(arg.LastOrderId)) //next prev
	order, err := new(client.OrderClient).Init(c.account.PublicKey, c.account.PrivateKey, host).GetHistoryOrders(request)

	if err != nil {
		log.Error("ListOrderHistory fail", zap.Error(err))
		return
	}
	log.Info("查询全部订单", zap.Any("arg", arg), zap.Any("data", order.Data))
	for _, o := range order.Data {
		order := model.ClientOrder{
			ID:   o.Id,
			Code: o.Symbol,
		}
		switch o.State {
		case OrderFullTrade:
			order.State = define.OrderStatusFull
		case OrderCancel, OrderPartCancel:
			order.State = define.OrderStatusPartCancel
		}
		list = append(list, order)
	}
	return
}

func (c *Client) Place(order *proto.ThirdOrderPlaceArg) (reply model.ClientPlaceReply, err error) {
	defer func() {
		c.dealError(err)
	}()
	if c.isLimit.Load() > 0 {
		log.Info("币安当前请求地址受限，本次不再强求", zap.Any("order", order))
		err = model.ErrApiLimit
		return
	}
	var oType string
	var amount, price decimal.Decimal
	if order.Side == define.OrderBuy {
		oType = "buy-"
	} else {
		oType = "sell-"
	}

	amount = order.Volume
	price = order.Price
	if order.EntrustType == define.EntrustTypeLimit {
		oType += "limit"
	} else {
		oType += "market"
		if order.Side == define.OrderBuy {
			amount = order.Money
		}
	}
	arg := &hOrder.PlaceOrderRequest{
		AccountId:     nums.Int64String(c.accountId),
		Symbol:        strings.ToLower(strings.ReplaceAll(order.ContractCode, "/", "")),
		Type:          oType,
		Amount:        amount.String(),
		Price:         price.String(),
		Source:        "spot-api",
		ClientOrderId: nums.Int64String(order.OrderId),
	}
	rsp, err := new(client.OrderClient).Init(c.account.PublicKey, c.account.PrivateKey, host).PlaceOrder(arg)
	if err != nil {
		log.Error("Binance Place fail", zap.Error(err))
		//apiErr, ok := err.(*common.APIError)
		//if ok && (apiErr.Code == -3041 || apiErr.Code == -2010) {
		//	//处理下单余额不足报警
		//	service.SendPlateBalanceLackMsg(order)
		//}
		return
	}
	log.Info("下单返回", zap.Any("data", order))
	reply.Id = nums.String2Int64(rsp.Data)
	return
}

func (c *Client) Cancel(code string, orderId int64) (err error) {
	log.Info("火币开始撤销第三方订单", zap.String("code", code), zap.Int64("orderId", orderId))
	defer func() {
		c.dealError(err)
	}()
	if c.isLimit.Load() > 0 {
		err = model.ErrApiLimit
		return
	}
	_, err = new(client.OrderClient).Init(c.account.PublicKey, c.account.PrivateKey, host).CancelOrderByClientOrderId(nums.Int64String(orderId))
	if err != nil {
		log.Error("Cancel fail", zap.Error(err), zap.String("code", code), zap.Int64("orderId", orderId))
		return
	}
	return
}

func (c *Client) GetHegeConfig() *proto.ThirdMarketConfig {
	return c.account
}

func (c *Client) reloadConfig() {
	cfg := service.GetMarketSourceConfig(define.MarketSourceHuoBi)
	if cfg == nil {
		return
	}
	if cfg.PrivateKey == c.account.PrivateKey && cfg.PublicKey == cfg.PublicKey {
		return
	}
	c.lock.Store(true)
	defer c.lock.Store(false)
	c.account = cfg
}

var _ model.Client = (*Client)(nil)
var host = "api-aws.huobi.pro"

func NewClient(id define.MarketSource, cfg *proto.ThirdMarketConfig) *Client {
	if cfg == nil {
		panic("对冲配置为空")
	}

	client := &Client{id: id, account: cfg}
	//go func() {
	//	t := time.Tick(30 * time.Second)
	//	for range t {
	//		last := client.isLimit.Load()
	//		if client.isLimit.Load() > 0 {
	//			if time.Now().Unix()-last > 20 {
	//				client.isLimit.Store(0)
	//			}
	//		}
	//	}
	//}()
	return client
}

func NewClientWithWs(id define.MarketSource, cfg *proto.ThirdMarketConfig) *Client {
	if cfg == nil {
		panic("对冲配置为空")
	}

	client := &Client{id: id, account: cfg}
	//client.ws = NewWsClient(client)
	//client.ws.Start()
	go func() {
		t := time.Tick(30 * time.Second)
		for range t {
			last := client.isLimit.Load()
			if client.isLimit.Load() > 0 {
				if time.Now().Unix()-last > 20 {
					client.isLimit.Store(0)
				}
			}
		}
	}()
	return client
}

func (c *Client) dealError(err error) {

}

func (c *Client) GetMyTrades(symbol string, orderId int64) (result []model.ClientTrade, err error) {
	defer func() {
		c.dealError(err)
	}()
	//var list []*binance.TradeV3
	list, err := new(client.OrderClient).Init(c.account.PublicKey, c.account.PrivateKey, host).GetMatchResultsById(nums.Int64String(orderId))
	if err != nil {
		log.Error("huobi GetMyTrades fail", zap.Error(err))
		return
	}
	for _, item := range list.Data {
		ct := model.ClientTrade{
			TradeId:      item.TradeId,
			ThirdOrderId: item.OrderId,
			Code:         item.Symbol,
			TradePrice:   nums.NewFromString(item.Price),
			TradeAmount:  nums.NewFromString(item.FilledAmount),
			IsMaker:      item.Role == "maker",
			CreateTime:   time.UnixMilli(item.CreatedAt),
			MarketSource: define.MarketSourceBinance,
		}
		ct.TradeMoney = ct.TradePrice.Mul(ct.TradeAmount)
		result = append(result, ct)
	}
	return
}

func (w *WsClient) dealOrderFinish(order *model.ClientOrder) {
	result, err := w.client.GetMyTrades(order.Code, order.ID)
	if err != nil {
		log.Error("dealOrderFinish GetMyTrades fail", zap.Error(err))
		return
	}
	for _, trade := range result {
		t := trade
		service.DealOrderThisTrade(&t)
	}
	mark := define.TradeMarkWithOrderDefault
	if order.State == define.OrderStatusFull {
		mark = define.TradeMarkWithTradeFinish
	}
	service.DealOrderFinish(order.ID, mark)
}
