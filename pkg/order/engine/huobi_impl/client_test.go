package huobi_impl

import (
	"github.com/huobirdcenter/huobi_golang/config"
	"github.com/huobirdcenter/huobi_golang/logging/applogger"
	"github.com/huobirdcenter/huobi_golang/pkg/client"
	"github.com/huobirdcenter/huobi_golang/pkg/client/orderwebsocketclient"
	"github.com/huobirdcenter/huobi_golang/pkg/model/auth"
	"github.com/huobirdcenter/huobi_golang/pkg/model/order"
	"go.uber.org/zap"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/nums"
	"spot/libs/proto"
	"spot/pkg/order/model"
	"testing"
	"time"
)

var _exchangeCfg = &proto.ThirdMarketConfig{
	SourceId:   define.MarketSourceBinance,
	Name:       "binance",
	PublicKey:  "ONogSOtzoIGM8fehwSJBa5KU1K18VMihqOLwfVDWUJDqrrG8P3BWVGQrZk5OSdWd",
	PrivateKey: "kUWKa9TOok64ZTtDEg58WFUJmMJDYCtfuLrkcDGTlqYCJSrcl6Mj9iCO24up5u5C",
	//PublicKey:  "5nIRlBp0Bz5XrBPSkZz3YWROEKFtD68dvZ2OXoiJzuGSxe2i0UEtgcoWXygwjoT5",
	//PrivateKey: "4Gmkq66PS3Qc5YBIklEtkKyU6M4ZJE9nhMfFuO0kCAuzvCGHvNv9MpCk35E8MXzt",
	HitPhrase:  "",
	CreateTime: time.Now(),
	UpdateTime: time.Now(),
	IsTest:     true,
}

func TestClient(t *testing.T) {
	log.InitLogger("info", "info", false)
	client := NewClient(1, _exchangeCfg)
	list, err := client.GetAccount()
	if err != nil {
		t.Log(err)
		return
	}
	t.Log(list)

}

func TestClientPlaceMarket(t *testing.T) {
	log.InitLogger("info", "info", false)
	client := NewClient(1, _exchangeCfg)

	arg := &proto.ThirdOrderPlaceArg{
		MarketSource: define.MarketSourceBinance,
		ContractCode: "BTC/USDT",
		EntrustType:  define.EntrustTypeMarket,
		Side:         "B",
		//Volume:       nums.NewFromFloat(0.5),
		Money: nums.NewFromFloat(1000),
	}
	id, err := client.Place(arg)
	if err != nil {
		t.Log(err)
		return
	}
	t.Log("获取下单的订单id", id)
}

func TestClientPlaceLimit(t *testing.T) {
	log.InitLogger("info", "info", false)
	client := NewClient(1, _exchangeCfg)

	arg := &proto.ThirdOrderPlaceArg{
		ContractCode: "BTC/USDT",
		EntrustType:  define.EntrustTypeLimit,
		Side:         "S",
		Price:        nums.NewFromFloat(48650),
		Volume:       nums.NewFromFloat(0.02),
		MarketSource: define.MarketSourceBinance,
	}
	id, err := client.Place(arg)
	if err != nil {
		t.Log(err)
		return
	}
	t.Log("获取下单的订单id", id)
}

func TestClientCancel(t *testing.T) {
	log.InitLogger("info", "info", false)
	client := NewClient(1, _exchangeCfg)

	err := client.Cancel("BTCUSDT", 2225346)
	//err := client.Cancel("BTCUSDT",2191470)
	if err != nil {
		t.Log(err)
		return
	}
}

func TestClientHistory(t *testing.T) {
	log.InitLogger("info", "info", false)
	client := NewClient(1, _exchangeCfg)
	arg := model.OrderHistoryArg{
		Code:        "BTCUSDT",
		LastOrderId: 0,
	}
	id, err := client.ListOrderHistory(arg)
	if err != nil {
		t.Log(err)
		return
	}
	t.Logf("%+v", id)
	log.Info("list", zap.Any("list", id))
}

func TestClientMyTrades(t *testing.T) {
	log.InitLogger("info", "info", false)
	client := NewClient(1, _exchangeCfg)

	id, err := client.GetMyTrades("BTCUSDT", 718346)
	if err != nil {
		t.Log(err)
		return
	}
	t.Log("获取下单的订单id", id)
}

func TestApi(t *testing.T) {
	host := "api-aws.huobi.pro"
	// Get the timestamp from Huobi server and print on console
	//client := new(client.CommonClient).Init(host)
	//resp, err := client.GetTimestamp()
	//
	//if err != nil {
	//	applogger.Error("Get timestamp error: %s", err)
	//} else {
	//	applogger.Info("Get timestamp: %d", resp)
	//}

	// Get the list of accounts owned by this API user and print the detail on console
	client := new(client.AccountClient).Init(config.AccessKey, "", host)
	resp, err := client.GetAccountInfo()
	if err != nil {
		applogger.Error("Get account error: %s", err)
	} else {
		applogger.Info("Get account, count=%d", len(resp))
		for _, result := range resp {
			applogger.Info("account: %+v", result)
		}
	}
}

func TestWs(t *testing.T) {
	// Initialize a new instance
	client := new(orderwebsocketclient.SubscribeOrderWebSocketV2Client).Init(config.AccessKey, "", config.Host)

	// Set the callback handlers
	client.SetHandler(
		// Connected handler
		func(resp *auth.WebSocketV2AuthenticationResponse) {
			if resp.IsSuccess() {
				// Subscribe if authentication passed
				client.Subscribe("btcusdt", "1149")
			} else {
				applogger.Info("Authentication error, code: %d, message:%s", resp.Code, resp.Message)
			}
		},
		// Response handler
		func(resp interface{}) {
			subResponse, ok := resp.(order.SubscribeOrderV2Response)
			if ok {
				if subResponse.Action == "sub" {
					if subResponse.IsSuccess() {
						applogger.Info("Subscription topic %s successfully", subResponse.Ch)
					} else {
						applogger.Error("Subscription topic %s error, code: %d, message: %s", subResponse.Ch, subResponse.Code, subResponse.Message)
					}
				} else if subResponse.Action == "push" {
					if subResponse.Data != nil {
						o := subResponse.Data
						applogger.Info("Order update, event: %s, symbol: %s, type: %s, status: %s",
							o.EventType, o.Symbol, o.Type, o.OrderStatus)
					}
				}
			} else {
				applogger.Warn("Received unknown response: %v", resp)
			}
		})

	// Connect to the server and wait for the handler to handle the response
	client.Connect(true)
}
