package huobi_impl

import (
	"github.com/adshao/go-binance/v2"
	"spot/libs/nums"
	"spot/pkg/order/model"
	"strings"
	"time"
)

func CachePriceLimitForBinance(list []binance.Symbol) (result []model.PriceLimit) {
	//获取当前支持对冲币种
	//cMap := make(map[string]proto.Coin)
	//coins := commonsrv.GetExchangeCoinSymbols()
	//for name, v := range coins {
	//	cMap[name] = v
	//}
	//log.Info("list",zap.Any("list",list))
	for _, symbol := range list {
		//if _, ok := cMap[symbol.Symbol]; ok {
		//
		//}
		if strings.HasSuffix(symbol.Symbol, "USDT") {
			pf := symbol.PriceFilter()
			ppf := symbol.PercentPriceFilter()
			pl := model.PriceLimit{
				CoinName: symbol.BaseAsset,
				Step:     nums.NewFromString(pf.TickSize),
				MinPrice: nums.NewFromString(pf.MinPrice),
				MaxPrice: nums.NewFromString(pf.MaxPrice),
				Up:       nums.NewFromString(ppf.MultiplierUp),
				Down:     nums.NewFromString(ppf.MultiplierDown),
				Ts:       time.Now(),
			}
			result = append(result, pl)
		}

	}

	return
}
