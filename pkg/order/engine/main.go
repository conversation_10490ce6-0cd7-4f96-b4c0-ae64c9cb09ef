package engine

import (
	"errors"
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
	"spot/pkg/order/service"
)

func Entrust(arg *proto.ThirdOrderPlaceArg) (id int64, err error) {
	//调用第三方接口
	client, ok := Hub.Get(arg.MarketSource)
	if !ok {
		err = errors.New("暂不支持该来源")
		log.Info("当前来源无效，不再执行", zap.Any("source", arg.MarketSource))
		return
	}
	reply, err := client.Place(arg)

	if err != nil {
		log.Error("Entrust fail", zap.Error(err), zap.Any("arg", arg))
		return
	}
	log.Info("收到第三方订单", zap.Any("arg", arg), zap.Any("reply", reply))
	if reply.Id > 0 {
		err = service.MarkThirdOrder(arg.OrderId, reply.Id)
		if err != nil {
			log.Error("Entrust service.MarkThirdOrder fail", zap.Error(err))
			return
		}
		//标记第三方订单号
		id = reply.Id
		cache.SetEntrustOrderForThirdId(arg)

		if reply.Status.IsFinished() {
			order := proto.EntrustOrder{
				ID:           arg.OrderId,
				UserID:       arg.UserId,
				ContractCode: arg.ContractCode,
				EntrustType:  arg.EntrustType,
				Side:         arg.Side,
				Price:        arg.Price,
				Volume:       arg.Volume,
				Money:        arg.Money,
				ThirdOrderId: reply.Id,
				MarketSource: arg.MarketSource,
				State:        reply.Status,
			}
			log.Info("下单后直接返回，状态完成", zap.Any("order", order), zap.Any("reply", reply))
			go DealOrderFinish(order)
		}
	}
	return
}

func Cancel(arg *proto.ThirdOrderCancelArg) (err error) {
	//调用第三方接口
	client, ok := Hub.Get(arg.MarketSource)
	if !ok {
		err = errors.New("暂不支持该来源")
		log.Info("当前来源无效，不再执行", zap.Any("source", arg.MarketSource))
		return
	}
	err = client.Cancel(arg.ContractCode, arg.ThirdOrderId)
	if err != nil {
		log.Error("Entrust fail", zap.Error(err), zap.Any("arg", arg))
		return
	}
	//标记第三方订单号
	err = service.MarkThirdOrderMark(arg.OrderId)
	if err != nil {
		log.Error("Cancel 标记第三方状态为已上报失败", zap.Error(err))
		return
	}
	return nil
}

func DealOrderFinish(order proto.EntrustOrder) {
	log.Info("开始查询订单成交明细，并处理订单结束", zap.Any("data", order))
	client, ok := Hub.Get(order.MarketSource)
	if !ok {
		log.Info("当前来源无效，不再执行", zap.Any("source", order.MarketSource))
		return
	}
	//symbol := strings.ReplaceAll(order.ContractCode, "/", "")
	result, err := client.GetMyTrades(order.ContractCode, order.ThirdOrderId)
	if err != nil {
		log.Error("dealOrderFinish GetMyTrades fail", zap.Error(err))
		return
	}
	for _, trade := range result {
		t := trade
		service.DealOrderThisTrade(&t)
	}
	mark := define.TradeMarkWithOrderDefault
	if order.State == define.OrderStatusFull {
		mark = define.TradeMarkWithTradeFinish
	}
	service.DealOrderFinish(order.ThirdOrderId, mark)
}
