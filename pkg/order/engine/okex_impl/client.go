package okex_impl

import (
	"errors"
	"go.uber.org/atomic"
	"go.uber.org/zap"
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/nums"
	"spot/libs/okx"
	"spot/libs/proto"
	"spot/pkg/order/model"
	"spot/pkg/order/service"
	"strings"
	"time"
)

const revWin = 10000

type Client struct {
	id      define.MarketSource
	account *proto.ThirdMarketConfig
	client  *okx.RESTClient
	lock    atomic.Bool
	isLimit atomic.Int64
	ws      *WsClient
}

var _ model.Client = (*Client)(nil)

func NewClient(id define.MarketSource, cfg *proto.ThirdMarketConfig) *Client {
	if cfg == nil {
		panic("对冲配置为空")
	}
	if cfg.IsTest {
		okx.SetGlobal("https://aws.okx.com", time.Second*5, true)
	} else {
		okx.SetGlobal("https://aws.okx.com", time.Second*5, false)
	}
	api := okx.NewRESTClient(okx.Config{
		ApiKey:     cfg.PublicKey,
		PassPhrase: cfg.HitPhrase,
		SecretKey:  cfg.PrivateKey,
	})
	client := &Client{id: id, account: cfg, client: api}
	go func() {
		t := time.Tick(30 * time.Second)
		for range t {
			last := client.isLimit.Load()
			if client.isLimit.Load() > 0 {
				if time.Now().Unix()-last > 20 {
					client.isLimit.Store(0)
				}
			}
		}
	}()
	return client
}

func NewClientWithWs(id define.MarketSource, cfg *proto.ThirdMarketConfig) *Client {
	if cfg == nil {
		panic("对冲配置为空")
	}
	if cfg.IsTest {
		okx.SetGlobal("https://aws.okx.com", time.Second*5, true)
	} else {
		okx.SetGlobal("https://aws.okx.com", time.Second*5, false)
	}
	api := okx.NewRESTClient(okx.Config{
		ApiKey:     cfg.PublicKey,
		PassPhrase: cfg.HitPhrase,
		SecretKey:  cfg.PrivateKey,
	})
	client := &Client{id: id, account: cfg, client: api}
	client.ws = NewWsClient(client)
	client.ws.Start()
	go func() {
		t := time.Tick(30 * time.Second)
		for range t {
			last := client.isLimit.Load()
			if client.isLimit.Load() > 0 {
				if time.Now().Unix()-last > 20 {
					client.isLimit.Store(0)
				}
			}
		}
	}()
	return client
}

type WsClient struct {
	client *Client
	ws     *okx.MarketWsClient
	isOk   bool
}

func NewWsClient(client *Client) *WsClient {
	wsc := &WsClient{client: client}
	wsc.ws = okx.NewMarketWsClient(&okx.Config{
		ApiKey:     client.account.PublicKey,
		PassPhrase: client.account.HitPhrase,
		SecretKey:  client.account.PrivateKey,
	}, func(data []byte) {
		log.Info("收到websocket 订单数据", zap.String("data", string(data)))
		d := new(okx.WsResponseChannel)
		e := json.Unmarshal(data, d)
		if e != nil {
			log.Error("okex ws unmarshal fail", zap.Error(e))
			return
		}
		wsc.dealWsUpdate(d)
	})
	wsc.ws.Start()
	return wsc
}

func (w *WsClient) dealWsUpdate(d *okx.WsResponseChannel) {
	for _, o := range d.Data {
		order := model.ClientOrder{
			ID:   nums.String2Int64(o.OrdId),
			Code: o.InstId,
		}
		switch o.State {
		case okx.OrderTypeCanceled:
			order.State = define.OrderStatusDefault
		case okx.OrderTypePartiallyFilled:
			order.State = define.OrderStatusPartCancel
		case okx.OrderTypeFilled:
			order.State = define.OrderStatusFull
		}
		if order.State.IsFinished() {
			w.dealOrderFinish(&order)
		}
	}
}

func (w *WsClient) dealOrderFinish(order *model.ClientOrder) {
	result, err := w.client.GetMyTrades(order.Code, order.ID)
	if err != nil {
		log.Error("dealOrderFinish GetMyTrades fail", zap.Error(err))
		return
	}
	for _, trade := range result {
		t := trade
		service.DealOrderThisTrade(&t)
	}
	mark := define.TradeMarkWithOrderDefault
	if order.State == define.OrderStatusFull {
		mark = define.TradeMarkWithTradeFinish
	}
	service.DealOrderFinish(order.ID, mark)
}

func (w *WsClient) Start() {
	//订阅私有order
	list := []okx.Channel{{
		Channel: "orders",
		InstId:  "SPOT",
	}}
	w.ws.Subscript(list)
}

func (c *Client) GetAccount() (list []model.ThirdAccountBalance, err error) {
	defer func() {
		c.dealError(err)
	}()
	if c.isLimit.Load() > 0 {
		err = model.ErrApiLimit
		return
	}
	res, err := c.GetApi().GetBalance("")
	if err != nil {
		log.Error("GetAccount fail", zap.Error(err))
		return
	}
	for _, balance := range res.Details {
		hb := model.ThirdAccountBalance{
			SourceId: c.account.SourceId,
			Coin:     balance.Ccy,
			Balance:  nums.NewFromString(balance.CashBal),
		}
		list = append(list, hb)
	}
	return
}

func (c *Client) ReloadConfig() {
	if c != nil {
		c.reloadConfig()
	}
}

func (c *Client) GetApi() *okx.RESTClient {
Retry:
	if c.lock.Load() {
		time.Sleep(50 * time.Millisecond)
		goto Retry
	}
	return c.client
}

func (c *Client) ListOrderHistory(data interface{}) (list []model.ClientOrder, err error) {
	defer func() {
		c.dealError(err)
	}()
	if c.isLimit.Load() > 0 {
		err = model.ErrApiLimit
		return
	}
	arg, ok := data.(model.OrderHistoryArg)
	if !ok {
		log.Error(" 查询订单 参数出错 not model.OrderHistoryArg")
		err = errors.New("请求参数无效")
		return
	}
	instId := strings.ReplaceAll(arg.Code, "/", "-")
	orders, err := c.GetApi().GetOrderHistory(instId, string(okx.InstrumentTypeSPOT), nums.Int64String(arg.LastOrderId))
	if err != nil {
		log.Error("ListOrderHistory fail", zap.Error(err))
		return
	}
	log.Info("查询全部订单", zap.Any("arg", arg), zap.Any("data", orders))
	for _, o := range orders {
		order := model.ClientOrder{
			ID:   nums.String2Int64(o.OrdId),
			Code: o.InstId,
		}
		switch o.State {
		case okx.OrderTypeCanceled:
			order.State = define.OrderStatusDefault
		case okx.OrderTypePartiallyFilled:
			order.State = define.OrderStatusPartCancel
		case okx.OrderTypeFilled:
			order.State = define.OrderStatusFull
		}
		list = append(list, order)
	}
	return
}

func (c *Client) Place(order *proto.ThirdOrderPlaceArg) (reply model.ClientPlaceReply, err error) {
	defer func() {
		c.dealError(err)
	}()
	if c.isLimit.Load() > 0 {
		log.Info("币安当前请求地址受限，本次不再强求", zap.Any("order", order))
		err = model.ErrApiLimit
		return
	}
	code := strings.ReplaceAll(order.ContractCode, "/", "-")
	var side okx.OrderSide
	if order.Side == define.OrderBuy {
		side = okx.OrderSideBuy
	} else {
		side = okx.OrderSideSell
	}
	amount := order.Volume
	if order.Side == define.OrderBuy && order.EntrustType == define.EntrustTypeMarket {
		amount = order.Money
	}
	var orderType okx.OrderType
	if order.EntrustType == define.EntrustTypeMarket {
		orderType = okx.OrderTypeMarket
	} else {
		orderType = okx.OrderTypeLimit
	}
	rOrder, err := c.GetApi().Place(code, nums.Int64String(order.OrderId), amount.String(), order.Price.String(), side, orderType)
	if err != nil {
		log.Error("Binance Place fail", zap.Error(err))
		//apiErr, ok := err.(*common.APIError)
		//if ok && (apiErr.Code == -3041 || apiErr.Code == -2010) {
		//	//处理下单余额不足报警
		//	service.SendPlateBalanceLackMsg(order)
		//}
		return
	}
	log.Info("下单返回", zap.Any("data", rOrder))
	reply.Id = nums.String2Int64(rOrder.OrdId)
	return
}

func (c *Client) Cancel(code string, orderId int64) (err error) {
	log.Info("币安开始撤销第三方订单", zap.String("code", code), zap.Int64("orderId", orderId))
	defer func() {
		c.dealError(err)
	}()
	if c.isLimit.Load() > 0 {
		err = model.ErrApiLimit
		return
	}
	code = strings.ReplaceAll(code, "/", "-")
	_, err = c.GetApi().Cancel(code, nums.Int64String(orderId))
	if err != nil {
		log.Error("Cancel fail", zap.Error(err), zap.String("code", code), zap.Int64("orderId", orderId))
		return
	}
	return
}

func (c *Client) GetHegeConfig() *proto.ThirdMarketConfig {
	return c.account
}

func (c *Client) reloadConfig() {
	cfg := service.GetMarketSourceConfig(define.MarketSourceOkex)
	if cfg == nil {
		return
	}
	if cfg.PrivateKey == c.account.PrivateKey && cfg.PublicKey == cfg.PublicKey {
		return
	}
	c.lock.Store(true)
	defer c.lock.Store(false)
	c.account = cfg
}

func (c *Client) dealError(err error) {

}

func (c *Client) GetMyTrades(symbol string, orderId int64) (result []model.ClientTrade, err error) {
	defer func() {
		c.dealError(err)
	}()
	code := strings.ReplaceAll(symbol, "/", "-")
	list, err := c.GetApi().GetOrderTrades(code, string(okx.InstrumentTypeSPOT), nums.Int64String(orderId))
	if err != nil {
		log.Error("GetMyTrades fail", zap.Error(err))
		return
	}
	for _, item := range list {
		ct := model.ClientTrade{
			TradeId:      nums.String2Int64(item.TradeId),
			ThirdOrderId: nums.String2Int64(item.OrdId),
			Code:         item.InstId,
			TradePrice:   nums.NewFromString(item.FillPx),
			TradeAmount:  nums.NewFromString(item.FillSz),
			IsMaker:      item.ExecType == "M",
			CreateTime:   time.UnixMilli(nums.String2Int64(item.Ts)),
			MarketSource: define.MarketSourceBinance,
		}
		ct.TradeMoney = ct.TradePrice.Mul(ct.TradeAmount)
		result = append(result, ct)
	}
	return
}
