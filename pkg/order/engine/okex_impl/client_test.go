package okex_impl

import (
	"go.uber.org/zap"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/nums"
	"spot/libs/proto"
	"spot/pkg/order/model"
	"testing"
	"time"
)

var _exchangeCfg = &proto.ThirdMarketConfig{
	SourceId:   define.MarketSourceBinance,
	Name:       "binance",
	PublicKey:  "ONogSOtzoIGM8fehwSJBa5KU1K18VMihqOLwfVDWUJDqrrG8P3BWVGQrZk5OSdWd",
	PrivateKey: "kUWKa9TOok64ZTtDEg58WFUJmMJDYCtfuLrkcDGTlqYCJSrcl6Mj9iCO24up5u5C",
	//PublicKey:  "5nIRlBp0Bz5XrBPSkZz3YWROEKFtD68dvZ2OXoiJzuGSxe2i0UEtgcoWXygwjoT5",
	//PrivateKey: "4Gmkq66PS3Qc5YBIklEtkKyU6M4ZJE9nhMfFuO0kCAuzvCGHvNv9MpCk35E8MXzt",
	HitPhrase:  "",
	CreateTime: time.Now(),
	UpdateTime: time.Now(),
	IsTest:     true,
}

func TestClient(t *testing.T) {
	log.InitLogger("info", "info", false)
	client := NewClient(1, _exchangeCfg)
	list, err := client.GetAccount()
	if err != nil {
		t.Log(err)
		return
	}
	t.Log(list)

}

func TestClientPlaceMarket(t *testing.T) {
	log.InitLogger("info", "info", false)
	client := NewClient(1, _exchangeCfg)

	arg := &proto.ThirdOrderPlaceArg{
		MarketSource: define.MarketSourceBinance,
		ContractCode: "BTC/USDT",
		EntrustType:  define.EntrustTypeMarket,
		Side:         "B",
		//Volume:       nums.NewFromFloat(0.5),
		Money: nums.NewFromFloat(1000),
	}
	id, err := client.Place(arg)
	if err != nil {
		t.Log(err)
		return
	}
	t.Log("获取下单的订单id", id)
}

func TestClientPlaceLimit(t *testing.T) {
	log.InitLogger("info", "info", false)
	client := NewClient(1, _exchangeCfg)

	arg := &proto.ThirdOrderPlaceArg{
		ContractCode: "BTC/USDT",
		EntrustType:  define.EntrustTypeLimit,
		Side:         "S",
		Price:        nums.NewFromFloat(48650),
		Volume:       nums.NewFromFloat(0.02),
		MarketSource: define.MarketSourceBinance,
	}
	id, err := client.Place(arg)
	if err != nil {
		t.Log(err)
		return
	}
	t.Log("获取下单的订单id", id)
}

func TestClientCancel(t *testing.T) {
	log.InitLogger("info", "info", false)
	client := NewClient(1, _exchangeCfg)

	err := client.Cancel("BTC-USDT", 2225346)
	if err != nil {
		t.Log(err)
		return
	}
}

func TestClientHistory(t *testing.T) {
	log.InitLogger("info", "info", false)
	client := NewClient(1, _exchangeCfg)
	arg := model.OrderHistoryArg{
		Code:        "BTCUSDT",
		LastOrderId: 0,
	}
	id, err := client.ListOrderHistory(arg)
	if err != nil {
		t.Log(err)
		return
	}
	t.Logf("%+v", id)
	log.Info("list", zap.Any("list", id))
}

func TestClientMyTrades(t *testing.T) {
	log.InitLogger("info", "info", false)
	client := NewClient(1, _exchangeCfg)

	id, err := client.GetMyTrades("BTCUSDT", 718346)
	if err != nil {
		t.Log(err)
		return
	}
	t.Log("获取下单的订单id", id)
}
