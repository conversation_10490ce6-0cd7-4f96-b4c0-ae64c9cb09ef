package engine

import (
	"go.uber.org/zap"
	"spot/libs/define"
	"spot/libs/log"
	"spot/pkg/order/engine/binance_impl"
	"spot/pkg/order/model"
	"spot/pkg/order/service"
	"sync"
	"time"
)

var Hub ClientHub

func InitApiEngineHub() {
	Hub.engineHub = make(map[define.MarketSource]model.Client)
	//初始化binance对冲通道
	cfg := service.GetMarketSourceConfig(define.MarketSourceBinance)
	log.Info("cfg", zap.Any("binance cfg", cfg))
	if cfg != nil {
		if cfg.PublicKey != "" && cfg.PrivateKey != "" {
			Hub.Add(define.MarketSourceBinance, binance_impl.NewClientWithWs(define.MarketSourceBinance, cfg))
		}
	}
	Hub.Run()
}

type ClientHub struct {
	engineHub map[define.MarketSource]model.Client
	sync.RWMutex
}

func (h *ClientHub) Get(id define.MarketSource) (client model.Client, ok bool) {
	h.RLock()
	defer h.RUnlock()
	client, ok = h.engineHub[id]
	return
}

func (h *ClientHub) Add(id define.MarketSource, client model.Client) {
	h.Lock()
	defer h.Unlock()

	h.engineHub[id] = client
}

func (h *ClientHub) Run() {
	//定时重载配置
	//go h.reloadConfig()
	//账户查看
	go h.checkAccount()
}

func (h *ClientHub) reloadConfig() {
	//定时重载配置
	t := time.Tick(1 * time.Minute)
	for range t {
		for source := range h.engineHub {
			client, ok := h.Get(source)
			if ok {
				client.ReloadConfig()
			}
		}
	}
}

func (h *ClientHub) checkAccount() {
	t := time.Tick(2 * time.Minute)
	for range t {
		for source := range h.engineHub {
			client, ok := h.Get(source)
			if ok {
				list, err := client.GetAccount()
				if err != nil {
					log.Error("checkAccount client.GetAccount fail", zap.Error(err))
					continue
				}
				log.Info("查询到当前对冲源账户信息", zap.Int("source", int(source)), zap.String("name", source.GetHedgeSource()), zap.Any("list", list))
				//checkPlateAccount(source, list)
			}
		}
	}
}
