package model

import (
	"github.com/shopspring/decimal"
	"spot/libs/define"
	"spot/libs/proto"
	"time"
)

type Client interface {
	ReloadConfig()
	GetAccount() ([]ThirdAccountBalance, error)                                 //账户信息
	Place(order *proto.ThirdOrderPlaceArg) (ClientPlaceReply, error)            // 下单
	Cancel(code string, orderId int64) error                                    // 撤销订单
	ListOrderHistory(data interface{}) (list []ClientOrder, err error)          //查询历史订单
	GetMyTrades(symbol string, orderId int64) (result []ClientTrade, err error) //查询指定订单成交明细
}

type ClientOrder struct {
	ID          int64             `json:"id"`
	Code        string            `json:"code"`
	TradePrice  decimal.Decimal   `db:"trade_price" json:"trade_price"`   //成交价格
	TradeAmount decimal.Decimal   `db:"trade_amount" json:"trade_amount"` //成交数量
	TradeMoney  decimal.Decimal   `db:"trade_money" json:"trade_money"`   //成交金额
	State       define.OrderState `db:"state" json:"state"`
	UpdateTime  time.Time         `db:"update_time" json:"update_time"` //更新时间
	CreateTime  time.Time         `db:"create_time" json:"create_time"` //创建时间
}

type ClientTrade struct {
	TradeId       int64               `json:"trade_id"`
	ThirdOrderId  int64               `json:"third_order_id"`
	ClientOrderId int64               `json:"client_order_id"`
	Code          string              `json:"code"`
	TradePrice    decimal.Decimal     ` json:"trade_price"` //成交价格
	TradeAmount   decimal.Decimal     `json:"trade_amount"` //成交数量
	TradeMoney    decimal.Decimal     `json:"trade_money"`  //成交金额
	IsMaker       bool                `json:"is_maker"`
	Fee           decimal.Decimal     `json:"fee"`
	FeeAsset      decimal.Decimal     `json:"fee_asset"`
	CreateTime    time.Time           `json:"create_time"` //创建时间
	MarketSource  define.MarketSource `json:"market_source"`
	IsWs          bool                `json:"is_ws"`
}

type ThirdAccountBalance struct {
	SourceId define.MarketSource
	Coin     string
	Balance  decimal.Decimal
	Lock     decimal.Decimal
	CanTrade bool
}

type ClientPlaceReply struct {
	Id     int64             `json:"id"` //下单id
	Status define.OrderState `json:"status"`
	list   []ClientTrade
}
