package model

import "spot/libs/define"

type WarnType int

const (
	WarnExchangePlateAccountLackTitle WarnType = iota + 1 //平台兑换账户不足
	WarnExchangePlateAccountLack                          //平台兑换账户不足
	WarnHedgeAccountLackTitle                             //对冲平台币种资产小于报警值
	WarnHedgeAccountLack                                  //对冲平台币种资产小于报警值
	WarnHedgePlaceAccountLackTitle                        //对冲时余额不足
	WarnHedgePlaceAccountLack                             //对冲时余额不足
	WarnHedgeAccountException                             //对冲账户api异常
	WarnHedgeBuyDiff                                      //小时买出交易额报警
	WarnHedgeSellDiff                                     //小时卖出交易额报警
)

var WarnMsgLanguageMap = map[define.ReqLang]map[WarnType]string{
	define.ReqLangCN: {
		WarnHedgePlaceAccountLackTitle: "%s [%s] 现货账户余额不足",
		WarnHedgePlaceAccountLack:      "%s [%s]【%s】【%s】余额不足。",
	},
}

func GetWarnContent(warnType WarnType, lange define.ReqLang) string {
	return WarnMsgLanguageMap[lange][warnType]
}
