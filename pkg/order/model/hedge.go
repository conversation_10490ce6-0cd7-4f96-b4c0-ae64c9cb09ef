package model

import (
	"errors"
	"github.com/shopspring/decimal"
	"time"
)

var ErrApiLimit = errors.New("api is restricted")

type OrderHistoryArg struct {
	Code        string
	LastOrderId int64
}

type HedgeStat struct {
	HedgeSource   int             `db:"hedge_source" json:"hedge_source"`
	Side          string          `db:"side" json:"side"`
	HedgeMoney    decimal.Decimal `db:"hedge_money" json:"hedge_money"`
	ExchangeMoney decimal.Decimal `db:"exchange_money" json:"exchange_money"`
}

type PriceLimit struct {
	CoinName string          `json:"coin_name"`
	Step     decimal.Decimal `json:"step"`
	MinPrice decimal.Decimal `json:"min_price"`
	MaxPrice decimal.Decimal `json:"max_price"`
	Up       decimal.Decimal `json:"up"`
	Down     decimal.Decimal `json:"down"`
	Ts       time.Time       `json:"ts"`
}
