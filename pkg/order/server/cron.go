package server

import (
	"github.com/robfig/cron"
	"spot/pkg/order/task"
)

const (
	assetFunding      = "0 0 %v,%v,%v * * *" // 秒,分,时,日,月,周 每分钟执行
	floatProfitUpdate = "0 */1 * * * *"      // 秒,分,时,日,月,周 每分钟执行
	everyMinute       = "0 * * * * *"        // 秒,分,时,日,月,周 每分钟执行
	dealerSummary     = "1 0 0 * * *"        // 秒,分,时,日,月,周
	dealSettle        = "1 0 0 * * *"        // 秒,分,时,日,月,周
)

func RunCronTask() {
	// 启动运行

	c := cron.New()
	c.AddFunc("*/15 * * * * * ", func() {
		task.DealHedgeUnfinishedOrderWithMarketSource()
	})
	c.Start()
}
