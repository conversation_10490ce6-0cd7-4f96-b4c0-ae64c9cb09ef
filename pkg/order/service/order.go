package service

import (
	"go.uber.org/zap"
	"spot/libs/database"
	"spot/libs/log"
)

func MarkThirdOrder(orderId, thirdOrderId int64) (err error) {
	log.Info("开始标记订单", zap.Int64("orderId", orderId), zap.Int64("thirdOrderId", thirdOrderId))
	err = database.UpdateOrderForThirdOrder(nil, orderId, thirdOrderId)
	if err != nil {
		log.Error("MarkThirdOrder fail ", zap.Error(err), zap.Any("orderId", orderId), zap.Any("thirdOrderId", thirdOrderId))
		return
	}
	return
}

func MarkThirdOrderMark(orderId int64) (err error) {
	err = database.UpdateThirdMark(nil, orderId)
	if err != nil {
		log.Error("MarkThirdOrderMark fail ", zap.Error(err), zap.Any("orderId", orderId))
		return
	}
	return
}
