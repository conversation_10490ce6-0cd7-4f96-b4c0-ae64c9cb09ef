package service

import (
	"github.com/go-redis/redis"
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
)

//获取指定兑换指定对冲配置
func GetMarketSourceConfig(channelId define.MarketSource) *proto.ThirdMarketConfig {
	lm := ListMarketSourceConfig()
	if lm == nil {
		return nil
	}
	config, ok := lm[channelId]
	if !ok {
		return nil
	}
	return &config
}

//查询兑换对冲配置
func ListMarketSourceConfig() map[define.MarketSource]proto.ThirdMarketConfig {
	list, err := cache.ListThirdMarketHedgeConfig()
	if err != nil {
		if err == redis.Nil {
			list, err = database.GetThirdMarketConfig(nil)
			if err != nil {
				log.Error("ListMarketSourceConfig database.GetMarketSourceConfig err", zap.Error(err))
				return nil
			}
			if len(list) > 0 {
				cache.SetThirdMarketConfig(list)
			}
		}
	}
	if len(list) == 0 {
		return nil
	}
	mk := make(map[define.MarketSource]proto.ThirdMarketConfig)
	for _, hedgeConfig := range list {
		mk[hedgeConfig.SourceId] = hedgeConfig
	}
	return mk
}
