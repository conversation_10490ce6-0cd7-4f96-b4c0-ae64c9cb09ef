package service

import (
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/commonsrv"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
	"spot/pkg/order/model"
)

func DealOrderThisTrade(trade *model.ClientTrade) {
	var thirdOrder *proto.ThirdOrderPlaceArg
	var order *proto.EntrustOrder
	var code string
	var err error
	var userId, orderId int64
	thirdOrder, err = cache.GetEntrustOrderForThirdId(trade.MarketSource, trade.ThirdOrderId)
	if thirdOrder == nil || err != nil {
		order, err = database.GetUserEntrustOrderByThirdOrderId(nil, trade.ThirdOrderId)
		if err != nil {
			log.Error("DealOrderThisTrade fail", zap.Error(err), zap.Any("thirdOrderId", trade.ThirdOrderId), zap.Any("data", trade))
			return
		}
		if order == nil {
			log.Info("没有查询到该订单", zap.Any("data", trade))
			return
		}
		if order.IsFinished() {
			return
		}
		userId, orderId, code = order.UserID, order.ID, order.ContractCode
	} else {
		userId, orderId, code = thirdOrder.UserId, thirdOrder.OrderId, thirdOrder.ContractCode
	}

	var mr = &proto.MRecord{
		ThirdOrderId: trade.ThirdOrderId,
		OrderId:      orderId,
		MatchID:      trade.TradeId,
		TradeId:      trade.TradeId,
		Code:         code,
		DealPrice:    trade.TradePrice,
		DealVolume:   trade.TradeAmount,
		MatchTime:    trade.CreateTime,
		IsMaker:      trade.IsMaker,
		UserID:       userId,
	}
	s := cache.IsThirdOrderTradeIdExist(trade.ThirdOrderId, trade.TradeId)
	if !s {
		commonsrv.DealUserTrade(mr)
	}
}

func DealOrderFinish(thirdOrderId int64, tradeMark int) {
	order, err := database.GetUserEntrustOrderByThirdOrderId(nil, thirdOrderId)
	if err != nil {
		log.Error("DealOrderThisTrade fail", zap.Error(err), zap.Any("thirdOrderId", thirdOrderId))
		return
	}
	if order != nil {
		mCancel := &proto.MCancel{
			OrderId:    order.ID,
			CancelMark: define.CancelOrderFinish,
			TradeMark:  tradeMark,
		}
		commonsrv.DealContractAccountOrderCanceled(mCancel)
	}
}
