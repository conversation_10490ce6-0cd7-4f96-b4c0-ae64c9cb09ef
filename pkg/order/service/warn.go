package service

import (
	"fmt"
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/conf"
	"spot/libs/crypto"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/msg"
	"spot/libs/nums"
	"spot/libs/proto"
	"spot/libs/utils"
	"spot/pkg/order/model"
	"strings"
	"time"
)

func GetHedgeWarnLanguage() define.ReqLang {
	return define.ReqLangCN
}

func SendExchangeWarnMsg(warnType model.WarnType, title, content string) {
	log.Info("开始对冲报警", zap.Any("subject", title), zap.Any("content", content), zap.Any("emails", conf.ExchangeCautionReceiver()))
	if len(conf.ExchangeCautionReceiver()) == 0 {
		log.Info("开始发送对冲相关报警,接收邮箱联系人数量为0,本次不再处理", zap.Any("subject", title), zap.Any("content", content), zap.Any("emails", conf.ExchangeCautionReceiver()))
		return
	}
	contentHash := crypto.Md5(content)
	key := utils.StrBuilderBySep(":", nums.Int2String(int(warnType)), contentHash)
	if !cache.SetRedisLockWithExp(15*time.Minute, key) {
		log.Info("开始发送对冲报警，15分钟发送过该报警，本次不再发发送", zap.Any("subject", title), zap.Any("content", content))
		return
	}
	go msg.SendExchangeCautionEmail(title, content)
}

func SendPlateBalanceLackMsg(order *proto.ThirdOrderPlaceArg) {
	coins := strings.Split(order.ContractCode, "/")
	var lackCoin string
	if order.Side == define.OrderBuy {
		lackCoin = coins[1]
	} else {
		lackCoin = coins[0]
	}
	name := order.MarketSource.GetHedgeSource()
	ft := model.GetWarnContent(model.WarnHedgePlaceAccountLackTitle, GetHedgeWarnLanguage())
	fc := model.GetWarnContent(model.WarnHedgePlaceAccountLack, GetHedgeWarnLanguage())
	title := fmt.Sprintf(ft, conf.MailPrefix(), name)
	content := fmt.Sprintf(fc, conf.MailPrefix(), name, time.Now().Format(define.TimeFormatNormal), lackCoin)
	SendExchangeWarnMsg(model.WarnHedgePlaceAccountLack, title, content)
}
