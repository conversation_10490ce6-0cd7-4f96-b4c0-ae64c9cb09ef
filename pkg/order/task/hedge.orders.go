package task

import (
	"sync"

	"go.uber.org/zap"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
	"spot/pkg/order/engine"
	"spot/pkg/order/model"
)

var _dealHedgeUnfinishedOrders sync.Mutex

func DealHedgeUnfinishedOrderWithMarketSource() {
	if !_dealHedgeUnfinishedOrders.TryLock() {
		return
	}
	defer _dealHedgeUnfinishedOrders.Unlock()

	runID := database.NextID()

	//查询本来源未完成的订单
	list, err := database.ListEntrustUnFinishOrdersForThirdOrder(nil)
	if err != nil {
		log.Error("DealHedgeUnfinishedOrderWithMarketSource database.ListEntrustUnFinishOrdersByMarketSource fail", zap.Int64("runId", runID), zap.Error(err))
		return
	}
	log.Info("开始处理第三方未完成的订单", zap.Int("当前未完成订单数", len(list)))

	if len(list) == 0 {
		return
	}

	cMap := make(map[define.MarketSource][]proto.EntrustOrder)
	for _, order := range list {
		list := cMap[order.MarketSource]
		list = append(list, order)
		cMap[order.MarketSource] = list
	}

	for ms, list := range cMap {
		if len(list) > 0 {
			DealUnFinishOrdersWithMarketSource(ms, list)
		}
	}
}

func DealUnFinishOrdersWithMarketSource(ms define.MarketSource, list []proto.EntrustOrder) {
	if ms == define.MarketSourceOkex {
		dealHedgeOrderStatus(ms, "", list)
	} else {
		cMap := make(map[string][]proto.EntrustOrder)
		for _, order := range list {
			l := cMap[order.ContractCode]
			l = append(l, order)
			cMap[order.ContractCode] = l
		}
		for code, orders := range cMap {
			dealHedgeOrderStatus(ms, code, orders)
		}
	}
}

func dealHedgeOrderStatus(ms define.MarketSource, code string, orders []proto.EntrustOrder) {
	log.Info("开始处理币安未完成合约", zap.String("code", code), zap.Any("data", orders))
	if len(orders) == 0 {
		log.Debug("合约未完成订单数量为0，本次不再查询合约订单", zap.String("code", code))
		return
	}
	hedgeMap := make(map[int64]proto.EntrustOrder)
	for _, order := range orders {
		hedgeMap[order.ThirdOrderId] = order
	}
	curHedgeId := orders[0].ThirdOrderId
	if curHedgeId > 0 {
		curHedgeId = curHedgeId - 1
	}
	//调用api查询所有订单信息
	client, ok := engine.Hub.Get(ms)
	if !ok {
		log.Info("当前来源无效，不再执行", zap.Any("source", ms))
		return
	}
	arg := model.OrderHistoryArg{Code: code, LastOrderId: curHedgeId}
	list, err := client.ListOrderHistory(arg)
	if err != nil {
		log.Error("binance 调用查询所有订单信息失败", zap.Error(err), zap.Any("arg", arg))
		return
	}
	var hedgeOverOrder []proto.EntrustOrder //本次对冲完成的订单
	for _, order := range list {
		if order.State.IsFinished() {
			if element, ok := hedgeMap[order.ID]; ok {
				element.State = order.State
				element.TradePrice = order.TradePrice
				element.TradeVolume = order.TradeAmount
				element.TradeMoney = order.TradeMoney
				hedgeOverOrder = append(hedgeOverOrder, element)
			}
		}
	}

	for _, order := range hedgeOverOrder {
		go engine.DealOrderFinish(order)
	}
}
