package main

import (
	"encoding/gob"
	"fmt"
	"os"
	"spot/libs/cache"
	"spot/libs/conf"
	"spot/libs/database"
	"spot/libs/log"
	"spot/libs/msg"
	"spot/libs/pprof"
	"spot/libs/proto"
	"spot/libs/xrpcclient/core_rpc"
	"spot/libs/xsignal"
	"spot/pkg/planorder/model"
	"spot/pkg/planorder/server"
	"spot/pkg/planorder/service"
)

var (
	gitHash   string
	gitDigest string
	gitBranch string
	buildUser string
	buildTime string
	goVersion string
)

func main() {
	args := os.Args
	if len(args) == 2 && (args[1] == "--version" || args[1] == "-v") {
		fmt.Printf("Git Branch: %s \n", gitBranch)
		fmt.Printf("Git Commit Hash: %s \n", gitHash)
		fmt.Printf("Git Commit Digest: %s \n", gitDigest)
		fmt.Printf("Build User: %s \n", buildUser)
		fmt.Printf("Build TimeStamp: %s \n", buildTime)
		fmt.Printf("GoLang Version: %s \n", goVersion)
		return
	}

	// 初始化配置文件
	conf.Init()

	// 初始化日志
	log.InitLogger(conf.LogFile(), conf.LogLevel(), conf.LogOnlyFile())

	// 初始化短信
	msg.InitNewMsg(conf.DebugEnv(), conf.MsgServerURL(), conf.MsgServerAppID(), conf.MsgServerSecretKey())

	// 初始化缓存
	cache.InitDefaultRedisConn()

	// 初始化数据库
	database.InitDefaultDB()

	//初始化rpc core
	core_rpc.InitClient(conf.Discovery(), conf.LocalName())
	//rpcclient.InitRpcCore(conf.CoreRPCAddr())

	//启动服务引擎
	service.StartEngine()

	//int message queue
	log.Infof("开始初始化消息队列")
	server.InitMessageQueue()

	// 启动定时任务
	server.RunCronTask()

	// 初始化服务监控
	pprof.Init(conf.PprofAddr())

	Register()

	//go func() {
	//	time.Sleep(5*time.Second)
	//	plan.TriggerPlanOrder(5)
	//}()

	// 监听系统信号
	xsignal.NewSignal().AppendCloseFunc(
		model.MsgConsumer.ShutDown,
		model.MsgProduct.ShutDown,
		pprof.Stop).SignalMonitor()
}

func Register() {
	gob.Register(new(proto.EntrustOrder))
	gob.Register(new(proto.OrderOpenRsp)) //订单处理

}
