/*
@Time : 2019-12-31 10:53
<AUTHOR> mocha
@File : message.queue
*/
package server

import (
	"encoding/json"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/commonsrv"
	"spot/libs/conf"
	"spot/libs/convert"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/mq"
	"spot/libs/proto"
	"spot/libs/utils"
	"spot/pkg/planorder/model"
	bizservice "spot/pkg/planorder/service/combiz"
	"spot/pkg/planorder/service/msg"
	"spot/pkg/planorder/service/plan"
	"strconv"
)

func NewMqConsumer() {
	model.MsgConsumer = mq.NewMessageQueue(conf.MQ())
	queueName := define.MQConsumerPlanOrderQueueName
	if conf.IsSimulate() {
		queueName = "sim:" + queueName
	}
	queueName = utils.StrBuilder(queueName, strconv.Itoa(conf.MQQueueID()))

	mq.DefaultHandleMap[define.MQTopicContractChange] = MQContractChange //处理合约变动
	mq.DefaultHandleMap[define.MQTopicPriceIndex] = MQTradePriceChange   //处理成交价格变动

	mq.DefaultHandleMap[define.MQTopicPlanOpenAdd] = MQPlanOpenAdd //处理计划单增加
	mq.DefaultHandleMap[define.MQTopicPlanOpenDel] = MQPlanOpenDel //处理计划单移除

	err := model.MsgConsumer.Consumer(define.MQDefaultExchangeName, mq.ExchangeTypeDirect, queueName, define.MQDefaultMQBindKey)
	if err != nil {
		log.Errorf("trigger创建mq消费者失败,%v", err)
	}
	model.MsgConsumer.IsForce = true
	model.MsgConsumer.Ping()
}

func MQTradePriceChange(mp mq.MessagePack) {
	d := new(proto.IndexHistory)
	err := json.Unmarshal(mp.Data, d)
	if err != nil {
		log.Errorf("MqOrderMatch json unMarsha fail,%v", err)
		return
	}
	if !conf.IsDev() {
		NotifyIndexChange(d)
	}
}

func MQContractChange(mp mq.MessagePack) {
	//处理合约变化
	contracts, err := commonsrv.GetContractList(0, nil)
	if err != nil {
		log.Error("ReloadContract GetContractList error", zap.Int64("reqID", 0), zap.Error(err))
		return
	}

	var codes []string
	for i := range contracts {
		if !contracts[i].Delisted {
			codes = append(codes, contracts[i].ContractCode)
		}
	}
	plan.GetPlanOrderWorker().UpdateContract(codes)

}

func MQPlanOpenDel(mp mq.MessagePack) {
	d := new(proto.ConditionOrder)
	err := json.Unmarshal(mp.Data, d)
	if err != nil {
		log.Errorf("MQPlanOpenAdd json unMarsha fail,%v", err)
		return
	}
	if !bizservice.IsHandleThisUser(d.UserId) {

		return
	}
	log.Warnf("开始处理计划开仓单触发完成：%+v", *d)
	plan.GetPlanOrderWorker().RemoveOrder(d.ContractCode, d.TriggerPrice, d.Condition, d.PlanOrderId)
	key := define.CacheTriggerPlanOpen + convert.Int64String(d.PlanOrderId)
	cache.SetRedisUnLockStr(key)
}

func MQPlanOpenAdd(mp mq.MessagePack) {
	d := new(proto.ConditionOrder)
	err := json.Unmarshal(mp.Data, d)
	if err != nil {
		log.Errorf("MQPlanOpenAdd json unMarsha fail,%v", err)
		return
	}
	if !bizservice.IsHandleThisUser(d.UserId) {
		log.Warnf("不处理用户：%+v", d.UserId)
		return
	}
	log.Warnf("开始处理计划开仓单：%+v", *d)
	plan.GetPlanOrderWorker().JoinOrder(d.ContractCode, d.TriggerPrice, d.Condition, d.PlanOrderId)
}

func InitMessageQueue() {
	mq.SetIdWorker(conf.WorkerID())
	log.Infof("开始初始化消息队列")
	NewMqConsumer()
	msg.NewMqProduct()
}

func NotifyIndexChange(d *proto.IndexHistory) {
	if d.TradePrice.LessThanOrEqual(decimal.Zero) {
		log.Errorf("无效的指数价格数据，d:%{+v", *d)
		return
	}
	// 通知计划单
	//go plan.PlanOrderTigger(d.ContractCode, nums.Float(d.TradePrice))
	plan.PriceEngine.NotifyPrice(d)
}
