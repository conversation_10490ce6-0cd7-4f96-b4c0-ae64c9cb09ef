/*
@Time : 3/9/20 4:02 下午
<AUTHOR> mocha
@File : notify 通知push模块
*/
package msg

import (
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/mq"
	"spot/libs/proto"
	"spot/pkg/planorder/model"
)

func sendMessage(msg mq.MessagePack) {
	err := model.MsgProduct.Publish(define.MQDefaultExchangeName, mq.ExchangeTypeDirect, define.MQDefaultMQBindKey, msg.<PERSON>a(), define.MQReliable)
	if err != nil {
		log.Errorf("发送mq消息失败,err:%v", err)
		return
	}
}

func NotifyUserMessage(d *proto.Message) {
	b, err := json.Marshal(d)
	if err != nil {
		log.Errorf("NotifyUserMessage json marshal fail,%v", err)
		return
	}
	msg := mq.MessagePack{Topic: define.MQTopicMessage, Data: b}
	sendMessage(msg)
}

//通知自有成交
func NotifyMqSelfNewTrade(trade *proto.MatchOrder) {
	b, err := json.Marshal(*trade)
	if err != nil {
		log.Errorf("notifyMqTradeMatchMsg json marshal fail,%v", err)
		return
	}
	msg := mq.MessagePack{Topic: define.MQTopicPlatFormTrade, Data: b}
	sendMessage(msg)
}

func NotifyMqPlanOpenAdd(order *proto.ConditionOrder) {
	b, err := json.Marshal(order)
	if err != nil {
		log.Errorf("NotifyMqPlanOpenAdd json marshal fail,%v", err)
		return
	}
	msg := mq.MessagePack{Topic: define.MQTopicPlanOpenAdd, Data: b}
	sendMessage(msg)
}

func NotifyMqPlanOpenDel(order *proto.ConditionOrder) {
	b, err := json.Marshal(order)
	if err != nil {
		log.Errorf("NotifyMqPlanOpenDel json marshal fail,%v", err)
		return
	}
	msg := mq.MessagePack{Topic: define.MQTopicPlanOpenDel, Data: b}
	sendMessage(msg)
}
