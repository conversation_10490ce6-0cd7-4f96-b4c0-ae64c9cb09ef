package msg

import (
	"encoding/json"
	"spot/libs/cache"
	"spot/libs/convert"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/mq"
	"spot/libs/proto"
	"time"
)

//计划单触发开仓通知
func NotifyMqPanOrderOpen(order *proto.ConditionOrder) {
	log.Warnf("计划单触发开仓通知：%+v", *order)
	b, err := json.Marshal(order)
	if err != nil {
		log.Errorf("notifyMqTradeMatchMsg json marshal fail,%v", err)
		return
	}
	key := define.CacheTriggerPlanOpen + convert.Int64String(order.PlanOrderId)
	if !cache.SetRedisLockWithExp(20*time.Second, key) {
		log.Warnf("计划单正在处理，稍后：%v", *order)
		return
	}
	msg := mq.MessagePack{Topic: define.MQTopicPlanOrderOpen, Data: b}
	sendMessage(msg)
}
