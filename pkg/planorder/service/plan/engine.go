package plan

import (
	"github.com/shopspring/decimal"
	"go.uber.org/atomic"
	"spot/libs/log"
	"spot/libs/proto"
	"time"
)

type ContractEngine struct {
	Code           string
	TradePrice     chan proto.IndexHistory
	LastTradePrice decimal.Decimal //最近处理的成交价
	LastTs         time.Time       //上次处理完毕时间
	LastSize       int
	isShutDown     *atomic.Bool
}

func NewCEngine(code string) *ContractEngine {
	return &ContractEngine{
		Code:       code,
		TradePrice: make(chan proto.IndexHistory, 256),
		isShutDown: atomic.NewBool(false),
	}
}

func (c *ContractEngine) Run() {
	log.Infof("%v引擎开始启动", c.Code)
	go func() {
		for !c.isShutDown.Load() {
			select {
			case price := <-c.TradePrice:
				c.dealMatch(price)
			}
		}
	}()
}

func (c *ContractEngine) ShutDown() {
	log.Infof("%v引擎开始关闭", c.Code)
	c.isShutDown.Store(true)
}

func (c *ContractEngine) dealMatch(price proto.IndexHistory) {
	log.Infof("%v,引擎价格队列数量：%v,当前处理id；%v,价格：%v", c.Code, len(c.TradePrice), price.IndexId, price.TradePrice.String())
	capacity, size := cap(c.TradePrice), len(c.TradePrice)
	if size == capacity {
		var h, l decimal.Decimal
		for i := 0; i < size; i++ {
			price = <-c.TradePrice
			if i == 0 {
				h, l = price.TradePrice, price.TradePrice
				continue
			}
			if price.TradePrice.GreaterThan(h) {
				h = price.TradePrice
			} else if price.TradePrice.LessThan(l) {
				l = price.TradePrice
			}
		}
		log.Infof("%v价格队列已满，将使用队列最高价最低价进行匹配，high：%v,low:%v", c.Code, h.String(), l.String())
		if h.GreaterThan(decimal.Zero) {
			c.match(h)
		}
		if l.GreaterThan(decimal.Zero) && !l.Equal(h) {
			c.match(l)
		}
	} else {
		c.match(price.TradePrice)
	}
}

func (c *ContractEngine) match(price decimal.Decimal) {
	s := time.Now()
	if c.LastTs.IsZero() {
		c.LastTs = s
	}
	defer func() {
		c.LastTradePrice = price
		c.LastTs = time.Now()
	}()

	PlanOrderTigger(c.Code, price)
}

type PriceMatchEngine struct {
	priceChan chan *proto.IndexHistory
	cEngines  map[string]*ContractEngine
}

func NewPriceMatchEngine() *PriceMatchEngine {
	return &PriceMatchEngine{
		priceChan: make(chan *proto.IndexHistory, 1024),
		cEngines:  make(map[string]*ContractEngine),
	}
}

func (h *PriceMatchEngine) NotifyPrice(index *proto.IndexHistory) {
	h.priceChan <- index
}

func (h *PriceMatchEngine) Run() {
	go func() {
		for {
			select {
			case index := <-h.priceChan:
				log.Infof("价格匹配引擎，队列长度：%v，当前数量：%v", cap(h.priceChan), len(h.priceChan))
				engine, ok := h.cEngines[index.ContractCode]
				if !ok {
					engine = NewCEngine(index.ContractCode)
					engine.Run()
					h.cEngines[index.ContractCode] = engine

				}
				engine.TradePrice <- *index
			}
		}
	}()
}

var PriceEngine *PriceMatchEngine
