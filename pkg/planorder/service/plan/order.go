package plan

import (
	"sort"
	"spot/libs/xrpcclient/core_rpc"
	"strconv"
	"strings"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"spot/libs/commonsrv"
	"spot/libs/convert"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
)

//条件单触发
func PlanOrderTigger(contractCode string, lastPrice decimal.Decimal) {
	//log.Warnf("本轮触发，code:%v，price；%v,ids：%v", contractCode, lastPrice)
	//if contractCode == "BTCUSDT" {
	//	TestPlan(lastPrice)
	//	return
	//}
	var idList []int64
	id := database.NextID()

	defer func() {
		log.Info("本轮触发", zap.Int64("id", id), zap.String("code", contractCode), zap.Any("price", lastPrice), zap.Int("数量", len(idList)), zap.Any("data", idList))
	}()

	idList = GetPlanOrderWorker().Trigger(contractCode, lastPrice)
	length := len(idList)
	if length == 0 {
		return
	}

	sort.Slice(idList, func(i, j int) bool {
		return idList[i] < idList[j]
	})

	var buf strings.Builder
	for i := 0; i < length; i++ {
		buf.WriteString(strconv.FormatInt(idList[i], 10))
		if i < length-1 {
			buf.WriteString(",")
		}
	}

	// 获取触发满足条件的条件单
	list, err := database.GetConditionOrderForMeetV2(contractCode, buf.String())
	if err != nil {
		log.Error("conditionOrderHandler GetConditionOrderForMeet error", zap.Int64("id", id), zap.String("price", lastPrice.String()), zap.Error(err))
		return
	}

	isMaintenance := commonsrv.IsMaintenance(contractCode)
	if isMaintenance {
		log.Error("条件开仓失败，当前合约系统维护中", zap.String("合约", contractCode))
		return
	}

	for i := range list {
		order := list[i]
		order.TriggerPrice = lastPrice
		planOrderOpen(&order)
		//msg.consumer.NotifyMqPanOrderOpen(&order)
	}
	log.Info("本轮触发完成", zap.Int64("id", id), zap.String("code", contractCode))

}

func TriggerPlanOrder(id int64) {
	log.Info("开始触发，条件单id", zap.Any("ida", id))
	o, err := database.GetConditionOrderByOrderId(nil, id)
	if err != nil {
		log.Error("err", zap.Error(err))
		return
	}
	planOrderOpen(o)
}

func planOrderOpen(order *proto.ConditionOrder) {
	if order == nil {
		log.Error("条件单开仓订单信息为空")
		return
	}
	log.Info("开始触发，条件单", zap.Any("order", order))

	reply := new(define.Reply)
	args := &proto.OrderOpenArgs{
		UserId:          order.UserId,
		RequestId:       database.NextID(),
		Lang:            0,
		NToken:          "",
		PlanOrderId:     order.PlanOrderId,
		ContractCode:    order.ContractCode,
		Side:            order.Side,
		Amount:          order.Amount,
		Money:           order.Money,
		OrderType:       define.OrderTypePlan,
		IpAddress:       order.IpAddress,
		DeviceId:        convert.Int64String(int64(order.OrderClient)),
		APPID:           int(define.OsAutomatic),
		TriggerPrice:    order.TriggerPrice,
		EntrustType:     order.EntrustType,
		EntrustStrategy: 0,
		Price:           order.EntrustPrice,
		Mode:            order.Mode,
		ClientOrderId:   0,
	}
	//core服务
	defer log.Info("条件单开仓", zap.Any("arg", args), zap.Any("reply", reply))
	err := core_rpc.EntrustOpen(args, reply)
	if err != nil {
		log.Errorf("CoreStopOrderTrigger>xrpc_client.EntrustOpen args；%+v,err:%v", *args, err)
		return
	}
}
