package plan

import (
	"container/list"
	"sync"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"spot/libs/commonsrv"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	bizservice "spot/pkg/planorder/service/combiz"
)

type PlanOrderHandler interface {
	Trigger(price decimal.Decimal) []int64
	JoinOrder(price decimal.Decimal, orderID int64)
	RemoveOrder(price decimal.Decimal, orderID int64)
	Range() []*PlanOrderNode
}

type PlanOrderWorker struct {
	worker map[string]map[int]PlanOrderHandler
	lock   sync.RWMutex
}

var _planOrderWorker = &PlanOrderWorker{
	worker: make(map[string]map[int]PlanOrderHandler),
}

func GetPlanOrderWorker() *PlanOrderWorker {
	return _planOrderWorker
}

func InitPlanOrderWorker(fromDB bool, codes []string) {
	if fromDB {
		codes = codes[:0]
		// 获取合约列表
		contracts, err := commonsrv.GetContractList(0, nil)
		if err != nil {
			log.Fatal("InitPlanOrderWorker GetContractList error", zap.Error(err))
		}
		for i := range contracts {
			codes = append(codes, contracts[i].ContractCode)
		}
	}

	for i := range codes {
		_planOrderWorker.worker[codes[i]] = make(map[int]PlanOrderHandler, 2)
		_planOrderWorker.worker[codes[i]][define.OrderConditionGreaterOrEqual] = NewPlanOrderGreater()
		_planOrderWorker.worker[codes[i]][define.OrderConditionLessOrEqual] = NewPlanOrderLess()

		if fromDB {
			// 重启时从数据库获取未触发的条件单,放入队列中
			lessList, err := database.GetAllPlanOrderID(codes[i], define.OrderConditionLessOrEqual)
			if err != nil {
				log.Errorf("InitPlanOrderHandler GetAllPlanLessOrderID error", zap.String("contract", codes[i]), zap.Error(err))
				return
			}
			for j := range lessList {
				log.Warnf("lessList[i]:%+v", lessList[j])
				if !bizservice.IsHandleThisUser(lessList[j].UserId) {
					continue
				}
				_planOrderWorker.JoinOrder(codes[i], lessList[j].TriggerPrice, define.OrderConditionLessOrEqual, lessList[j].PlanOrderID)
			}

			greaterList, err := database.GetAllPlanOrderID(codes[i], define.OrderConditionGreaterOrEqual)
			if err != nil {
				log.Fatal("InitPlanOrderHandler GetAllPlanGreaterOrderID error", zap.String("contract", codes[i]), zap.Error(err))
			}
			for j := range greaterList {
				_planOrderWorker.JoinOrder(codes[i], greaterList[j].TriggerPrice, define.OrderConditionGreaterOrEqual, greaterList[j].PlanOrderID)
			}

		}
	}
}

func NewContract(code string) {
	_planOrderWorker.worker[code] = make(map[int]PlanOrderHandler, 2)
	_planOrderWorker.worker[code][define.OrderConditionGreaterOrEqual] = NewPlanOrderGreater()
	_planOrderWorker.worker[code][define.OrderConditionLessOrEqual] = NewPlanOrderLess()
}

func (w *PlanOrderWorker) UpdateContract(codes []string) {
	w.lock.Lock()
	defer w.lock.Unlock()

	for _, code := range codes {
		w.CheckWorker(code, true)
	}
}

func (w *PlanOrderWorker) LoadDataFromDB(code string) {
	// 重启时从数据库获取未触发的条件单,放入队列中
	lessList, err := database.GetAllPlanOrderID(code, define.OrderConditionLessOrEqual)
	if err != nil {
		log.Fatal("UpdateContract GetAllPlanLessOrderID error", zap.String("contract", code), zap.Error(err))
	}
	for j := range lessList {
		_planOrderWorker.worker[code][define.OrderConditionLessOrEqual].JoinOrder(lessList[j].TriggerPrice, lessList[j].PlanOrderID)
	}

	greaterList, err := database.GetAllPlanOrderID(code, define.OrderConditionGreaterOrEqual)
	if err != nil {
		log.Fatal("UpdateContract GetAllPlanGreaterOrderID error", zap.String("contract", code), zap.Error(err))
	}
	for j := range greaterList {
		_planOrderWorker.worker[code][define.OrderConditionGreaterOrEqual].JoinOrder(greaterList[j].TriggerPrice, greaterList[j].PlanOrderID)
	}
}

func (w *PlanOrderWorker) CheckWorker(code string, isLoadData bool) {
	if _, ok := _planOrderWorker.worker[code]; ok {
		return
	}
	w.worker[code] = make(map[int]PlanOrderHandler, 2)
	w.worker[code][define.OrderConditionGreaterOrEqual] = NewPlanOrderGreater()
	w.worker[code][define.OrderConditionLessOrEqual] = NewPlanOrderLess()
	if isLoadData {
		w.LoadDataFromDB(code)
	}
}

func (w *PlanOrderWorker) Trigger(contract string, price decimal.Decimal) []int64 {
	w.lock.RLock()
	defer w.lock.RUnlock()

	w.CheckWorker(contract, true)

	if _, ok := w.worker[contract]; !ok {
		log.Error("当前引擎不支持该交易对", zap.String("code", contract))
		return nil
	}

	ids := w.worker[contract][define.OrderConditionLessOrEqual].Trigger(price)
	ids = append(ids, w.worker[contract][define.OrderConditionGreaterOrEqual].Trigger(price)...)
	return ids
}

func (w *PlanOrderWorker) JoinOrder(contract string, price decimal.Decimal, condition int, orderID int64) {
	w.lock.RLock()
	defer w.lock.RUnlock()

	w.CheckWorker(contract, false)

	var ok bool
	if _, ok = w.worker[contract]; !ok {
		log.Error("当前引擎不支持交易对", zap.String("code", contract))
		return
	}
	if _, ok = w.worker[contract][condition]; !ok {
		return
	}

	w.worker[contract][condition].JoinOrder(price, orderID)
}

func (w *PlanOrderWorker) RemoveOrder(contract string, price decimal.Decimal, condition int, orderID int64) {
	w.lock.RLock()
	defer w.lock.RUnlock()

	var ok bool
	if _, ok = w.worker[contract]; !ok {
		return
	}
	if _, ok = w.worker[contract][condition]; !ok {
		return
	}

	w.worker[contract][condition].RemoveOrder(price, orderID)
}

type PlanOrderGreater struct {
	list *list.List // 大于等于时触发 按价格从小到大排列
	sync.Mutex
}

func NewPlanOrderGreater() *PlanOrderGreater {
	return &PlanOrderGreater{list: list.New()}
}

// 判断价格进行触发
func (o *PlanOrderGreater) Trigger(price decimal.Decimal) []int64 {
	var idList []int64
	//var removeList []*list.Element
	o.Lock()
	defer o.Unlock()

	// 获取大于等于当前价格的订单id
	for node := o.list.Front(); node != nil; node = node.Next() {
		p, ok := node.Value.(*PlanOrderNode)
		if !ok {
			break
		}
		if p.Price.GreaterThan(price) {
			break
		}

		if p.Price.LessThanOrEqual(price) {
			for orderID := range p.List {
				idList = append(idList, orderID)
			}
			//removeList = append(removeList, node)
		}
	}
	// 改为通知移除
	//for i := range removeList {
	//	o.list.Remove(removeList[i])
	//}

	return idList
}

// 插入大于等于时触发的订单
func (o *PlanOrderGreater) JoinOrder(price decimal.Decimal, orderID int64) {
	order := &PlanOrderNode{
		Price: price,
		List:  map[int64]bool{orderID: true},
	}

	o.Lock()
	defer o.Unlock()

	node := o.list.Front()
	if node == nil {
		o.list.PushBack(order)
	} else {
		for node != nil {
			p, ok := node.Value.(*PlanOrderNode)
			if !ok {
				break
			}

			if p.Price.Equal(price) {
				// 如果与当前节点价格相等,则追加到当前节点列表中
				p.List[orderID] = true
				break
			} else if p.Price.GreaterThan(price) {
				// 因为是从小到大排序,如果当前节点价格大于本订单价格,那么插入到当前节点前面即可
				o.list.InsertBefore(order, node)
				break
			} else {
				// 当前节点价格大于本订单价格,判断下一个节点
				if node != o.list.Back() {
					node = node.Next()
				} else {
					o.list.PushBack(order)
					break
				}
			}
		}
	}
}

// 移除大于等于条件订单
func (o *PlanOrderGreater) RemoveOrder(price decimal.Decimal, orderID int64) {
	o.Lock()
	defer o.Unlock()

	var (
		ok   bool
		p    *PlanOrderNode
		node *list.Element
	)
	for node = o.list.Front(); node != nil; node = node.Next() {
		p, ok = node.Value.(*PlanOrderNode)
		if !ok {
			break
		}
		if p.Price.Equal(price) {
			if _, ok := p.List[orderID]; ok {
				delete(p.List, orderID)
			}
			break
		} else if p.Price.GreaterThan(price) {
			break
		}
	}
	if p != nil && len(p.List) == 0 {
		o.list.Remove(node)
	}
}

// 获取所有列表
func (o *PlanOrderGreater) Range() []*PlanOrderNode {
	o.Lock()
	defer o.Unlock()

	var l []*PlanOrderNode
	for node := o.list.Front(); node != nil; node = node.Next() {
		p, ok := node.Value.(*PlanOrderNode)
		if !ok {
			break
		}
		l = append(l, p)
	}
	return l
}

type PlanOrderLess struct {
	list *list.List // 小于等于时触发 按价格从大到小排列
	sync.Mutex
}

func NewPlanOrderLess() *PlanOrderLess {
	return &PlanOrderLess{list: list.New()}
}

type PlanOrderNode struct {
	Price decimal.Decimal
	List  map[int64]bool
}

// 判断价格进行触发
func (o *PlanOrderLess) Trigger(price decimal.Decimal) []int64 {
	var idList []int64
	//var removeList []*list.Element
	o.Lock()
	defer o.Unlock()

	// 获取小于等于当前价格的订单id
	for node := o.list.Front(); node != nil; node = node.Next() {
		p, ok := node.Value.(*PlanOrderNode)
		if !ok {
			break
		}
		if p.Price.LessThan(price) {
			break
		}

		if p.Price.GreaterThanOrEqual(price) {
			for orderID := range p.List {
				idList = append(idList, orderID)
			}
			//removeList = append(removeList, node)
		}
	}
	// 改为通知移除
	//for i := range removeList {
	//	o.list.Remove(removeList[i])
	//}
	return idList
}

// 插入小等于时触发的订单
func (o *PlanOrderLess) JoinOrder(price decimal.Decimal, orderID int64) {
	order := &PlanOrderNode{
		Price: price,
		List:  map[int64]bool{orderID: true},
	}

	o.Lock()
	defer o.Unlock()

	node := o.list.Front()
	if node == nil {
		o.list.PushBack(order)
	} else {
		for node != nil {
			p, ok := node.Value.(*PlanOrderNode)
			if !ok {
				break
			}

			if p.Price.Equal(price) {
				// 如果与当前节点价格相等,则追加到当前节点列表中
				p.List[orderID] = true
				break
			} else if p.Price.LessThan(price) {
				// 因为是从大到小排序,如果当前节点价格小于本订单价格,那么插入到当前节点前面即可
				o.list.InsertBefore(order, node)
				break
			} else {
				// 当前节点价格大于本订单价格,判断下一个节点
				if node != o.list.Back() {
					node = node.Next()
				} else {
					o.list.PushBack(order)
					break
				}
			}
		}
	}
}

// 移除小于等于条件订单
func (o *PlanOrderLess) RemoveOrder(price decimal.Decimal, orderID int64) {
	o.Lock()
	defer o.Unlock()

	var (
		ok   bool
		p    *PlanOrderNode
		node *list.Element
	)
	for node = o.list.Front(); node != nil; node = node.Next() {
		p, ok = node.Value.(*PlanOrderNode)
		if !ok {
			break
		}
		if p.Price.Equal(price) {
			if _, ok := p.List[orderID]; ok {
				delete(p.List, orderID)
			}
			break
		} else if p.Price.LessThan(price) {
			break
		}
	}
	if p != nil && len(p.List) == 0 {
		o.list.Remove(node)
	}
}

// 获取所有列表
func (o *PlanOrderLess) Range() []*PlanOrderNode {
	o.Lock()
	defer o.Unlock()

	var l []*PlanOrderNode
	for node := o.list.Front(); node != nil; node = node.Next() {
		p, ok := node.Value.(*PlanOrderNode)
		if !ok {
			break
		}
		l = append(l, p)
	}
	return l
}
