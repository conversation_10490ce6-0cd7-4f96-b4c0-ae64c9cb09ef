package plan

import (
	"math/rand"
	"testing"
	"time"

	"github.com/shopspring/decimal"
	"spot/libs/define"
)

func InitTestPlanData(t *testing.T) {
	InitPlanOrderWorker(false, []string{"BTCUSDT"})

	r := rand.New(rand.NewSource(time.Now().UnixNano()))

	// 插入数据
	for i := 0; i < 100; i++ {
		GetPlanOrderWorker().<PERSON><PERSON><PERSON><PERSON><PERSON>("BTCUSDT", decimal.New(r.Int63n(10)+1000, -r.Int31n(3)), define.OrderConditionGreaterOrEqual, int64(i*1234+1234))
		GetPlanOrderWorker().<PERSON><PERSON><PERSON><PERSON><PERSON>("BTCUSDT", decimal.New(r.Int63n(10)+1000, -r.Int31n(3)), define.OrderConditionLessOrEqual, int64(i*1234+1234))
	}

	for _, n := range GetPlanOrderWorker().worker["BTCUSDT"][define.OrderConditionGreaterOrEqual].Range() {
		t.Logf("gl: %+v", n)
	}

	for _, n := range GetPlanOrderWorker().worker["BTCUSDT"][define.OrderConditionLessOrEqual].Range() {
		t.Logf("ll: %+v", n)
	}
}

//func TestPlanOrderWorker_Trigger(t *testing.T) {
//	InitTestPlanData(t)
//
//	for i := 1; i < 10; i++ {
//		t.Log("+++++++++++++++++++++++++++++++++")
//		p := decimal.New(r.Int63n(10)+1000, -r.Int31n(3))
//		list := GetPlanOrderWorker().Trigger("BTCUSDT", p)
//		t.Logf("%v, %+v", p, list)
//		for _, n := range GetPlanOrderWorker().worker["BTCUSDT"][define.OrderConditionGreaterOrEqual].Range() {
//			t.Logf("%+v", n)
//		}
//	}
//}

//func TestPlanOrderWorker_Trigger2(t *testing.T) {
//	InitTestPlanData(t)
//
//	for i := 1; i < 10; i++ {
//		t.Log("+++++++++++++++++++++++++++++++++")
//		p := decimal.New(r.Int63n(10)+1000, -r.Int31n(3))
//		list := GetPlanOrderWorker().Trigger("BTCUSDT", p)
//		t.Logf("%v, %+v", p, list)
//		for _, n := range GetPlanOrderWorker().worker["BTCUSDT"][define.OrderConditionLessOrEqual].Range() {
//			t.Logf("%+v", n)
//		}
//	}
//}

func TestPlanOrderWorker_RemoveOrder(t *testing.T) {
	InitTestPlanData(t)

	var (
		id    int64
		price decimal.Decimal
	)

	nodes := GetPlanOrderWorker().worker["BTCUSDT"][define.OrderConditionGreaterOrEqual].Range()
	if len(nodes) != 0 {
		price = nodes[0].Price
		for id = range nodes[0].List {
			break
		}
	}

	// 测试删除不存在的id
	GetPlanOrderWorker().RemoveOrder("BTCUSDT", price, define.OrderConditionGreaterOrEqual, id+100000)
	t.Logf("%v, %d", price, id+100000)
	for _, n := range GetPlanOrderWorker().worker["BTCUSDT"][define.OrderConditionGreaterOrEqual].Range() {
		t.Logf("%+v", n)
	}

	// 测试删除存在的id
	GetPlanOrderWorker().RemoveOrder("BTCUSDT", price, define.OrderConditionGreaterOrEqual, id)
	t.Logf("%v, %d", price, id)
	for _, n := range GetPlanOrderWorker().worker["BTCUSDT"][define.OrderConditionGreaterOrEqual].Range() {
		t.Logf("%+v", n)
	}

	// 测试删除不存在的价格中的id
	GetPlanOrderWorker().RemoveOrder("BTCUSDT", price.Mul(decimal.New(10000, 0)), define.OrderConditionGreaterOrEqual, id)
	t.Logf("%v, %d", price.Mul(decimal.New(10000, 0)), id)
	for _, n := range GetPlanOrderWorker().worker["BTCUSDT"][define.OrderConditionGreaterOrEqual].Range() {
		t.Logf("%+v", n)
	}
}

func TestPlanOrderWorker_RemoveOrder2(t *testing.T) {
	InitTestPlanData(t)

	var (
		id    int64
		price decimal.Decimal
	)

	nodes := GetPlanOrderWorker().worker["BTCUSDT"][define.OrderConditionLessOrEqual].Range()
	if len(nodes) != 0 {
		price = nodes[0].Price
		for id = range nodes[0].List {
			break
		}
	}

	// 测试删除不存在的id
	GetPlanOrderWorker().RemoveOrder("BTCUSDT", price, define.OrderConditionLessOrEqual, id+100000)
	t.Logf("%v, %d", price, id+100000)
	for _, n := range GetPlanOrderWorker().worker["BTCUSDT"][define.OrderConditionLessOrEqual].Range() {
		t.Logf("%+v", n)
	}

	// 测试删除存在的id
	GetPlanOrderWorker().RemoveOrder("BTCUSDT", price, define.OrderConditionLessOrEqual, id)
	t.Logf("%v, %d", price, id)
	for _, n := range GetPlanOrderWorker().worker["BTCUSDT"][define.OrderConditionLessOrEqual].Range() {
		t.Logf("%+v", n)
	}

	// 测试删除不存在的价格中的id
	GetPlanOrderWorker().RemoveOrder("BTCUSDT", price.Mul(decimal.New(10000, 0)), define.OrderConditionLessOrEqual, id)
	t.Logf("%v, %d", price.Mul(decimal.New(10000, 0)), id)
	for _, n := range GetPlanOrderWorker().worker["BTCUSDT"][define.OrderConditionLessOrEqual].Range() {
		t.Logf("%+v", n)
	}
}
