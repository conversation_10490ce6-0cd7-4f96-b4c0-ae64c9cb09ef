run_env: "test" #运行环境，prod-生产,test-测试，dev-开发
debug: true # 是否启用调试模式
zip_http: false # 是否启用zip压缩
local_name: "158 BaseCoin Push Server" # 服务名称
listen_addr: "0.0.0.0:9911" # http服务监听地址
read_timeout: 30 # 读超时时间
write_timeout: 30 # 写超时时间
sms_server: "http://127.0.0.1:8085" # 短信服务Url
mq: "amqp://user:password@localhost:5672" #rabbit mq config
default_db: "futures:JDtAJhfNan7Hgu25@tcp(***************:3306)/futures_basecoin?charset=utf8&parseTime=true&loc=Local" # 数据库连接
log_file: "push" # 日志文件位置
log_level: "info" # 日志等级
sms_prefix: "【Base】" # 发送短信前缀
mail_prefix: "【Base】" # 发送邮件前缀
caution_receiver: [ ] # 警告邮件通知列表
sensitive_conf: "" # 敏感信息配置文件位置
mq_queue_name: "push"

default_redis_conf: { # 默认redis连接
  "address": "127.0.0.1:6379", # 连接地址
  "password": "", # 连接密码
  "use_tls": false, # 是否使用tls连接
  "default_db": 5, # 使用的db号
  "pool_size": 20, # 连接池数量
  "db_nums": { # 使用的redis库号,仅会初始化这里定义了的db号 key(db号):value(描述)
    #    0: "db 0",
    #    1: "db 1",
    2: "公共数据",
    #    3: "db 3",
#    4: "USD合约",
    5: "现货",
    #    6: "db 6",
    #    7: "db 7",
    #    8: "db 8",
    #    9: "db 9",
    #    10: "db 10",
    #    11: "db 11",
    #    12: "db 12",
    #    13: "db 13",
    #    14: "db 14",
    #    15: "db 15",
  },
}
