/*
@Time : 3/9/20 2:31 下午
<AUTHOR> mocha
@File : model
*/
package model

type AuthArg struct {
	Token string `json:"token"`
	AppId int    `json:"app_id"`
	Ts    int64  `json:"ts"`
	Sign  string `json:"sign"`
}

type NewTradeArg struct {
	Symbols []string `json:"symbols"`
}

type ContractApplies struct {
	Ts           int64  `json:"ts"`
	ContractCode string `json:"contract_code"`
	Price        string `json:"price"`
	Change       string `json:"change"`
}

type SubSymbolArg struct {
	Symbol string `json:"symbol"`
}

type SubDepthSwitchArg struct {
	Level int `json:"level"`
}

type ApiAuthArg struct {
	AccessKey string `json:"accessKey"`
	ApiV      string `json:"apiV"` // 用户api
	Ts        int64  `json:"ts"`
	Sign      string `json:"sign"`
}
