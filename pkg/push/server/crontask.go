/*
@Time : 2/12/19 4:45 PM
<AUTHOR> mocha
@File : interesttask
*/
package server

import (
	"github.com/robfig/cron"
	"spot/pkg/push/service"
)

const (
	everySecond = "*/1 * * * * *" // 秒,分,时,日,月,周 每秒执行
)

func RunCronTask() {
	// 启动运行

	c := cron.New()
	_ = c.AddFunc(everySecond, func() {
		service.NotifyAllContractApplies()
	})
	//_ = c.AddFunc(everySecond, service.NotifyAllSpotRate)
	c.Start()
}

type ListTask struct {
	Tasks []cron.Job
}

func (tasks ListTask) Run() {
	for _, job := range tasks.Tasks {
		job.Run()
	}

}
