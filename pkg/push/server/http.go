package server

import (
	"context"
	"net"
	"net/http"
	"time"

	"github.com/gorilla/mux"
	"go.uber.org/zap"
	"spot/libs/conf"
	"spot/libs/log"
	"spot/libs/msg"
	"spot/libs/wserver"
	"spot/pkg/push/service"
)

var httpServer *http.Server

func InitHTTP() (err error) {
	// http listen
	muxRouter := mux.NewRouter()
	muxRouter.HandleFunc("/v2/spot/ws/", func(writer http.ResponseWriter, request *http.Request) {
		ws.ServeWs(service.WsHub, writer, request, func(i int64) {

		})
	})
	//pprof.Wrap(muxRouter)

	log.Infof("start http listen, addr[%s]", conf.ListenAddr())
	go httpListen(muxRouter, conf.ListenAddr())
	return
}

// 关http server
func shutdownHTTPServer() {
	_ = httpServer.Shutdown(context.Background())
}

func httpListen(mux *mux.Router, addr string) {
	httpServer = &http.Server{
		Handler:      mux,
		ReadTimeout:  time.Duration(int64(time.Second) * conf.ReadTimeout()),
		WriteTimeout: time.Duration(int64(time.Second) * conf.WriteTimeout())}
	httpServer.SetKeepAlivesEnabled(true)
	l, err := net.Listen("tcp", addr)
	if err != nil {
		log.Fatalf("httpListen error, addr[%s], err:%v", addr, err)
	}
	defer l.Close()
	if err := httpServer.Serve(l); err != nil {
		// 发送报错邮件
		msg.SendCautionEmail("服务停止提醒", "push服务停止,如非手动关闭,请检查!\n"+err.Error())
		log.Fatal("server error", zap.Error(err))
	}
}
