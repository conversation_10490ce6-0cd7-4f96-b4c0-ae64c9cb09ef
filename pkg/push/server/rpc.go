package server

import (
	"net"
	"net/rpc"
	"time"

	"spot/libs/conf"
	"spot/libs/define"
	"spot/libs/log"
)

var rpcServer *RPC

func InitRPC() (err error) {
	rpcServer = &RPC{available: true}
	err = rpc.Register(rpcServer)
	if err != nil {
		log.Errorf("register rpc server failed, err:%v", err)
		return
	}
	log.Infof("start listen rpc addr: [%s]", conf.RPCAddr())
	go rpcListen(conf.RPCAddr())

	return
}

func shutdownServers() {
	log.Info("qbPro push service begin stop")
	if rpcServer != nil {
		rpcServer.setAvailable(false)
		time.Sleep(time.Second)
	}
	log.Info("qbPro push service stop")
}

func rpcListen(addr string) {
	l, err := net.Listen("tcp", addr)
	if err != nil {
		log.Errorf("net.Listen[%s], error(%v)", addr, err)
		panic(err)
	}
	// if process exit, then close the rpc bind
	defer func() {
		log.Infof("rpc addr [%s] close", addr)
		if err := l.Close(); err != nil {
			log.Errorf("listener.Close() error(%v)", err)
		}
	}()
	rpc.Accept(l)
}

// RPC
type RPC struct {
	available bool
}

func (r *RPC) Ping(arg *define.NoneArg, reply *define.NoneReply) error {
	return nil
}

func (r *RPC) setAvailable(available bool) {
	r.available = available
}

func (r *RPC) checkAvailable() uint16 {
	if r.available {
		return define.ErrCodeNone
	} else {
		return define.ErrCodeServerNotAvailable
	}
}
