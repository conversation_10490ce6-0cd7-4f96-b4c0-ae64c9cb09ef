/*
@Time : 3/12/20 3:30 下午
<AUTHOR> mocha
@File : applies
*/
package service

import (
	"spot/libs/commonsrv"
	"spot/libs/log"
)

func NotifyAllContractApplies() {
	//log.Debugf("准备查询所有交易对价格")
	list, err := commonsrv.GetContractList(0, nil)
	if err != nil {
		log.Errorf("cache.GetAllContractList() fail,%v", err)
		return
	}
	l := commonsrv.GetApiSymbolList(list)
	if len(l) > 0 {
		MQAllContractAppliesMsg(l)
	}

}
