/*
@Time : 3/9/20 3:50 下午
<AUTHOR> mocha
@File : message.consumer
*/
package service

import (
	"fmt"
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/mq"
	"spot/libs/proto"
	"spot/libs/wserver"
	"strconv"
	"time"
)

//处理指定合约涨跌幅
func MQAppliesMsg(mp mq.MessagePack) {
	d := new(proto.ContractApply)
	err := json.Unmarshal(mp.Data, d)
	if err != nil {
		log.Errorf("MQAppliesMsg json unMarsha fail,%v", err)
		return
	}
	log.Debugf("开始处理指定合约的涨跌幅,%v", d.Symbol)
	checkAndInitWsRoom(d.Symbol)
	room := fmt.Sprintf(define.RoomMarket, d.Symbol)
	msg := ws.WMessagePack{Action: define.WsActionNotify, Topic: define.NotifyContractInfo, Ts: time.Now().Unix(), Data: mp.Data}
	WsHub.SendRoomMsg(room, msg)
	return
}

func MQUserNewTicker(mp mq.MessagePack) {
	d := new(proto.MatchOrder)
	err := json.Unmarshal(mp.Data, d)
	if err != nil {
		log.Errorf("MQDealNewTradeMsg json unMarsha fail,%v", err)
		return
	}
	now := time.Now()
	rsp := proto.NewTradeReply{Ts: now.Unix(), Symbol: d.Symbol}
	td := proto.TradeDetail{Ts: now.Unix(), Price: d.TradePrice.StringFixedBank(d.Digit), PriceCn: d.TradePriceCn.StringFixedBank(define.CNYPrecision), Amount: d.TradeVolume, Side: d.Side}
	list := []proto.TradeDetail{td}
	rsp.List = list
	room := fmt.Sprintf(define.RoomMarket, d.Symbol)
	data, err := json.Marshal(rsp)
	if err != nil {
		log.Errorf("MQUserNewTicker json marshal fail,%v", err)
		return
	}
	checkAndInitWsRoom(d.Symbol)
	msg := ws.WMessagePack{Action: define.WsActionNotify, Topic: define.NotifyTicker, Ts: time.Now().Unix(), Data: data}
	WsHub.SendRoomMsg(room, msg)
	log.Infof("发送ticker消息到room")
}

type IndexPrice struct {
	Price string `json:"price,omitempty"`
	proto.ComplexPrice
}

////用户最新成交消息
//func MQUserNewTrade(mp mq.MessagePack) {
//	d := new(proto.Trade)
//	err := json.Unmarshal(mp.Data, d)
//	if err != nil {
//		log.Errorf("MQDealNewTradeMsg json unMarsha fail,%v", err)
//		return
//	}
//	msg := ws.WMessagePack{Action: define.WsActionNotify, Topic: define.NotifyTradeRecent, Ts: time.Now().Unix(), Data: mp.Data}
//	WsHub.SendUser(d.UserId, msg)
//}
//深度消息
func MQDepthMsg(mp mq.MessagePack) {
	//log.Infof("处理深度消息.....")
	d := new(proto.DepthContainerPub)
	//d := new(proto.DepthContainer)
	err := json.Unmarshal(mp.Data, d)
	if err != nil {
		log.Errorf("MQDepthMsg json unMarsha fail,%v", err)
		return
	}
	//data, err := json.Marshal(d)
	//if err != nil {
	//	log.Errorf("MQDepthMsg json marshal fail,%v", err)
	//	return
	//}
	data := mp.Data
	checkAndInitWsRoom(d.Symbol)
	//log.Infof("MQDepthMsg handle msg:%+v", string(data))
	if !isDepthUseRoom {
		//需lib/ws底仓做处理
		room := fmt.Sprintf(define.RoomMarket, d.Symbol)
		msg := ws.WMessagePack{Action: define.WsActionNotify, Topic: define.NotifyDepth, Ts: time.Now().Unix(), Data: data}
		WsHub.SendRoomMsg(room, msg)
		return
	}

	//根据深度级别推送
	room := fmt.Sprintf(define.RoomDepth, d.Symbol, strconv.Itoa(int(d.Level)))
	msg := ws.WMessagePack{Action: define.WsActionNotify, Topic: define.NotifyDepth, Ts: time.Now().Unix(), Data: data}
	WsHub.SendRoomMsg(room, msg)
	//

	////从原始深度消息，获取缓存各级别深度，进行推送
	//for l := range define.DepthSupportLevel {
	//	room := fmt.Sprintf(define.RoomDepth, d.ContractCode, strconv.Itoa(l))
	//	depth := cache.GetContractDepth(d.ContractCode, l)
	//	b, err := json.Marshal(depth)
	//	if err != nil {
	//		log.Errorf("json marsha depth fail,%v", err)
	//		continue
	//	}
	//	msg := ws.WMessagePack{Action: define.WsActionNotify, Topic: define.NotifyDepth, Ts: time.Now().Unix(), Data: b}
	//	WsHub.SendRoomMsg(room, msg)
	//}
}

////发送合约涨跌幅消息
//func SendAppliesMsg(data proto.ApiContractDetail) {
//	checkAndInitWsRoom(data.ContractCode)
//	room := fmt.Sprintf(define.RoomMarket, data.ContractCode)
//	d, _ := json.Marshal(data)
//	msg := ws.WMessagePack{Action: define.WsActionNotify, Topic: define.NotifyContractInfo, Ts: time.Now().Unix(), Data: d}
//	WsHub.SendRoomMsg(room, msg)
//}

//发送所有交易对价格和涨跌幅,以定期
func MQAllContractAppliesMsg(list []proto.ApiSymbolList) {
	if len(list) == 0 {
		return
	}
	room := define.RoomSymbols
	d, _ := json.Marshal(list)
	msg := ws.WMessagePack{Action: define.WsActionNotify, Topic: define.NotifySymbols, Ts: time.Now().Unix(), Data: d}
	//log.Infof("发送symbol.market,name:%+v,msg:%+v",room,msg)
	WsHub.SendRoomMsg(room, msg)
}

//推送ws文本消息
func MQTopicMessage(mp mq.MessagePack) {
	d := new(proto.Message)
	err := json.Unmarshal(mp.Data, d)
	if err != nil {
		log.Errorf("MQTopicMessage json unMarsha fail,%v", err)
		return
	}
	//log.Infof("收到文本消息：%+v",string(mp.Data))
	msg := ws.WMessagePack{Action: define.WsActionNotify, Topic: define.NotifyMessage, Ts: time.Now().Unix(), Data: mp.Data}
	WsHub.SendUser(d.ReceiverID, msg)
}

func MQTopicOrderChange(message mq.MessagePack) {
	order := new(proto.EntrustOrder)
	err := json.Unmarshal(message.Data, order)
	if err != nil {
		return
	}
	msg := ws.WMessagePack{Action: define.WsActionNotify, Topic: define.NotifyOrderChange, Ts: time.Now().Unix(), Data: message.Data}
	WsHub.SendUser(order.UserID, msg)
}

func TestTopicMessage() {
	msg := &proto.Message{
		NotifyMessage: proto.NotifyMessage{
			ID:             1,
			SenderID:       120,
			ReceiverID:     180,
			SenderNickname: "sys",
			Title:          "测试消息",
			Content:        "测试消息内容",
			Category:       0,
			LanguageType:   0,
			CreateTime:     time.Now(),
		},
	}
	b, _ := json.Marshal(msg)
	mp := mq.MessagePack{MsgId: 1, Data: b}
	MQTopicMessage(mp)
}

// 推送所有币种的价格汇率
func MQNotifyAllSpotRateMsg(list []proto.CoinRateReply) {
	if len(list) == 0 {
		return
	}
	room := define.RoomSymbols
	//room := define.RoomExchangeRate
	d, _ := json.Marshal(list)
	msg := ws.WMessagePack{Action: define.WsActionNotify, Topic: define.NotifyExchangeRate, Ts: time.Now().Unix(), Data: d}
	log.Infof("发送symbol.market,name:%+v,msg:%+v", room, msg)
	WsHub.SendRoomMsg(room, msg)
}
