/*
@Time : 2019-12-31 10:53
<AUTHOR> mocha
@File : message.queue
*/
package service

import (
	"spot/libs/conf"
	"spot/libs/define"
	"spot/libs/messagequeue"
	"spot/libs/mq"
	"spot/libs/nums"
)

func NewMqConsumer() {
	handler := make(map[string]mq.MsgHandleFunc)
	handler[define.MQTopicDepth] = MQDepthMsg                     //处理深度
	handler[define.MQTopicNewTrade] = MQUserNewTicker             //处理成交ticker
	handler[define.MQTopicContractApplies] = MQAppliesMsg         //处理合约涨跌幅
	handler[define.MQTopicMessage] = MQTopicMessage               //用户通知消息
	handler[define.MQTopicOrderStatusChange] = MQTopicOrderChange //用户通知消息

	messagequeue.NewMqConsumer(define.MQDefaultExchangeName, define.MQConsumerPushQueueName, define.MQDefaultMQBindKey, nums.Int2String(conf.MQQueueID()), handler)
}

func InitMessageQueue() {
	NewMqConsumer()
}
