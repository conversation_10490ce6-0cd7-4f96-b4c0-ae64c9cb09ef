/*
@Time : 3/12/20 5:51 下午
<AUTHOR> mocha
@File : test_userhold
*/
package service

import (
	"spot/libs/json"
	"spot/libs/nums"
	"spot/libs/proto"
	"testing"
)

func TestUserHold(t *testing.T) {
	//up := &proto.UserPosition{UserId: 180, ContractCode: "ETHUSD", Side: "B", Volume: 100, NominalPrice: 1000.02, AccountType: 2,
	//	Lever: 100, ForceClosePrice: 500.23, InitMargin: 200.23, PositionMargin: 100.20, AdjustMargin: 20.02, FloatProfit: 20.20, ProfitRatio: 20.02, Roic: 10.2, Commission: 14.2,
	//}
	//b, err := json.Marshal(up)
	//if err != nil {
	//	t.Logf("err:%v", err)
	//	return
	//}
	//t.Logf("b:%+v", string(b))
	//
	//s := new(proto.UserPosition)
	//e := json.Unmarshal(b, s)
	//if e != nil {
	//	t.Logf("unmarsha fail,%v", e)
	//	return
	//}
	//t.Logf("v:%+v", *s)
}

func TestPrice(t *testing.T) {
	s := IndexPrice{
		Price: "",
	}
	s.ComplexPrice = proto.ComplexPrice{
		ContractCode: "123",
		Price:        nums.NewFromString("12345"),
		PriceStr:     "20.20",
	}
	a, err := json.Marshal(s)
	if err != nil {
		return
	}
	t.Logf("a:%v", string(a))
}
