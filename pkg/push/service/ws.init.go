// Copyright 2013 The Gorilla WebSocket Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

package service

import (
	"fmt"
	"spot/libs/commonsrv"
	"spot/libs/conf"
	"spot/libs/container/safemap"
	"spot/libs/database"
	"spot/libs/proto"
	"strconv"
	"time"

	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/wserver"
)

var WsHub *ws.Hub
var supportSymbol = safemap.New()

var isDepthUseRoom = true

//在建立连接同时校验token,并传递token
func InitWebSocket() {
	fmt.Println("初始化websocket")
	if WsHub == nil {
		var spaySeconds int64 = 10
		if conf.IsProduct() {
			spaySeconds = 60 //1分钟检测一次
		}
		WsHub = ws.NewHubWithConfig(&ws.HubConfig{
			SpaySeconds: spaySeconds,
		})
	}
	go WsHub.Run()
	time.Sleep(1 * time.Second)
	notifySymbolsChange() //初始化ws房间
	WsHub.AddMsgHandler("auth", handleAuth)
	WsHub.AddMsgHandler("close", handleCloseConn)
	WsHub.AddMsgHandler("ApiAuth", handleApiAuth)
	WsHub.AddMsgHandler("unAuth", handleExitAuth)
	WsHub.AddMsgHandler("market.symbol.switch", handleSwitchSymbol)
	WsHub.AddMsgHandler("market.depth.switch", handleSwitchDepth) //
	WsHub.AddMsgHandler(define.RoomSymbols, handleSymbolsMarket)  //订阅所有合约的价格及涨跌幅 以定时任务实现

	WsHub.AddMsgHandler("auth_test", handleAuthTest)
}

//退出用户鉴权
func handleExitAuth(c *ws.Client, message ws.WMessageRequest) {
	msg := ws.WMessagePack{}
	msg.Action = message.Action
	msg.Topic = message.Topic
	msg.Ts = time.Now().Unix()
	defer func() {
		c.SendMsg(msg.Marsha())
	}()
	c.UnRegisterUserClient()
}

//func handleHeart(c *ws.Client, message ws.WMessageRequest) {
//	msg := ws.WMessagePack{}
//	msg.Action = message.Action
//	msg.Topic = message.Topic
//	msg.Ts = time.Now().Unix()
//	defer func() {
//		c.SendMsg(msg.Marsha())
//	}()
//	c.SetRecHeart()
//}

func notifySymbolsChange() {
	var topics []string
	//获取支持交易对
	list, err := database.GetOnlineContractsTx(nil)
	if err != nil {
		list, err = database.GetSupportContract()
		if err != nil {
			log.Errorf("dao.GetSupportContract() fail,%v", err)
			return
		}
		log.Errorf("获取所有合约信息失败,%v", err)
		return
	}
	log.Infof("获取depthrooms:%+v", GetSymbolDepthRooms(list))
	for _, v := range list {
		supportSymbol.Set(v.ContractCode, v)
		topics = append(topics, GetSymbolRoomNames(v.ContractCode)...)
	}
	if isDepthUseRoom {
		topics = append(topics, GetSymbolDepthRooms(list)...)
	}
	topics = append(topics, define.RoomSymbols) //注册和交易对无关room
	WsHub.AddNewRooms(topics)
}

//处理每个depth等级使用不同的房间的rooms名称
func GetSymbolDepthRooms(list []proto.Contract) []string {
	var topics []string
	if isDepthUseRoom {
		for _, c := range list {
			for l := range define.DepthSupportLevel {
				name := fmt.Sprintf(define.RoomDepth, c.ContractCode, strconv.Itoa(l))
				topics = append(topics, name)
			}
		}
	}
	return topics
}

//处理自动给连接订阅的房间名
func GetSymbolRoomNames(symbol string) []string {
	var topics []string
	for _, room := range define.AutoSubRooms {
		topics = append(topics, fmt.Sprintf(room, symbol))
	}
	return topics
}

//检测并初始化ws房间
func checkSubAndInitWsRoom(contractCode string) bool {
	_, ok := supportSymbol.Get(contractCode)
	if !ok {
		c, _ := commonsrv.GetContractDetail(0, contractCode, nil)
		if c == nil {
			log.Infof("用户订阅合约,该合约不存在:%v", contractCode)
			return false
		}
		log.Infof("合约存在,准备初始化合约rooms,code:%v", contractCode)
		WsHub.AddNewRooms(GetSymbolRoomNames(contractCode)) //增加room处理
		supportSymbol.Set(contractCode, *c)
		return true
	}
	return true
}

func checkAndInitWsRoom(contractCode string) bool {
	_, ok := supportSymbol.Get(contractCode)
	if !ok {
		c, _ := commonsrv.GetContractDetail(0, contractCode, nil)
		if c == nil {
			log.Infof("用户订阅合约,该合约不存在:%v", contractCode)
			return false
		}
		if c.Delisted {
			return false
		}
		log.Infof("合约存在,准备初始化合约rooms,code:%v", contractCode)
		WsHub.AddNewRooms(GetSymbolRoomNames(contractCode)) //增加room处理
	}
	return true
}
