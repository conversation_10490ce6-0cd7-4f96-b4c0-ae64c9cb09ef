/*
@Time : 2019-12-27 11:05
<AUTHOR> mocha
@File : ws.service
*/
package service

import (
	"crypto/md5"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/dgrijalva/jwt-go"
	"go.uber.org/zap"
	"net/url"
	"sort"
	"spot/libs/cache"
	"spot/libs/convert"
	"spot/libs/sign"
	"spot/pkg/push/model"
	"strconv"
	"strings"
	"time"

	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/wserver"
)

func handleCloseConn(c *ws.Client, message ws.WMessageRequest) {

	msg := ws.WMessagePack{}
	msg.Action = message.Action
	msg.Topic = message.Topic
	msg.Ts = time.Now().Unix()
	defer func() {
		c.SendMsg(msg.Marsha())
	}()
	c.UnRegisterUserClient()
	c.Destory()
	return
}

func handleAuth(c *ws.Client, message ws.WMessageRequest) {
	auth := new(model.AuthArg)
	err := json.Unmarshal(message.Data, auth)
	if err != nil {
		log.Errorf("handleAuth unmarshal fail,%v", err)
		return
	}
	log.Infof("auth args:%+v", *auth)

	c.Token = auth.Token
	msg := ws.WMessagePack{}
	msg.Action = message.Action
	msg.Topic = message.Topic
	msg.Ts = time.Now().Unix()
	defer func() {
		c.SendMsg(msg.Marsha())
	}()

	params := make(map[string]interface{}, 0)
	params["token"] = auth.Token
	params["ts"] = auth.Ts
	params["app_id"] = auth.AppId
	os := strconv.Itoa(auth.AppId)
	//验证签名 todo
	var secretKey string
	switch os {
	case define.OsStrWeb, define.OsStrH5:
		secretKey = define.SecretKeyWeb
	case define.OsStrIos:
		secretKey = define.SecretKeyIos
	case define.OsStrAndroid:
		secretKey = define.SecretKeyAndroid
	default:
		log.Error("appId参数错误", zap.Any("params", params))
		msg.Code = define.ErrCodeParam
		msg.ErrMsg = "参数有误"
		return
	}
	//log.Infof("%+v",params)
	paramsStr := getParasStr(params)

	origin := paramsStr + secretKey
	log.Debug("validateSignRequest", zap.String("origin params", origin))
	//mySig := mySha256(myMd5(myMd5(origin)))
	mySig := sign.NormalEncode(origin)
	if auth.Sign != strings.ToLower(mySig) {
		log.Infof("mySig:%v,sign:%v", strings.ToLower(mySig), auth.Sign)
		log.Error("request请求签名验证失败", zap.Any("params", params))
		msg.Code = define.ErrCodeSignature
		msg.ErrMsg = "签名校验失败"
		return
	}

	userId, err := GetTokenUid(c.Token)
	if err == nil {
		c.UserId = userId
		c.RegisterUserClient()
		//ws.SendAuthFailMsg(c, msg.Marsha())
	} else {
		log.Infof("userid:%v,err:%v", userId, err)
		msg.Code = define.ErrCodeBusy
		msg.ErrMsg = "token校验失败"
	}
	return
}

func handleApiAuth(c *ws.Client, message ws.WMessageRequest) {
	auth := new(model.ApiAuthArg)
	err := json.Unmarshal(message.Data, auth)
	if err != nil {
		log.Errorf("handleAuth unmarshal fail,%v", err)
		return
	}
	log.Infof("auth args:%+v", *auth)

	c.Token = auth.AccessKey
	msg := ws.WMessagePack{}
	msg.Action = message.Action
	msg.Topic = message.Topic
	msg.Ts = time.Now().Unix()
	defer func() {
		c.SendMsg(msg.Marsha())
	}()

	params := make(map[string]interface{}, 0)
	params["accessKey"] = auth.AccessKey
	params["ts"] = auth.Ts
	params["apiV"] = auth.ApiV
	AccInfo, err := cache.GetAPiAccount(auth.AccessKey)
	if err != nil || len(AccInfo.AppSecret) == 0 {
		msg.Code = define.ErrCodeUserNotExist
		msg.ErrMsg = "用户不存在"
		return
	}
	if AccInfo.Status != 0 || AccInfo.ExpiredAt < time.Now().Unix() {
		msg.Code = define.ErrCodeUserNotExist
		msg.ErrMsg = "用户不存在"
		return
	}
	//log.Infof("%+v",params)
	paramsStr := getParasStr(params)

	origin := paramsStr + AccInfo.AppSecret
	log.Debug("validateSignRequest", zap.String("origin params", origin))
	//mySig := mySha256(myMd5(myMd5(origin)))
	mySig := sign.NormalEncode(origin)
	if auth.Sign != strings.ToLower(mySig) {
		log.Infof("mySig:%v,sign:%v", strings.ToLower(mySig), auth.Sign)
		log.Error("request请求签名验证失败", zap.Any("params", params))
		msg.Code = define.ErrCodeSignature
		msg.ErrMsg = "签名校验失败"
		return
	}

	userId := AccInfo.UserId
	if err == nil {
		c.UserId = userId
		c.RegisterUserClient()
		//ws.SendAuthFailMsg(c, msg.Marsha())
	} else {
		log.Infof("userid:%v,err:%v", userId, err)
		msg.Code = define.ErrCodeBusy
		msg.ErrMsg = "token校验失败"
	}
	return
}

func handleAuthTest(c *ws.Client, message ws.WMessageRequest) {
	auth := new(model.AuthArg)
	err := json.Unmarshal(message.Data, auth)
	if err != nil {
		return
	}

	c.Token = auth.Token
	msg := ws.WMessagePack{}
	msg.Action = message.Action
	msg.Topic = message.Topic
	msg.Ts = time.Now().Unix()
	defer func() {
		c.SendMsg(msg.Marsha())
	}()

	//params := make(map[string]interface{}, 0)
	//params["token"] = auth.Token
	//params["ts"] = auth.Ts
	//params["app_id"] = auth.AppId
	//os := strconv.Itoa(auth.AppId)
	////验证签名 todo
	//var secretKey string
	//switch os {
	//case define.OsStrWeb, define.OsStrH5:
	//	secretKey = define.SecretKeyWeb
	//case define.OsStrIos:
	//	secretKey = define.SecretKeyIos
	//case define.OsStrAndroid:
	//	secretKey = define.SecretKeyAndroid
	//default:
	//	log.Error("appId参数错误", zap.Any("params", params))
	//	msg.Code = define.ErrCodeParam
	//	msg.ErrMsg = "参数有误"
	//	return
	//}
	//paramsStr := getParasStr(params)
	//
	//origin := paramsStr + secretKey
	//log.Debug("validateSignRequest", zap.String("origin params", origin))
	//mySig := mySha256(myMd5(myMd5(origin)))
	//
	//if auth.Sign != strings.ToLower(mySig) {
	//	log.Error("request请求签名验证失败", zap.Any("params", params))
	//	msg.Code = define.ErrCodeSignature
	//	msg.ErrMsg = "签名校验失败"
	//	return
	//}
	//
	//userId, ok := checkAuth(c, msg)
	//if ok {
	//	c.UserId = userId
	//	//ws.SendAuthFailMsg(c, msg.Marsha())
	//} else {
	//	msg.Code = define.ErrCodeBusy
	//	msg.ErrMsg = "token校验失败"
	//}
	c.UserId = 181
	c.RegisterUserClient()
	return
}

func mySha256(s string) string {
	h := sha256.New()
	h.Write(convert.Str2Bytes(s))
	return hex.EncodeToString(h.Sum(nil))
}

func myMd5(s string) string {
	h := md5.New()
	h.Write(convert.Str2Bytes(s))
	return hex.EncodeToString(h.Sum(nil))
}

func getParasStr(params map[string]interface{}) string {
	paramsSli := make([]string, 0, len(params))
	getParamsSli(&paramsSli, params)
	sort.SliceStable(paramsSli, func(i, j int) bool {
		return paramsSli[i] < paramsSli[j]
	})
	return strings.Join(paramsSli, "&")
}

func getParamsSli(paramsSli *[]string, params map[string]interface{}) {
	for k, v := range params {
		switch val := v.(type) {
		case string:
			if len(val) == 0 {
				continue
			}
			//urlVal := url.Values{k: []string{val}}
			//fmt.Println(urlVal.Encode())
			//*paramsSli = append(*paramsSli, urlVal.Encode())
			tmp := k + "=" + url.QueryEscape(val)
			//fmt.Println(tmp)
			*paramsSli = append(*paramsSli, tmp)
		case int, int64:
			*paramsSli = append(*paramsSli, fmt.Sprintf("%s=%d", k, val))
		case bool:
			*paramsSli = append(*paramsSli, k+"="+strconv.FormatBool(val))
		case json.Number:
			*paramsSli = append(*paramsSli, k+"="+val.String())
		case float64:
			f := strconv.FormatFloat(val, 'f', -1, 64)
			*paramsSli = append(*paramsSli, k+"="+f)
		case []interface{}:
		case map[string]interface{}:
			getParamsSli(paramsSli, val)
		}
	}
}

//订阅行情
func handleSwitchSymbol(c *ws.Client, message ws.WMessageRequest) {
	msg := ws.WMessagePack{}
	msg.Action = message.Action
	msg.Topic = message.Topic
	msg.Ts = time.Now().Unix()
	defer func() {
		c.SendMsg(msg.Marsha())
	}()
	detail := new(model.SubSymbolArg)
	err := json.Unmarshal(message.Data, &detail)
	if err != nil {
		log.Errorf("handleSwitchSymbol json unMarshal fail,%v", err)
		return
	}
	if msg.Action != define.WsActionSub && msg.Action != define.WsActionUnSub {
		msg.Code = ws.Fail
		msg.ErrMsg = ws.ErrActionNotSupport
		return
	}

	if !checkSubAndInitWsRoom(detail.Symbol) {
		msg.Code = ws.Fail
		msg.ErrMsg = ws.ErrNotSupportSymbol
		return
	}

	oldSymbol := ""
	if msg.Action == define.WsActionUnSub {
		oldSymbol = detail.Symbol
	} else {
		oldSymbol = c.CurSymbol
	}

	//退订
	if oldSymbol != "" {
		for _, v := range define.AutoSubRooms {
			c.UnRegisterFromRoom(fmt.Sprintf(v, oldSymbol))
		}
		if isDepthUseRoom {
			c.UnRegisterFromRoom(fmt.Sprintf(define.RoomDepth, oldSymbol, strconv.Itoa(c.CurDepthLevel)))
		}
	}
	if msg.Action == define.WsActionSub {
		c.CurDepthLevel = define.DefalutDepthValue
		//订阅新的交易对房间
		for _, v := range define.AutoSubRooms {
			c.Register2Room(fmt.Sprintf(v, detail.Symbol))
		}
		if isDepthUseRoom {
			c.Register2Room(fmt.Sprintf(define.RoomDepth, detail.Symbol, "0"))
		}
	}
	c.CurSymbol = detail.Symbol
	return
}

//切换交易对深度
func handleSwitchDepth(c *ws.Client, message ws.WMessageRequest) {
	msg := ws.WMessagePack{}
	msg.Action = message.Action
	msg.Topic = message.Topic
	msg.Ts = time.Now().Unix()
	defer func() {
		c.SendMsg(msg.Marsha())
	}()
	detail := new(model.SubDepthSwitchArg)
	err := json.Unmarshal(message.Data, &detail)
	if err != nil {
		return
	}
	if msg.Action != define.WsActionSub {
		msg.Code = ws.Fail
		msg.ErrMsg = ws.ErrActionNotSupport
		return
	}

	if _, ok := define.DepthSupportLevel[detail.Level]; !ok {
		msg.Code = ws.Fail
		msg.ErrMsg = ws.ErrParam.Error()
		return
	}
	if isDepthUseRoom {
		c.UnRegisterFromRoom(fmt.Sprintf(define.RoomDepth, c.CurSymbol, strconv.Itoa(c.CurDepthLevel)))
		c.CurDepthLevel = detail.Level
		c.Register2Room(fmt.Sprintf(define.RoomDepth, c.CurSymbol, strconv.Itoa(detail.Level)))
	}
	c.CurDepthLevel = detail.Level
	return
}

//订阅所有交易价格,涨跌幅
func handleSymbolsMarket(c *ws.Client, message ws.WMessageRequest) {
	msg := ws.WMessagePack{}
	msg.Action = message.Action
	msg.Topic = message.Topic
	msg.Ts = time.Now().Unix()
	defer func() {
		c.SendMsg(msg.Marsha())
	}()
	if msg.Action == define.WsActionSub {
		c.Register2Room(msg.Topic)
	} else if msg.Action == define.WsActionUnSub {
		c.UnRegisterFromRoom(msg.Topic)
	} else {
		msg.Code = ws.Fail
		msg.ErrMsg = ws.ErrActionNotSupport
	}

	return
}

func GetTokenUid(token string) (userId int64, err error) {
	if len(token) == 0 {
		return
	}
	t, err := jwt.Parse(token, func(token *jwt.Token) (interface{}, error) {
		return convert.Str2Bytes(define.TokenKey), nil
	})
	if err != nil {
		//log.Error("parseToken failed", zap.Error(err))
		return
	}
	if !t.Valid {
		err = errors.New("token is valid")
		return
	}
	// 解析token载荷
	claim, ok := t.Claims.(jwt.MapClaims)
	if !ok {
		return 0, define.ErrMsgShouldLogin
	}
	ptr, ok := claim["jti"]
	if !ok {
		return 0, define.ErrMsgShouldLogin
	}
	jti, ok := ptr.(string)
	if !ok {
		return 0, define.ErrMsgShouldLogin
	}

	// 根据jti到redis获取用户信息
	tokenPayload, err := cache.GetTokenPayload(jti)
	if err != nil {
		return 0, define.ErrMsgShouldLogin
	}
	return tokenPayload.UserID, nil
}
