/*
@Time : 3/12/20 8:08 下午
<AUTHOR> mocha
@File : ws_test
*/
package service

import (
	"fmt"
	"github.com/gorilla/websocket"
	"spot/libs/convert"
	"spot/libs/json"
	"spot/libs/wserver"
	"spot/pkg/push/model"
	"testing"
)

func TestClient(te *testing.T) {
	//w, _, err := websocket.DefaultDialer.Dial("ws://127.0.0.1:9911/v1/ws/", nil)
	//w, _, err := websocket.DefaultDialer.Dial("wss://api.basecoin.pro:443/v1/ws/", nil)
	//w, _, err := websocket.DefaultDialer.Dial("wss://api.vill.work/v1/ws/", nil)
	//w, _, err := websocket.DefaultDialer.Dial("wss://api.officeqb.com:443/v1/ws/", nil)
	w, _, err := websocket.DefaultDialer.Dial("wss://api.luckyjerry.com/v2/spot/ws/", nil)
	//w, _, err := websocket.DefaultDialer.Dial("wss://api.luckyjerry.com:/v2/usd/ws/", nil)
	//w, _, err := websocket.DefaultDialer.Dial("wss://api.pandafe.pro/v1/ws/", nil)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("susccess")
	//b, err := ioutil.ReadAll(rsp.Body)
	//if err != nil {
	//	fmt.Println(err)
	//	return
	//}
	//fmt.Println(convert.Bytes2Str(b))

	go func() {
		for {
			w.SetPingHandler(func(appData string) error {
				//te.Log(appData)
				w.WriteMessage(websocket.PongMessage, nil)
				return nil
			})
			t, b, err := w.ReadMessage()
			//d, _ := zip.UnzipByte(b)
			//s := convert.Bytes2Str(b)
			te.Log(t, convert.Bytes2Str(b), err)
			//if strings.Contains(s, "depth") {
			//	te.Log(time.Now(), t, err)
			//}
		}
	}()
	//go func() {
	//	for {
	//		sub2 := ws.WMessageRequest{Action: "heart", Topic: "heart"}
	//		t3, _ := json.Marshal(sub2)
	//		w.WriteMessage(websocket.TextMessage, t3)
	//	}
	//}()

	go func() {

		t := model.SubSymbolArg{Symbol: "BTC/USDT"}
		//RE:
		t1, _ := json.Marshal(t)
		sub := ws.WMessageRequest{Action: "sub", Topic: "market.symbol.switch", Data: t1}
		t2, _ := json.Marshal(sub)
		w.WriteMessage(websocket.TextMessage, t2)

		//time.Sleep(15 * time.Second)
		//auth := ws.WMessageRequest{Action: "close", Topic: "close"}
		//as, _ := json.Marshal(auth)
		//w.WriteMessage(websocket.TextMessage, as)
		//time.Sleep(1 * time.Second)
		//w.WriteMessage(websocket.TextMessage, t2)
		//time.Sleep(1 * time.Second)
		//w.WriteMessage(websocket.TextMessage, t2)

		//sub2 := ws.WMessageRequest{Action: "sub", Topic: "symbols.market", Data: nil}
		//t3, _ := json.Marshal(sub2)
		//w.WriteMessage(websocket.TextMessage, t3)

		//time.Sleep(1 * time.Minute)
		//testDepthSwitch2(w)
		//
		//time.Sleep(1 * time.Minute)
		//testDepthSwitch3(w)

		//authReq := model.AuthArg{Token: "180", AppId: 1, Ts: time.Now().Unix(), Sign: "abc"}
		//s, _ := json.Marshal(authReq)
		//auth := ws.WMessageRequest{Action: "auth", Topic: "auth_test", Data: s}
		//auth := ws.WMessageRequest{Action: "auth", Topic: "auth", Data: s}
		//as, _ := json.Marshal(auth)
		//te.Logf("req:%+v", string(as))
		//as := []byte(`{"topic":"auth","action":"auth","data":{"sig":"5ace8720f1dd043a2a52c6a20acd5d97ca4a125f00af71cb6bab95ddc8b5b3a4","ts":"1611830289","app_id":"1","token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiIxNjYxNzQ2Njg0OTA4ODI0ODdfcGhvbmUiLCJpYXQiOjE2MTE4Mjc0NjgsInN1YiI6IjMzMmYxNTYzMWQwZjM2NThiNGQ2NDk0OWEwMDhhOWZlYmNkZDg4N2IifQ.Shzw7TUPEeWtd4nA_-EpfbvXrYln8MSFCuDwrFUd0Lk"}`)
		//w.WriteMessage(websocket.TextMessage, as)
		//time.Sleep(1 * time.Minute)
		//t = model.SubSymbolArg{ContractCode: "eth/usdt"}
		//goto RE
	}()
	select {}

}

func testDepthSwitch2(w *websocket.Conn) {
	s := model.SubDepthSwitchArg{Level: 2}
	b, _ := json.Marshal(s)
	sub2 := ws.WMessageRequest{Action: "sub", Topic: "market.depth.switch", Data: b}
	t3, _ := json.Marshal(sub2)
	w.WriteMessage(websocket.TextMessage, t3)
}

func testDepthSwitch3(w *websocket.Conn) {
	s := model.SubDepthSwitchArg{Level: 3}
	b, _ := json.Marshal(s)
	sub2 := ws.WMessageRequest{Action: "sub", Topic: "market.depth.switch", Data: b}
	t3, _ := json.Marshal(sub2)
	w.WriteMessage(websocket.TextMessage, t3)
}

func TestTokenS(t *testing.T) {
	token := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI3MTg1NTAzX3Bob25lIiwiaWF0IjoxNjA1MjYyMDY1LCJzdWIiOiJiOTMyOWQzNDQ0MjhjODNiIn0.bix-7PzJJ4TaV7zeWdYYsHBsSPcPG84a7TiQZwWHOIU"
	uid, err := GetTokenUid(token)
	if err != nil {
		t.Logf("t:%v", err)
		return
	}
	t.Logf("uid:%v", uid)
}
