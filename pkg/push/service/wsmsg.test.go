/*
@Time : 3/13/20 2:49 下午
<AUTHOR> mocha
@File : test
*/
package service

//
//func Test() {
//	time.Sleep(30 * time.Second)
//	log.Infof("开始发送测试数据")
//	testUserOrderStatus()
//	testUserHold()
//
//	ConditionOrder()
//	testTrade()
//}
//
//func testUserHold() {
//	up := &proto.UserPosition{UserId: 180, ContractCode: "ETHUSD", Side: "B", Volume: 100, NominalPrice: 1000.02, AccountType: 2,
//		Lever: 100, ForceClosePrice: 500.23, InitMargin: 200.23, PositionMargin: 100.20, AdjustMargin: 20.02, FloatProfit: 20.20, ProfitRatio: 20.02, Roic: 10.2, Commission: 14.2,
//	}
//	b, _ := json.Marshal(up)
//	s := mq.MessagePack{Data: b}
//	MQUserHoldMsg(s)
//}
//
//func testUserOrderStatus() {
//	order := &proto.Order{ThirdOrderId: 1, UserId: 180, Side: define.OrderBuy, Type: 1, Price: 1000.01, Volume: 100, TradeVolume: 0, Status: 0}
//	b, _ := json.Marshal(order)
//	s := mq.MessagePack{Data: b}
//	MQOrderStatusMsg(s)
//}
//
//func ConditionOrder() {
//	order := &proto.ConditionOrder{ConditionOrderId: 1, UserId: 180, Side: define.OrderBuy, Type: 1, Price: 1000.01, Volume: 100, Status: 0}
//	b, _ := json.Marshal(order)
//	s := mq.MessagePack{Data: b}
//	MQCondationStatusMsg(s)
//}
//
//func testTrade() {
//	order := &proto.Trade{TradeId: 1001, UserId: 180, Side: define.OrderBuy, Price: 200, Volume: 200, MarketPrice: 200.02, LastPrice: 200, Identity: 1}
//	b, _ := json.Marshal(order)
//	s := mq.MessagePack{Data: b}
//	MQUserNewTrade(s)
//}
