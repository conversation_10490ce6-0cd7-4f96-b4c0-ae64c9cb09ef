run_env: "test" #运行环境，prod-生产,test-测试，dev-开发
debug: true # 是否启用调试模式
zip_http: false # 是否启用zip压缩
h5_can_login: true # 是否启用h5注册后自动登录
local_name: "Panda Spot Test Task Server" # 服务名称
listen_addr: "0.0.0.0:8080" # http服务监听地址
rpc_addr: "************:18230" # rpc服务监听地址
pprof_addr: "************:18231" # 服务运行状态监听地址
discovery: "************:8500" # 服务发现地址
read_timeout: 30 # 读超时时间
write_timeout: 30 # 写超时时间
mq: "amqp://base:v3OEpqHwaaQlmq@************:5672" #rabbit mq config
default_db: "root:v3OEpqHwaaQlfbx@tcp(************:3306)/futures_basecoin?charset=utf8mb4&parseTime=true&loc=Local" # 数据库连接
log_file: "/data/logs/spot/task" # 日志文件位置
log_level: "info" # 日志等级
log_only_file: true # 是否仅打印日志到文件
mail_prefix: "【Panda】" # 发送邮件前缀(报警邮件用)
caution_receiver: [ ] # 警告邮件通知列表
sensitive_conf: "" # 敏感信息配置文件位置
wallet_url: "https://test.walletservice.com/api/v2" # 大钱包地址
wallet_token: "3760780f34c3dd2718f12fdf7b4f766d8cb079aa4f28870f2cc9a995215c6397" # 大钱包token
cws_network: "test" # main
h5_notice_page: "www.officeqb.com/notice?id=" # h5公告详情页
is_simulate: false # 是否是模拟盘
has_simulate: true # 是否有模拟盘
market_alive_duration: 90
share_language: true # 开启分享语言
c2c_listen_addr: "http://************:8809"
c2c_platform_id: "********************************"
c2c_secret_key: "e5b24964e503eb22d61be9f7735f92fa"
withdraw_caution_receiver: [ "<EMAIL>", "<EMAIL>" ] # 提币预警邮件通知列表
statistics_email_receiver: [ ] # 统计数据邮件接收列表
err_collect_email_receiver: [ ] # 错误收集邮件接收列表
withdraw_timeout_email_receiver: [ "<EMAIL>", "<EMAIL>" ] # 提币超时邮件接收列表
ip_white_list_for_cws: [ ] # cws回调ip白名单
msg_server_url: "http://************:10110" # 新短信邮件服务地址
msg_server_app_id: "025633e8-c0ea-463e-bc27-04c50e8b05b8" # 新短信邮件服务appID
msg_server_secret_key: "954146C06A993123F376A0F3DD7C8076F480B4C837AEB426746410F493800871" # 新短信邮件服务secret

# 商汤相关
sense_api_key: "4b8c74b521634cb598f6206fc0362ef9"
sense_api_secret: "13780492ee5144d18f33dde895f6de79"
sense_api_url: "https://v2-auth-api.visioncloudapi.com"

#主从及副本配置
is_slave: false
clone_nums: 1 #副本/服务个数，(配置大于1，触发服务则使用切片方式）
clone_id: 0 #副本id

default_redis_conf: { # 默认redis连接
  "address": "************:6379", # 连接地址
  "password": "v3OEpqHwaaQlrds", # 连接密码
  "use_tls": false, # 是否使用tls连接
  "default_db": 5, # 使用的db号
  "pool_size": 20, # 连接池数量
  "db_nums": { # 使用的redis库号,仅会初始化这里定义了的db号 key(db号):value(描述)
    #    0: "db 0",
    #    1: "db 1",
    2: "公共数据",
    #    3: "db 3",
    #    4: "USD合约",
    5: "现货",
    #    6: "db 6",
    #    7: "db 7",
    #    8: "db 8",
    #    9: "db 9",
    #    10: "db 10",
    #    11: "db 11",
    #    12: "db 12",
    #    13: "db 13",
    #    14: "db 14",
    #    15: "db 15",
  },
}

coin_gecko_open_api: "https://api.coingecko.com/api/v3" # 币虎接口
