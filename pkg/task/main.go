package main

import (
	"context"
	"fmt"
	"os"
	"spot/pkg/task/service"

	"spot/libs/cache"
	"spot/libs/conf"
	"spot/libs/database"
	"spot/libs/log"
	"spot/libs/msg"
	"spot/libs/pprof"
	"spot/libs/xrpcclient/user_rpc"
	"spot/pkg/task/server"
	"spot/pkg/task/task"
)

var (
	gitHash   string
	gitDigest string
	gitBranch string
	buildUser string
	buildTime string
	goVersion string
)

func main() {
	args := os.Args
	if len(args) == 2 && (args[1] == "--version" || args[1] == "-v") {
		fmt.Printf("Git Branch: %s \n", gitBranch)
		fmt.Printf("Git Commit Hash: %s \n", gitHash)
		fmt.Printf("Git Commit Digest: %s \n", gitDigest)
		fmt.Printf("Build User: %s \n", buildUser)
		fmt.Printf("Build TimeStamp: %s \n", buildTime)
		fmt.Printf("GoLang Version: %s \n", goVersion)
		return
	}

	// 初始化配置文件
	conf.Init()

	// 初始化日志
	log.InitLogger(conf.LogFile(), conf.LogLevel(), conf.LogOnlyFile())

	// 初始化短信
	msg.InitNewMsg(conf.DebugEnv(), conf.MsgServerURL(), conf.MsgServerAppID(), conf.MsgServerSecretKey())

	// 初始化缓存
	cache.InitDefaultRedisConn()
	if conf.IsSimulate() {
		cache.InitExtraRedisConn()
	}

	// 初始化数据库
	database.InitDefaultDB()

	// 重新加载必要数据到redis
	task.CacheBasicData()

	//初始化user服务
	user_rpc.InitUserClient(context.Background())

	//初始化消息队列
	service.InitMessageQueue()

	// 启动定时任务
	server.RunCronTask()

	// 初始化服务监控
	pprof.Init(conf.PprofAddr())

	// 监听系统信号
	server.InitSignal()
}
