package model

import (
	"github.com/shopspring/decimal"
	"time"
)

type ActivityInfoReq struct {
	CurrentInfo ActivityCurrent `json:"current_info"`
	AcStageIa   ActivityStageIa `json:"acstageia"`
	WardList    []WardList      `json:"wardlist"`
	ServerTime  string          `json:"servertime"`
}

//当期信息
type ActivityCurrent struct {
	StartTime    string          `json:"start_time" db:"start_time"`
	EndTime      string          `json:"end_time" db:"end_time"`
	OpeningTime  string          `json:"opening_time" db:"opening_time"`
	CurrentBonus decimal.Decimal `json:"current_bonus" db:"current_bonus"`
	EndDay       string          `json:"end_day" db:"end_day"`
	StarDay      string          `json:"star_day" db:"star_day"`
	MyTraders    int             `json:"my_traders" db:"my_traders"`
}

//上期信息
type ActivityStageIa struct {
	Hash   string   `json:"hash"`
	MyWard []MyWard `json:"myward"`
}

type ActivityLast struct {
	Hash           string `json:"hash" db:"hash"`
	ActivityListId int    `json:"id" db:"id"`
}

type MyWard struct {
	Amount    decimal.Decimal `json:"amount" db:"amount"`
	Frequency int             `json:"frequency" db:"frequency"`
	GetType   int             `json:"grade" db:"grade"`
}

//获取列表
type WardList struct {
	Amount   decimal.Decimal `json:"amount" db:"totalward"`
	UserName string          `json:"user_name" db:"user_name"`
}

type MyWardReply struct {
	Total int          `json:"total"` //记录数
	List  []MyWardList `json:"list"`
}

type MyWardList struct {
	Amount         decimal.Decimal `json:"bonus" db:"bonus"`
	UserId         int64           `json:"user_id" db:"user_id"`
	ID             int             `json:"id" db:"id"`
	GetType        int             `json:"grade" db:"grade"`
	CheckStatus    int             `json:"check_status" db:"check_status"`
	ActivityListId int             `json:"activity_list_id" db:"activity_list_id"`
	OpenTime       string          `json:"open_time" db:"open_time"`
	OrderId        string          `json:"order_id" db:"order_id"`
	ShareImg       string          `json:"share_img" db:"share_img"`
}

type MyWardArg struct {
	PageNo   int `json:"pageNo"`   // 页数
	PageSize int `json:"pagesize"` // 分页数量
}

type UpShareArg struct {
	Id     int    `json:"id"`     // 数据id
	ImgUrl string `json:"imgurl"` // 分页数量
}

type ActivityList struct {
	Id           int             `json:"id" db:"id"`
	ActivityName string          `json:"activity_name" db:"activity_name"`
	Describe     string          `json:"describe" db:"describe"`
	LimitUp      decimal.Decimal `json:"limit_up" db:"limit_up"`
	Ratio        decimal.Decimal `json:"ratio" db:"ratio"`
	StartDay     string          `json:"start_day" db:"start_day"`
	EndDay       string          `json:"end_day" db:"end_day"`
	StartTime    string          `json:"start_time" db:"start_time"`
	EndTime      string          `json:"end_time" db:"end_time"`
	OpeningTime  string          `json:"opening_time" db:"opening_time"`
	EndImgTime   time.Time       `json:"end_img_time" db:"end_img_time"`
	FirstPrize   float64         `json:"first_prize" db:"first_prize"`
	SecondAward  float64         `json:"second_award" db:"second_award"`
	ThirdAward   float64         `json:"third_award" db:"third_award"`
	CurrentBonus decimal.Decimal `json:"current_bonus" db:"current_bonus"`
}

type GetActivityByList struct {
	Id       int             `json:"id" db:"id"`
	SetBonus decimal.Decimal `json:"set_bonus" db:"set_bonus"`
	Hash     string          `json:"hash" db:"hash"`
}

type GetUserTradeList struct {
	TradeId string `json:"trade_id" db:"trade_id"`
	UserId  string `json:"user_id" db:"user_id"`
}

type ChannelActivity struct {
	ID        int    `db:"id" json:"id"`
	Rule      []byte `db:"rule" json:"rule"`
	StartTime int64  `db:"start_time" json:"start_time"`
	EndTime   int64  `db:"end_time" json:"end_time"`
	Valid     bool   `db:"valid" json:"valid"`
}

type ChannelActivityRule struct {
	Register struct {
		Reward struct {
			Min decimal.Decimal `json:"min"` // 奖励最小
			Max decimal.Decimal `json:"max"` // 奖励最大
		} `json:"reward"` // 奖励
	} `json:"register"` // 注册
}
