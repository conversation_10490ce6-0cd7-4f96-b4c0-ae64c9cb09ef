package model

import (
	"encoding/json"
	"github.com/shopspring/decimal"
	"spot/libs/define"
)

type TransferRpcArg struct {
	UserId    int64   `json:"userId"`
	TransWith string  `json:"trans_with"`
	Currency  string  `json:"currency"`
	Amount    float64 `json:"amount"`
	Sequence  string  `json:"sequence"`
	Remark    string  `json:"remark"`
}

type RpcReply struct {
	Action string          `json:"a"`
	Ret    uint16          `json:"r"`
	Msg    string          `json:"m"`
	Data   json.RawMessage `json:"d"`
}

type UserWallet struct {
	Balance        float64 `json:"balance" db:"balance"`
	Available      float64 `json:"available" db:"available"`
	OrderMargin    float64 `json:"order_margin" db:"order_margin"`
	PositionMargin float64 `json:"position_margin" db:"position_margin"`
}

type AssetDecreaseArg struct {
	Amount decimal.Decimal `json:"amount"`
}

type WithdrawState struct {
	UserID       int64                  `db:"user_id" json:"user_id"`
	OrderID      int64                  `db:"order_id" json:"order_id"`
	Amount       decimal.Decimal        `db:"amount" json:"amount"`
	CurrencyName string                 `db:"currency_name" json:"currency_name"`
	Tag          string                 `db:"tag" json:"tag"`
	ToAddr       string                 `db:"to_addr" json:"to_addr"`
	State        define.WalletBillState `db:"status" json:"state"`
}
