package model

import (
	"github.com/shopspring/decimal"
	"time"

	"spot/libs/define"
)

var (
	// 资金费率相关定义

	// 时间区间 当前时间的小时数/8
	FundingTimeZoneFirst  = 0 // [0-8)
	FundingTimeZoneSecond = 1 // [8-16)
	FundingTimeZoneThird  = 2 // [16-0)
)

// 检查更新请求参数
type CheckUpgradeArg struct {
	OS         define.OsType  `json:"os"`          // 客户端类型 1 android 2 ios
	Lang       define.ReqLang `json:"lang"`        // 客户端语言
	PlatformID int            `json:"platform_id"` // 平台id
	ChannelID  int            `json:"channel_id"`  // 渠道id
	VerNum     int            `json:"ver_num"`     // 客户端数字版本号
}

type VersionInfo struct {
	PlatformID    int            `db:"platform_id" json:"platform_id"`       // 平台id
	OSType        define.OsType  `db:"os_type" json:"os_type"`               // 平台类型
	LangType      define.ReqLang `db:"lang_type" json:"lang_type"`           // 语言类型
	Content       string         `db:"content" json:"content"`               // 更新文案
	Version       int            `db:"version" json:"version"`               // 数字版本号
	VersionString string         `db:"version_string" json:"version_string"` // 文字版本号
	ForceUpgrade  bool           `db:"force_upgrade" json:"force_upgrade"`   // 是否强制升级
	Link          string         `db:"link" json:"link"`                     // 下载页面
	URL           string         `db:"url" json:"url"`                       // 下载链接
	ExtraURL      string         `db:"base_url" json:"extra_url"`            // 额外下载链接
	CreatedTime   time.Time      `db:"created_time" json:"created_time"`     // 更新时间

	LastForce int `db:"last_force" json:"last_force"` // 最后一个强制更新版本
}

// 版本更新信息
type UpgradeInfo struct {
	NeedUpgrade   bool   `json:"needUpgrade"`                    // 需要更新
	VersionString string `db:"version_string" json:"version"`    // 文字版本号
	Content       string `db:"content" json:"content"`           // 更新内容
	ForceUpgrade  bool   `db:"force_upgrade" json:"force"`       // 是否强制更新
	Link          string `db:"link" json:"link"`                 // 下载界面
	Url           string `db:"url" json:"url"`                   // 下载链接
	ExtraUrl      string `db:"base_url" json:"extra_url"`        // 额外下载链接
	CreatedTime   int64  `db:"created_time" json:"created_time"` // 更新时间(时间戳)
}

// 验证码
type AuthCodeInfo struct {
	Code string `json:"code"` // 短信验证码
	Mode uint8  `json:"mode"` // 操作 0 常规请求 1 找回密码
}

// banner图返回值
type BannerReply struct {
	Title      string         `json:"title,omitempty" db:"title"`             // 标题
	Image      string         `json:"image,omitempty" db:"image"`             // 图片
	Link       string         `json:"link,omitempty" db:"link"`               // 跳转链接
	Start      int64          `json:"start,omitempty" db:"start"`             // 开始时间
	End        int64          `json:"end,omitempty" db:"end"`                 // 结束时间
	Limit      define.OsType  `json:"limit,omitempty" db:"limit"`             // 轮播图对应客户端 0 全平台, 1 安卓, 2 ios 3 web
	Weight     float64        `json:"weight,omitempty" db:"weight"`           // 显示权重 倒序
	PlatformID int            `json:"platform_id,omitempty" db:"platform_id"` // 平台id
	LangType   define.ReqLang `json:"lang_type,omitempty" db:"lang_type"`     // 语言类型 0-简中 1-英文 2-繁中 3-韩语 4-日语
}

// 公告数据
type NoticeDetail struct {
	ID          int            `db:"id" json:"id"`                     // 公告id
	IsTop       bool           `db:"is_top" json:"is_top"`             // 是否置顶
	IsImportant bool           `db:"is_important" json:"is_important"` // 是否是重要通知
	CreatedAt   time.Time      `db:"created_at" json:"created_at"`     // 创建时间
	Title       string         `db:"title" json:"title"`               // 标题
	Content     string         `db:"content" json:"content"`           // 内容
	Link        string         `db:"link" json:"link"`                 // 跳转链接
	PlatformID  int            `db:"platform_id" json:"platform_id"`   // 平台id
	LangType    define.ReqLang `db:"lang_type" json:"lang_type"`       // 语言类型 0-简中 1-英文 2-繁中 3-韩语 4-日语
}

// 公告列表返回数据
type NoticeListReply struct {
	ID          int    `db:"id" json:"id"`                     // 公告id
	IsTop       bool   `db:"is_top" json:"is_top"`             // 是否置顶
	IsImportant bool   `db:"is_important" json:"is_important"` // 是否是重要通知
	CreatedAt   int64  `db:"created_at" json:"created_at"`     // 创建时间
	Title       string `db:"title" json:"title"`               // 标题
	Content     string `db:"content" json:"content,omitempty"` // 内容
	Link        string `db:"link" json:"link"`                 // 跳转链接
}

// 公告详情返回数据
type NoticeInfoReply struct {
	ID        int    `db:"id" json:"id"`                // 公告id
	CreatedAt int64  `db:"created_at" json:"createdAt"` // 发布时间
	Title     string `db:"title" json:"title"`          // 标题
	Content   string `db:"content" json:"content"`      // 内容
}

// ImportantNotice 重要通知
type ImportantNotice struct {
	ID         int            `db:"id" json:"id"`                   // 通知id
	PlatformID int            `db:"platform_id" json:"platform_id"` // 平台id
	Title      string         `db:"title" json:"title"`             // 标题
	Content    string         `db:"content" json:"content"`         // 内容
	Link       string         `db:"link" json:"link"`               // 跳转链接
	NoticeType int            `db:"notice_type" json:"notice_type"` // 通知类型 1-文本通知 2-图片通知
	LangType   define.ReqLang `db:"lang_type" json:"lang_type"`     // 语言类型
	StartTime  time.Time      `db:"start_time" json:"start_time"`   // 开始时间
	EndTime    time.Time      `db:"end_time" json:"end_time"`       // 结束时间
}

// ImportantNoticeReply 重要通知返回数据
type ImportantNoticeReply struct {
	ID         int    `json:"id"`          // 通知id
	Title      string `json:"title"`       // 标题
	Content    string `json:"content"`     // 内容
	Link       string `json:"link"`        // 跳转链接
	NoticeType int    `json:"notice_type"` // 通知类型 1-文本通知 2-图片通知
}

// 检查更新请求参数
type AppDownloadArg struct {
	OS string `json:"os"` // 客户端类型 1 android 2 ios
}

// 检查更新请求参数
type AppDownloadReply struct {
	OS   define.OsType        `json:"os"`   // 客户端类型 1 android 2 ios
	List []AppDownloadSubData `json:"list"` // 下载链接
}

type AppDownloadSubData struct {
	ChannelID int               `json:"channel_id"` // 渠道id 安卓{0-极速下载 1-本地下载} IOS{0-testflight 1-超级签 2-企业签}
	Version   string            `json:"version"`    // 版本号
	URLs      map[string]string `json:"urls"`       // 下载地址列表
}

type TimeArg struct {
	Day string `json:"day"` // 2020-01-01
}

//获取国家区域信息
type CountryAreacode struct {
	ID             int64  `db:"id" json:"id"`                           // 数据id
	CountryEn      string `db:"country_en" json:"country_en"`           // 英文
	CountryCn      string `db:"country_cn" json:"country_cn"`           // 中文
	CountryTw      string `db:"country_tw" json:"country_tw"`           // 繁体
	CountryCode    string `db:"country_code" json:"country_code"`       // 编号
	CountryEncrypt string `db:"country_encrypt" json:"country_encrypt"` // 国家代码
}

type CurrentFundingRate struct {
	ContractCode string `db:"contract_code" json:"contract_code"`
	FundingRate  string `db:"funding_rate" json:"funding_rate"`
}

type AboutCommunityReply struct {
	SupportEmail string      `json:"support_email"` // 客服邮箱
	Communities  []Community `json:"communities"`   // 社群列表
}

type Community struct {
	Name     string `json:"name"`      // 社群名称(只会返回英文名称)
	Icon     string `json:"icon"`      // 社群图标
	Account  string `json:"account"`   // 账户名
	Link     string `json:"link"`      // 链接(微信为二维码链接)
	IsWechat bool   `json:"is_wechat"` // 是否是微信(微信连接为二维码图片链接,需要特殊处理)
}

type AboutCommunity struct {
	PlatformID    int    `db:"platform_id" json:"platform_id"`       // 平台id
	Community     string `db:"community" json:"community"`           // 社群名称
	CommunityIcon string `db:"community_icon" json:"community_icon"` // 社群图标
	Account       string `db:"account" json:"account"`               // 账户名
	Link          string `db:"link" json:"link"`                     // 链接
	Category      int    `db:"category" json:"category"`             // 类型 1-客服邮箱 2-社群
	IsWechat      bool   `db:"is_wechat" json:"is_wechat"`           // 是否是微信
}

type ServerConfig struct {
	HostList             []string        `json:"host_list"`              // 域名列表
	ServerHosts          []ServerHost    `json:"server_hosts"`           // 域名列表
	PublicURL            string          `json:"public_url"`             // 公共域名
	PrivateURL           string          `json:"private_url"`            // 私有域名
	IsSlider             bool            `json:"is_slider"`              // 是否开启网易滑块
	IsThirdKYC           bool            `json:"is_third_kyc"`           // 是否走三方KYC
	ChinaMobileChecking  bool            `json:"china_mobile_checking"`  // 是否开启国内手机号限制
	GestureInterval      int             `json:"gesture_interval"`       // 手势密码间隔
	MaxAPICount          int             `json:"max_api_count"`          // 最大api个数
	TraderLatelyPosition int             `json:"trader_lately_position"` // 交易员持仓数据显示
	TraderLatelyHistory  int             `json:"trader_lately_history"`  // 交易员历史数据条数
	ShowFollowUsers      int             `json:"show_follow_users"`      // 跟随着盈利从大到小人数
	BlowingRate          decimal.Decimal `json:"blowing_rate"`           // 爆仓率
}

type ServerHost struct {
	Recommend   bool   `json:"recommend"`    // 是否推荐
	HostAddress string `json:"host_address"` // 主机地址
}

type Pong struct {
	IP string `json:"ip"`
}

type ClientErrorArg struct {
	List []ClientErrorLog `json:"list"`
}

type ClientErrorLog struct {
	ErrorID    int64         `json:"error_id" db:"error_id"`       // 记录id
	ErrorType  uint16        `json:"error_type" db:"error_type"`   // 错误类型 1-本地异常 2-api response 3-Ws response ... 其它http错误码
	ErrorMsg   string        `json:"error_msg" db:"error_msg"`     // 报错内容
	Os         define.OsType `json:"os" db:"os"`                   // 设备类型（1: android 2: iOS 3: WEB 4: H5 7: 管理后台)
	Version    int           `db:"version" json:"version"`         // app版本号
	SystemOs   string        `json:"system_os" db:"system_os"`     // 操作系统
	DeviceName string        `json:"device_name" db:"device_name"` // 设备型号
	IPAddress  string        `json:"ip_address" db:"ip_address"`   // ip地址
	Location   string        `json:"location" db:"location"`       // 所在地
	UserID     int64         `db:"user_id" json:"user_id"`         // 用户id
	ErrorTime  int64         `json:"error_time" db:"error_time"`   // 错误产生时间
	SubmitTime time.Time     `json:"submit_time" db:"submit_time"` // 提交时间
}

type AppDownloadSubmitArg struct {
	Os           define.OsType `json:"os"`            // 设备类型（1: android 2: iOS)
	DownloadMode int           `json:"download_mode"` // 下载模式 1-极速下载 2-本地下载 3-本地更新
	AppVersion   string        `json:"app_version"`   // 下载版本号
}
