package model

type ApiErrCodeList struct {
	ErrCode int    `db:"err_code" json:"err_code"`
	Count   string `db:"count" json:"count"`
}

type ExternalErrList struct {
	ErrCode int    `db:"err_code" json:"err_code"`
	ErrMsg  string `db:"err_msg" json:"err_msg"`
	Count   string `db:"count" json:"count"`
}

type TickCloseErrList struct {
	ContractCode string `db:"contract_code" json:"contract_code"`
	Count        string `db:"count" json:"count"`
}
