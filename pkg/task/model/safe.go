package model

import "spot/libs/proto"

// 用于返回前端,通知需要提交的验证类型
type SafeVerifyState struct {
	Phone      string `json:"phone"`       // 需要验证的手机
	Email      string `json:"email"`       // 需要验证的邮箱
	SpareEmail string `json:"spare_email"` // 需要验证的备用邮箱
	Totp       bool   `json:"totp"`        // 需要验证令牌
}

// 两步验证参数
type SafeTwoStep struct {
	Phone          string `json:"phone"`            // 需要验证的手机
	PhoneCode      string `json:"phone_code"`       // 手机验证码
	Email          string `json:"email"`            // 需要验证的邮箱
	EmailCode      string `json:"email_code"`       // 邮箱验证码
	SpareEmail     string `json:"spare_email"`      // 需要验证的备用邮箱
	SpareEmailCode string `json:"spare_email_code"` // 备用邮箱验证码
	Totp           string `json:"totp"`             // 需要验证令牌
	TotpCode       string `json:"totp_code"`        // 令牌验证码
}

// 改绑账号请求参数
type SafeModifyAccountArg struct {
	NewAccount      string `json:"new_account"`  // 新账号
	AreaCode        string `json:"area_code"`    // 手机区号
	CountryCode     string `json:"country_code"` // 国家代码
	Code            string `json:"code"`         // 验证码
	VerifySwitch    bool   `json:"-"`            // 登录验证开关
	VerifySwitchIOS uint8  `json:"login_verify"` // 登录验证开关 0-关闭 1-开启
	proto.SafeVerifyCode
}

// 改绑备用邮箱请求参数
type SafeModifySpareEmailArg struct {
	NewEmail string `json:"new_email"` // 新备用邮箱
	Code     string `json:"code"`      // 验证码
	proto.SafeVerifyCode
}

// 改密码请求参数
type SafeModifyPasswordArg struct {
	NewPassword string `json:"new_password"` // 新密码
	VerifyType  uint8  `json:"verify_type"`  // 验证类型(修改资金密码使用),0-永不验证 1-24小时内验证一次 2-永远验证
	proto.SafeVerifyCode
}

// 重置密码请求参数
type SafeResetPasswordArg struct {
	Account     string `json:"account"`      // 账号
	NewPassword string `json:"new_password"` // 新密码
	proto.SafeVerifyCode
}

// 判断用户需要的两步验证参数
type UserSafeVerifyMode struct {
	Phone      string `json:"phone"`       // 需要验证的手机
	Email      string `json:"email"`       // 需要验证的邮箱
	SpareEmail string `json:"spare_email"` // 需要验证的备用邮箱
	Totp       string `json:"totp"`        // 需要验证的令牌
}

// 改资金密码验证时效请求参数
type SafeModifyTradeVerifyArg struct {
	VerifyType uint8 `json:"verify_type"` // 验证类型(修改资金密码使用),0-永不验证 1-24小时内验证一次 2-永远验证
	proto.SafeVerifyCode
}

type SafeVerifyPWD struct {
	Password string `json:"password"` // 资金密码
}

// 返回最新的谷歌私钥
type SafeTotpSecret struct {
	Secret string `json:"secret"` // 验证器私钥
}

// 修改谷歌私钥请求参数
type SafeModifyTotpSecretArg struct {
	Code            string `json:"code"`         // 验证码
	VerifySwitch    bool   `json:"-"`            // 登录验证开关
	VerifySwitchIOS uint8  `json:"login_verify"` // 登录验证开关 0-关闭 1-开启
	proto.SafeVerifyCode
}

// 修改登录验证方式
type SafeModifyLoginVerifyModeArg struct {
	VerifyPhone    bool  `json:"-"`            // 是否开启验证手机
	VerifyEmail    bool  `json:"-"`            // 是否开启验证邮箱
	VerifyTotp     bool  `json:"-"`            // 是否开启验证令牌
	VerifyPhoneIOS uint8 `json:"verify_phone"` // 是否开启验证手机 0-关 1-开
	VerifyEmailIOS uint8 `json:"verify_email"` // 是否开启验证邮箱
	VerifyTotpIOS  uint8 `json:"verify_totp"`  // 是否开启验证令牌
	proto.SafeVerifyCode
}

func (mlv *SafeModifyLoginVerifyModeArg) Parse() {
	mlv.VerifyPhone = mlv.VerifyPhoneIOS != 0
	mlv.VerifyEmail = mlv.VerifyEmailIOS != 0
	mlv.VerifyTotp = mlv.VerifyTotpIOS != 0
}

type SafeLoginVerifyModeState struct {
	VerifyPhone bool `json:"verify_phone"` // 是否开启验证手机
	VerifyEmail bool `json:"verify_email"` // 是否开启验证邮箱
	VerifyTotp  bool `json:"verify_totp"`  // 是否开启验证令牌
}

type SafePasswordMiscount struct {
	Miscount int64 `json:"miscount"` // 错误计数
}

type SafeVerifyMarkID struct {
	VerifyID int64 `json:"verify_id"` // 验证id
}
