package model

import (
	"github.com/shopspring/decimal"
	"time"
)

//"contract_code": "BTCUSD", // 合约code
//"hold_type": 1, // 持仓类型 1:全仓 2:逐仓
//"side": "B", // 方向 B买 S卖
//"price": "10000.01", // 价格
//"pay_money": "37.02", // 购买的金额
//"lever": 10 // 杠杠倍数
type OrderPlanArgs struct {
	ContractCode string          `json:"contract_code"`
	HoldType     int             `json:"hold_type"`
	Side         string          `json:"side"`
	Price        string          `json:"price"` //触发价
	Amount       decimal.Decimal `json:"amount"`
	//PayMoney     string `json:"pay_money"`
	Lever        int    `json:"lever"`
	Limit        string `json:"limit"`         // 止盈价格
	Stop         string `json:"stop"`          // 止损价格
	PlatformID   int    `json:"platform_id"`   //平台id
	FundPassword string `json:"fund_password"` // 资金密码

	EntrustType     int             `db:"entrust_type" json:"entrust_type"`         //委托类型 0-市价 1-限价
	EntrustStrategy int             `db:"entrust_strategy" json:"entrust_strategy"` //委托策略 0-默认 1-fok 2-ioc 3-maker
	EntrustPrice    decimal.Decimal `db:"entrust_price" json:"entrust_price"`       //委托执行价
	Mode            int             `db:"mode" json:"mode"`                         //下单模式 1-对手价 2-最优3挡 3-最优5挡
	CurrencyName    string          `db:"currency_name" json:"currency_name"`
}

type OrderCancelArg struct {
	ID           int64  `json:"id"`
	OrderId      int64  `json:"order_id"`
	PlatformID   int    `json:"platform_id"`   //平台id
	FundPassword string `json:"fund_password"` // 资金密码
}

//"position_id": 123456, // 持仓id
//"limit":"39.29", //止盈价
//"stop":"39.29", //止损价
type OrderFullStopArgs struct {
	PlanCloseOrderID int64  `json:"plan_close_order_id"`
	PositionId       int64  `json:"position_id"`
	LimitStr         string `json:"limit"`
	StopStr          string `json:"stop"`
	Limit            decimal.Decimal
	Stop             decimal.Decimal
	Amount           decimal.Decimal `json:"amount"`
	PlatformID       int             `json:"platform_id"`   //平台id
	FundPassword     string          `json:"fund_password"` // 资金密码
}

type PlanContactLeverCount struct {
	Lever int `json:"lever"`
	Count int `json:"count"`
}

type PlanOrderInitData struct {
	PlanOrderID  int64           `json:"plan_order_id" db:"plan_order_id"`
	UserId       int64           `json:"user_id" db:"user_id"`
	TriggerPrice decimal.Decimal `json:"trigger_price" db:"trigger_price"`
}

type StopPriceListArg struct {
	UserID      int64 `json:"user_id" db:"user_id"`
	PlanOrderID int64 `json:"plan_order_id" db:"plan_order_id"`
	PlatformID  int   `json:"platform_id"` //平台id
}

type StopPriceListReply struct {
	PlanCloseOrderID int64           `json:"plan_close_order_id" db:"plan_close_order_id"`
	ContractCode     string          `json:"-" db:"contract_code"`
	Amount           int             `json:"amount" db:"amount"`
	Limit            decimal.Decimal `json:"limit" db:"limit"`
	Stop             decimal.Decimal `json:"stop" db:"stop"`
	CreateTime       time.Time       `db:"create_time" json:"create_time"`
}

type StopPriceListApiReply struct {
	PlanCloseOrderID int64  `json:"plan_close_order_id" db:"plan_close_order_id"`
	ContractCode     string `json:"-" db:"contract_code"`
	Amount           int    `json:"amount" db:"amount"`
	Limit            string `json:"limit" db:"limit"`
	Stop             string `json:"stop" db:"stop"`
	CreateTime       int64  `json:"create_time"`
}

type CancelStopPriceArg struct {
	PlanCloseOrderID int64  `json:"plan_close_order_id" db:"plan_close_order_id"`
	UserID           int64  `json:"user_id" db:"user_id"`
	PlatformID       int    `json:"platform_id"`   //平台id
	FundPassword     string `json:"fund_password"` // 资金密码
}

type MarginModifyArg struct {
	PositionId int64           `json:"position_id"` // 持仓id
	Amount     decimal.Decimal `json:"amount"`      // 数量
	Side       string          `json:"side"`        // 方向 add-增加 sub-减少
	PlatformID int             `json:"platform_id"` //平台id
}

type HistoryEntrustArg struct {
	UserID       int64            `json:"user_id" db:"user_id"`             // 用户id
	ContractCode string           `json:"contract_code" db:"contract_code"` // 合约代码
	Offset       string           `db:"offset" json:"offset"`               // 开平仓方向
	Filter       OrderStateFilter `db:"filter" json:"filter"`               // 筛选状态 0-全部 1-已完成
}

type HistoryPlanEntrustArg struct {
	UserID       int64             `json:"user_id" db:"user_id"`             // 用户id
	ContractCode string            `json:"contract_code" db:"contract_code"` // 合约代码
	Filter       PlanEntrustFilter `db:"filter" json:"filter"`               // 筛选状态 0-全部 1-计划单 2-止盈止损
}
