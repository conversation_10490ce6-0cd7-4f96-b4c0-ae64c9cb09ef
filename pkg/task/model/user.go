package model

import (
	"spot/libs/define"
	"spot/libs/proto"
)

type Invite<PERSON>hain struct {
	Inviter int64 `json:"inviter"` // 邀请人
	Parent  int64 `json:"parent"`  // 邀请人的上级
}

type ModifyUserInfoArg struct {
	Nickname    string `db:"nickname" json:"nickname"`         // 中文昵称
	NicknameEn  string `db:"nickname_en" json:"nickname_en"`   // 英文昵称
	Introduce   string `db:"introduce" json:"introduce"`       // 中文介绍
	IntroduceEn string `db:"introduce_en" json:"introduce_en"` // 英文介绍
	Avatar      string `db:"avatar" json:"avatar"`             // 头像
}

// 动作>:库字段名,校验,防止sql注入
var _userSwitch = map[string]string{
	UserSwitchKeyTradeConfirm:        UserSwitchKeyTradeConfirm,
	UserSwitchKeyFastCloseoutConfirm: UserSwitchKeyFastCloseoutConfirm,
}

const (
	UserSwitchKeyTradeConfirm        = "trade_confirm"
	UserSwitchKeyFastCloseoutConfirm = "fast_closeout_confirm"
)

type UserSwitchArg struct {
	Action  string `json:"action"`   // 功能key
	Open    bool   `json:"open"`     // 是否开启
	OpenFix int    `json:"open_fix"` // 是否开启,不支持bool类型的客户端用 0-关闭 1-开启
}

func (us *UserSwitchArg) Key() string {
	return _userSwitch[us.Action]
}

type userOption interface {
	check(int) bool
}

type uoChangeStyle struct{}

func (u *uoChangeStyle) check(option int) bool {
	switch option {
	case define.UserChangeStyleGreenUp, define.UserChangeStyleRedUp:
		return true
	default:
		return false
	}
}

type uoProfitStyle struct{}

func (u *uoProfitStyle) check(option int) bool {
	switch option {
	case define.UserProfitStyleWithIndex, define.UserProfitStyleWithTrade:
		return true
	default:
		return false
	}
}

var (
	_ userOption = new(uoChangeStyle)
	_ userOption = new(uoProfitStyle)
)

// 动作>:库字段名,校验,防止sql注入
var _userOption = map[string]userOption{
	UserOptionKeyChangeStyle: new(uoChangeStyle),
	UserOptionKeyProfitStyle: new(uoProfitStyle),
}

const (
	UserOptionKeyChangeStyle = "change_style"
	UserOptionKeyProfitStyle = "profit_style"
)

type UserOptionArg struct {
	Action string `json:"action"` // 功能key
	Option int    `json:"option"` // 选项值
}

func (uo *UserOptionArg) Key() string {
	o, ok := _userOption[uo.Action]
	if !ok || !o.check(uo.Option) {
		return ""
	}
	return uo.Action
}

type RegisterConfigOfSuperiorAgent struct {
	LabelID    int  `db:"label_id" json:"label_id"`         // 标签id
	IsChildAPI bool `db:"is_child_api" json:"is_child_api"` // 是否有开通api功能权限
}

type UserApiArg struct {
	ID     int    `json:"id"`      // 记录id
	Remark string `json:"remark"`  // 备注名
	IPList string `json:"ip_list"` // 绑定ip列表,以','分隔
	proto.SafeVerifyCode
}

type UserAPI struct {
	ID         int    `db:"id" json:"id"`                   // 记录id
	PlatformID int    `db:"platform_id" json:"platform_id"` // 平台id
	AppName    string `db:"app_name" json:"app_name"`       // 备注
	UserID     int64  `db:"user_id" json:"user_id"`         // 用户id
	AppKey     string `db:"app_key" json:"app_key"`         // 公钥
	AppSecret  string `db:"app_secret" json:"app_secret"`   // 私钥
	AuthIPs    string `db:"auth_ips" json:"auth_ips"`       // 绑定ip
	ExpiredAt  int64  `db:"expired_at" json:"expired_at"`   // 失效时间戳
	CreateTime int64  `db:"create_time" json:"create_time"` // 创建时间
	State      int    `db:"state" json:"state"`             // 状态 0-正常 1-删除 2-失效(该状态为过期后赋值,库里没有)
}

type UserAPIReply struct {
	ID         int    `db:"id" json:"id"`                           // 记录id
	AppName    string `db:"app_name" json:"app_name"`               // 备注
	AppKey     string `db:"app_key" json:"app_key"`                 // 公钥
	AppSecret  string `db:"app_secret" json:"app_secret,omitempty"` // 私钥
	AuthIPs    string `db:"auth_ips" json:"auth_ips"`               // 绑定ip
	CreateTime int64  `db:"create_time" json:"create_time"`         // 创建时间
	State      int    `db:"state" json:"state"`                     // 状态 0-正常 1-删除 2-失效(该状态为过期后赋值,库里没有)
}
