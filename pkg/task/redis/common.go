package redis

import (
	"github.com/go-redis/redis"
	"spot/libs/cache"
	"spot/libs/define"
)

func GetCoreServerTask() (map[string][]byte, error) {
	var (
		err    error
		cursor uint64
		result []string
	)
	task := make(map[string][]byte)

loop:
	result, cursor, err = cache.DefaultRedis().HScan(define.CacheKeyCoreServerTask, cursor, "", 0).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, err
	}

	for i, val := range result {
		if i|1 == i {
			// 奇数位为值
			task[result[i-1]] = []byte(val)
		}
	}

	if cursor != 0 {
		goto loop
	}

	return task, nil
}
