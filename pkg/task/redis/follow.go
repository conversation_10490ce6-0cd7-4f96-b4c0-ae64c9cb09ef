package redis

import (
	"strconv"

	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/pkg/task/model"
)

func SetLeaderList(key string, list []model.LeaderListData) {
	data, err := json.Marshal(list)
	if err != nil {
		log.Error("SetLeaderList json error", zap.String("key", key), zap.Error(err))
		return
	}

	err = cache.DefaultRedis().Set(key, data, 0).Err()
	if err != nil {
		log.Error("SetLeaderList redis error", zap.String("key", key), zap.Error(err))
	}
}

func SetLeaderDetail(list []model.LeaderListData) {
	pipe := cache.DefaultRedis().TxPipeline()
	pipe.Del(define.CacheLeaderDetailData)
	for i := range list {
		data, err := json.Marshal(list[i])
		if err != nil {
			log.Error("SetLeaderDetail json error", zap.Int64("traderID", list[i].UserID), zap.Error(err))
			return
		}
		pipe.HSet(define.CacheLeaderDetailData, strconv.FormatInt(list[i].UserID, 10), data)
	}
	_, err := pipe.Exec()
	if err != nil {
		log.Error("SetLeaderDetail redis exec error", zap.Error(err))
	}
}

func SetLeaderFollowDetail(list []model.LeaderDetailListData) {
	for i := range list {
		data, err := json.Marshal(list[i])
		if err != nil {
			log.Error("SetLeaderFollowDetail json error", zap.Int64("traderID", list[i].UserID), zap.Error(err))
			return
		}

		err = cache.DefaultRedis().HSet(define.CacheLeaderFollowDetailData, strconv.FormatInt(list[i].UserID, 10), data).Err()
		if err != nil {
			log.Error("SetLeaderFollowDetail redis error", zap.Int64("traderID", list[i].UserID), zap.Error(err))
		}
	}
}
