package server

import (
	"github.com/robfig/cron"
	"spot/libs/commonsrv"
	"spot/libs/conf"
	"spot/libs/log"
	"spot/pkg/task/task"
)

const (
	everySecond           = "* * * * * *"        // 每秒执行
	assetFunding          = "0 0 %v,%v,%v * * *" // 秒,分,时,日,月,周 每分钟执行
	assetFundingPre       = "0 59 23,7,15 * * *" // 秒,分,时,日,月,周 每分钟执行
	floatProfitUpdate     = "0 */5 * * * *"      // 秒,分,时,日,月,周 每分钟执行
	everyMinute           = "0 * * * * *"        // 秒,分,时,日,月,周 每分钟执行
	everyFiveSecond       = "*/5 * * * * *"      // 秒,分,时,日,月,周 每5秒执行
	fundingTest           = "0 */10 * * * *"     // 秒,分,时,日,月,周
	everyHour5Minute      = "0 5 * * * *"        // 秒,分,时,日,月,周 每天每个5分的时候执行
	contractIndicator     = "0 */4 * * * *"      // 秒,分,时,日,月,周 每5秒执行
	dailyStatisticsMail   = "0 0 * * * *"        // 秒,分,时,日,月,周 每天0点执行,如果没有触发,那么下个小时的时候执行
	halfDayStatisticsMail = "0 0 16-23 * * *"    // 秒,分,时,日,月,周 每天16点执行,如果没有触发,那么下个小时的时候执行
	errCollectMail        = "10 0 * * * *"       // 秒,分,时,日,月,周 每天0点0分10秒执行,如果没有触发,那么下个10秒的时候执行
)

func RunCronTask() {
	if !conf.IsProduct() {
		////线下环境
		//task.DealFundingRate()
		//task.DealFundingCash()
	}

	defer func() {
		if e := recover(); e != nil {
			log.Errorf("任务异常恢复：%v", e)
		}
	}()

	c := cron.New()

	//启动任务
	// 启动运行
	//go task.InitFsAccount()

	// 同步币种合约信息
	_ = c.AddFunc(everyFiveSecond, task.LoadContractList)

	//行情任务
	addMarketJob(c)

	//定时任务自检
	addSelfCheck(c)

	//订单定时任务
	addOrderJob(c)

	c.Start()
}

func addOrderJob(c *cron.Cron) {
	//处理订单强平任务
	_ = c.AddFunc("*/30 * * * * *", func() {
		task.DealUnFinishOrder()
	})
}

func addSelfCheck(c *cron.Cron) {
	err := c.AddFunc(everyMinute, func() {
		log.Infof("定时任务执行检测，正常")
	})
	if err != nil {
		log.Errorf("添加定时任务自检失败，%v", err)
	}
}

func addMarketJob(c *cron.Cron) {
	c.AddFunc("0 * * * * *", func() {
		go dealDepthConfig()
		task.GenerateContractSpotIndexBaseHistory() //每分钟生成合约基准差值历史数据
	})
}

func dealDepthConfig() {
	_, err := commonsrv.LoadContractDepthConfig()
	if err != nil {
		log.Errorf("LoadContractDepthConfig fail,%v", err)
		return
	}
}
