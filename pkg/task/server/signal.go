package server

import (
	"fmt"
	"os"
	"os/signal"
	"spot/libs/log"
	"spot/libs/msg"
	"spot/libs/pprof"
	"spot/pkg/task/rpcserver"
	"syscall"
)

// InitSignal register signals handler.
func InitSignal() {
	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGHUP, syscall.SIGQUIT, syscall.SIGTERM, syscall.SIGINT)
	msg.SendCautionEmail("服务重启提醒", "task服务已重新启动完成!")
	for {
		s := <-c
		log.Infof("task service get a signal %s", s.String())
		msg.SendCautionEmail("服务停止提醒", fmt.Sprintf("task服务接收到系统信号[%s],正在关闭!", s.String()))
		switch s {
		case syscall.SIGQUIT, syscall.SIGTERM, syscall.SIGINT:
			rpcserver.ShutdownServers()
			//shutdownHTTPServer()
			//shutdownCallbackServer()
			pprof.Stop()
			return
		case syscall.SIGHUP:
			return
		default:
			return
		}
	}
}
