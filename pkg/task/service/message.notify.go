/*
@Time : 3/9/20 4:02 下午
<AUTHOR> mocha
@File : notify 通知push模块
*/
package service

import (
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/define"
	"spot/libs/json"
	"spot/libs/log"
	"spot/libs/mq"
	"spot/libs/proto"
)

func sendMessageForClosing(msg mq.MessagePack) {
	log.Info("开始发送mq消息", zap.Any("目标", define.MQDefaultMQBindKeyClosing), zap.Any("data", msg))
Retry:
	err := mqProduct.Publish(define.MQDefaultExchangeName, mq.ExchangeTypeDirect, define.MQDefaultMQBindKeyClosing, msg.Marsha(), define.MQReliable)
	if err != nil {
		log.Error("发送消息到清算失败", zap.Error(err), zap.Any("data", msg), zap.Any("msgData", string(msg.<PERSON>())))
		log.Errorf("发送mq消息失败,sendMessageForClosing err:%v", err)
		goto Retry
	}

}

func sendMessage(msg mq.MessagePack) {
	mg := msg.Marsha()
	log.Info("开始发送消息到队列", zap.String("key", define.MQDefaultMQBindKey), zap.Any("data", mg))
Retry:
	err := mqProduct.Publish(define.MQDefaultExchangeName, mq.ExchangeTypeDirect, define.MQDefaultMQBindKey, mg, define.MQReliable)
	if err != nil {
		log.Errorf("发送mq消息失败,err:%v", err)
		goto Retry
	}
}

//发送订单完成消息
func MQTopicOrderOverMsg(d *proto.MatchOverMsg) {
	b, err := json.Marshal(d)
	if err != nil {
		log.Errorf("MQTopicOrderOverMsg json marshal fail,%v", err)
		return
	}
	msg := mq.MessagePack{Topic: define.MQTopicUserOrderOver, Code: d.Code, Data: b, Identifier: d.Identifier, UserId: d.UserId}
	msg.SetExtra(d.OrderId, cache.IncrementOrderSeqId(d.OrderId))
	log.Infof("开始发送mq消息：%+v,topic:%v", string(b), msg.Topic)
	sendMessageForClosing(msg)
}

//发送订单成交消息
func MQTopicOrderTradeMsg(d *proto.OrderTrade) {
	b, err := json.Marshal(d)
	if err != nil {
		log.Errorf("MQTopicOrderTradeMsg json marshal fail,%v", err)
		return
	}
	msg := mq.MessagePack{Topic: define.MQTopicUserOrderTrade, Code: d.Code, Data: b, Identifier: d.Order.Identifier, UserId: d.Order.UserId}
	id := d.Order.OrderId
	msg.SetExtra(id, cache.IncrementOrderSeqId(id))
	log.Infof("开始发送mq消息：%+v,topic:%v", string(b), msg.Topic)
	sendMessageForClosing(msg)
}
