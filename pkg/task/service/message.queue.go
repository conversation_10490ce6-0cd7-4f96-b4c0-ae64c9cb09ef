/*
@Time : 2019-12-31 10:53
<AUTHOR> mocha
@File : message.queue
*/
package service

import (
	"spot/libs/conf"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/mq"
	"spot/libs/utils"
	"strconv"
)

var (
	mqProduct  *mq.MessageQueue
	mqConsumer *mq.MessageQueue
)

func NewMqProduct() {
	mqProduct = mq.NewMessageQueue(conf.MQ())
	mqProduct.Ping()
}

func NewMqConsumer() {
	mqConsumer = mq.NewMessageQueue(conf.MQ())
	queueName := define.MQConsumerTaskQueueName
	if conf.IsSimulate() {
		queueName = "sim:" + queueName
	}
	queueName = utils.StrBuilder(queueName, strconv.Itoa(conf.CloneID()))

	err := mqConsumer.Consumer(define.MQDefaultExchangeName, mq.ExchangeTypeDirect, queueName, define.MQDefaultMQBindKey)
	if err != nil {
		log.Errorf("创建mq消费者失败,%v", err)
	}
	log.Infof("消费者队列完毕，queueName；%+v", queueName)
	mqConsumer.Ping()
}

func MQIndexPriceChange(message mq.MessagePack) {

}

func InitMessageQueue() {
	NewMqProduct()
	//NewMqConsumer()
}

func StopMQProduct() {
	mqProduct.ShutDown()
}

func StopMQConsumer() {
	mqConsumer.ShutDown()
}
