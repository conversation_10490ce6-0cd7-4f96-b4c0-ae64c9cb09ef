package task

import (
	"context"
	"github.com/shopspring/decimal"
	"spot/libs/proto"
	"sync"

	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/coinprice"
	"spot/libs/database"
	"spot/libs/log"
)

func CacheBasicData() {
	LoadContractList()
	LoadCoinRate()
}

var _loadContractListLock sync.Mutex

// 缓存合约列表
func LoadContractList() {
	if !_loadContractListLock.TryLock() {
		return
	}
	defer _loadContractListLock.Unlock()

	// 获取全部交易对
	list, err := database.GetSupportContract()
	if err != nil {
		log.Error("LoadContractList GetSupportContract fail", zap.Error(err))
		return
	}

	// 获取市场配置
	marketConf, err := database.GetCoinMarketConf(context.Background())
	if err != nil {
		log.Error("LoadContractList GetCoinMarketConf fail", zap.Error(err))
		return
	}

	var (
		ok bool
		mc proto.CoinMarketConf
	)
	for idx, symbol := range list {
		mc, ok = marketConf[symbol.CoinName]
		if !ok {
			continue
		}
		if mc.MinAmount.IsPositive() || symbol.MinOrderMoney.IsPositive() {
			list[idx].MinOrderMoney = decimal.Max(mc.MinAmount, symbol.MinOrderMoney)
		}
	}

	cache.SaveCoinMarketConf(marketConf)

	//log.Info("LoadContractList", zap.Any("list", list))
	cache.SaveContractList(list, true)
}

func LoadCoinRate() {
	usdt, err := coinprice.GetRateByCoinGecko(1, "tether")
	if err != nil {
		log.Error("LoadCoinRate GetRateByCoinGecko fail", zap.Error(err))
		return
	}
	bitcoin, err := coinprice.GetRateByCoinGecko(1, "bitcoin")
	if err != nil {
		log.Error("LoadCoinRate GetRateByCoinGecko fail", zap.Error(err))
		return
	}
	cache.SaveCoinLegalRate("usdt", usdt)
	cache.SaveCoinLegalRate("btc", bitcoin)
}
