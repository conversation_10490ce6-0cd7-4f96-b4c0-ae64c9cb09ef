package task

//
//import (
//	"fmt"
//	"github.com/shopspring/decimal"
//	"go.uber.org/zap"
//	"spot/libs/cache"
//	"spot/libs/define"
//	"spot/libs/log"
//	"spot/libs/nums"
//	"spot/libs/proto"
//	"sort"
//	"testing"
//	"time"
//)
//
//type u struct {
//	name string
//}
//
//func Test(t *testing.T) {
//	m := map[int]u{1: {name: "liupeng"}, 2: {name: "zhang<PERSON><PERSON>"}}
//	deal(m)
//	t.Logf("%+v", m)
//}
//
//func deal(m map[int]u) {
//	for i, u := range m {
//		if i == 1 {
//			u.name = "liupeng2"
//			m[i] = u
//		}
//	}
//	fmt.Printf("11:%v\n", m)
//}
//
//type U struct {
//	Id     int64
//	Code   string
//	UserId int64
//}
//
//func TestMap(t *testing.T) {
//	l := []U{{1, "a", 1}, {2, "a", 1}, {3, "b", 1}, {4, "a", 2}, {3, "b", 2}}
//
//	s := make(map[int64]map[string][]U)
//	for _, p := range l {
//		m, ok := s[p.UserId]
//		if !ok {
//			m = make(map[string][]U)
//			s[p.UserId] = m
//		}
//		list := m[p.Code]
//		list = append(list, p)
//		m[p.Code] = list
//	}
//	t.Logf("m:%+v", s)
//}
//
//type s struct {
//	U map[string]map[int64]Giving
//}
//
//func TestDjson(t *testing.T) {
//	//log.InitLogger("","info")
//	g := Giving{
//		Value:  nums.NewFromFloat(0.02),
//		NetPos: 15,
//	}
//	s := &s{}
//	m := make(map[string]map[int64]Giving)
//
//	l := make(map[int64]Giving)
//	l[1] = g
//	m["a"] = l
//	s.U = m
//	//log.Infof("%+v",*s)
//	//n:=nums.NewFromFloat(0.32)
//	t.Logf("%+v", *s)
//}
//
//func NewUfsTotal2() UfsTotal {
//	return UfsTotal{
//		WaitGivingFull:   make(map[string]map[int64]Giving),
//		WaitGivingWare:   make(map[string]map[int64]Giving),
//		WaitGivingFollow: make(map[string]map[int64]Giving),
//		IncomeMap:        make(map[string]decimal.Decimal),
//		NetPosMap:        make(map[string]int),
//	}
//}
//
//func TestABC(t *testing.T) {
//	ut := NewUfsTotal2()
//	m := make(map[string]map[int64]Giving)
//	l := make(map[int64]Giving)
//	l[1] = Giving{Value: nums.NewFromFloat(1.0), NetPos: 20}
//	l[2] = Giving{Value: nums.NewFromFloat(1.0), NetPos: 20}
//	m["a"] = l
//	ut.WaitGivingFollow = m
//	t.Logf("%+v", ut)
//}
//
//func TestSort(t *testing.T) {
//	list := []proto.UserPosition{
//		{Id: 1, ContractCode: "BTCUSDT", Volume: 10, Margin: 50, InitMargin: 40, Commission: 10},
//		{Id: 2, ContractCode: "ETHUSDT", Volume: 5, Margin: 100, InitMargin: 100, Commission: 20},
//		{Id: 3, ContractCode: "ETHUSDT", Volume: 3, Margin: 5, InitMargin: 4, Commission: 1},
//		{Id: 4, ContractCode: "BTCUSDT", Volume: 10, Margin: 2, InitMargin: 2, Commission: 0},
//		{Id: 5, ContractCode: "BTCUSDT", Volume: 20, Margin: 2000, InitMargin: 1900, Commission: 200},
//		{Id: 6, ContractCode: "EOSUSDT", Volume: 20, Margin: 35, InitMargin: 40, Commission: 5},
//		{Id: 7, ContractCode: "EOSUSDT", Volume: 10, Margin: 70, InitMargin: 50, Commission: 20},
//	}
//	m := map[string]decimal.Decimal{"EOSUSDT": decimal.Zero, "ETHUSDT": decimal.Zero}
//	list = sortPositions(list, m)
//	for _, position := range list {
//		t.Logf("id:%v,c:%v,v；%v", position.Id, position.ContractCode, position.Margin)
//	}
//}
//
//func isMatchPosTest(code string, cMap map[string]struct{}) bool {
//	if _, ok := cMap[code]; ok {
//		return true
//	}
//	return false
//}
//
//func TestSortForGiving(t *testing.T) {
//	list := []proto.UserPosition{
//		{Id: 1, ContractCode: "BTCUSDT", Volume: 10, Margin: 50, InitMargin: 40, Commission: 10},
//		{Id: 2, ContractCode: "ETHUSDT", Volume: 5, Margin: 100, InitMargin: 100, Commission: 20},
//		{Id: 3, ContractCode: "ETHUSDT", Volume: 3, Margin: 5, InitMargin: 4, Commission: 1},
//		{Id: 4, ContractCode: "BTCUSDT", Volume: 10, Margin: 2, InitMargin: 2, Commission: 0},
//		{Id: 5, ContractCode: "BTCUSDT", Volume: 20, Margin: 2000, InitMargin: 1900, Commission: 200},
//		{Id: 6, ContractCode: "EOSUSDT", Volume: 20, Margin: 35, InitMargin: 40, Commission: 5},
//		{Id: 7, ContractCode: "EOSUSDT", Volume: 10, Margin: 70, InitMargin: 50, Commission: 20},
//	}
//	//根据用户持仓列表,排序持仓列表，扣除的合约放在前序
//	sort.Slice(list, func(i, j int) bool {
//		a, b := list[i], list[j]
//		return a.MarginDiff().GreaterThan(b.MarginDiff())
//	})
//	for _, position := range list {
//		t.Logf("diff:%+v,pos:%+v", position.MarginDiff(), position)
//	}
//}
//
//func TestRedis(t *testing.T) {
//	log.InitLogger("logs/panda/closing", "info", false)
//	log.Info("hello")
//	define.RedisCommonDb = 2
//	cache.InitDefaultRedisDBCon("127.0.0.1:6379", "", 2, false)
//	go func() {
//		var cusor uint64
//		var keys []string
//		var err error
//		var count int
//		for {
//			log.Info("开始处理数据", zap.Uint64("当前游标", cusor), zap.Int("当前处理", count))
//			keys, cusor, err = cache.DefaultRedis().Scan(cusor, "*closing:deal:cur:seq*", 200).Result()
//			if err != nil {
//				log.Error("cache.DefaultRedis().Scan fail", zap.Error(err))
//				break
//			}
//			count += len(keys)
//			for _, key := range keys {
//				e := cache.DefaultRedis().Expire(key, 12*time.Hour).Err()
//				if e != nil {
//					log.Error("设置key有效时间出错", zap.Error(err))
//				}
//				t.Log(key)
//			}
//			if cusor == 0 {
//				log.Info("处理完毕")
//				return
//			}
//		}
//
//	}()
//	select {}
//}
