package task

import (
	"github.com/shopspring/decimal"
	"spot/libs/cache"
	"spot/libs/proto"
	"time"
)

// GenerateContractSpotIndexBaseHistory 计算分钟合约基差历史
func GenerateContractSpotIndexBaseHistory() {
	spotIndex := cache.GetAllContractSpotIndexPrice()
	tradeMap := cache.GetAllPriceIndex()
	depthInfo := cache.GetAllContractDepthInfo()
	depthBase := cache.GetAllContractDepthBasePrice()

	for code, price := range spotIndex {
		if price.Price.LessThanOrEqual(decimal.Zero) {
			continue
		}
		trade, ok := tradeMap[code]
		if !ok {
			continue
		}
		di := depthInfo[code]

		cdBase := depthBase[code]
		indexBaseHistory := proto.IndexBaseHistory{
			Code:           code,
			SpotIndexPrice: price.Price,
			TradePrice:     trade.TradePrice,
			//BuyFirst:       trade.SellPrice,
			//SellFirst:      trade.BuyPrice,
			DepthInfo: di,
			DepthBase: &cdBase,
			TS:        time.Now(),
		}
		indexBaseHistory.CalBaseDiff()
		cache.PushContractSpotIndexBaseHistory(&indexBaseHistory)
	}
}
