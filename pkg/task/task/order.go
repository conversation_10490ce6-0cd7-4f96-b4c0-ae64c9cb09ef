package task

import (
	"go.uber.org/zap"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/proto"
	"spot/pkg/task/service"
	"time"
)

/***
* 本任务为处理core,follow,force生成的委托订单因rabbitmq发送失败导致无法处理的订单
 */

var (
	forceDuration  = 2 * time.Minute
	marketDuration = 30 * time.Second
)

func DealUnFinishOrder() {
	log.Info("开始检查未成交的市价订单")
	dealOrderForEntrustMarket()
}

//仅处理非第三方市场订单
func dealOrderForEntrustMarket() {
	list, err := database.ListEntrustUnFinishOrdersByOriginalMarket(nil, marketDuration)
	if err != nil {
		log.Error("dealOrderForEntrustMarket database.ListEntrustUnFinishOrdersByOriginalMarket fail", zap.Error(err))
		return
	}
	log.Info("本次检查到超过5秒钟未完成市价订单数量", zap.Int("size", len(list)))
	for _, order := range list {
		if order.MarketSource == define.MarketSourceSourceDefault {
			dealMarketOrder(order)
		}
	}

}

func dealMarketOrder(order proto.EntrustOrder) {
	t := time.Now()
	service.MQTopicOrderOverMsg(&proto.MatchOverMsg{
		Code:        order.ContractCode,
		OrderId:     order.ID,
		UserId:      order.UserID,
		Identifier:  define.IdentifierUser,
		DealerState: define.MatchOverForce,
		Ts:          t,
	})
}
