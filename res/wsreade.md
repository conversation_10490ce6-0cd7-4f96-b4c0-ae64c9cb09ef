### Panda 现货 websoket请求文档

接入URL

wss://{localhost}:{port}/v2/spot/ws/

eg:wss://sapi.officeqb.com/v2/spot/ws/

#### 数据压缩

暂不进行数据压缩

#### 心跳消息

服务器会向客户端发送ping消息,客户端收到消息后恢复pong消息,此为标准协议,暂不做特殊处理,大部分第三方库种会提供收到心跳处理逻辑，推荐使用第三方库处理

如无法处理，请使用以下自定义心跳，每50s主动向服务发送心跳，此方法为后备方案，一般不建议使用。

```
{
  "action": "heart", //动作:auth,req,sub
  "topic": "heart",
  "data": { 
        "app_id":1,
        "token":"用户token",
        "ts": 时间戳(秒),
        "sign": "签名"
  }
}
```

#### 鉴权(如不需要接收指定用户消息，可忽略掉鉴权)

成功建立与Websocket服务器的连接后，向websocket发送鉴权请求。

```
{
  "action": "auth", //动作:auth,req,sub
  "topic": "auth",
  "data": { 
        "app_id":1,
        "token":"用户token",
        "ts": 时间戳(秒),
        "sign": "签名"
  }
}
```

为简化鉴权,签名方式与api请求签名方式一致. 成功后收到返回:

```
{"action":"auth","topic":"auth","code":0,"ts":1577268301}

```

注销鉴权

```
{
  "action": "unAuth",
  "topic": "unAuth",
  "data": {
  }
}
```

#### 其他

websocket 使用自定义消息类型，发的消息需包括，action,topic,data字段，具体见以上结构。请勿主动向服务器发送任何本服务不支持的协议，后端有可能会增加安全措施，对不支持的数据结构及协议判断为攻击行为，主动断开服务。

#### 订阅主题

成功建立与Websocket服务器的连接后，Websocket客户端发送如下请求以订阅/取消订阅特定主题. 以下为本项目支持的主题:

###### [1. 初始化订阅/切换交易对](# 实例:初始化订阅/切换交易对)

topic: market.symbol.switch

   ```json
   {
  "action": "sub",
  "//": "订阅动作,sub-订阅,unSub-取消订阅 req-请求 auth-验证"
  "topic": "market.symbol.switch",
  "//": "主题",
  "data": {
    "//": "携带数据,不同请求数据不同",
    "symbol": "BTC/USDT"
  }
}
   ```

该主题可收到订阅教育最新成交,深度,合约明细,合约涨跌幅

###### [2. 订阅所有合约价格及涨跌幅](# 实例:订阅所有交易对价格及涨跌幅)

topic: symbols.market

   ```json
   {
  "action": "sub",
  "//": "订阅动作,sub-订阅,unSub-取消订阅 req-请求 auth-验证"
  "topic": "symbols.market",
  "//": "主题",
  "data": {}
}
   ```

订阅成功后,会收到所有交易对价格及涨跌幅推送,推送频率为每秒推送一次.

订阅后如不在需要改数据,可取消订阅,将action修改为"unSub",发送撤销订阅请求.

##### 实例:初始化订阅/切换交易对

```json
{
  "action": "sub",
  //订阅动作,sub-订阅,unSub-取消订阅 req-请求 auth-验证
  "topic": "market.symbol.switch",
  //主题
  "data": {
    //携带数据,不同请求数据不同
    "symbol": "BTC/USDT"
  }
}
```

订阅成功后会返回数据:

```
{
  "action": "sub",
  "topic": "market.symbol.switch",
  "code":0, //状态,0-成功
  "err_msg":"",
  "ts":1333333333, //时间
}
```

之后, 一旦所订阅的主题有更新，Websocket客户端将收到服务器推送的更新消息（push),收到的消息topic会增加项目推送的前缀："spot.",见下面topic,

```
{
  "action": "notify",
  "topic": "spot.market.ticker",
  "ts":1333333333, //时间
  "data": { //不同数据内容不同
        "symbol":"BTC/USDT",
        .....
  }
}
```

notify数据见推送类型推送类型,请参考以下类型

以下为接收到的推送数据类型:

1. [合约涨跌幅](#1-合约涨跌幅)
2. [深度数据](#2-深度数据)
3. [交易对最新成交](#3-交易对最新成交)
4. [所有交易对价格及涨跌幅](#4-所有交易对价格及涨跌幅)

用户消息：

21. [委托订单更新(需鉴权)](#21-委托订单更新)

##### 1 合约涨跌幅

notify:

```
{
  "action": "notify",
  "topic": "spot.symbol.applies",
  "ts":1333333333, //时间
  "data": {
    "symbol": "BTC/USDT", // 合约code
    "change_ratio": "12.23", // 当日涨跌幅 12.23%
    "change":"-0.237",//涨幅
    "high_price": "3000",//最高价(请与最新成交比较，取大）
    "low_price": "3000",//最低价（请于最新成交比较，取小）
    "trade_24h":"2999" //24h成交量
    "trade_money_24h":"300000.123" //24h成交额（等价于最新价*trade_24h）
  }
}
```

##### 2 深度数据

notify:

```
{
  "action": "notify",
  "topic": "spot.market.depth",
  "ts":1333333333, //时间
  "data": {
        "level":2,
        "symbol": "BTC/USDT", // 交易对,
        "digit":2,//价格精度
        "volume_digit":0,//数量精度
        "buy":[
            {
                "price": "39.20", //价格
                "amount": "1000.02" //数量,精度取决于合约下单精度
                "total_amount":1500,//累计数量
                }
        ],
        "sell":[
        {
                 "price": "39.20", //价格
                "amount": "1000.02" //数量,精度取决于合约下单精度
                "total_amount":1500,//累计数量
                        }
                ],
          }
}
```

##### 3 交易对最新成交

notify:

```
{
  "action": "notify",
  "topic": "spot.market.ticker",
  "ts":1333333333, //时间
  "data": { 
        "symbol":"BTC/USDT", // 交易对
        "list":[
            {
                "price": "39.20", //价格
                "price_cn": "6029.02" //人民币价格
                "amount":"0.279", //成交量
                "side":"B",//方向 B-买 S-卖
                "ts":133333333,//时间
            },
        ]
  }
}
```

##### 4 所有交易对价格及涨跌幅

notify:

```
本推送和合约列表数据返回一致，具体可参考rest api文档
{
  "action": "notify",
  "topic": "spot.symbols.market",
  "ts":1333333333, //时间
  "data": [
      {
    	    "symbol": "BTC/USDT", // 交易对
          "price": "10234.2323", // 最新成交价价格
          "change_ratio": "12.23", // 当日涨跌幅 12.23%
          "change":"-0.237"//涨幅
      },
    ]
}
```

##### 21 委托订单更新

notify:

```
{
  "action": "notify",
  "topic": "spot.user.entrust.order.update",
  "ts":1333333333, //时间
  "data": 
      {
      }
}
```