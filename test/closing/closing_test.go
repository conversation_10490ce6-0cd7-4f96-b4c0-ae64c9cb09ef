package closing

import (
	"spot/libs/cache"
	"spot/libs/commonsrv"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/messagequeue"
	"spot/libs/nums"
	"spot/libs/proto"
	"testing"
	"time"
)

//测试开仓成交
func TestTradeClosingFullOpen(t *testing.T) {
	log.InitLogger("logs/panda/closing", "info", false)
	log.Info("hello")
	define.RedisCommonDb = 5
	cache.InitDefaultRedisDBCon("127.0.0.1:6379", "", 5, false)
	database.InitTest("root:123456@tcp(127.0.0.1:3306)/futures_basecoin?charset=utf8&parseTime=true&loc=Local", 2)
	messagequeue.NewMqProduct("amqp://user:password@localhost:5672", "1")

	mr := &proto.MRecord{
		OrderId:         1000, //必须
		TradeId:         9,
		Code:            "BTC/USDT",
		DealPrice:       nums.NewFromString("4500"), //必须
		DealVolume:      nums.NewFromFloat(0.001),   //必须
		MatchTime:       time.Now(),
		IsProtocalTrade: false,
		IsMaker:         false,
		IsReturn:        false,
		TradeFinish:     true,
	}
	commonsrv.DealUserTrade(mr)
}

var userId int64 = 180
