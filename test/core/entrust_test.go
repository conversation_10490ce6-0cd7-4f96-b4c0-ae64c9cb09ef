package core

import (
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/commonsrv"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/nums"
	"spot/libs/proto"
	"spot/libs/xrpcclient/core_rpc"
	"testing"
)

var userId int64 = 180

func init() {
	log.InitLogger("logs/panda/closing", "info", false)
	log.Info("hello")
	define.RedisCommonDb = 5
	cache.InitDefaultRedisDBCon("127.0.0.1:6379", "", 5, false)
	database.InitDefaultDataBase("root:123456@tcp(127.0.0.1:3306)/futures_basecoin?charset=utf8&parseTime=true&loc=Local")

	core_rpc.InitClient("127.0.0.1:8500", "core")
}

func TestEntrustOpen(t *testing.T) {
	reply := new(define.Reply)
	arg := &proto.OrderOpenArgs{
		ContractCode: "BTC/USDT",
		Side:         "B",
		//Amount:          nums.NewFromFloat(0.2),
		Money:           nums.NewFromFloat(2000),
		OrderType:       0,
		UserId:          userId,
		EntrustType:     define.EntrustTypeMarket,
		EntrustStrategy: define.MatchStrategyDefault,
		Price:           decimal.Decimal{},
		Mode:            commonsrv.GetDefaultEntrustMode(),
		ClientOrderId:   database.NextID(),
	}

	err := core_rpc.EntrustOpen(arg, reply)
	if err != nil {
		log.Error("requst error", zap.Error(err), zap.Any("s", reply))
		return
	}
	log.Info("返回", zap.Any("reply", reply))
}

func TestEntrustOpen2(t *testing.T) {
	reply := new(define.Reply)
	arg := &proto.OrderOpenArgs{
		ContractCode:    "BTC/USDT",
		Side:            "S",
		Amount:          nums.NewFromFloat(0.02),
		OrderType:       0,
		UserId:          userId,
		EntrustType:     define.EntrustTypeMarket,
		EntrustStrategy: define.MatchStrategyDefault,
		Price:           decimal.Decimal{},
		Mode:            commonsrv.GetDefaultEntrustMode(),
		ClientOrderId:   database.NextID(),
	}

	err := core_rpc.EntrustOpen(arg, reply)
	if err != nil {
		log.Error("requst error", zap.Error(err), zap.Any("s", reply))
		return
	}
	log.Info("返回", zap.Any("reply", reply))
}

func TestEntrustOpenLimit(t *testing.T) {
	reply := new(define.Reply)
	arg := &proto.OrderOpenArgs{
		ContractCode:    "BTC/USDT",
		Side:            "S",
		Amount:          nums.NewFromFloat(0.02),
		OrderType:       0,
		UserId:          userId,
		EntrustType:     define.EntrustTypeLimit,
		EntrustStrategy: define.MatchStrategyDefault,
		Price:           nums.NewFromFloat(47068),
		Mode:            commonsrv.GetDefaultEntrustMode(),
	}

	err := core_rpc.EntrustOpen(arg, reply)
	if err != nil {
		log.Error("requst error", zap.Error(err), zap.Any("s", reply))
		return
	}
	log.Info("返回", zap.Any("reply", reply))
}

func TestEntrustCancel(t *testing.T) {
	reply := new(define.Reply)
	arg := &proto.OrderCancelArgs{
		RequestId: database.NextID(),
		UserId:    userId,
		ID:        "381006614418685952",
	}

	err := core_rpc.EntrustCancel(arg, reply)
	if err != nil {
		log.Error("requst error", zap.Error(err), zap.Any("s", reply))
		return
	}
	log.Info("返回", zap.Any("reply", reply))
}
