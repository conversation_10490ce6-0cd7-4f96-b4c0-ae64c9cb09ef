package core

import (
	"go.uber.org/zap"
	"spot/libs/cache"
	"spot/libs/database"
	"spot/libs/define"
	"spot/libs/log"
	"spot/libs/nums"
	"spot/libs/proto"
	"spot/libs/xrpcclient/order_rpc"
	"testing"
)

var userId int64 = 180

var followUserId int64 = 180

func init() {
	log.InitLogger("logs/spot/order", "info", false)
	log.Info("hello")
	define.RedisCommonDb = 5
	cache.InitDefaultRedisDBCon("127.0.0.1:6379", "", 5, false)
	database.InitDefaultDataBase("root:123456@tcp(127.0.0.1:3306)/futures_basecoin?charset=utf8&parseTime=true&loc=Local")

	order_rpc.InitClient("127.0.0.1:8500", "core")
}

func TestEntrustOpen(t *testing.T) {
	reply := &define.Reply{}
	arg := &proto.ThirdOrderPlaceArg{
		ReqLang:      0,
		RequestId:    database.NextID(),
		ThirdOrderId: 0,
		OrderId:      1,
		UserId:       180,
		EntrustType:  define.EntrustTypeMarket,
		Side:         "B",
		Money:        nums.NewFromFloat(20),
		MarketSource: 1,
	}

	err := order_rpc.EntrustOpen(arg, reply)
	if err != nil {
		log.Error("requst error", zap.Error(err), zap.Any("s", reply))
		return
	}
	log.Info("返回", zap.Any("reply", reply))
	//o := new(proto.ThirdOrderPlaceArg)
	//e := json.Unmarshal(reply.Data, o)

}
